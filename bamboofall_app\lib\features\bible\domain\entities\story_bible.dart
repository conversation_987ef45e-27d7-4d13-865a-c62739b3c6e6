import 'package:freezed_annotation/freezed_annotation.dart';

import 'character.dart';
import 'location.dart';
import 'world_settings.dart';

part 'story_bible.freezed.dart';
part 'story_bible.g.dart';

/// 故事圣经 - 整个小说创作的核心约束和设定系统
@freezed
class StoryBible with _$StoryBible {
  const factory StoryBible({
    required String id,
    required String projectId,
    required String title,
    String? description,
    required WorldSettings worldSettings,
    @Default([]) List<Character> characters,
    @Default([]) List<Location> locations,
    @Default([]) List<PlotLine> plotLines,
    @Default([]) List<Item> items,
    @Default([]) List<Skill> skills,
    @Default([]) List<Constraint> constraints,
    @Default({}) Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
    @Default(1) int version,
  }) = _StoryBible;

  factory StoryBible.fromJson(Map<String, dynamic> json) => _$StoryBibleFromJson(json);
}

/// 情节线
@freezed
class PlotLine with _$PlotLine {
  const factory PlotLine({
    required String id,
    required String name,
    String? description,
    required PlotLineType type,
    required PlotLineStatus status,
    @Default([]) List<PlotPoint> plotPoints,
    @Default([]) List<String> involvedCharacterIds,
    @Default([]) List<String> involvedLocationIds,
    @Default({}) Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _PlotLine;

  factory PlotLine.fromJson(Map<String, dynamic> json) => _$PlotLineFromJson(json);
}

/// 情节线类型
enum PlotLineType {
  main('主线'),
  sub('支线'),
  character('角色线'),
  world('世界线');

  const PlotLineType(this.displayName);
  final String displayName;
}

/// 情节线状态
enum PlotLineStatus {
  planning('规划中'),
  active('进行中'),
  completed('已完成'),
  paused('暂停'),
  cancelled('已取消');

  const PlotLineStatus(this.displayName);
  final String displayName;
}

/// 情节点
@freezed
class PlotPoint with _$PlotPoint {
  const factory PlotPoint({
    required String id,
    required String title,
    String? description,
    required int order,
    PlotPointStatus? status,
    @Default([]) List<String> characterIds,
    @Default([]) List<String> locationIds,
    @Default([]) List<String> itemIds,
    @Default([]) List<String> skillIds,
    String? chapterId,
    @Default({}) Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _PlotPoint;

  factory PlotPoint.fromJson(Map<String, dynamic> json) => _$PlotPointFromJson(json);
}

/// 情节点状态
enum PlotPointStatus {
  planned('已规划'),
  inProgress('进行中'),
  completed('已完成'),
  skipped('已跳过');

  const PlotPointStatus(this.displayName);
  final String displayName;
}

/// 道具/物品
@freezed
class Item with _$Item {
  const factory Item({
    required String id,
    required String name,
    String? description,
    required ItemType type,
    required ItemRarity rarity,
    String? origin,
    String? currentOwnerId, // 当前拥有者的角色ID
    @Default([]) List<String> previousOwnerIds,
    @Default([]) List<ItemProperty> properties,
    @Default([]) List<String> relatedSkillIds,
    String? imageUrl,
    @Default({}) Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _Item;

  factory Item.fromJson(Map<String, dynamic> json) => _$ItemFromJson(json);
}

/// 道具类型
enum ItemType {
  weapon('武器'),
  armor('护甲'),
  accessory('饰品'),
  consumable('消耗品'),
  tool('工具'),
  book('书籍'),
  artifact('神器'),
  currency('货币'),
  other('其他');

  const ItemType(this.displayName);
  final String displayName;
}

/// 道具稀有度
enum ItemRarity {
  common('普通'),
  uncommon('不常见'),
  rare('稀有'),
  epic('史诗'),
  legendary('传说'),
  mythic('神话');

  const ItemRarity(this.displayName);
  final String displayName;
}

/// 道具属性
@freezed
class ItemProperty with _$ItemProperty {
  const factory ItemProperty({
    required String name,
    required String value,
    String? description,
    PropertyType? type,
  }) = _ItemProperty;

  factory ItemProperty.fromJson(Map<String, dynamic> json) => _$ItemPropertyFromJson(json);
}

/// 属性类型
enum PropertyType {
  numeric('数值'),
  text('文本'),
  boolean('布尔'),
  list('列表');

  const PropertyType(this.displayName);
  final String displayName;
}

/// 技能/能力
@freezed
class Skill with _$Skill {
  const factory Skill({
    required String id,
    required String name,
    String? description,
    required SkillType type,
    required SkillLevel level,
    @Default([]) List<String> prerequisites, // 前置技能ID
    @Default([]) List<SkillEffect> effects,
    String? learnMethod,
    int? cost,
    int? cooldown,
    @Default([]) List<String> relatedItemIds,
    @Default({}) Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _Skill;

  factory Skill.fromJson(Map<String, dynamic> json) => _$SkillFromJson(json);
}

/// 技能类型
enum SkillType {
  combat('战斗'),
  magic('魔法'),
  social('社交'),
  crafting('制作'),
  knowledge('知识'),
  survival('生存'),
  other('其他');

  const SkillType(this.displayName);
  final String displayName;
}

/// 技能等级
enum SkillLevel {
  novice('新手'),
  apprentice('学徒'),
  journeyman('熟练'),
  expert('专家'),
  master('大师'),
  grandmaster('宗师');

  const SkillLevel(this.displayName);
  final String displayName;
}

/// 技能效果
@freezed
class SkillEffect with _$SkillEffect {
  const factory SkillEffect({
    required String name,
    required String description,
    EffectType? type,
    String? target,
    String? duration,
    @Default({}) Map<String, dynamic> parameters,
  }) = _SkillEffect;

  factory SkillEffect.fromJson(Map<String, dynamic> json) => _$SkillEffectFromJson(json);
}

/// 效果类型
enum EffectType {
  damage('伤害'),
  heal('治疗'),
  buff('增益'),
  debuff('减益'),
  control('控制'),
  utility('功能');

  const EffectType(this.displayName);
  final String displayName;
}

/// 约束规则
@freezed
class Constraint with _$Constraint {
  const factory Constraint({
    required String id,
    required String name,
    required String description,
    required ConstraintType type,
    required ConstraintSeverity severity,
    @Default([]) List<String> affectedEntityIds,
    @Default([]) List<ConstraintRule> rules,
    bool? isActive,
    @Default({}) Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _Constraint;

  factory Constraint.fromJson(Map<String, dynamic> json) => _$ConstraintFromJson(json);
}

/// 约束类型
enum ConstraintType {
  character('角色约束'),
  world('世界约束'),
  plot('情节约束'),
  relationship('关系约束'),
  timeline('时间线约束'),
  logic('逻辑约束');

  const ConstraintType(this.displayName);
  final String displayName;
}

/// 约束严重程度
enum ConstraintSeverity {
  suggestion('建议'),
  warning('警告'),
  error('错误'),
  critical('严重');

  const ConstraintSeverity(this.displayName);
  final String displayName;
}

/// 约束规则
@freezed
class ConstraintRule with _$ConstraintRule {
  const factory ConstraintRule({
    required String condition,
    required String action,
    String? message,
    @Default({}) Map<String, dynamic> parameters,
  }) = _ConstraintRule;

  factory ConstraintRule.fromJson(Map<String, dynamic> json) => _$ConstraintRuleFromJson(json);
}

/// 关系
@freezed
class Relationship with _$Relationship {
  const factory Relationship({
    required String id,
    required String fromEntityId,
    required String toEntityId,
    required RelationshipType type,
    String? description,
    int? strength, // 关系强度 -100 到 100
    @Default([]) List<RelationshipEvent> history,
    @Default({}) Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _Relationship;

  factory Relationship.fromJson(Map<String, dynamic> json) => _$RelationshipFromJson(json);
}

/// 关系类型
enum RelationshipType {
  family('家族'),
  friend('朋友'),
  enemy('敌人'),
  ally('盟友'),
  romantic('恋人'),
  mentor('师父'),
  student('学生'),
  rival('对手'),
  neutral('中性'),
  unknown('未知');

  const RelationshipType(this.displayName);
  final String displayName;
}

/// 关系事件
@freezed
class RelationshipEvent with _$RelationshipEvent {
  const factory RelationshipEvent({
    required String id,
    required String description,
    required DateTime timestamp,
    int? impactOnStrength,
    String? chapterId,
    @Default({}) Map<String, dynamic> metadata,
  }) = _RelationshipEvent;

  factory RelationshipEvent.fromJson(Map<String, dynamic> json) => _$RelationshipEventFromJson(json);
}

/// 时间线
@freezed
class Timeline with _$Timeline {
  const factory Timeline({
    required String id,
    required String name,
    String? description,
    @Default([]) List<TimelineEvent> events,
    DateTime? startDate,
    DateTime? endDate,
    @Default({}) Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _Timeline;

  factory Timeline.fromJson(Map<String, dynamic> json) => _$TimelineFromJson(json);
}

/// 时间线事件
@freezed
class TimelineEvent with _$TimelineEvent {
  const factory TimelineEvent({
    required String id,
    required String title,
    String? description,
    required DateTime eventDate,
    @Default([]) List<String> involvedCharacterIds,
    @Default([]) List<String> involvedLocationIds,
    String? chapterId,
    @Default({}) Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _TimelineEvent;

  factory TimelineEvent.fromJson(Map<String, dynamic> json) => _$TimelineEventFromJson(json);
}

/// 章节引用
@freezed
class ChapterReference with _$ChapterReference {
  const factory ChapterReference({
    required String id,
    required String title,
    int? chapterNumber,
    String? summary,
    @Default([]) List<String> characterIds,
    @Default([]) List<String> locationIds,
    @Default([]) List<String> plotPointIds,
    DateTime? publishDate,
    @Default({}) Map<String, dynamic> metadata,
  }) = _ChapterReference;

  factory ChapterReference.fromJson(Map<String, dynamic> json) => _$ChapterReferenceFromJson(json);
}