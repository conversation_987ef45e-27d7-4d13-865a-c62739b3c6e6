// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'character.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CharacterImpl _$$CharacterImplFromJson(
  Map<String, dynamic> json,
) => _$CharacterImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  aliases:
      (json['aliases'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  description: json['description'] as String?,
  type: $enumDecode(_$CharacterTypeEnumMap, json['type']),
  status: $enumDecode(_$CharacterStatusEnumMap, json['status']),
  appearance: json['appearance'] == null
      ? null
      : CharacterAppearance.fromJson(
          json['appearance'] as Map<String, dynamic>,
        ),
  personality: json['personality'] == null
      ? null
      : CharacterPersonality.fromJson(
          json['personality'] as Map<String, dynamic>,
        ),
  background: json['background'] == null
      ? null
      : CharacterBackground.fromJson(
          json['background'] as Map<String, dynamic>,
        ),
  abilities:
      (json['abilities'] as List<dynamic>?)
          ?.map((e) => CharacterAbility.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  skillIds:
      (json['skillIds'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  itemIds:
      (json['itemIds'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  relationshipIds:
      (json['relationshipIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  currentLocationId: json['currentLocationId'] as String?,
  goals:
      (json['goals'] as List<dynamic>?)
          ?.map((e) => CharacterGoal.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  secrets:
      (json['secrets'] as List<dynamic>?)
          ?.map((e) => CharacterSecret.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  characterArcs:
      (json['characterArcs'] as List<dynamic>?)
          ?.map((e) => CharacterArc.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  voiceStyle: json['voiceStyle'] as String?,
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
  imageUrl: json['imageUrl'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$CharacterImplToJson(_$CharacterImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'aliases': instance.aliases,
      'description': instance.description,
      'type': _$CharacterTypeEnumMap[instance.type]!,
      'status': _$CharacterStatusEnumMap[instance.status]!,
      'appearance': instance.appearance,
      'personality': instance.personality,
      'background': instance.background,
      'abilities': instance.abilities,
      'skillIds': instance.skillIds,
      'itemIds': instance.itemIds,
      'relationshipIds': instance.relationshipIds,
      'currentLocationId': instance.currentLocationId,
      'goals': instance.goals,
      'secrets': instance.secrets,
      'characterArcs': instance.characterArcs,
      'voiceStyle': instance.voiceStyle,
      'customAttributes': instance.customAttributes,
      'imageUrl': instance.imageUrl,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$CharacterTypeEnumMap = {
  CharacterType.protagonist: 'protagonist',
  CharacterType.antagonist: 'antagonist',
  CharacterType.supporting: 'supporting',
  CharacterType.minor: 'minor',
  CharacterType.background: 'background',
  CharacterType.narrator: 'narrator',
};

const _$CharacterStatusEnumMap = {
  CharacterStatus.alive: 'alive',
  CharacterStatus.dead: 'dead',
  CharacterStatus.missing: 'missing',
  CharacterStatus.unknown: 'unknown',
  CharacterStatus.inactive: 'inactive',
};

_$CharacterAppearanceImpl _$$CharacterAppearanceImplFromJson(
  Map<String, dynamic> json,
) => _$CharacterAppearanceImpl(
  age: (json['age'] as num?)?.toInt(),
  gender: json['gender'] as String?,
  height: json['height'] as String?,
  weight: json['weight'] as String?,
  build: json['build'] as String?,
  hairColor: json['hairColor'] as String?,
  hairStyle: json['hairStyle'] as String?,
  eyeColor: json['eyeColor'] as String?,
  skinTone: json['skinTone'] as String?,
  distinguishingFeatures:
      (json['distinguishingFeatures'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  clothing: json['clothing'] as String?,
  accessories:
      (json['accessories'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  overallDescription: json['overallDescription'] as String?,
  customFeatures: json['customFeatures'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$CharacterAppearanceImplToJson(
  _$CharacterAppearanceImpl instance,
) => <String, dynamic>{
  'age': instance.age,
  'gender': instance.gender,
  'height': instance.height,
  'weight': instance.weight,
  'build': instance.build,
  'hairColor': instance.hairColor,
  'hairStyle': instance.hairStyle,
  'eyeColor': instance.eyeColor,
  'skinTone': instance.skinTone,
  'distinguishingFeatures': instance.distinguishingFeatures,
  'clothing': instance.clothing,
  'accessories': instance.accessories,
  'overallDescription': instance.overallDescription,
  'customFeatures': instance.customFeatures,
};

_$CharacterPersonalityImpl _$$CharacterPersonalityImplFromJson(
  Map<String, dynamic> json,
) => _$CharacterPersonalityImpl(
  traits:
      (json['traits'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  strengths:
      (json['strengths'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  weaknesses:
      (json['weaknesses'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  fears:
      (json['fears'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  desires:
      (json['desires'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  habits:
      (json['habits'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  quirks:
      (json['quirks'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  moralAlignment: json['moralAlignment'] as String?,
  temperament: json['temperament'] as String?,
  worldview: json['worldview'] as String?,
  personalityScores:
      (json['personalityScores'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toInt()),
      ) ??
      const {},
  customTraits: json['customTraits'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$CharacterPersonalityImplToJson(
  _$CharacterPersonalityImpl instance,
) => <String, dynamic>{
  'traits': instance.traits,
  'strengths': instance.strengths,
  'weaknesses': instance.weaknesses,
  'fears': instance.fears,
  'desires': instance.desires,
  'habits': instance.habits,
  'quirks': instance.quirks,
  'moralAlignment': instance.moralAlignment,
  'temperament': instance.temperament,
  'worldview': instance.worldview,
  'personalityScores': instance.personalityScores,
  'customTraits': instance.customTraits,
};

_$CharacterBackgroundImpl _$$CharacterBackgroundImplFromJson(
  Map<String, dynamic> json,
) => _$CharacterBackgroundImpl(
  birthPlace: json['birthPlace'] as String?,
  birthDate: json['birthDate'] == null
      ? null
      : DateTime.parse(json['birthDate'] as String),
  family: json['family'] as String?,
  education: json['education'] as String?,
  occupation: json['occupation'] as String?,
  socialClass: json['socialClass'] as String?,
  languages:
      (json['languages'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  lifeEvents:
      (json['lifeEvents'] as List<dynamic>?)
          ?.map((e) => BackgroundEvent.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  currentResidence: json['currentResidence'] as String?,
  backstory: json['backstory'] as String?,
  customBackground:
      json['customBackground'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$CharacterBackgroundImplToJson(
  _$CharacterBackgroundImpl instance,
) => <String, dynamic>{
  'birthPlace': instance.birthPlace,
  'birthDate': instance.birthDate?.toIso8601String(),
  'family': instance.family,
  'education': instance.education,
  'occupation': instance.occupation,
  'socialClass': instance.socialClass,
  'languages': instance.languages,
  'lifeEvents': instance.lifeEvents,
  'currentResidence': instance.currentResidence,
  'backstory': instance.backstory,
  'customBackground': instance.customBackground,
};

_$BackgroundEventImpl _$$BackgroundEventImplFromJson(
  Map<String, dynamic> json,
) => _$BackgroundEventImpl(
  id: json['id'] as String,
  title: json['title'] as String,
  description: json['description'] as String?,
  date: json['date'] == null ? null : DateTime.parse(json['date'] as String),
  location: json['location'] as String?,
  involvedCharacterIds:
      (json['involvedCharacterIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  type: $enumDecodeNullable(_$BackgroundEventTypeEnumMap, json['type']),
  impactLevel: (json['impactLevel'] as num?)?.toInt(),
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$BackgroundEventImplToJson(
  _$BackgroundEventImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'description': instance.description,
  'date': instance.date?.toIso8601String(),
  'location': instance.location,
  'involvedCharacterIds': instance.involvedCharacterIds,
  'type': _$BackgroundEventTypeEnumMap[instance.type],
  'impactLevel': instance.impactLevel,
  'metadata': instance.metadata,
};

const _$BackgroundEventTypeEnumMap = {
  BackgroundEventType.birth: 'birth',
  BackgroundEventType.education: 'education',
  BackgroundEventType.career: 'career',
  BackgroundEventType.relationship: 'relationship',
  BackgroundEventType.trauma: 'trauma',
  BackgroundEventType.achievement: 'achievement',
  BackgroundEventType.loss: 'loss',
  BackgroundEventType.discovery: 'discovery',
  BackgroundEventType.other: 'other',
};

_$CharacterAbilityImpl _$$CharacterAbilityImplFromJson(
  Map<String, dynamic> json,
) => _$CharacterAbilityImpl(
  name: json['name'] as String,
  description: json['description'] as String?,
  type: $enumDecode(_$AbilityTypeEnumMap, json['type']),
  level: (json['level'] as num).toInt(),
  source: json['source'] as String?,
  limitations:
      (json['limitations'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  parameters: json['parameters'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$CharacterAbilityImplToJson(
  _$CharacterAbilityImpl instance,
) => <String, dynamic>{
  'name': instance.name,
  'description': instance.description,
  'type': _$AbilityTypeEnumMap[instance.type]!,
  'level': instance.level,
  'source': instance.source,
  'limitations': instance.limitations,
  'parameters': instance.parameters,
};

const _$AbilityTypeEnumMap = {
  AbilityType.physical: 'physical',
  AbilityType.mental: 'mental',
  AbilityType.magical: 'magical',
  AbilityType.supernatural: 'supernatural',
  AbilityType.technological: 'technological',
  AbilityType.social: 'social',
  AbilityType.other: 'other',
};

_$CharacterGoalImpl _$$CharacterGoalImplFromJson(Map<String, dynamic> json) =>
    _$CharacterGoalImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      type: $enumDecode(_$GoalTypeEnumMap, json['type']),
      priority: $enumDecode(_$GoalPriorityEnumMap, json['priority']),
      status: $enumDecode(_$GoalStatusEnumMap, json['status']),
      obstacles:
          (json['obstacles'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      steps:
          (json['steps'] as List<dynamic>?)?.map((e) => e as String).toList() ??
          const [],
      motivation: json['motivation'] as String?,
      deadline: json['deadline'] == null
          ? null
          : DateTime.parse(json['deadline'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$CharacterGoalImplToJson(_$CharacterGoalImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'type': _$GoalTypeEnumMap[instance.type]!,
      'priority': _$GoalPriorityEnumMap[instance.priority]!,
      'status': _$GoalStatusEnumMap[instance.status]!,
      'obstacles': instance.obstacles,
      'steps': instance.steps,
      'motivation': instance.motivation,
      'deadline': instance.deadline?.toIso8601String(),
      'metadata': instance.metadata,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$GoalTypeEnumMap = {
  GoalType.shortTerm: 'shortTerm',
  GoalType.longTerm: 'longTerm',
  GoalType.lifeGoal: 'lifeGoal',
  GoalType.immediate: 'immediate',
  GoalType.conditional: 'conditional',
};

const _$GoalPriorityEnumMap = {
  GoalPriority.low: 'low',
  GoalPriority.medium: 'medium',
  GoalPriority.high: 'high',
  GoalPriority.critical: 'critical',
};

const _$GoalStatusEnumMap = {
  GoalStatus.active: 'active',
  GoalStatus.completed: 'completed',
  GoalStatus.failed: 'failed',
  GoalStatus.abandoned: 'abandoned',
  GoalStatus.paused: 'paused',
};

_$CharacterSecretImpl _$$CharacterSecretImplFromJson(
  Map<String, dynamic> json,
) => _$CharacterSecretImpl(
  id: json['id'] as String,
  title: json['title'] as String,
  description: json['description'] as String,
  type: $enumDecode(_$SecretTypeEnumMap, json['type']),
  severity: $enumDecode(_$SecretSeverityEnumMap, json['severity']),
  knownByCharacterIds:
      (json['knownByCharacterIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  revealCondition: json['revealCondition'] as String?,
  consequence: json['consequence'] as String?,
  isRevealed: json['isRevealed'] as bool?,
  revealedAt: json['revealedAt'] == null
      ? null
      : DateTime.parse(json['revealedAt'] as String),
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$CharacterSecretImplToJson(
  _$CharacterSecretImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'description': instance.description,
  'type': _$SecretTypeEnumMap[instance.type]!,
  'severity': _$SecretSeverityEnumMap[instance.severity]!,
  'knownByCharacterIds': instance.knownByCharacterIds,
  'revealCondition': instance.revealCondition,
  'consequence': instance.consequence,
  'isRevealed': instance.isRevealed,
  'revealedAt': instance.revealedAt?.toIso8601String(),
  'metadata': instance.metadata,
  'createdAt': instance.createdAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
};

const _$SecretTypeEnumMap = {
  SecretType.identity: 'identity',
  SecretType.past: 'past',
  SecretType.relationship: 'relationship',
  SecretType.ability: 'ability',
  SecretType.knowledge: 'knowledge',
  SecretType.crime: 'crime',
  SecretType.shame: 'shame',
  SecretType.other: 'other',
};

const _$SecretSeverityEnumMap = {
  SecretSeverity.minor: 'minor',
  SecretSeverity.moderate: 'moderate',
  SecretSeverity.major: 'major',
  SecretSeverity.critical: 'critical',
};

_$CharacterArcImpl _$$CharacterArcImplFromJson(Map<String, dynamic> json) =>
    _$CharacterArcImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      type: $enumDecode(_$ArcTypeEnumMap, json['type']),
      status: $enumDecode(_$ArcStatusEnumMap, json['status']),
      milestones:
          (json['milestones'] as List<dynamic>?)
              ?.map((e) => ArcMilestone.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      startingPoint: json['startingPoint'] as String?,
      endingPoint: json['endingPoint'] as String?,
      relatedPlotLineIds:
          (json['relatedPlotLineIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$CharacterArcImplToJson(_$CharacterArcImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'type': _$ArcTypeEnumMap[instance.type]!,
      'status': _$ArcStatusEnumMap[instance.status]!,
      'milestones': instance.milestones,
      'startingPoint': instance.startingPoint,
      'endingPoint': instance.endingPoint,
      'relatedPlotLineIds': instance.relatedPlotLineIds,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$ArcTypeEnumMap = {
  ArcType.growth: 'growth',
  ArcType.fall: 'fall',
  ArcType.redemption: 'redemption',
  ArcType.transformation: 'transformation',
  ArcType.discovery: 'discovery',
  ArcType.revenge: 'revenge',
  ArcType.love: 'love',
  ArcType.sacrifice: 'sacrifice',
};

const _$ArcStatusEnumMap = {
  ArcStatus.planned: 'planned',
  ArcStatus.active: 'active',
  ArcStatus.completed: 'completed',
  ArcStatus.paused: 'paused',
  ArcStatus.cancelled: 'cancelled',
};

_$ArcMilestoneImpl _$$ArcMilestoneImplFromJson(Map<String, dynamic> json) =>
    _$ArcMilestoneImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      order: (json['order'] as num).toInt(),
      status: $enumDecodeNullable(_$MilestoneStatusEnumMap, json['status']),
      chapterId: json['chapterId'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      achievedAt: json['achievedAt'] == null
          ? null
          : DateTime.parse(json['achievedAt'] as String),
    );

Map<String, dynamic> _$$ArcMilestoneImplToJson(_$ArcMilestoneImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'order': instance.order,
      'status': _$MilestoneStatusEnumMap[instance.status],
      'chapterId': instance.chapterId,
      'metadata': instance.metadata,
      'achievedAt': instance.achievedAt?.toIso8601String(),
    };

const _$MilestoneStatusEnumMap = {
  MilestoneStatus.pending: 'pending',
  MilestoneStatus.achieved: 'achieved',
  MilestoneStatus.skipped: 'skipped',
  MilestoneStatus.modified: 'modified',
};

_$CharacterStatsImpl _$$CharacterStatsImplFromJson(Map<String, dynamic> json) =>
    _$CharacterStatsImpl(
      characterId: json['characterId'] as String,
      appearanceCount: (json['appearanceCount'] as num?)?.toInt(),
      dialogueCount: (json['dialogueCount'] as num?)?.toInt(),
      mentionCount: (json['mentionCount'] as num?)?.toInt(),
      mostInteractedCharacters:
          (json['mostInteractedCharacters'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      frequentLocations:
          (json['frequentLocations'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      emotionFrequency:
          (json['emotionFrequency'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, (e as num).toInt()),
          ) ??
          const {},
      lastAppearance: json['lastAppearance'] == null
          ? null
          : DateTime.parse(json['lastAppearance'] as String),
      customStats: json['customStats'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$CharacterStatsImplToJson(
  _$CharacterStatsImpl instance,
) => <String, dynamic>{
  'characterId': instance.characterId,
  'appearanceCount': instance.appearanceCount,
  'dialogueCount': instance.dialogueCount,
  'mentionCount': instance.mentionCount,
  'mostInteractedCharacters': instance.mostInteractedCharacters,
  'frequentLocations': instance.frequentLocations,
  'emotionFrequency': instance.emotionFrequency,
  'lastAppearance': instance.lastAppearance?.toIso8601String(),
  'customStats': instance.customStats,
};
