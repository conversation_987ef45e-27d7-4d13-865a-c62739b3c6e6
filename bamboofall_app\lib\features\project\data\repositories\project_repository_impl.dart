import 'dart:convert';
import 'dart:io';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';

import '../../domain/entities/project.dart';
import '../../domain/repositories/project_repository.dart';
import '../models/project_model.dart';

/// 项目仓库实现（文件存储）
/// 提供项目数据的文件存储和访问
class ProjectRepositoryImpl implements ProjectRepository {
  final List<Project> _projects = [];
  final List<ProjectTemplate> _templates = [];
  final Map<String, DateTime> _accessTimes = {};
  
  bool _initialized = false;

  /// 初始化仓库
  Future<void> _ensureInitialized() async {
    if (_initialized) return;
    
    await _loadProjects();
    await _loadTemplates();
    await _initializeBuiltInTemplates();
    
    _initialized = true;
  }

  /// 获取项目存储文件路径
  Future<String> _getProjectsFilePath() async {
    final directory = await getApplicationDocumentsDirectory();
    final projectsDir = Directory('${directory.path}/bamboofall/projects');
    if (!await projectsDir.exists()) {
      await projectsDir.create(recursive: true);
    }
    return '${projectsDir.path}/projects.json';
  }

  /// 获取模板存储文件路径
  Future<String> _getTemplatesFilePath() async {
    final directory = await getApplicationDocumentsDirectory();
    final projectsDir = Directory('${directory.path}/bamboofall/projects');
    if (!await projectsDir.exists()) {
      await projectsDir.create(recursive: true);
    }
    return '${projectsDir.path}/templates.json';
  }

  /// 加载项目数据
  Future<void> _loadProjects() async {
    try {
      final filePath = await _getProjectsFilePath();
      final file = File(filePath);
      
      if (await file.exists()) {
        final jsonString = await file.readAsString();
        final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;
        
        final projectsJson = jsonData['projects'] as List<dynamic>? ?? [];
        final accessTimesJson = jsonData['accessTimes'] as Map<String, dynamic>? ?? {};
        
        _projects.clear();
        for (final projectJson in projectsJson) {
          final projectModel = ProjectModel.fromJson(projectJson as Map<String, dynamic>);
          _projects.add(projectModel.toEntity());
        }
        
        _accessTimes.clear();
        accessTimesJson.forEach((key, value) {
          _accessTimes[key] = DateTime.parse(value as String);
        });
      }
    } catch (e) {
      // 如果加载失败，使用空列表
      _projects.clear();
      _accessTimes.clear();
    }
  }

  /// 保存项目数据
  Future<void> _saveProjects() async {
    try {
      final filePath = await _getProjectsFilePath();
      final file = File(filePath);
      
      final projectsJson = _projects.map((project) {
        final model = ProjectModel.fromEntity(project);
        return model.toJson();
      }).toList();
      
      final accessTimesJson = <String, String>{};
      _accessTimes.forEach((key, value) {
        accessTimesJson[key] = value.toIso8601String();
      });
      
      final jsonData = {
        'projects': projectsJson,
        'accessTimes': accessTimesJson,
      };
      
      await file.writeAsString(jsonEncode(jsonData));
    } catch (e) {
      // 保存失败时抛出异常
      throw Exception('Failed to save projects: $e');
    }
  }

  /// 加载模板数据
  Future<void> _loadTemplates() async {
    try {
      final filePath = await _getTemplatesFilePath();
      final file = File(filePath);
      
      if (await file.exists()) {
        final jsonString = await file.readAsString();
        final templatesJson = jsonDecode(jsonString) as List<dynamic>;
        
        _templates.clear();
        for (final templateJson in templatesJson) {
          final templateModel = ProjectTemplateModel.fromJson(templateJson as Map<String, dynamic>);
          _templates.add(_templateModelToEntity(templateModel));
        }
      }
    } catch (e) {
      // 如果加载失败，使用空列表
      _templates.clear();
    }
  }

  /// 保存模板数据
  Future<void> _saveTemplates() async {
    try {
      final filePath = await _getTemplatesFilePath();
      final file = File(filePath);
      
      final templatesJson = _templates.map((template) {
        final model = _templateEntityToModel(template);
        return model.toJson();
      }).toList();
      
      await file.writeAsString(jsonEncode(templatesJson));
    } catch (e) {
      throw Exception('Failed to save templates: $e');
    }
  }

  /// 模板实体转换为模型
  ProjectTemplateModel _templateEntityToModel(ProjectTemplate template) {
    return ProjectTemplateModel(
      templateId: template.id,
      name: template.name,
      description: template.description,
      type: template.type,
      createdAt: template.createdAt,
      createdBy: 'system',
      tags: template.tags,
      configJson: jsonEncode(template.config.toJson()),
      isBuiltIn: template.isBuiltIn,
      isPublic: true,
      usageCount: 0,
      rating: 0.0,
    );
  }

  /// 模板模型转换为实体
  ProjectTemplate _templateModelToEntity(ProjectTemplateModel model) {
    return ProjectTemplate(
      id: model.templateId,
      name: model.name,
      description: model.description,
      type: model.type,
      config: model.toProjectConfig(),
      tags: model.tags,
      isBuiltIn: model.isBuiltIn,
      createdAt: model.createdAt,
    );
  }

  /// 初始化内置模板
  Future<void> _initializeBuiltInTemplates() async {
    if (_templates.any((t) => t.isBuiltIn)) return;

    final builtInTemplates = [
      ProjectTemplate(
        id: 'template_novel_basic',
        name: '基础小说模板',
        description: '适合长篇小说创作的基础模板，包含标准的章节结构和写作目标设置',
        type: ProjectType.novel,
        config: const ProjectConfig(
          targetWordCount: 80000,
          dailyWritingGoal: 1000,
          enableVersionControl: true,
          enableAIAssistant: true,
        ),
        tags: ['小说', '长篇', '基础'],
        isBuiltIn: true,
        createdAt: DateTime.now(),
      ),
      ProjectTemplate(
        id: 'template_short_story',
        name: '短篇故事模板',
        description: '适合短篇故事创作的模板，设置较小的字数目标和快速完成周期',
        type: ProjectType.shortStory,
        config: const ProjectConfig(
          targetWordCount: 5000,
          dailyWritingGoal: 500,
          enableVersionControl: true,
          enableAIAssistant: true,
        ),
        tags: ['短篇', '故事'],
        isBuiltIn: true,
        createdAt: DateTime.now(),
      ),
      ProjectTemplate(
        id: 'template_screenplay',
        name: '剧本创作模板',
        description: '专为剧本创作设计的模板，包含剧本格式和结构要求',
        type: ProjectType.screenplay,
        config: const ProjectConfig(
          targetWordCount: 25000,
          dailyWritingGoal: 800,
          enableVersionControl: true,
          enableAIAssistant: true,
        ),
        tags: ['剧本', '影视'],
        isBuiltIn: true,
        createdAt: DateTime.now(),
      ),
    ];

    _templates.addAll(builtInTemplates);
    await _saveTemplates();
  }

  @override
  Future<List<Project>> getAllProjects() async {
    await _ensureInitialized();
    return List.from(_projects);
  }

  @override
  Future<Project?> getProjectById(String projectId) async {
    await _ensureInitialized();
    try {
      return _projects.firstWhere((project) => project.id == projectId);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<List<Project>> getProjectsByStatus(ProjectStatus status) async {
    await _ensureInitialized();
    return _projects.where((project) => project.status == status).toList();
  }

  @override
  Future<List<Project>> getFavoriteProjects() async {
    await _ensureInitialized();
    return _projects.where((project) => project.isFavorite).toList();
  }

  @override
  Future<List<Project>> getProjectsByTag(String tag) async {
    await _ensureInitialized();
    return _projects.where((project) => project.tags.contains(tag)).toList();
  }

  @override
  Future<List<Project>> searchProjects(String query) async {
    await _ensureInitialized();
    if (query.trim().isEmpty) return [];

    final lowercaseQuery = query.toLowerCase();
    return _projects.where((project) {
      return project.name.toLowerCase().contains(lowercaseQuery) ||
             project.description.toLowerCase().contains(lowercaseQuery) ||
             project.tags.any((tag) => tag.toLowerCase().contains(lowercaseQuery));
    }).toList();
  }

  @override
  Future<Project> createProject(Project project) async {
    await _ensureInitialized();
    _projects.add(project);
    await _saveProjects();
    return project;
  }

  @override
  Future<Project> updateProject(Project project) async {
    await _ensureInitialized();
    final index = _projects.indexWhere((p) => p.id == project.id);
    if (index != -1) {
      _projects[index] = project;
      await _saveProjects();
      return project;
    } else {
      throw Exception('Project not found: ${project.id}');
    }
  }

  @override
  Future<void> deleteProject(String projectId) async {
    await _ensureInitialized();
    _projects.removeWhere((project) => project.id == projectId);
    _accessTimes.remove(projectId);
    await _saveProjects();
  }

  @override
  Future<Project> archiveProject(String projectId, String reason) async {
    await _ensureInitialized();
    final project = await getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    final archivedProject = project.archive(reason);
    return await updateProject(archivedProject);
  }

  @override
  Future<Project> restoreProject(String projectId) async {
    await _ensureInitialized();
    final project = await getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    final restoredProject = project.restore();
    return await updateProject(restoredProject);
  }

  @override
  Future<Map<ProjectStatus, int>> getProjectStatistics() async {
    await _ensureInitialized();
    final stats = <ProjectStatus, int>{};
    
    for (final status in ProjectStatus.values) {
      stats[status] = _projects.where((p) => p.status == status).length;
    }
    
    return stats;
  }

  @override
  Future<List<Project>> getRecentProjects({int limit = 10}) async {
    await _ensureInitialized();
    final projectsWithAccess = _projects.where((p) => _accessTimes.containsKey(p.id)).toList();
    
    projectsWithAccess.sort((a, b) {
      final aTime = _accessTimes[a.id] ?? DateTime.fromMillisecondsSinceEpoch(0);
      final bTime = _accessTimes[b.id] ?? DateTime.fromMillisecondsSinceEpoch(0);
      return bTime.compareTo(aTime);
    });
    
    return projectsWithAccess.take(limit).toList();
  }

  @override
  Future<void> updateProjectAccessTime(String projectId) async {
    await _ensureInitialized();
    _accessTimes[projectId] = DateTime.now();
    await _saveProjects();
  }

  @override
  Future<void> cleanupDeletedProjects({Duration olderThan = const Duration(days: 30)}) async {
    await _ensureInitialized();
    final cutoffDate = DateTime.now().subtract(olderThan);
    
    _projects.removeWhere((project) => 
        project.status == ProjectStatus.deleted && 
        project.updatedAt.isBefore(cutoffDate));
    
    await _saveProjects();
  }

  @override
  Future<Map<String, dynamic>> exportProject(String projectId) async {
    await _ensureInitialized();
    final project = await getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    final model = ProjectModel.fromEntity(project);
    return {
      'project': model.toJson(),
      'exportedAt': DateTime.now().toIso8601String(),
      'version': '1.0',
    };
  }

  @override
  Future<Project> importProject(Map<String, dynamic> projectData) async {
    await _ensureInitialized();
    final projectJson = projectData['project'] as Map<String, dynamic>;
    final model = ProjectModel.fromJson(projectJson);
    final project = model.toEntity();
    
    // 确保项目ID唯一
    final existingProject = await getProjectById(project.id);
    if (existingProject != null) {
      throw Exception('Project with ID ${project.id} already exists');
    }
    
    return await createProject(project);
  }

  @override
  Future<Project> duplicateProject(String projectId, String newName) async {
    await _ensureInitialized();
    final originalProject = await getProjectById(projectId);
    if (originalProject == null) {
      throw Exception('Project not found: $projectId');
    }

    final now = DateTime.now();
    final newProjectId = 'project_${now.millisecondsSinceEpoch}_copy';
    
    final duplicatedProject = Project(
      id: newProjectId,
      name: newName,
      description: '${originalProject.description}\n\n(复制自: ${originalProject.name})',
      type: originalProject.type,
      status: ProjectStatus.active,
      createdAt: now,
      updatedAt: now,
      createdBy: originalProject.createdBy,
      coverImagePath: originalProject.coverImagePath,
      tags: [...originalProject.tags, '复制'],
      config: originalProject.config,
      statistics: const ProjectStatistics(),
      templateId: originalProject.templateId,
      colorTheme: originalProject.colorTheme,
      customFields: Map.from(originalProject.customFields),
    );

    return await createProject(duplicatedProject);
  }

  @override
  Future<List<ProjectTemplate>> getProjectTemplates() async {
    await _ensureInitialized();
    return List.from(_templates);
  }

  @override
  Future<ProjectTemplate> createProjectTemplate(ProjectTemplate template) async {
    await _ensureInitialized();
    _templates.add(template);
    await _saveTemplates();
    return template;
  }

  @override
  Future<void> deleteProjectTemplate(String templateId) async {
    await _ensureInitialized();
    _templates.removeWhere((template) => template.id == templateId);
    await _saveTemplates();
  }

  @override
  Future<Project> createProjectFromTemplate(String templateId, String projectName, String createdBy) async {
    await _ensureInitialized();
    final template = _templates.where((t) => t.id == templateId).firstOrNull;
    if (template == null) {
      throw Exception('Template not found: $templateId');
    }

    final now = DateTime.now();
    final projectId = 'project_${now.millisecondsSinceEpoch}_${(1000 + (999 * (now.microsecond / 1000000))).round()}';

    final project = Project(
      id: projectId,
      name: projectName,
      description: template.description,
      type: template.type,
      status: ProjectStatus.active,
      createdAt: now,
      updatedAt: now,
      createdBy: createdBy,
      tags: template.tags,
      config: template.config,
      statistics: const ProjectStatistics(),
      templateId: templateId,
    );

    return await createProject(project);
  }

  /// 初始化示例数据
  Future<void> initializeSampleData() async {
    await _ensureInitialized();
    if (_projects.isNotEmpty) return;

    final now = DateTime.now();
    
    final sampleProjects = [
      Project(
        id: 'project_sample_1',
        name: '我的第一部小说',
        description: '这是我的第一部长篇小说，讲述了一个关于成长和友谊的故事。',
        type: ProjectType.novel,
        status: ProjectStatus.active,
        createdAt: now.subtract(const Duration(days: 30)),
        updatedAt: now.subtract(const Duration(days: 1)),
        createdBy: 'user',
        tags: ['小说', '成长', '友谊'],
        config: const ProjectConfig(
          targetWordCount: 80000,
          dailyWritingGoal: 1000,
        ),
        statistics: const ProjectStatistics(
          totalWordCount: 25000,
          totalCharacterCount: 50000,
          chapterCount: 10,
          writingDays: 25,
          averageDailyWords: 1000,
          completionProgress: 31.25,
        ),
        isFavorite: true,
        colorTheme: '#2196F3',
      ),
      
      Project(
        id: 'project_sample_2',
        name: '短篇故事集',
        description: '收集了我写的各种短篇故事。',
        type: ProjectType.shortStory,
        status: ProjectStatus.completed,
        createdAt: now.subtract(const Duration(days: 60)),
        updatedAt: now.subtract(const Duration(days: 10)),
        createdBy: 'user',
        tags: ['短篇', '故事集'],
        config: const ProjectConfig(
          targetWordCount: 15000,
          dailyWritingGoal: 500,
        ),
        statistics: const ProjectStatistics(
          totalWordCount: 15000,
          totalCharacterCount: 30000,
          chapterCount: 5,
          writingDays: 30,
          averageDailyWords: 500,
          completionProgress: 100.0,
        ),
        colorTheme: '#4CAF50',
      ),
      
      Project(
        id: 'project_sample_3',
        name: '剧本草稿',
        description: '正在创作的一个剧本，还在早期阶段。',
        type: ProjectType.screenplay,
        status: ProjectStatus.paused,
        createdAt: now.subtract(const Duration(days: 15)),
        updatedAt: now.subtract(const Duration(days: 7)),
        createdBy: 'user',
        tags: ['剧本', '草稿'],
        config: const ProjectConfig(
          targetWordCount: 25000,
          dailyWritingGoal: 800,
        ),
        statistics: const ProjectStatistics(
          totalWordCount: 5000,
          totalCharacterCount: 10000,
          chapterCount: 2,
          writingDays: 8,
          averageDailyWords: 625,
          completionProgress: 20.0,
        ),
        colorTheme: '#FF9800',
      ),
    ];

    for (final project in sampleProjects) {
      await createProject(project);
      _accessTimes[project.id] = project.updatedAt;
    }
  }
}

/// 项目仓库实现提供者
final projectRepositoryImplProvider = Provider<ProjectRepositoryImpl>((ref) {
  final repository = ProjectRepositoryImpl();
  // 初始化示例数据
  repository.initializeSampleData();
  return repository;
});

/// 重新定义项目仓库提供者
final projectRepositoryProvider = Provider<ProjectRepository>((ref) {
  return ref.read(projectRepositoryImplProvider);
});