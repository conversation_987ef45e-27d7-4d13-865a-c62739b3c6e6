# BambooFall App Release Packaging Script
# Create distribution-ready packages

param(
    [string]$Platform = "windows",
    [string]$OutputDir = ".\packages"
)

Write-Host "=== BambooFall App Packaging Script ===" -ForegroundColor Green

# Create output directory
if (!(Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir
}

# Get app information
$appName = "BambooFall"
$version = "1.0.0"
$buildNumber = "1"
$releaseDir = ".\release"

# Windows packaging
if ($Platform -eq "windows" -or $Platform -eq "all") {
    Write-Host "Packaging Windows version..." -ForegroundColor Cyan
    
    $windowsSourceDir = "$releaseDir\windows"
    if (Test-Path $windowsSourceDir) {
        $windowsPackageName = "${appName}_v${version}_Windows_x64.zip"
        $windowsPackagePath = "$OutputDir\$windowsPackageName"
        
        # Create ZIP package
        if (Test-Path $windowsPackagePath) {
            Remove-Item $windowsPackagePath
        }
        
        Compress-Archive -Path "$windowsSourceDir\*" -DestinationPath $windowsPackagePath
        
        Write-Host "Windows package created: $windowsPackagePath" -ForegroundColor Green
        
        # Create portable version directory
        $portableDir = "$OutputDir\${appName}_v${version}_Windows_Portable"
        if (Test-Path $portableDir) {
            Remove-Item -Recurse -Force $portableDir
        }
        Copy-Item -Recurse $windowsSourceDir $portableDir
        
        # Create launch script
        $launchScript = @"
@echo off
title BambooFall - AI Writing Assistant
echo Starting BambooFall application...
echo.
bamboofall_app.exe
pause
"@
        $launchScript | Out-File -FilePath "$portableDir\Launch_BambooFall.bat" -Encoding UTF8
        
        Write-Host "Windows portable version created: $portableDir" -ForegroundColor Green
    } else {
        Write-Host "Windows build files not found, please run build script first" -ForegroundColor Red
    }
}

# macOS packaging
if ($Platform -eq "macos" -or $Platform -eq "all") {
    Write-Host "Packaging macOS version..." -ForegroundColor Cyan
    
    $macosSourceDir = "$releaseDir\macos"
    if (Test-Path $macosSourceDir) {
        $macosPackageName = "${appName}_v${version}_macOS.zip"
        $macosPackagePath = "$OutputDir\$macosPackageName"
        
        # Create ZIP package
        if (Test-Path $macosPackagePath) {
            Remove-Item $macosPackagePath
        }
        
        Compress-Archive -Path "$macosSourceDir\*" -DestinationPath $macosPackagePath
        
        Write-Host "macOS package created: $macosPackagePath" -ForegroundColor Green
    } else {
        Write-Host "macOS build files not found" -ForegroundColor Yellow
    }
}

# Linux packaging
if ($Platform -eq "linux" -or $Platform -eq "all") {
    Write-Host "Packaging Linux version..." -ForegroundColor Cyan
    
    $linuxSourceDir = "$releaseDir\linux"
    if (Test-Path $linuxSourceDir) {
        $linuxPackageName = "${appName}_v${version}_Linux_x64.tar.gz"
        $linuxPackagePath = "$OutputDir\$linuxPackageName"
        
        # Create tar.gz package (use ZIP as alternative on Windows)
        Write-Host "Linux package needs tar.gz format on Linux system" -ForegroundColor Yellow
        
        # Create ZIP package as alternative
        $linuxZipName = "${appName}_v${version}_Linux_x64.zip"
        $linuxZipPath = "$OutputDir\$linuxZipName"
        
        if (Test-Path $linuxZipPath) {
            Remove-Item $linuxZipPath
        }
        
        Compress-Archive -Path "$linuxSourceDir\*" -DestinationPath $linuxZipPath
        
        Write-Host "Linux package created: $linuxZipPath" -ForegroundColor Green
    } else {
        Write-Host "Linux build files not found" -ForegroundColor Yellow
    }
}

# Create release notes
$releaseNotes = @"
# BambooFall v${version} Release Notes

## Application Information
- **App Name**: BambooFall
- **Version**: ${version}
- **Build Number**: ${buildNumber}
- **Release Date**: $(Get-Date -Format 'yyyy-MM-dd')

## Features
- AI-assisted novel writing
- Multi-AI model integration (OpenAI, Claude, Gemini, etc.)
- Local data storage and management
- Cross-platform desktop application support
- Modern user interface

## System Requirements

### Windows
- Windows 10 or higher
- x64 architecture

### macOS
- macOS 10.15 or higher
- Intel or Apple Silicon

### Linux
- Modern Linux distributions
- x64 architecture

## Installation Instructions

### Windows
1. Download BambooFall_v${version}_Windows_x64.zip
2. Extract to any directory
3. Run bamboofall_app.exe or double-click Launch_BambooFall.bat

### macOS
1. Download BambooFall_v${version}_macOS.zip
2. Extract and drag the app to Applications folder
3. First run may require allowing in System Preferences

### Linux
1. Download BambooFall_v${version}_Linux_x64.zip
2. Extract to any directory
3. Grant execute permissions and run the application

## Technical Support
Contact the development team for any issues.

---
Build Time: $(Get-Date)
Flutter Version: $(flutter --version | Select-String 'Flutter' | Select-Object -First 1)
"@

$releaseNotes | Out-File -FilePath "$OutputDir\Release_Notes.txt" -Encoding UTF8

Write-Host "`n=== Packaging Complete ===" -ForegroundColor Green
Write-Host "Package location: $OutputDir" -ForegroundColor Cyan
Write-Host "Release notes created: $OutputDir\Release_Notes.txt" -ForegroundColor Cyan

# Display package size information
Write-Host "`nPackage Size Information:" -ForegroundColor Yellow
Get-ChildItem $OutputDir -File | ForEach-Object {
    $sizeInMB = [math]::Round($_.Length / 1MB, 2)
    Write-Host "$($_.Name): ${sizeInMB} MB"
}