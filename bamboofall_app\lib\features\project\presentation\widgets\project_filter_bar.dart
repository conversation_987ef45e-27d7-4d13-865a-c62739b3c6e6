import 'package:flutter/material.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

import '../../domain/entities/project.dart';
import '../pages/project_list_page.dart';

/// 项目筛选栏组件
/// 提供搜索、筛选和排序功能
class ProjectFilterBar extends StatelessWidget {
  final String searchQuery;
  final ProjectStatus? statusFilter;
  final String? tagFilter;
  final ProjectSortBy sortBy;
  final bool sortAscending;
  final bool showFavoritesOnly;
  final ValueChanged<String> onSearchChanged;
  final ValueChanged<ProjectStatus?> onStatusFilterChanged;
  final ValueChanged<String?> onTagFilterChanged;
  final Function(ProjectSortBy, bool) onSortChanged;
  final ValueChanged<bool> onFavoritesToggled;

  const ProjectFilterBar({
    super.key,
    required this.searchQuery,
    required this.statusFilter,
    required this.tagFilter,
    required this.sortBy,
    required this.sortAscending,
    required this.showFavoritesOnly,
    required this.onSearchChanged,
    required this.onStatusFilterChanged,
    required this.onTagFilterChanged,
    required this.onSortChanged,
    required this.onFavoritesToggled,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: fluent.FluentTheme.of(context).resources.layerFillColorDefault,
        border: Border(
          bottom: BorderSide(
            color: fluent.FluentTheme.of(context).resources.dividerStrokeColorDefault,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // 第一行：搜索框和收藏筛选
          Row(
            children: [
              Expanded(
                child: fluent.TextBox(
                  placeholder: '搜索项目...',
                  prefix: const Padding(
                    padding: EdgeInsets.only(left: 8),
                    child: fluent.Icon(fluent.FluentIcons.search),
                  ),
                  suffix: searchQuery.isNotEmpty
                      ? fluent.IconButton(
                          icon: const fluent.Icon(fluent.FluentIcons.clear),
                          onPressed: () => onSearchChanged(''),
                        )
                      : null,
                  onChanged: onSearchChanged,
                ),
              ),
              const SizedBox(width: 16),
              fluent.ToggleButton(
                checked: showFavoritesOnly,
                onChanged: onFavoritesToggled,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    fluent.Icon(
                      showFavoritesOnly 
                          ? fluent.FluentIcons.favorite_star_fill 
                          : fluent.FluentIcons.favorite_star,
                      color: showFavoritesOnly ? Colors.amber : null,
                    ),
                    const SizedBox(width: 4),
                    const Text('收藏'),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // 第二行：筛选和排序选项
          Row(
            children: [
              // 状态筛选
              _buildStatusFilter(),
              const SizedBox(width: 16),
              
              // 标签筛选
              _buildTagFilter(),
              
              const Spacer(),
              
              // 排序选项
              _buildSortOptions(),
            ],
          ),
          
          // 活跃筛选指示器
          if (_hasActiveFilters()) ...[
            const SizedBox(height: 12),
            _buildActiveFilters(),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusFilter() {
    return fluent.ComboBox<ProjectStatus?>(
      placeholder: const Text('所有状态'),
      value: statusFilter,
      items: [
        const fluent.ComboBoxItem<ProjectStatus?>(
          value: null,
          child: Text('所有状态'),
        ),
        ...ProjectStatus.values.map((status) {
          return fluent.ComboBoxItem<ProjectStatus?>(
            value: status,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: _getStatusColor(status),
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Text(status.displayName),
              ],
            ),
          );
        }),
      ],
      onChanged: onStatusFilterChanged,
    );
  }

  Widget _buildTagFilter() {
    // TODO: 从项目中获取所有可用标签
    final availableTags = ['小说', '短篇', '剧本', '诗歌', '散文', '学术', '技术'];
    
    return fluent.ComboBox<String?>(
      placeholder: const Text('所有标签'),
      value: tagFilter,
      items: [
        const fluent.ComboBoxItem<String?>(
          value: null,
          child: Text('所有标签'),
        ),
        ...availableTags.map((tag) {
          return fluent.ComboBoxItem<String?>(
            value: tag,
            child: Text(tag),
          );
        }),
      ],
      onChanged: onTagFilterChanged,
    );
  }

  Widget _buildSortOptions() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        fluent.ComboBox<ProjectSortBy>(
          value: sortBy,
          items: ProjectSortBy.values.map((sort) {
            return fluent.ComboBoxItem<ProjectSortBy>(
              value: sort,
              child: Text(sort.displayName),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              onSortChanged(value, sortAscending);
            }
          },
        ),
        const SizedBox(width: 8),
        fluent.IconButton(
          icon: fluent.Icon(
            sortAscending 
                ? fluent.FluentIcons.sort_up 
                : fluent.FluentIcons.sort_down,
          ),
          onPressed: () => onSortChanged(sortBy, !sortAscending),
        ),
      ],
    );
  }

  Widget _buildActiveFilters() {
    final filters = <Widget>[];
    
    if (searchQuery.isNotEmpty) {
      filters.add(_buildFilterChip('搜索: $searchQuery', () => onSearchChanged('')));
    }
    
    if (statusFilter != null) {
      filters.add(_buildFilterChip(
        '状态: ${statusFilter!.displayName}', 
        () => onStatusFilterChanged(null),
      ));
    }
    
    if (tagFilter != null) {
      filters.add(_buildFilterChip(
        '标签: $tagFilter', 
        () => onTagFilterChanged(null),
      ));
    }
    
    if (showFavoritesOnly) {
      filters.add(_buildFilterChip('仅收藏', () => onFavoritesToggled(false)));
    }
    
    return Row(
      children: [
        const Text(
          '活跃筛选: ',
          style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
        ),
        ...filters.map((filter) => Padding(
          padding: const EdgeInsets.only(right: 8),
          child: filter,
        )),
        fluent.Button(
          onPressed: _clearAllFilters,
          child: const Text('清除全部'),
        ),
      ],
    );
  }

  Widget _buildFilterChip(String label, VoidCallback onRemove) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 11),
          ),
          const SizedBox(width: 4),
          fluent.IconButton(
            icon: const fluent.Icon(fluent.FluentIcons.clear, size: 10),
            onPressed: onRemove,
          ),
        ],
      ),
    );
  }

  bool _hasActiveFilters() {
    return searchQuery.isNotEmpty ||
           statusFilter != null ||
           tagFilter != null ||
           showFavoritesOnly;
  }

  void _clearAllFilters() {
    onSearchChanged('');
    onStatusFilterChanged(null);
    onTagFilterChanged(null);
    onFavoritesToggled(false);
  }

  Color _getStatusColor(ProjectStatus status) {
    switch (status) {
      case ProjectStatus.active:
        return Colors.green;
      case ProjectStatus.paused:
        return Colors.orange;
      case ProjectStatus.completed:
        return Colors.blue;
      case ProjectStatus.archived:
        return Colors.grey;
      case ProjectStatus.deleted:
        return Colors.red;
    }
  }
}