// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'character.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

Character _$CharacterFromJson(Map<String, dynamic> json) {
  return _Character.fromJson(json);
}

/// @nodoc
mixin _$Character {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  List<String> get aliases => throw _privateConstructorUsedError; // 别名
  String? get description => throw _privateConstructorUsedError;
  CharacterType get type => throw _privateConstructorUsedError;
  CharacterStatus get status => throw _privateConstructorUsedError;
  CharacterAppearance? get appearance => throw _privateConstructorUsedError;
  CharacterPersonality? get personality => throw _privateConstructorUsedError;
  CharacterBackground? get background => throw _privateConstructorUsedError;
  List<CharacterAbility> get abilities => throw _privateConstructorUsedError;
  List<String> get skillIds => throw _privateConstructorUsedError;
  List<String> get itemIds => throw _privateConstructorUsedError;
  List<String> get relationshipIds => throw _privateConstructorUsedError;
  String? get currentLocationId => throw _privateConstructorUsedError;
  List<CharacterGoal> get goals => throw _privateConstructorUsedError;
  List<CharacterSecret> get secrets => throw _privateConstructorUsedError;
  List<CharacterArc> get characterArcs => throw _privateConstructorUsedError;
  String? get voiceStyle => throw _privateConstructorUsedError; // 说话风格
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this Character to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Character
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CharacterCopyWith<Character> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CharacterCopyWith<$Res> {
  factory $CharacterCopyWith(Character value, $Res Function(Character) then) =
      _$CharacterCopyWithImpl<$Res, Character>;
  @useResult
  $Res call({
    String id,
    String name,
    List<String> aliases,
    String? description,
    CharacterType type,
    CharacterStatus status,
    CharacterAppearance? appearance,
    CharacterPersonality? personality,
    CharacterBackground? background,
    List<CharacterAbility> abilities,
    List<String> skillIds,
    List<String> itemIds,
    List<String> relationshipIds,
    String? currentLocationId,
    List<CharacterGoal> goals,
    List<CharacterSecret> secrets,
    List<CharacterArc> characterArcs,
    String? voiceStyle,
    Map<String, dynamic> customAttributes,
    String? imageUrl,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });

  $CharacterAppearanceCopyWith<$Res>? get appearance;
  $CharacterPersonalityCopyWith<$Res>? get personality;
  $CharacterBackgroundCopyWith<$Res>? get background;
}

/// @nodoc
class _$CharacterCopyWithImpl<$Res, $Val extends Character>
    implements $CharacterCopyWith<$Res> {
  _$CharacterCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Character
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? aliases = null,
    Object? description = freezed,
    Object? type = null,
    Object? status = null,
    Object? appearance = freezed,
    Object? personality = freezed,
    Object? background = freezed,
    Object? abilities = null,
    Object? skillIds = null,
    Object? itemIds = null,
    Object? relationshipIds = null,
    Object? currentLocationId = freezed,
    Object? goals = null,
    Object? secrets = null,
    Object? characterArcs = null,
    Object? voiceStyle = freezed,
    Object? customAttributes = null,
    Object? imageUrl = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            aliases: null == aliases
                ? _value.aliases
                : aliases // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as CharacterType,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as CharacterStatus,
            appearance: freezed == appearance
                ? _value.appearance
                : appearance // ignore: cast_nullable_to_non_nullable
                      as CharacterAppearance?,
            personality: freezed == personality
                ? _value.personality
                : personality // ignore: cast_nullable_to_non_nullable
                      as CharacterPersonality?,
            background: freezed == background
                ? _value.background
                : background // ignore: cast_nullable_to_non_nullable
                      as CharacterBackground?,
            abilities: null == abilities
                ? _value.abilities
                : abilities // ignore: cast_nullable_to_non_nullable
                      as List<CharacterAbility>,
            skillIds: null == skillIds
                ? _value.skillIds
                : skillIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            itemIds: null == itemIds
                ? _value.itemIds
                : itemIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            relationshipIds: null == relationshipIds
                ? _value.relationshipIds
                : relationshipIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            currentLocationId: freezed == currentLocationId
                ? _value.currentLocationId
                : currentLocationId // ignore: cast_nullable_to_non_nullable
                      as String?,
            goals: null == goals
                ? _value.goals
                : goals // ignore: cast_nullable_to_non_nullable
                      as List<CharacterGoal>,
            secrets: null == secrets
                ? _value.secrets
                : secrets // ignore: cast_nullable_to_non_nullable
                      as List<CharacterSecret>,
            characterArcs: null == characterArcs
                ? _value.characterArcs
                : characterArcs // ignore: cast_nullable_to_non_nullable
                      as List<CharacterArc>,
            voiceStyle: freezed == voiceStyle
                ? _value.voiceStyle
                : voiceStyle // ignore: cast_nullable_to_non_nullable
                      as String?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            imageUrl: freezed == imageUrl
                ? _value.imageUrl
                : imageUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }

  /// Create a copy of Character
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CharacterAppearanceCopyWith<$Res>? get appearance {
    if (_value.appearance == null) {
      return null;
    }

    return $CharacterAppearanceCopyWith<$Res>(_value.appearance!, (value) {
      return _then(_value.copyWith(appearance: value) as $Val);
    });
  }

  /// Create a copy of Character
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CharacterPersonalityCopyWith<$Res>? get personality {
    if (_value.personality == null) {
      return null;
    }

    return $CharacterPersonalityCopyWith<$Res>(_value.personality!, (value) {
      return _then(_value.copyWith(personality: value) as $Val);
    });
  }

  /// Create a copy of Character
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CharacterBackgroundCopyWith<$Res>? get background {
    if (_value.background == null) {
      return null;
    }

    return $CharacterBackgroundCopyWith<$Res>(_value.background!, (value) {
      return _then(_value.copyWith(background: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CharacterImplCopyWith<$Res>
    implements $CharacterCopyWith<$Res> {
  factory _$$CharacterImplCopyWith(
    _$CharacterImpl value,
    $Res Function(_$CharacterImpl) then,
  ) = __$$CharacterImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    List<String> aliases,
    String? description,
    CharacterType type,
    CharacterStatus status,
    CharacterAppearance? appearance,
    CharacterPersonality? personality,
    CharacterBackground? background,
    List<CharacterAbility> abilities,
    List<String> skillIds,
    List<String> itemIds,
    List<String> relationshipIds,
    String? currentLocationId,
    List<CharacterGoal> goals,
    List<CharacterSecret> secrets,
    List<CharacterArc> characterArcs,
    String? voiceStyle,
    Map<String, dynamic> customAttributes,
    String? imageUrl,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });

  @override
  $CharacterAppearanceCopyWith<$Res>? get appearance;
  @override
  $CharacterPersonalityCopyWith<$Res>? get personality;
  @override
  $CharacterBackgroundCopyWith<$Res>? get background;
}

/// @nodoc
class __$$CharacterImplCopyWithImpl<$Res>
    extends _$CharacterCopyWithImpl<$Res, _$CharacterImpl>
    implements _$$CharacterImplCopyWith<$Res> {
  __$$CharacterImplCopyWithImpl(
    _$CharacterImpl _value,
    $Res Function(_$CharacterImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Character
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? aliases = null,
    Object? description = freezed,
    Object? type = null,
    Object? status = null,
    Object? appearance = freezed,
    Object? personality = freezed,
    Object? background = freezed,
    Object? abilities = null,
    Object? skillIds = null,
    Object? itemIds = null,
    Object? relationshipIds = null,
    Object? currentLocationId = freezed,
    Object? goals = null,
    Object? secrets = null,
    Object? characterArcs = null,
    Object? voiceStyle = freezed,
    Object? customAttributes = null,
    Object? imageUrl = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$CharacterImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        aliases: null == aliases
            ? _value._aliases
            : aliases // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as CharacterType,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as CharacterStatus,
        appearance: freezed == appearance
            ? _value.appearance
            : appearance // ignore: cast_nullable_to_non_nullable
                  as CharacterAppearance?,
        personality: freezed == personality
            ? _value.personality
            : personality // ignore: cast_nullable_to_non_nullable
                  as CharacterPersonality?,
        background: freezed == background
            ? _value.background
            : background // ignore: cast_nullable_to_non_nullable
                  as CharacterBackground?,
        abilities: null == abilities
            ? _value._abilities
            : abilities // ignore: cast_nullable_to_non_nullable
                  as List<CharacterAbility>,
        skillIds: null == skillIds
            ? _value._skillIds
            : skillIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        itemIds: null == itemIds
            ? _value._itemIds
            : itemIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        relationshipIds: null == relationshipIds
            ? _value._relationshipIds
            : relationshipIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        currentLocationId: freezed == currentLocationId
            ? _value.currentLocationId
            : currentLocationId // ignore: cast_nullable_to_non_nullable
                  as String?,
        goals: null == goals
            ? _value._goals
            : goals // ignore: cast_nullable_to_non_nullable
                  as List<CharacterGoal>,
        secrets: null == secrets
            ? _value._secrets
            : secrets // ignore: cast_nullable_to_non_nullable
                  as List<CharacterSecret>,
        characterArcs: null == characterArcs
            ? _value._characterArcs
            : characterArcs // ignore: cast_nullable_to_non_nullable
                  as List<CharacterArc>,
        voiceStyle: freezed == voiceStyle
            ? _value.voiceStyle
            : voiceStyle // ignore: cast_nullable_to_non_nullable
                  as String?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        imageUrl: freezed == imageUrl
            ? _value.imageUrl
            : imageUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CharacterImpl implements _Character {
  const _$CharacterImpl({
    required this.id,
    required this.name,
    final List<String> aliases = const [],
    this.description,
    required this.type,
    required this.status,
    this.appearance,
    this.personality,
    this.background,
    final List<CharacterAbility> abilities = const [],
    final List<String> skillIds = const [],
    final List<String> itemIds = const [],
    final List<String> relationshipIds = const [],
    this.currentLocationId,
    final List<CharacterGoal> goals = const [],
    final List<CharacterSecret> secrets = const [],
    final List<CharacterArc> characterArcs = const [],
    this.voiceStyle,
    final Map<String, dynamic> customAttributes = const {},
    this.imageUrl,
    final Map<String, dynamic> metadata = const {},
    this.createdAt,
    this.updatedAt,
  }) : _aliases = aliases,
       _abilities = abilities,
       _skillIds = skillIds,
       _itemIds = itemIds,
       _relationshipIds = relationshipIds,
       _goals = goals,
       _secrets = secrets,
       _characterArcs = characterArcs,
       _customAttributes = customAttributes,
       _metadata = metadata;

  factory _$CharacterImpl.fromJson(Map<String, dynamic> json) =>
      _$$CharacterImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  final List<String> _aliases;
  @override
  @JsonKey()
  List<String> get aliases {
    if (_aliases is EqualUnmodifiableListView) return _aliases;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_aliases);
  }

  // 别名
  @override
  final String? description;
  @override
  final CharacterType type;
  @override
  final CharacterStatus status;
  @override
  final CharacterAppearance? appearance;
  @override
  final CharacterPersonality? personality;
  @override
  final CharacterBackground? background;
  final List<CharacterAbility> _abilities;
  @override
  @JsonKey()
  List<CharacterAbility> get abilities {
    if (_abilities is EqualUnmodifiableListView) return _abilities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_abilities);
  }

  final List<String> _skillIds;
  @override
  @JsonKey()
  List<String> get skillIds {
    if (_skillIds is EqualUnmodifiableListView) return _skillIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_skillIds);
  }

  final List<String> _itemIds;
  @override
  @JsonKey()
  List<String> get itemIds {
    if (_itemIds is EqualUnmodifiableListView) return _itemIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_itemIds);
  }

  final List<String> _relationshipIds;
  @override
  @JsonKey()
  List<String> get relationshipIds {
    if (_relationshipIds is EqualUnmodifiableListView) return _relationshipIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_relationshipIds);
  }

  @override
  final String? currentLocationId;
  final List<CharacterGoal> _goals;
  @override
  @JsonKey()
  List<CharacterGoal> get goals {
    if (_goals is EqualUnmodifiableListView) return _goals;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_goals);
  }

  final List<CharacterSecret> _secrets;
  @override
  @JsonKey()
  List<CharacterSecret> get secrets {
    if (_secrets is EqualUnmodifiableListView) return _secrets;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_secrets);
  }

  final List<CharacterArc> _characterArcs;
  @override
  @JsonKey()
  List<CharacterArc> get characterArcs {
    if (_characterArcs is EqualUnmodifiableListView) return _characterArcs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_characterArcs);
  }

  @override
  final String? voiceStyle;
  // 说话风格
  final Map<String, dynamic> _customAttributes;
  // 说话风格
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  final String? imageUrl;
  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'Character(id: $id, name: $name, aliases: $aliases, description: $description, type: $type, status: $status, appearance: $appearance, personality: $personality, background: $background, abilities: $abilities, skillIds: $skillIds, itemIds: $itemIds, relationshipIds: $relationshipIds, currentLocationId: $currentLocationId, goals: $goals, secrets: $secrets, characterArcs: $characterArcs, voiceStyle: $voiceStyle, customAttributes: $customAttributes, imageUrl: $imageUrl, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CharacterImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other._aliases, _aliases) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.appearance, appearance) ||
                other.appearance == appearance) &&
            (identical(other.personality, personality) ||
                other.personality == personality) &&
            (identical(other.background, background) ||
                other.background == background) &&
            const DeepCollectionEquality().equals(
              other._abilities,
              _abilities,
            ) &&
            const DeepCollectionEquality().equals(other._skillIds, _skillIds) &&
            const DeepCollectionEquality().equals(other._itemIds, _itemIds) &&
            const DeepCollectionEquality().equals(
              other._relationshipIds,
              _relationshipIds,
            ) &&
            (identical(other.currentLocationId, currentLocationId) ||
                other.currentLocationId == currentLocationId) &&
            const DeepCollectionEquality().equals(other._goals, _goals) &&
            const DeepCollectionEquality().equals(other._secrets, _secrets) &&
            const DeepCollectionEquality().equals(
              other._characterArcs,
              _characterArcs,
            ) &&
            (identical(other.voiceStyle, voiceStyle) ||
                other.voiceStyle == voiceStyle) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    name,
    const DeepCollectionEquality().hash(_aliases),
    description,
    type,
    status,
    appearance,
    personality,
    background,
    const DeepCollectionEquality().hash(_abilities),
    const DeepCollectionEquality().hash(_skillIds),
    const DeepCollectionEquality().hash(_itemIds),
    const DeepCollectionEquality().hash(_relationshipIds),
    currentLocationId,
    const DeepCollectionEquality().hash(_goals),
    const DeepCollectionEquality().hash(_secrets),
    const DeepCollectionEquality().hash(_characterArcs),
    voiceStyle,
    const DeepCollectionEquality().hash(_customAttributes),
    imageUrl,
    const DeepCollectionEquality().hash(_metadata),
    createdAt,
    updatedAt,
  ]);

  /// Create a copy of Character
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CharacterImplCopyWith<_$CharacterImpl> get copyWith =>
      __$$CharacterImplCopyWithImpl<_$CharacterImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CharacterImplToJson(this);
  }
}

abstract class _Character implements Character {
  const factory _Character({
    required final String id,
    required final String name,
    final List<String> aliases,
    final String? description,
    required final CharacterType type,
    required final CharacterStatus status,
    final CharacterAppearance? appearance,
    final CharacterPersonality? personality,
    final CharacterBackground? background,
    final List<CharacterAbility> abilities,
    final List<String> skillIds,
    final List<String> itemIds,
    final List<String> relationshipIds,
    final String? currentLocationId,
    final List<CharacterGoal> goals,
    final List<CharacterSecret> secrets,
    final List<CharacterArc> characterArcs,
    final String? voiceStyle,
    final Map<String, dynamic> customAttributes,
    final String? imageUrl,
    final Map<String, dynamic> metadata,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$CharacterImpl;

  factory _Character.fromJson(Map<String, dynamic> json) =
      _$CharacterImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  List<String> get aliases; // 别名
  @override
  String? get description;
  @override
  CharacterType get type;
  @override
  CharacterStatus get status;
  @override
  CharacterAppearance? get appearance;
  @override
  CharacterPersonality? get personality;
  @override
  CharacterBackground? get background;
  @override
  List<CharacterAbility> get abilities;
  @override
  List<String> get skillIds;
  @override
  List<String> get itemIds;
  @override
  List<String> get relationshipIds;
  @override
  String? get currentLocationId;
  @override
  List<CharacterGoal> get goals;
  @override
  List<CharacterSecret> get secrets;
  @override
  List<CharacterArc> get characterArcs;
  @override
  String? get voiceStyle; // 说话风格
  @override
  Map<String, dynamic> get customAttributes;
  @override
  String? get imageUrl;
  @override
  Map<String, dynamic> get metadata;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of Character
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CharacterImplCopyWith<_$CharacterImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CharacterAppearance _$CharacterAppearanceFromJson(Map<String, dynamic> json) {
  return _CharacterAppearance.fromJson(json);
}

/// @nodoc
mixin _$CharacterAppearance {
  int? get age => throw _privateConstructorUsedError;
  String? get gender => throw _privateConstructorUsedError;
  String? get height => throw _privateConstructorUsedError;
  String? get weight => throw _privateConstructorUsedError;
  String? get build => throw _privateConstructorUsedError; // 体型
  String? get hairColor => throw _privateConstructorUsedError;
  String? get hairStyle => throw _privateConstructorUsedError;
  String? get eyeColor => throw _privateConstructorUsedError;
  String? get skinTone => throw _privateConstructorUsedError;
  List<String> get distinguishingFeatures =>
      throw _privateConstructorUsedError; // 显著特征
  String? get clothing => throw _privateConstructorUsedError;
  List<String> get accessories => throw _privateConstructorUsedError;
  String? get overallDescription => throw _privateConstructorUsedError;
  Map<String, dynamic> get customFeatures => throw _privateConstructorUsedError;

  /// Serializes this CharacterAppearance to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CharacterAppearance
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CharacterAppearanceCopyWith<CharacterAppearance> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CharacterAppearanceCopyWith<$Res> {
  factory $CharacterAppearanceCopyWith(
    CharacterAppearance value,
    $Res Function(CharacterAppearance) then,
  ) = _$CharacterAppearanceCopyWithImpl<$Res, CharacterAppearance>;
  @useResult
  $Res call({
    int? age,
    String? gender,
    String? height,
    String? weight,
    String? build,
    String? hairColor,
    String? hairStyle,
    String? eyeColor,
    String? skinTone,
    List<String> distinguishingFeatures,
    String? clothing,
    List<String> accessories,
    String? overallDescription,
    Map<String, dynamic> customFeatures,
  });
}

/// @nodoc
class _$CharacterAppearanceCopyWithImpl<$Res, $Val extends CharacterAppearance>
    implements $CharacterAppearanceCopyWith<$Res> {
  _$CharacterAppearanceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CharacterAppearance
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? age = freezed,
    Object? gender = freezed,
    Object? height = freezed,
    Object? weight = freezed,
    Object? build = freezed,
    Object? hairColor = freezed,
    Object? hairStyle = freezed,
    Object? eyeColor = freezed,
    Object? skinTone = freezed,
    Object? distinguishingFeatures = null,
    Object? clothing = freezed,
    Object? accessories = null,
    Object? overallDescription = freezed,
    Object? customFeatures = null,
  }) {
    return _then(
      _value.copyWith(
            age: freezed == age
                ? _value.age
                : age // ignore: cast_nullable_to_non_nullable
                      as int?,
            gender: freezed == gender
                ? _value.gender
                : gender // ignore: cast_nullable_to_non_nullable
                      as String?,
            height: freezed == height
                ? _value.height
                : height // ignore: cast_nullable_to_non_nullable
                      as String?,
            weight: freezed == weight
                ? _value.weight
                : weight // ignore: cast_nullable_to_non_nullable
                      as String?,
            build: freezed == build
                ? _value.build
                : build // ignore: cast_nullable_to_non_nullable
                      as String?,
            hairColor: freezed == hairColor
                ? _value.hairColor
                : hairColor // ignore: cast_nullable_to_non_nullable
                      as String?,
            hairStyle: freezed == hairStyle
                ? _value.hairStyle
                : hairStyle // ignore: cast_nullable_to_non_nullable
                      as String?,
            eyeColor: freezed == eyeColor
                ? _value.eyeColor
                : eyeColor // ignore: cast_nullable_to_non_nullable
                      as String?,
            skinTone: freezed == skinTone
                ? _value.skinTone
                : skinTone // ignore: cast_nullable_to_non_nullable
                      as String?,
            distinguishingFeatures: null == distinguishingFeatures
                ? _value.distinguishingFeatures
                : distinguishingFeatures // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            clothing: freezed == clothing
                ? _value.clothing
                : clothing // ignore: cast_nullable_to_non_nullable
                      as String?,
            accessories: null == accessories
                ? _value.accessories
                : accessories // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            overallDescription: freezed == overallDescription
                ? _value.overallDescription
                : overallDescription // ignore: cast_nullable_to_non_nullable
                      as String?,
            customFeatures: null == customFeatures
                ? _value.customFeatures
                : customFeatures // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CharacterAppearanceImplCopyWith<$Res>
    implements $CharacterAppearanceCopyWith<$Res> {
  factory _$$CharacterAppearanceImplCopyWith(
    _$CharacterAppearanceImpl value,
    $Res Function(_$CharacterAppearanceImpl) then,
  ) = __$$CharacterAppearanceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int? age,
    String? gender,
    String? height,
    String? weight,
    String? build,
    String? hairColor,
    String? hairStyle,
    String? eyeColor,
    String? skinTone,
    List<String> distinguishingFeatures,
    String? clothing,
    List<String> accessories,
    String? overallDescription,
    Map<String, dynamic> customFeatures,
  });
}

/// @nodoc
class __$$CharacterAppearanceImplCopyWithImpl<$Res>
    extends _$CharacterAppearanceCopyWithImpl<$Res, _$CharacterAppearanceImpl>
    implements _$$CharacterAppearanceImplCopyWith<$Res> {
  __$$CharacterAppearanceImplCopyWithImpl(
    _$CharacterAppearanceImpl _value,
    $Res Function(_$CharacterAppearanceImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CharacterAppearance
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? age = freezed,
    Object? gender = freezed,
    Object? height = freezed,
    Object? weight = freezed,
    Object? build = freezed,
    Object? hairColor = freezed,
    Object? hairStyle = freezed,
    Object? eyeColor = freezed,
    Object? skinTone = freezed,
    Object? distinguishingFeatures = null,
    Object? clothing = freezed,
    Object? accessories = null,
    Object? overallDescription = freezed,
    Object? customFeatures = null,
  }) {
    return _then(
      _$CharacterAppearanceImpl(
        age: freezed == age
            ? _value.age
            : age // ignore: cast_nullable_to_non_nullable
                  as int?,
        gender: freezed == gender
            ? _value.gender
            : gender // ignore: cast_nullable_to_non_nullable
                  as String?,
        height: freezed == height
            ? _value.height
            : height // ignore: cast_nullable_to_non_nullable
                  as String?,
        weight: freezed == weight
            ? _value.weight
            : weight // ignore: cast_nullable_to_non_nullable
                  as String?,
        build: freezed == build
            ? _value.build
            : build // ignore: cast_nullable_to_non_nullable
                  as String?,
        hairColor: freezed == hairColor
            ? _value.hairColor
            : hairColor // ignore: cast_nullable_to_non_nullable
                  as String?,
        hairStyle: freezed == hairStyle
            ? _value.hairStyle
            : hairStyle // ignore: cast_nullable_to_non_nullable
                  as String?,
        eyeColor: freezed == eyeColor
            ? _value.eyeColor
            : eyeColor // ignore: cast_nullable_to_non_nullable
                  as String?,
        skinTone: freezed == skinTone
            ? _value.skinTone
            : skinTone // ignore: cast_nullable_to_non_nullable
                  as String?,
        distinguishingFeatures: null == distinguishingFeatures
            ? _value._distinguishingFeatures
            : distinguishingFeatures // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        clothing: freezed == clothing
            ? _value.clothing
            : clothing // ignore: cast_nullable_to_non_nullable
                  as String?,
        accessories: null == accessories
            ? _value._accessories
            : accessories // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        overallDescription: freezed == overallDescription
            ? _value.overallDescription
            : overallDescription // ignore: cast_nullable_to_non_nullable
                  as String?,
        customFeatures: null == customFeatures
            ? _value._customFeatures
            : customFeatures // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CharacterAppearanceImpl implements _CharacterAppearance {
  const _$CharacterAppearanceImpl({
    this.age,
    this.gender,
    this.height,
    this.weight,
    this.build,
    this.hairColor,
    this.hairStyle,
    this.eyeColor,
    this.skinTone,
    final List<String> distinguishingFeatures = const [],
    this.clothing,
    final List<String> accessories = const [],
    this.overallDescription,
    final Map<String, dynamic> customFeatures = const {},
  }) : _distinguishingFeatures = distinguishingFeatures,
       _accessories = accessories,
       _customFeatures = customFeatures;

  factory _$CharacterAppearanceImpl.fromJson(Map<String, dynamic> json) =>
      _$$CharacterAppearanceImplFromJson(json);

  @override
  final int? age;
  @override
  final String? gender;
  @override
  final String? height;
  @override
  final String? weight;
  @override
  final String? build;
  // 体型
  @override
  final String? hairColor;
  @override
  final String? hairStyle;
  @override
  final String? eyeColor;
  @override
  final String? skinTone;
  final List<String> _distinguishingFeatures;
  @override
  @JsonKey()
  List<String> get distinguishingFeatures {
    if (_distinguishingFeatures is EqualUnmodifiableListView)
      return _distinguishingFeatures;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_distinguishingFeatures);
  }

  // 显著特征
  @override
  final String? clothing;
  final List<String> _accessories;
  @override
  @JsonKey()
  List<String> get accessories {
    if (_accessories is EqualUnmodifiableListView) return _accessories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_accessories);
  }

  @override
  final String? overallDescription;
  final Map<String, dynamic> _customFeatures;
  @override
  @JsonKey()
  Map<String, dynamic> get customFeatures {
    if (_customFeatures is EqualUnmodifiableMapView) return _customFeatures;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customFeatures);
  }

  @override
  String toString() {
    return 'CharacterAppearance(age: $age, gender: $gender, height: $height, weight: $weight, build: $build, hairColor: $hairColor, hairStyle: $hairStyle, eyeColor: $eyeColor, skinTone: $skinTone, distinguishingFeatures: $distinguishingFeatures, clothing: $clothing, accessories: $accessories, overallDescription: $overallDescription, customFeatures: $customFeatures)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CharacterAppearanceImpl &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.weight, weight) || other.weight == weight) &&
            (identical(other.build, build) || other.build == build) &&
            (identical(other.hairColor, hairColor) ||
                other.hairColor == hairColor) &&
            (identical(other.hairStyle, hairStyle) ||
                other.hairStyle == hairStyle) &&
            (identical(other.eyeColor, eyeColor) ||
                other.eyeColor == eyeColor) &&
            (identical(other.skinTone, skinTone) ||
                other.skinTone == skinTone) &&
            const DeepCollectionEquality().equals(
              other._distinguishingFeatures,
              _distinguishingFeatures,
            ) &&
            (identical(other.clothing, clothing) ||
                other.clothing == clothing) &&
            const DeepCollectionEquality().equals(
              other._accessories,
              _accessories,
            ) &&
            (identical(other.overallDescription, overallDescription) ||
                other.overallDescription == overallDescription) &&
            const DeepCollectionEquality().equals(
              other._customFeatures,
              _customFeatures,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    age,
    gender,
    height,
    weight,
    build,
    hairColor,
    hairStyle,
    eyeColor,
    skinTone,
    const DeepCollectionEquality().hash(_distinguishingFeatures),
    clothing,
    const DeepCollectionEquality().hash(_accessories),
    overallDescription,
    const DeepCollectionEquality().hash(_customFeatures),
  );

  /// Create a copy of CharacterAppearance
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CharacterAppearanceImplCopyWith<_$CharacterAppearanceImpl> get copyWith =>
      __$$CharacterAppearanceImplCopyWithImpl<_$CharacterAppearanceImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CharacterAppearanceImplToJson(this);
  }
}

abstract class _CharacterAppearance implements CharacterAppearance {
  const factory _CharacterAppearance({
    final int? age,
    final String? gender,
    final String? height,
    final String? weight,
    final String? build,
    final String? hairColor,
    final String? hairStyle,
    final String? eyeColor,
    final String? skinTone,
    final List<String> distinguishingFeatures,
    final String? clothing,
    final List<String> accessories,
    final String? overallDescription,
    final Map<String, dynamic> customFeatures,
  }) = _$CharacterAppearanceImpl;

  factory _CharacterAppearance.fromJson(Map<String, dynamic> json) =
      _$CharacterAppearanceImpl.fromJson;

  @override
  int? get age;
  @override
  String? get gender;
  @override
  String? get height;
  @override
  String? get weight;
  @override
  String? get build; // 体型
  @override
  String? get hairColor;
  @override
  String? get hairStyle;
  @override
  String? get eyeColor;
  @override
  String? get skinTone;
  @override
  List<String> get distinguishingFeatures; // 显著特征
  @override
  String? get clothing;
  @override
  List<String> get accessories;
  @override
  String? get overallDescription;
  @override
  Map<String, dynamic> get customFeatures;

  /// Create a copy of CharacterAppearance
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CharacterAppearanceImplCopyWith<_$CharacterAppearanceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CharacterPersonality _$CharacterPersonalityFromJson(Map<String, dynamic> json) {
  return _CharacterPersonality.fromJson(json);
}

/// @nodoc
mixin _$CharacterPersonality {
  List<String> get traits => throw _privateConstructorUsedError; // 性格特征
  List<String> get strengths => throw _privateConstructorUsedError; // 优点
  List<String> get weaknesses => throw _privateConstructorUsedError; // 缺点
  List<String> get fears => throw _privateConstructorUsedError; // 恐惧
  List<String> get desires => throw _privateConstructorUsedError; // 欲望
  List<String> get habits => throw _privateConstructorUsedError; // 习惯
  List<String> get quirks => throw _privateConstructorUsedError; // 怪癖
  String? get moralAlignment => throw _privateConstructorUsedError; // 道德倾向
  String? get temperament => throw _privateConstructorUsedError; // 气质
  String? get worldview => throw _privateConstructorUsedError; // 世界观
  Map<String, int> get personalityScores =>
      throw _privateConstructorUsedError; // 性格评分 (如五大人格)
  Map<String, dynamic> get customTraits => throw _privateConstructorUsedError;

  /// Serializes this CharacterPersonality to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CharacterPersonality
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CharacterPersonalityCopyWith<CharacterPersonality> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CharacterPersonalityCopyWith<$Res> {
  factory $CharacterPersonalityCopyWith(
    CharacterPersonality value,
    $Res Function(CharacterPersonality) then,
  ) = _$CharacterPersonalityCopyWithImpl<$Res, CharacterPersonality>;
  @useResult
  $Res call({
    List<String> traits,
    List<String> strengths,
    List<String> weaknesses,
    List<String> fears,
    List<String> desires,
    List<String> habits,
    List<String> quirks,
    String? moralAlignment,
    String? temperament,
    String? worldview,
    Map<String, int> personalityScores,
    Map<String, dynamic> customTraits,
  });
}

/// @nodoc
class _$CharacterPersonalityCopyWithImpl<
  $Res,
  $Val extends CharacterPersonality
>
    implements $CharacterPersonalityCopyWith<$Res> {
  _$CharacterPersonalityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CharacterPersonality
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? traits = null,
    Object? strengths = null,
    Object? weaknesses = null,
    Object? fears = null,
    Object? desires = null,
    Object? habits = null,
    Object? quirks = null,
    Object? moralAlignment = freezed,
    Object? temperament = freezed,
    Object? worldview = freezed,
    Object? personalityScores = null,
    Object? customTraits = null,
  }) {
    return _then(
      _value.copyWith(
            traits: null == traits
                ? _value.traits
                : traits // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            strengths: null == strengths
                ? _value.strengths
                : strengths // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            weaknesses: null == weaknesses
                ? _value.weaknesses
                : weaknesses // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            fears: null == fears
                ? _value.fears
                : fears // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            desires: null == desires
                ? _value.desires
                : desires // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            habits: null == habits
                ? _value.habits
                : habits // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            quirks: null == quirks
                ? _value.quirks
                : quirks // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            moralAlignment: freezed == moralAlignment
                ? _value.moralAlignment
                : moralAlignment // ignore: cast_nullable_to_non_nullable
                      as String?,
            temperament: freezed == temperament
                ? _value.temperament
                : temperament // ignore: cast_nullable_to_non_nullable
                      as String?,
            worldview: freezed == worldview
                ? _value.worldview
                : worldview // ignore: cast_nullable_to_non_nullable
                      as String?,
            personalityScores: null == personalityScores
                ? _value.personalityScores
                : personalityScores // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
            customTraits: null == customTraits
                ? _value.customTraits
                : customTraits // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CharacterPersonalityImplCopyWith<$Res>
    implements $CharacterPersonalityCopyWith<$Res> {
  factory _$$CharacterPersonalityImplCopyWith(
    _$CharacterPersonalityImpl value,
    $Res Function(_$CharacterPersonalityImpl) then,
  ) = __$$CharacterPersonalityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    List<String> traits,
    List<String> strengths,
    List<String> weaknesses,
    List<String> fears,
    List<String> desires,
    List<String> habits,
    List<String> quirks,
    String? moralAlignment,
    String? temperament,
    String? worldview,
    Map<String, int> personalityScores,
    Map<String, dynamic> customTraits,
  });
}

/// @nodoc
class __$$CharacterPersonalityImplCopyWithImpl<$Res>
    extends _$CharacterPersonalityCopyWithImpl<$Res, _$CharacterPersonalityImpl>
    implements _$$CharacterPersonalityImplCopyWith<$Res> {
  __$$CharacterPersonalityImplCopyWithImpl(
    _$CharacterPersonalityImpl _value,
    $Res Function(_$CharacterPersonalityImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CharacterPersonality
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? traits = null,
    Object? strengths = null,
    Object? weaknesses = null,
    Object? fears = null,
    Object? desires = null,
    Object? habits = null,
    Object? quirks = null,
    Object? moralAlignment = freezed,
    Object? temperament = freezed,
    Object? worldview = freezed,
    Object? personalityScores = null,
    Object? customTraits = null,
  }) {
    return _then(
      _$CharacterPersonalityImpl(
        traits: null == traits
            ? _value._traits
            : traits // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        strengths: null == strengths
            ? _value._strengths
            : strengths // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        weaknesses: null == weaknesses
            ? _value._weaknesses
            : weaknesses // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        fears: null == fears
            ? _value._fears
            : fears // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        desires: null == desires
            ? _value._desires
            : desires // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        habits: null == habits
            ? _value._habits
            : habits // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        quirks: null == quirks
            ? _value._quirks
            : quirks // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        moralAlignment: freezed == moralAlignment
            ? _value.moralAlignment
            : moralAlignment // ignore: cast_nullable_to_non_nullable
                  as String?,
        temperament: freezed == temperament
            ? _value.temperament
            : temperament // ignore: cast_nullable_to_non_nullable
                  as String?,
        worldview: freezed == worldview
            ? _value.worldview
            : worldview // ignore: cast_nullable_to_non_nullable
                  as String?,
        personalityScores: null == personalityScores
            ? _value._personalityScores
            : personalityScores // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
        customTraits: null == customTraits
            ? _value._customTraits
            : customTraits // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CharacterPersonalityImpl implements _CharacterPersonality {
  const _$CharacterPersonalityImpl({
    final List<String> traits = const [],
    final List<String> strengths = const [],
    final List<String> weaknesses = const [],
    final List<String> fears = const [],
    final List<String> desires = const [],
    final List<String> habits = const [],
    final List<String> quirks = const [],
    this.moralAlignment,
    this.temperament,
    this.worldview,
    final Map<String, int> personalityScores = const {},
    final Map<String, dynamic> customTraits = const {},
  }) : _traits = traits,
       _strengths = strengths,
       _weaknesses = weaknesses,
       _fears = fears,
       _desires = desires,
       _habits = habits,
       _quirks = quirks,
       _personalityScores = personalityScores,
       _customTraits = customTraits;

  factory _$CharacterPersonalityImpl.fromJson(Map<String, dynamic> json) =>
      _$$CharacterPersonalityImplFromJson(json);

  final List<String> _traits;
  @override
  @JsonKey()
  List<String> get traits {
    if (_traits is EqualUnmodifiableListView) return _traits;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_traits);
  }

  // 性格特征
  final List<String> _strengths;
  // 性格特征
  @override
  @JsonKey()
  List<String> get strengths {
    if (_strengths is EqualUnmodifiableListView) return _strengths;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_strengths);
  }

  // 优点
  final List<String> _weaknesses;
  // 优点
  @override
  @JsonKey()
  List<String> get weaknesses {
    if (_weaknesses is EqualUnmodifiableListView) return _weaknesses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_weaknesses);
  }

  // 缺点
  final List<String> _fears;
  // 缺点
  @override
  @JsonKey()
  List<String> get fears {
    if (_fears is EqualUnmodifiableListView) return _fears;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_fears);
  }

  // 恐惧
  final List<String> _desires;
  // 恐惧
  @override
  @JsonKey()
  List<String> get desires {
    if (_desires is EqualUnmodifiableListView) return _desires;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_desires);
  }

  // 欲望
  final List<String> _habits;
  // 欲望
  @override
  @JsonKey()
  List<String> get habits {
    if (_habits is EqualUnmodifiableListView) return _habits;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_habits);
  }

  // 习惯
  final List<String> _quirks;
  // 习惯
  @override
  @JsonKey()
  List<String> get quirks {
    if (_quirks is EqualUnmodifiableListView) return _quirks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_quirks);
  }

  // 怪癖
  @override
  final String? moralAlignment;
  // 道德倾向
  @override
  final String? temperament;
  // 气质
  @override
  final String? worldview;
  // 世界观
  final Map<String, int> _personalityScores;
  // 世界观
  @override
  @JsonKey()
  Map<String, int> get personalityScores {
    if (_personalityScores is EqualUnmodifiableMapView)
      return _personalityScores;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_personalityScores);
  }

  // 性格评分 (如五大人格)
  final Map<String, dynamic> _customTraits;
  // 性格评分 (如五大人格)
  @override
  @JsonKey()
  Map<String, dynamic> get customTraits {
    if (_customTraits is EqualUnmodifiableMapView) return _customTraits;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customTraits);
  }

  @override
  String toString() {
    return 'CharacterPersonality(traits: $traits, strengths: $strengths, weaknesses: $weaknesses, fears: $fears, desires: $desires, habits: $habits, quirks: $quirks, moralAlignment: $moralAlignment, temperament: $temperament, worldview: $worldview, personalityScores: $personalityScores, customTraits: $customTraits)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CharacterPersonalityImpl &&
            const DeepCollectionEquality().equals(other._traits, _traits) &&
            const DeepCollectionEquality().equals(
              other._strengths,
              _strengths,
            ) &&
            const DeepCollectionEquality().equals(
              other._weaknesses,
              _weaknesses,
            ) &&
            const DeepCollectionEquality().equals(other._fears, _fears) &&
            const DeepCollectionEquality().equals(other._desires, _desires) &&
            const DeepCollectionEquality().equals(other._habits, _habits) &&
            const DeepCollectionEquality().equals(other._quirks, _quirks) &&
            (identical(other.moralAlignment, moralAlignment) ||
                other.moralAlignment == moralAlignment) &&
            (identical(other.temperament, temperament) ||
                other.temperament == temperament) &&
            (identical(other.worldview, worldview) ||
                other.worldview == worldview) &&
            const DeepCollectionEquality().equals(
              other._personalityScores,
              _personalityScores,
            ) &&
            const DeepCollectionEquality().equals(
              other._customTraits,
              _customTraits,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_traits),
    const DeepCollectionEquality().hash(_strengths),
    const DeepCollectionEquality().hash(_weaknesses),
    const DeepCollectionEquality().hash(_fears),
    const DeepCollectionEquality().hash(_desires),
    const DeepCollectionEquality().hash(_habits),
    const DeepCollectionEquality().hash(_quirks),
    moralAlignment,
    temperament,
    worldview,
    const DeepCollectionEquality().hash(_personalityScores),
    const DeepCollectionEquality().hash(_customTraits),
  );

  /// Create a copy of CharacterPersonality
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CharacterPersonalityImplCopyWith<_$CharacterPersonalityImpl>
  get copyWith =>
      __$$CharacterPersonalityImplCopyWithImpl<_$CharacterPersonalityImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CharacterPersonalityImplToJson(this);
  }
}

abstract class _CharacterPersonality implements CharacterPersonality {
  const factory _CharacterPersonality({
    final List<String> traits,
    final List<String> strengths,
    final List<String> weaknesses,
    final List<String> fears,
    final List<String> desires,
    final List<String> habits,
    final List<String> quirks,
    final String? moralAlignment,
    final String? temperament,
    final String? worldview,
    final Map<String, int> personalityScores,
    final Map<String, dynamic> customTraits,
  }) = _$CharacterPersonalityImpl;

  factory _CharacterPersonality.fromJson(Map<String, dynamic> json) =
      _$CharacterPersonalityImpl.fromJson;

  @override
  List<String> get traits; // 性格特征
  @override
  List<String> get strengths; // 优点
  @override
  List<String> get weaknesses; // 缺点
  @override
  List<String> get fears; // 恐惧
  @override
  List<String> get desires; // 欲望
  @override
  List<String> get habits; // 习惯
  @override
  List<String> get quirks; // 怪癖
  @override
  String? get moralAlignment; // 道德倾向
  @override
  String? get temperament; // 气质
  @override
  String? get worldview; // 世界观
  @override
  Map<String, int> get personalityScores; // 性格评分 (如五大人格)
  @override
  Map<String, dynamic> get customTraits;

  /// Create a copy of CharacterPersonality
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CharacterPersonalityImplCopyWith<_$CharacterPersonalityImpl>
  get copyWith => throw _privateConstructorUsedError;
}

CharacterBackground _$CharacterBackgroundFromJson(Map<String, dynamic> json) {
  return _CharacterBackground.fromJson(json);
}

/// @nodoc
mixin _$CharacterBackground {
  String? get birthPlace => throw _privateConstructorUsedError;
  DateTime? get birthDate => throw _privateConstructorUsedError;
  String? get family => throw _privateConstructorUsedError;
  String? get education => throw _privateConstructorUsedError;
  String? get occupation => throw _privateConstructorUsedError;
  String? get socialClass => throw _privateConstructorUsedError;
  List<String> get languages => throw _privateConstructorUsedError;
  List<BackgroundEvent> get lifeEvents => throw _privateConstructorUsedError;
  String? get currentResidence => throw _privateConstructorUsedError;
  String? get backstory => throw _privateConstructorUsedError;
  Map<String, dynamic> get customBackground =>
      throw _privateConstructorUsedError;

  /// Serializes this CharacterBackground to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CharacterBackground
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CharacterBackgroundCopyWith<CharacterBackground> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CharacterBackgroundCopyWith<$Res> {
  factory $CharacterBackgroundCopyWith(
    CharacterBackground value,
    $Res Function(CharacterBackground) then,
  ) = _$CharacterBackgroundCopyWithImpl<$Res, CharacterBackground>;
  @useResult
  $Res call({
    String? birthPlace,
    DateTime? birthDate,
    String? family,
    String? education,
    String? occupation,
    String? socialClass,
    List<String> languages,
    List<BackgroundEvent> lifeEvents,
    String? currentResidence,
    String? backstory,
    Map<String, dynamic> customBackground,
  });
}

/// @nodoc
class _$CharacterBackgroundCopyWithImpl<$Res, $Val extends CharacterBackground>
    implements $CharacterBackgroundCopyWith<$Res> {
  _$CharacterBackgroundCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CharacterBackground
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? birthPlace = freezed,
    Object? birthDate = freezed,
    Object? family = freezed,
    Object? education = freezed,
    Object? occupation = freezed,
    Object? socialClass = freezed,
    Object? languages = null,
    Object? lifeEvents = null,
    Object? currentResidence = freezed,
    Object? backstory = freezed,
    Object? customBackground = null,
  }) {
    return _then(
      _value.copyWith(
            birthPlace: freezed == birthPlace
                ? _value.birthPlace
                : birthPlace // ignore: cast_nullable_to_non_nullable
                      as String?,
            birthDate: freezed == birthDate
                ? _value.birthDate
                : birthDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            family: freezed == family
                ? _value.family
                : family // ignore: cast_nullable_to_non_nullable
                      as String?,
            education: freezed == education
                ? _value.education
                : education // ignore: cast_nullable_to_non_nullable
                      as String?,
            occupation: freezed == occupation
                ? _value.occupation
                : occupation // ignore: cast_nullable_to_non_nullable
                      as String?,
            socialClass: freezed == socialClass
                ? _value.socialClass
                : socialClass // ignore: cast_nullable_to_non_nullable
                      as String?,
            languages: null == languages
                ? _value.languages
                : languages // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            lifeEvents: null == lifeEvents
                ? _value.lifeEvents
                : lifeEvents // ignore: cast_nullable_to_non_nullable
                      as List<BackgroundEvent>,
            currentResidence: freezed == currentResidence
                ? _value.currentResidence
                : currentResidence // ignore: cast_nullable_to_non_nullable
                      as String?,
            backstory: freezed == backstory
                ? _value.backstory
                : backstory // ignore: cast_nullable_to_non_nullable
                      as String?,
            customBackground: null == customBackground
                ? _value.customBackground
                : customBackground // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CharacterBackgroundImplCopyWith<$Res>
    implements $CharacterBackgroundCopyWith<$Res> {
  factory _$$CharacterBackgroundImplCopyWith(
    _$CharacterBackgroundImpl value,
    $Res Function(_$CharacterBackgroundImpl) then,
  ) = __$$CharacterBackgroundImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String? birthPlace,
    DateTime? birthDate,
    String? family,
    String? education,
    String? occupation,
    String? socialClass,
    List<String> languages,
    List<BackgroundEvent> lifeEvents,
    String? currentResidence,
    String? backstory,
    Map<String, dynamic> customBackground,
  });
}

/// @nodoc
class __$$CharacterBackgroundImplCopyWithImpl<$Res>
    extends _$CharacterBackgroundCopyWithImpl<$Res, _$CharacterBackgroundImpl>
    implements _$$CharacterBackgroundImplCopyWith<$Res> {
  __$$CharacterBackgroundImplCopyWithImpl(
    _$CharacterBackgroundImpl _value,
    $Res Function(_$CharacterBackgroundImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CharacterBackground
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? birthPlace = freezed,
    Object? birthDate = freezed,
    Object? family = freezed,
    Object? education = freezed,
    Object? occupation = freezed,
    Object? socialClass = freezed,
    Object? languages = null,
    Object? lifeEvents = null,
    Object? currentResidence = freezed,
    Object? backstory = freezed,
    Object? customBackground = null,
  }) {
    return _then(
      _$CharacterBackgroundImpl(
        birthPlace: freezed == birthPlace
            ? _value.birthPlace
            : birthPlace // ignore: cast_nullable_to_non_nullable
                  as String?,
        birthDate: freezed == birthDate
            ? _value.birthDate
            : birthDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        family: freezed == family
            ? _value.family
            : family // ignore: cast_nullable_to_non_nullable
                  as String?,
        education: freezed == education
            ? _value.education
            : education // ignore: cast_nullable_to_non_nullable
                  as String?,
        occupation: freezed == occupation
            ? _value.occupation
            : occupation // ignore: cast_nullable_to_non_nullable
                  as String?,
        socialClass: freezed == socialClass
            ? _value.socialClass
            : socialClass // ignore: cast_nullable_to_non_nullable
                  as String?,
        languages: null == languages
            ? _value._languages
            : languages // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        lifeEvents: null == lifeEvents
            ? _value._lifeEvents
            : lifeEvents // ignore: cast_nullable_to_non_nullable
                  as List<BackgroundEvent>,
        currentResidence: freezed == currentResidence
            ? _value.currentResidence
            : currentResidence // ignore: cast_nullable_to_non_nullable
                  as String?,
        backstory: freezed == backstory
            ? _value.backstory
            : backstory // ignore: cast_nullable_to_non_nullable
                  as String?,
        customBackground: null == customBackground
            ? _value._customBackground
            : customBackground // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CharacterBackgroundImpl implements _CharacterBackground {
  const _$CharacterBackgroundImpl({
    this.birthPlace,
    this.birthDate,
    this.family,
    this.education,
    this.occupation,
    this.socialClass,
    final List<String> languages = const [],
    final List<BackgroundEvent> lifeEvents = const [],
    this.currentResidence,
    this.backstory,
    final Map<String, dynamic> customBackground = const {},
  }) : _languages = languages,
       _lifeEvents = lifeEvents,
       _customBackground = customBackground;

  factory _$CharacterBackgroundImpl.fromJson(Map<String, dynamic> json) =>
      _$$CharacterBackgroundImplFromJson(json);

  @override
  final String? birthPlace;
  @override
  final DateTime? birthDate;
  @override
  final String? family;
  @override
  final String? education;
  @override
  final String? occupation;
  @override
  final String? socialClass;
  final List<String> _languages;
  @override
  @JsonKey()
  List<String> get languages {
    if (_languages is EqualUnmodifiableListView) return _languages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_languages);
  }

  final List<BackgroundEvent> _lifeEvents;
  @override
  @JsonKey()
  List<BackgroundEvent> get lifeEvents {
    if (_lifeEvents is EqualUnmodifiableListView) return _lifeEvents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_lifeEvents);
  }

  @override
  final String? currentResidence;
  @override
  final String? backstory;
  final Map<String, dynamic> _customBackground;
  @override
  @JsonKey()
  Map<String, dynamic> get customBackground {
    if (_customBackground is EqualUnmodifiableMapView) return _customBackground;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customBackground);
  }

  @override
  String toString() {
    return 'CharacterBackground(birthPlace: $birthPlace, birthDate: $birthDate, family: $family, education: $education, occupation: $occupation, socialClass: $socialClass, languages: $languages, lifeEvents: $lifeEvents, currentResidence: $currentResidence, backstory: $backstory, customBackground: $customBackground)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CharacterBackgroundImpl &&
            (identical(other.birthPlace, birthPlace) ||
                other.birthPlace == birthPlace) &&
            (identical(other.birthDate, birthDate) ||
                other.birthDate == birthDate) &&
            (identical(other.family, family) || other.family == family) &&
            (identical(other.education, education) ||
                other.education == education) &&
            (identical(other.occupation, occupation) ||
                other.occupation == occupation) &&
            (identical(other.socialClass, socialClass) ||
                other.socialClass == socialClass) &&
            const DeepCollectionEquality().equals(
              other._languages,
              _languages,
            ) &&
            const DeepCollectionEquality().equals(
              other._lifeEvents,
              _lifeEvents,
            ) &&
            (identical(other.currentResidence, currentResidence) ||
                other.currentResidence == currentResidence) &&
            (identical(other.backstory, backstory) ||
                other.backstory == backstory) &&
            const DeepCollectionEquality().equals(
              other._customBackground,
              _customBackground,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    birthPlace,
    birthDate,
    family,
    education,
    occupation,
    socialClass,
    const DeepCollectionEquality().hash(_languages),
    const DeepCollectionEquality().hash(_lifeEvents),
    currentResidence,
    backstory,
    const DeepCollectionEquality().hash(_customBackground),
  );

  /// Create a copy of CharacterBackground
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CharacterBackgroundImplCopyWith<_$CharacterBackgroundImpl> get copyWith =>
      __$$CharacterBackgroundImplCopyWithImpl<_$CharacterBackgroundImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CharacterBackgroundImplToJson(this);
  }
}

abstract class _CharacterBackground implements CharacterBackground {
  const factory _CharacterBackground({
    final String? birthPlace,
    final DateTime? birthDate,
    final String? family,
    final String? education,
    final String? occupation,
    final String? socialClass,
    final List<String> languages,
    final List<BackgroundEvent> lifeEvents,
    final String? currentResidence,
    final String? backstory,
    final Map<String, dynamic> customBackground,
  }) = _$CharacterBackgroundImpl;

  factory _CharacterBackground.fromJson(Map<String, dynamic> json) =
      _$CharacterBackgroundImpl.fromJson;

  @override
  String? get birthPlace;
  @override
  DateTime? get birthDate;
  @override
  String? get family;
  @override
  String? get education;
  @override
  String? get occupation;
  @override
  String? get socialClass;
  @override
  List<String> get languages;
  @override
  List<BackgroundEvent> get lifeEvents;
  @override
  String? get currentResidence;
  @override
  String? get backstory;
  @override
  Map<String, dynamic> get customBackground;

  /// Create a copy of CharacterBackground
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CharacterBackgroundImplCopyWith<_$CharacterBackgroundImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BackgroundEvent _$BackgroundEventFromJson(Map<String, dynamic> json) {
  return _BackgroundEvent.fromJson(json);
}

/// @nodoc
mixin _$BackgroundEvent {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  DateTime? get date => throw _privateConstructorUsedError;
  String? get location => throw _privateConstructorUsedError;
  List<String> get involvedCharacterIds => throw _privateConstructorUsedError;
  BackgroundEventType? get type => throw _privateConstructorUsedError;
  int? get impactLevel => throw _privateConstructorUsedError; // 影响程度 1-10
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;

  /// Serializes this BackgroundEvent to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BackgroundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BackgroundEventCopyWith<BackgroundEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BackgroundEventCopyWith<$Res> {
  factory $BackgroundEventCopyWith(
    BackgroundEvent value,
    $Res Function(BackgroundEvent) then,
  ) = _$BackgroundEventCopyWithImpl<$Res, BackgroundEvent>;
  @useResult
  $Res call({
    String id,
    String title,
    String? description,
    DateTime? date,
    String? location,
    List<String> involvedCharacterIds,
    BackgroundEventType? type,
    int? impactLevel,
    Map<String, dynamic> metadata,
  });
}

/// @nodoc
class _$BackgroundEventCopyWithImpl<$Res, $Val extends BackgroundEvent>
    implements $BackgroundEventCopyWith<$Res> {
  _$BackgroundEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BackgroundEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = freezed,
    Object? date = freezed,
    Object? location = freezed,
    Object? involvedCharacterIds = null,
    Object? type = freezed,
    Object? impactLevel = freezed,
    Object? metadata = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            date: freezed == date
                ? _value.date
                : date // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            location: freezed == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as String?,
            involvedCharacterIds: null == involvedCharacterIds
                ? _value.involvedCharacterIds
                : involvedCharacterIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as BackgroundEventType?,
            impactLevel: freezed == impactLevel
                ? _value.impactLevel
                : impactLevel // ignore: cast_nullable_to_non_nullable
                      as int?,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$BackgroundEventImplCopyWith<$Res>
    implements $BackgroundEventCopyWith<$Res> {
  factory _$$BackgroundEventImplCopyWith(
    _$BackgroundEventImpl value,
    $Res Function(_$BackgroundEventImpl) then,
  ) = __$$BackgroundEventImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String? description,
    DateTime? date,
    String? location,
    List<String> involvedCharacterIds,
    BackgroundEventType? type,
    int? impactLevel,
    Map<String, dynamic> metadata,
  });
}

/// @nodoc
class __$$BackgroundEventImplCopyWithImpl<$Res>
    extends _$BackgroundEventCopyWithImpl<$Res, _$BackgroundEventImpl>
    implements _$$BackgroundEventImplCopyWith<$Res> {
  __$$BackgroundEventImplCopyWithImpl(
    _$BackgroundEventImpl _value,
    $Res Function(_$BackgroundEventImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of BackgroundEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = freezed,
    Object? date = freezed,
    Object? location = freezed,
    Object? involvedCharacterIds = null,
    Object? type = freezed,
    Object? impactLevel = freezed,
    Object? metadata = null,
  }) {
    return _then(
      _$BackgroundEventImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        date: freezed == date
            ? _value.date
            : date // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        location: freezed == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as String?,
        involvedCharacterIds: null == involvedCharacterIds
            ? _value._involvedCharacterIds
            : involvedCharacterIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as BackgroundEventType?,
        impactLevel: freezed == impactLevel
            ? _value.impactLevel
            : impactLevel // ignore: cast_nullable_to_non_nullable
                  as int?,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$BackgroundEventImpl implements _BackgroundEvent {
  const _$BackgroundEventImpl({
    required this.id,
    required this.title,
    this.description,
    this.date,
    this.location,
    final List<String> involvedCharacterIds = const [],
    this.type,
    this.impactLevel,
    final Map<String, dynamic> metadata = const {},
  }) : _involvedCharacterIds = involvedCharacterIds,
       _metadata = metadata;

  factory _$BackgroundEventImpl.fromJson(Map<String, dynamic> json) =>
      _$$BackgroundEventImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String? description;
  @override
  final DateTime? date;
  @override
  final String? location;
  final List<String> _involvedCharacterIds;
  @override
  @JsonKey()
  List<String> get involvedCharacterIds {
    if (_involvedCharacterIds is EqualUnmodifiableListView)
      return _involvedCharacterIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_involvedCharacterIds);
  }

  @override
  final BackgroundEventType? type;
  @override
  final int? impactLevel;
  // 影响程度 1-10
  final Map<String, dynamic> _metadata;
  // 影响程度 1-10
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  String toString() {
    return 'BackgroundEvent(id: $id, title: $title, description: $description, date: $date, location: $location, involvedCharacterIds: $involvedCharacterIds, type: $type, impactLevel: $impactLevel, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BackgroundEventImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.location, location) ||
                other.location == location) &&
            const DeepCollectionEquality().equals(
              other._involvedCharacterIds,
              _involvedCharacterIds,
            ) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.impactLevel, impactLevel) ||
                other.impactLevel == impactLevel) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    title,
    description,
    date,
    location,
    const DeepCollectionEquality().hash(_involvedCharacterIds),
    type,
    impactLevel,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of BackgroundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BackgroundEventImplCopyWith<_$BackgroundEventImpl> get copyWith =>
      __$$BackgroundEventImplCopyWithImpl<_$BackgroundEventImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$BackgroundEventImplToJson(this);
  }
}

abstract class _BackgroundEvent implements BackgroundEvent {
  const factory _BackgroundEvent({
    required final String id,
    required final String title,
    final String? description,
    final DateTime? date,
    final String? location,
    final List<String> involvedCharacterIds,
    final BackgroundEventType? type,
    final int? impactLevel,
    final Map<String, dynamic> metadata,
  }) = _$BackgroundEventImpl;

  factory _BackgroundEvent.fromJson(Map<String, dynamic> json) =
      _$BackgroundEventImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String? get description;
  @override
  DateTime? get date;
  @override
  String? get location;
  @override
  List<String> get involvedCharacterIds;
  @override
  BackgroundEventType? get type;
  @override
  int? get impactLevel; // 影响程度 1-10
  @override
  Map<String, dynamic> get metadata;

  /// Create a copy of BackgroundEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BackgroundEventImplCopyWith<_$BackgroundEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CharacterAbility _$CharacterAbilityFromJson(Map<String, dynamic> json) {
  return _CharacterAbility.fromJson(json);
}

/// @nodoc
mixin _$CharacterAbility {
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  AbilityType get type => throw _privateConstructorUsedError;
  int get level => throw _privateConstructorUsedError; // 1-10
  String? get source => throw _privateConstructorUsedError; // 能力来源
  List<String> get limitations => throw _privateConstructorUsedError;
  Map<String, dynamic> get parameters => throw _privateConstructorUsedError;

  /// Serializes this CharacterAbility to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CharacterAbility
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CharacterAbilityCopyWith<CharacterAbility> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CharacterAbilityCopyWith<$Res> {
  factory $CharacterAbilityCopyWith(
    CharacterAbility value,
    $Res Function(CharacterAbility) then,
  ) = _$CharacterAbilityCopyWithImpl<$Res, CharacterAbility>;
  @useResult
  $Res call({
    String name,
    String? description,
    AbilityType type,
    int level,
    String? source,
    List<String> limitations,
    Map<String, dynamic> parameters,
  });
}

/// @nodoc
class _$CharacterAbilityCopyWithImpl<$Res, $Val extends CharacterAbility>
    implements $CharacterAbilityCopyWith<$Res> {
  _$CharacterAbilityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CharacterAbility
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? type = null,
    Object? level = null,
    Object? source = freezed,
    Object? limitations = null,
    Object? parameters = null,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as AbilityType,
            level: null == level
                ? _value.level
                : level // ignore: cast_nullable_to_non_nullable
                      as int,
            source: freezed == source
                ? _value.source
                : source // ignore: cast_nullable_to_non_nullable
                      as String?,
            limitations: null == limitations
                ? _value.limitations
                : limitations // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            parameters: null == parameters
                ? _value.parameters
                : parameters // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CharacterAbilityImplCopyWith<$Res>
    implements $CharacterAbilityCopyWith<$Res> {
  factory _$$CharacterAbilityImplCopyWith(
    _$CharacterAbilityImpl value,
    $Res Function(_$CharacterAbilityImpl) then,
  ) = __$$CharacterAbilityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    String? description,
    AbilityType type,
    int level,
    String? source,
    List<String> limitations,
    Map<String, dynamic> parameters,
  });
}

/// @nodoc
class __$$CharacterAbilityImplCopyWithImpl<$Res>
    extends _$CharacterAbilityCopyWithImpl<$Res, _$CharacterAbilityImpl>
    implements _$$CharacterAbilityImplCopyWith<$Res> {
  __$$CharacterAbilityImplCopyWithImpl(
    _$CharacterAbilityImpl _value,
    $Res Function(_$CharacterAbilityImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CharacterAbility
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? type = null,
    Object? level = null,
    Object? source = freezed,
    Object? limitations = null,
    Object? parameters = null,
  }) {
    return _then(
      _$CharacterAbilityImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as AbilityType,
        level: null == level
            ? _value.level
            : level // ignore: cast_nullable_to_non_nullable
                  as int,
        source: freezed == source
            ? _value.source
            : source // ignore: cast_nullable_to_non_nullable
                  as String?,
        limitations: null == limitations
            ? _value._limitations
            : limitations // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        parameters: null == parameters
            ? _value._parameters
            : parameters // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CharacterAbilityImpl implements _CharacterAbility {
  const _$CharacterAbilityImpl({
    required this.name,
    this.description,
    required this.type,
    required this.level,
    this.source,
    final List<String> limitations = const [],
    final Map<String, dynamic> parameters = const {},
  }) : _limitations = limitations,
       _parameters = parameters;

  factory _$CharacterAbilityImpl.fromJson(Map<String, dynamic> json) =>
      _$$CharacterAbilityImplFromJson(json);

  @override
  final String name;
  @override
  final String? description;
  @override
  final AbilityType type;
  @override
  final int level;
  // 1-10
  @override
  final String? source;
  // 能力来源
  final List<String> _limitations;
  // 能力来源
  @override
  @JsonKey()
  List<String> get limitations {
    if (_limitations is EqualUnmodifiableListView) return _limitations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_limitations);
  }

  final Map<String, dynamic> _parameters;
  @override
  @JsonKey()
  Map<String, dynamic> get parameters {
    if (_parameters is EqualUnmodifiableMapView) return _parameters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_parameters);
  }

  @override
  String toString() {
    return 'CharacterAbility(name: $name, description: $description, type: $type, level: $level, source: $source, limitations: $limitations, parameters: $parameters)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CharacterAbilityImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.source, source) || other.source == source) &&
            const DeepCollectionEquality().equals(
              other._limitations,
              _limitations,
            ) &&
            const DeepCollectionEquality().equals(
              other._parameters,
              _parameters,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    description,
    type,
    level,
    source,
    const DeepCollectionEquality().hash(_limitations),
    const DeepCollectionEquality().hash(_parameters),
  );

  /// Create a copy of CharacterAbility
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CharacterAbilityImplCopyWith<_$CharacterAbilityImpl> get copyWith =>
      __$$CharacterAbilityImplCopyWithImpl<_$CharacterAbilityImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CharacterAbilityImplToJson(this);
  }
}

abstract class _CharacterAbility implements CharacterAbility {
  const factory _CharacterAbility({
    required final String name,
    final String? description,
    required final AbilityType type,
    required final int level,
    final String? source,
    final List<String> limitations,
    final Map<String, dynamic> parameters,
  }) = _$CharacterAbilityImpl;

  factory _CharacterAbility.fromJson(Map<String, dynamic> json) =
      _$CharacterAbilityImpl.fromJson;

  @override
  String get name;
  @override
  String? get description;
  @override
  AbilityType get type;
  @override
  int get level; // 1-10
  @override
  String? get source; // 能力来源
  @override
  List<String> get limitations;
  @override
  Map<String, dynamic> get parameters;

  /// Create a copy of CharacterAbility
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CharacterAbilityImplCopyWith<_$CharacterAbilityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CharacterGoal _$CharacterGoalFromJson(Map<String, dynamic> json) {
  return _CharacterGoal.fromJson(json);
}

/// @nodoc
mixin _$CharacterGoal {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  GoalType get type => throw _privateConstructorUsedError;
  GoalPriority get priority => throw _privateConstructorUsedError;
  GoalStatus get status => throw _privateConstructorUsedError;
  List<String> get obstacles => throw _privateConstructorUsedError; // 障碍
  List<String> get steps => throw _privateConstructorUsedError; // 实现步骤
  String? get motivation => throw _privateConstructorUsedError; // 动机
  DateTime? get deadline => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this CharacterGoal to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CharacterGoal
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CharacterGoalCopyWith<CharacterGoal> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CharacterGoalCopyWith<$Res> {
  factory $CharacterGoalCopyWith(
    CharacterGoal value,
    $Res Function(CharacterGoal) then,
  ) = _$CharacterGoalCopyWithImpl<$Res, CharacterGoal>;
  @useResult
  $Res call({
    String id,
    String title,
    String? description,
    GoalType type,
    GoalPriority priority,
    GoalStatus status,
    List<String> obstacles,
    List<String> steps,
    String? motivation,
    DateTime? deadline,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$CharacterGoalCopyWithImpl<$Res, $Val extends CharacterGoal>
    implements $CharacterGoalCopyWith<$Res> {
  _$CharacterGoalCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CharacterGoal
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = freezed,
    Object? type = null,
    Object? priority = null,
    Object? status = null,
    Object? obstacles = null,
    Object? steps = null,
    Object? motivation = freezed,
    Object? deadline = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as GoalType,
            priority: null == priority
                ? _value.priority
                : priority // ignore: cast_nullable_to_non_nullable
                      as GoalPriority,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as GoalStatus,
            obstacles: null == obstacles
                ? _value.obstacles
                : obstacles // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            steps: null == steps
                ? _value.steps
                : steps // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            motivation: freezed == motivation
                ? _value.motivation
                : motivation // ignore: cast_nullable_to_non_nullable
                      as String?,
            deadline: freezed == deadline
                ? _value.deadline
                : deadline // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CharacterGoalImplCopyWith<$Res>
    implements $CharacterGoalCopyWith<$Res> {
  factory _$$CharacterGoalImplCopyWith(
    _$CharacterGoalImpl value,
    $Res Function(_$CharacterGoalImpl) then,
  ) = __$$CharacterGoalImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String? description,
    GoalType type,
    GoalPriority priority,
    GoalStatus status,
    List<String> obstacles,
    List<String> steps,
    String? motivation,
    DateTime? deadline,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$CharacterGoalImplCopyWithImpl<$Res>
    extends _$CharacterGoalCopyWithImpl<$Res, _$CharacterGoalImpl>
    implements _$$CharacterGoalImplCopyWith<$Res> {
  __$$CharacterGoalImplCopyWithImpl(
    _$CharacterGoalImpl _value,
    $Res Function(_$CharacterGoalImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CharacterGoal
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = freezed,
    Object? type = null,
    Object? priority = null,
    Object? status = null,
    Object? obstacles = null,
    Object? steps = null,
    Object? motivation = freezed,
    Object? deadline = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$CharacterGoalImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as GoalType,
        priority: null == priority
            ? _value.priority
            : priority // ignore: cast_nullable_to_non_nullable
                  as GoalPriority,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as GoalStatus,
        obstacles: null == obstacles
            ? _value._obstacles
            : obstacles // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        steps: null == steps
            ? _value._steps
            : steps // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        motivation: freezed == motivation
            ? _value.motivation
            : motivation // ignore: cast_nullable_to_non_nullable
                  as String?,
        deadline: freezed == deadline
            ? _value.deadline
            : deadline // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CharacterGoalImpl implements _CharacterGoal {
  const _$CharacterGoalImpl({
    required this.id,
    required this.title,
    this.description,
    required this.type,
    required this.priority,
    required this.status,
    final List<String> obstacles = const [],
    final List<String> steps = const [],
    this.motivation,
    this.deadline,
    final Map<String, dynamic> metadata = const {},
    this.createdAt,
    this.updatedAt,
  }) : _obstacles = obstacles,
       _steps = steps,
       _metadata = metadata;

  factory _$CharacterGoalImpl.fromJson(Map<String, dynamic> json) =>
      _$$CharacterGoalImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String? description;
  @override
  final GoalType type;
  @override
  final GoalPriority priority;
  @override
  final GoalStatus status;
  final List<String> _obstacles;
  @override
  @JsonKey()
  List<String> get obstacles {
    if (_obstacles is EqualUnmodifiableListView) return _obstacles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_obstacles);
  }

  // 障碍
  final List<String> _steps;
  // 障碍
  @override
  @JsonKey()
  List<String> get steps {
    if (_steps is EqualUnmodifiableListView) return _steps;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_steps);
  }

  // 实现步骤
  @override
  final String? motivation;
  // 动机
  @override
  final DateTime? deadline;
  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'CharacterGoal(id: $id, title: $title, description: $description, type: $type, priority: $priority, status: $status, obstacles: $obstacles, steps: $steps, motivation: $motivation, deadline: $deadline, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CharacterGoalImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.priority, priority) ||
                other.priority == priority) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(
              other._obstacles,
              _obstacles,
            ) &&
            const DeepCollectionEquality().equals(other._steps, _steps) &&
            (identical(other.motivation, motivation) ||
                other.motivation == motivation) &&
            (identical(other.deadline, deadline) ||
                other.deadline == deadline) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    title,
    description,
    type,
    priority,
    status,
    const DeepCollectionEquality().hash(_obstacles),
    const DeepCollectionEquality().hash(_steps),
    motivation,
    deadline,
    const DeepCollectionEquality().hash(_metadata),
    createdAt,
    updatedAt,
  );

  /// Create a copy of CharacterGoal
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CharacterGoalImplCopyWith<_$CharacterGoalImpl> get copyWith =>
      __$$CharacterGoalImplCopyWithImpl<_$CharacterGoalImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CharacterGoalImplToJson(this);
  }
}

abstract class _CharacterGoal implements CharacterGoal {
  const factory _CharacterGoal({
    required final String id,
    required final String title,
    final String? description,
    required final GoalType type,
    required final GoalPriority priority,
    required final GoalStatus status,
    final List<String> obstacles,
    final List<String> steps,
    final String? motivation,
    final DateTime? deadline,
    final Map<String, dynamic> metadata,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$CharacterGoalImpl;

  factory _CharacterGoal.fromJson(Map<String, dynamic> json) =
      _$CharacterGoalImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String? get description;
  @override
  GoalType get type;
  @override
  GoalPriority get priority;
  @override
  GoalStatus get status;
  @override
  List<String> get obstacles; // 障碍
  @override
  List<String> get steps; // 实现步骤
  @override
  String? get motivation; // 动机
  @override
  DateTime? get deadline;
  @override
  Map<String, dynamic> get metadata;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of CharacterGoal
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CharacterGoalImplCopyWith<_$CharacterGoalImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CharacterSecret _$CharacterSecretFromJson(Map<String, dynamic> json) {
  return _CharacterSecret.fromJson(json);
}

/// @nodoc
mixin _$CharacterSecret {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  SecretType get type => throw _privateConstructorUsedError;
  SecretSeverity get severity => throw _privateConstructorUsedError;
  List<String> get knownByCharacterIds =>
      throw _privateConstructorUsedError; // 知道这个秘密的角色
  String? get revealCondition => throw _privateConstructorUsedError; // 揭露条件
  String? get consequence => throw _privateConstructorUsedError; // 揭露后果
  bool? get isRevealed => throw _privateConstructorUsedError;
  DateTime? get revealedAt => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this CharacterSecret to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CharacterSecret
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CharacterSecretCopyWith<CharacterSecret> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CharacterSecretCopyWith<$Res> {
  factory $CharacterSecretCopyWith(
    CharacterSecret value,
    $Res Function(CharacterSecret) then,
  ) = _$CharacterSecretCopyWithImpl<$Res, CharacterSecret>;
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    SecretType type,
    SecretSeverity severity,
    List<String> knownByCharacterIds,
    String? revealCondition,
    String? consequence,
    bool? isRevealed,
    DateTime? revealedAt,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$CharacterSecretCopyWithImpl<$Res, $Val extends CharacterSecret>
    implements $CharacterSecretCopyWith<$Res> {
  _$CharacterSecretCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CharacterSecret
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? type = null,
    Object? severity = null,
    Object? knownByCharacterIds = null,
    Object? revealCondition = freezed,
    Object? consequence = freezed,
    Object? isRevealed = freezed,
    Object? revealedAt = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as SecretType,
            severity: null == severity
                ? _value.severity
                : severity // ignore: cast_nullable_to_non_nullable
                      as SecretSeverity,
            knownByCharacterIds: null == knownByCharacterIds
                ? _value.knownByCharacterIds
                : knownByCharacterIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            revealCondition: freezed == revealCondition
                ? _value.revealCondition
                : revealCondition // ignore: cast_nullable_to_non_nullable
                      as String?,
            consequence: freezed == consequence
                ? _value.consequence
                : consequence // ignore: cast_nullable_to_non_nullable
                      as String?,
            isRevealed: freezed == isRevealed
                ? _value.isRevealed
                : isRevealed // ignore: cast_nullable_to_non_nullable
                      as bool?,
            revealedAt: freezed == revealedAt
                ? _value.revealedAt
                : revealedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CharacterSecretImplCopyWith<$Res>
    implements $CharacterSecretCopyWith<$Res> {
  factory _$$CharacterSecretImplCopyWith(
    _$CharacterSecretImpl value,
    $Res Function(_$CharacterSecretImpl) then,
  ) = __$$CharacterSecretImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    SecretType type,
    SecretSeverity severity,
    List<String> knownByCharacterIds,
    String? revealCondition,
    String? consequence,
    bool? isRevealed,
    DateTime? revealedAt,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$CharacterSecretImplCopyWithImpl<$Res>
    extends _$CharacterSecretCopyWithImpl<$Res, _$CharacterSecretImpl>
    implements _$$CharacterSecretImplCopyWith<$Res> {
  __$$CharacterSecretImplCopyWithImpl(
    _$CharacterSecretImpl _value,
    $Res Function(_$CharacterSecretImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CharacterSecret
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? type = null,
    Object? severity = null,
    Object? knownByCharacterIds = null,
    Object? revealCondition = freezed,
    Object? consequence = freezed,
    Object? isRevealed = freezed,
    Object? revealedAt = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$CharacterSecretImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as SecretType,
        severity: null == severity
            ? _value.severity
            : severity // ignore: cast_nullable_to_non_nullable
                  as SecretSeverity,
        knownByCharacterIds: null == knownByCharacterIds
            ? _value._knownByCharacterIds
            : knownByCharacterIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        revealCondition: freezed == revealCondition
            ? _value.revealCondition
            : revealCondition // ignore: cast_nullable_to_non_nullable
                  as String?,
        consequence: freezed == consequence
            ? _value.consequence
            : consequence // ignore: cast_nullable_to_non_nullable
                  as String?,
        isRevealed: freezed == isRevealed
            ? _value.isRevealed
            : isRevealed // ignore: cast_nullable_to_non_nullable
                  as bool?,
        revealedAt: freezed == revealedAt
            ? _value.revealedAt
            : revealedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CharacterSecretImpl implements _CharacterSecret {
  const _$CharacterSecretImpl({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.severity,
    final List<String> knownByCharacterIds = const [],
    this.revealCondition,
    this.consequence,
    this.isRevealed,
    this.revealedAt,
    final Map<String, dynamic> metadata = const {},
    this.createdAt,
    this.updatedAt,
  }) : _knownByCharacterIds = knownByCharacterIds,
       _metadata = metadata;

  factory _$CharacterSecretImpl.fromJson(Map<String, dynamic> json) =>
      _$$CharacterSecretImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  @override
  final SecretType type;
  @override
  final SecretSeverity severity;
  final List<String> _knownByCharacterIds;
  @override
  @JsonKey()
  List<String> get knownByCharacterIds {
    if (_knownByCharacterIds is EqualUnmodifiableListView)
      return _knownByCharacterIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_knownByCharacterIds);
  }

  // 知道这个秘密的角色
  @override
  final String? revealCondition;
  // 揭露条件
  @override
  final String? consequence;
  // 揭露后果
  @override
  final bool? isRevealed;
  @override
  final DateTime? revealedAt;
  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'CharacterSecret(id: $id, title: $title, description: $description, type: $type, severity: $severity, knownByCharacterIds: $knownByCharacterIds, revealCondition: $revealCondition, consequence: $consequence, isRevealed: $isRevealed, revealedAt: $revealedAt, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CharacterSecretImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.severity, severity) ||
                other.severity == severity) &&
            const DeepCollectionEquality().equals(
              other._knownByCharacterIds,
              _knownByCharacterIds,
            ) &&
            (identical(other.revealCondition, revealCondition) ||
                other.revealCondition == revealCondition) &&
            (identical(other.consequence, consequence) ||
                other.consequence == consequence) &&
            (identical(other.isRevealed, isRevealed) ||
                other.isRevealed == isRevealed) &&
            (identical(other.revealedAt, revealedAt) ||
                other.revealedAt == revealedAt) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    title,
    description,
    type,
    severity,
    const DeepCollectionEquality().hash(_knownByCharacterIds),
    revealCondition,
    consequence,
    isRevealed,
    revealedAt,
    const DeepCollectionEquality().hash(_metadata),
    createdAt,
    updatedAt,
  );

  /// Create a copy of CharacterSecret
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CharacterSecretImplCopyWith<_$CharacterSecretImpl> get copyWith =>
      __$$CharacterSecretImplCopyWithImpl<_$CharacterSecretImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CharacterSecretImplToJson(this);
  }
}

abstract class _CharacterSecret implements CharacterSecret {
  const factory _CharacterSecret({
    required final String id,
    required final String title,
    required final String description,
    required final SecretType type,
    required final SecretSeverity severity,
    final List<String> knownByCharacterIds,
    final String? revealCondition,
    final String? consequence,
    final bool? isRevealed,
    final DateTime? revealedAt,
    final Map<String, dynamic> metadata,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$CharacterSecretImpl;

  factory _CharacterSecret.fromJson(Map<String, dynamic> json) =
      _$CharacterSecretImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String get description;
  @override
  SecretType get type;
  @override
  SecretSeverity get severity;
  @override
  List<String> get knownByCharacterIds; // 知道这个秘密的角色
  @override
  String? get revealCondition; // 揭露条件
  @override
  String? get consequence; // 揭露后果
  @override
  bool? get isRevealed;
  @override
  DateTime? get revealedAt;
  @override
  Map<String, dynamic> get metadata;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of CharacterSecret
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CharacterSecretImplCopyWith<_$CharacterSecretImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CharacterArc _$CharacterArcFromJson(Map<String, dynamic> json) {
  return _CharacterArc.fromJson(json);
}

/// @nodoc
mixin _$CharacterArc {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  ArcType get type => throw _privateConstructorUsedError;
  ArcStatus get status => throw _privateConstructorUsedError;
  List<ArcMilestone> get milestones => throw _privateConstructorUsedError;
  String? get startingPoint => throw _privateConstructorUsedError; // 起点描述
  String? get endingPoint => throw _privateConstructorUsedError; // 终点描述
  List<String> get relatedPlotLineIds => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this CharacterArc to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CharacterArc
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CharacterArcCopyWith<CharacterArc> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CharacterArcCopyWith<$Res> {
  factory $CharacterArcCopyWith(
    CharacterArc value,
    $Res Function(CharacterArc) then,
  ) = _$CharacterArcCopyWithImpl<$Res, CharacterArc>;
  @useResult
  $Res call({
    String id,
    String title,
    String? description,
    ArcType type,
    ArcStatus status,
    List<ArcMilestone> milestones,
    String? startingPoint,
    String? endingPoint,
    List<String> relatedPlotLineIds,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$CharacterArcCopyWithImpl<$Res, $Val extends CharacterArc>
    implements $CharacterArcCopyWith<$Res> {
  _$CharacterArcCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CharacterArc
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = freezed,
    Object? type = null,
    Object? status = null,
    Object? milestones = null,
    Object? startingPoint = freezed,
    Object? endingPoint = freezed,
    Object? relatedPlotLineIds = null,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as ArcType,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as ArcStatus,
            milestones: null == milestones
                ? _value.milestones
                : milestones // ignore: cast_nullable_to_non_nullable
                      as List<ArcMilestone>,
            startingPoint: freezed == startingPoint
                ? _value.startingPoint
                : startingPoint // ignore: cast_nullable_to_non_nullable
                      as String?,
            endingPoint: freezed == endingPoint
                ? _value.endingPoint
                : endingPoint // ignore: cast_nullable_to_non_nullable
                      as String?,
            relatedPlotLineIds: null == relatedPlotLineIds
                ? _value.relatedPlotLineIds
                : relatedPlotLineIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CharacterArcImplCopyWith<$Res>
    implements $CharacterArcCopyWith<$Res> {
  factory _$$CharacterArcImplCopyWith(
    _$CharacterArcImpl value,
    $Res Function(_$CharacterArcImpl) then,
  ) = __$$CharacterArcImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String? description,
    ArcType type,
    ArcStatus status,
    List<ArcMilestone> milestones,
    String? startingPoint,
    String? endingPoint,
    List<String> relatedPlotLineIds,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$CharacterArcImplCopyWithImpl<$Res>
    extends _$CharacterArcCopyWithImpl<$Res, _$CharacterArcImpl>
    implements _$$CharacterArcImplCopyWith<$Res> {
  __$$CharacterArcImplCopyWithImpl(
    _$CharacterArcImpl _value,
    $Res Function(_$CharacterArcImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CharacterArc
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = freezed,
    Object? type = null,
    Object? status = null,
    Object? milestones = null,
    Object? startingPoint = freezed,
    Object? endingPoint = freezed,
    Object? relatedPlotLineIds = null,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$CharacterArcImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as ArcType,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as ArcStatus,
        milestones: null == milestones
            ? _value._milestones
            : milestones // ignore: cast_nullable_to_non_nullable
                  as List<ArcMilestone>,
        startingPoint: freezed == startingPoint
            ? _value.startingPoint
            : startingPoint // ignore: cast_nullable_to_non_nullable
                  as String?,
        endingPoint: freezed == endingPoint
            ? _value.endingPoint
            : endingPoint // ignore: cast_nullable_to_non_nullable
                  as String?,
        relatedPlotLineIds: null == relatedPlotLineIds
            ? _value._relatedPlotLineIds
            : relatedPlotLineIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CharacterArcImpl implements _CharacterArc {
  const _$CharacterArcImpl({
    required this.id,
    required this.title,
    this.description,
    required this.type,
    required this.status,
    final List<ArcMilestone> milestones = const [],
    this.startingPoint,
    this.endingPoint,
    final List<String> relatedPlotLineIds = const [],
    final Map<String, dynamic> metadata = const {},
    this.createdAt,
    this.updatedAt,
  }) : _milestones = milestones,
       _relatedPlotLineIds = relatedPlotLineIds,
       _metadata = metadata;

  factory _$CharacterArcImpl.fromJson(Map<String, dynamic> json) =>
      _$$CharacterArcImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String? description;
  @override
  final ArcType type;
  @override
  final ArcStatus status;
  final List<ArcMilestone> _milestones;
  @override
  @JsonKey()
  List<ArcMilestone> get milestones {
    if (_milestones is EqualUnmodifiableListView) return _milestones;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_milestones);
  }

  @override
  final String? startingPoint;
  // 起点描述
  @override
  final String? endingPoint;
  // 终点描述
  final List<String> _relatedPlotLineIds;
  // 终点描述
  @override
  @JsonKey()
  List<String> get relatedPlotLineIds {
    if (_relatedPlotLineIds is EqualUnmodifiableListView)
      return _relatedPlotLineIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_relatedPlotLineIds);
  }

  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'CharacterArc(id: $id, title: $title, description: $description, type: $type, status: $status, milestones: $milestones, startingPoint: $startingPoint, endingPoint: $endingPoint, relatedPlotLineIds: $relatedPlotLineIds, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CharacterArcImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(
              other._milestones,
              _milestones,
            ) &&
            (identical(other.startingPoint, startingPoint) ||
                other.startingPoint == startingPoint) &&
            (identical(other.endingPoint, endingPoint) ||
                other.endingPoint == endingPoint) &&
            const DeepCollectionEquality().equals(
              other._relatedPlotLineIds,
              _relatedPlotLineIds,
            ) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    title,
    description,
    type,
    status,
    const DeepCollectionEquality().hash(_milestones),
    startingPoint,
    endingPoint,
    const DeepCollectionEquality().hash(_relatedPlotLineIds),
    const DeepCollectionEquality().hash(_metadata),
    createdAt,
    updatedAt,
  );

  /// Create a copy of CharacterArc
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CharacterArcImplCopyWith<_$CharacterArcImpl> get copyWith =>
      __$$CharacterArcImplCopyWithImpl<_$CharacterArcImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CharacterArcImplToJson(this);
  }
}

abstract class _CharacterArc implements CharacterArc {
  const factory _CharacterArc({
    required final String id,
    required final String title,
    final String? description,
    required final ArcType type,
    required final ArcStatus status,
    final List<ArcMilestone> milestones,
    final String? startingPoint,
    final String? endingPoint,
    final List<String> relatedPlotLineIds,
    final Map<String, dynamic> metadata,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$CharacterArcImpl;

  factory _CharacterArc.fromJson(Map<String, dynamic> json) =
      _$CharacterArcImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String? get description;
  @override
  ArcType get type;
  @override
  ArcStatus get status;
  @override
  List<ArcMilestone> get milestones;
  @override
  String? get startingPoint; // 起点描述
  @override
  String? get endingPoint; // 终点描述
  @override
  List<String> get relatedPlotLineIds;
  @override
  Map<String, dynamic> get metadata;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of CharacterArc
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CharacterArcImplCopyWith<_$CharacterArcImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ArcMilestone _$ArcMilestoneFromJson(Map<String, dynamic> json) {
  return _ArcMilestone.fromJson(json);
}

/// @nodoc
mixin _$ArcMilestone {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  int get order => throw _privateConstructorUsedError;
  MilestoneStatus? get status => throw _privateConstructorUsedError;
  String? get chapterId => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  DateTime? get achievedAt => throw _privateConstructorUsedError;

  /// Serializes this ArcMilestone to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ArcMilestone
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ArcMilestoneCopyWith<ArcMilestone> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ArcMilestoneCopyWith<$Res> {
  factory $ArcMilestoneCopyWith(
    ArcMilestone value,
    $Res Function(ArcMilestone) then,
  ) = _$ArcMilestoneCopyWithImpl<$Res, ArcMilestone>;
  @useResult
  $Res call({
    String id,
    String title,
    String? description,
    int order,
    MilestoneStatus? status,
    String? chapterId,
    Map<String, dynamic> metadata,
    DateTime? achievedAt,
  });
}

/// @nodoc
class _$ArcMilestoneCopyWithImpl<$Res, $Val extends ArcMilestone>
    implements $ArcMilestoneCopyWith<$Res> {
  _$ArcMilestoneCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ArcMilestone
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = freezed,
    Object? order = null,
    Object? status = freezed,
    Object? chapterId = freezed,
    Object? metadata = null,
    Object? achievedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            order: null == order
                ? _value.order
                : order // ignore: cast_nullable_to_non_nullable
                      as int,
            status: freezed == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as MilestoneStatus?,
            chapterId: freezed == chapterId
                ? _value.chapterId
                : chapterId // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            achievedAt: freezed == achievedAt
                ? _value.achievedAt
                : achievedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ArcMilestoneImplCopyWith<$Res>
    implements $ArcMilestoneCopyWith<$Res> {
  factory _$$ArcMilestoneImplCopyWith(
    _$ArcMilestoneImpl value,
    $Res Function(_$ArcMilestoneImpl) then,
  ) = __$$ArcMilestoneImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String? description,
    int order,
    MilestoneStatus? status,
    String? chapterId,
    Map<String, dynamic> metadata,
    DateTime? achievedAt,
  });
}

/// @nodoc
class __$$ArcMilestoneImplCopyWithImpl<$Res>
    extends _$ArcMilestoneCopyWithImpl<$Res, _$ArcMilestoneImpl>
    implements _$$ArcMilestoneImplCopyWith<$Res> {
  __$$ArcMilestoneImplCopyWithImpl(
    _$ArcMilestoneImpl _value,
    $Res Function(_$ArcMilestoneImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ArcMilestone
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = freezed,
    Object? order = null,
    Object? status = freezed,
    Object? chapterId = freezed,
    Object? metadata = null,
    Object? achievedAt = freezed,
  }) {
    return _then(
      _$ArcMilestoneImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        order: null == order
            ? _value.order
            : order // ignore: cast_nullable_to_non_nullable
                  as int,
        status: freezed == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as MilestoneStatus?,
        chapterId: freezed == chapterId
            ? _value.chapterId
            : chapterId // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        achievedAt: freezed == achievedAt
            ? _value.achievedAt
            : achievedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ArcMilestoneImpl implements _ArcMilestone {
  const _$ArcMilestoneImpl({
    required this.id,
    required this.title,
    this.description,
    required this.order,
    this.status,
    this.chapterId,
    final Map<String, dynamic> metadata = const {},
    this.achievedAt,
  }) : _metadata = metadata;

  factory _$ArcMilestoneImpl.fromJson(Map<String, dynamic> json) =>
      _$$ArcMilestoneImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String? description;
  @override
  final int order;
  @override
  final MilestoneStatus? status;
  @override
  final String? chapterId;
  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  final DateTime? achievedAt;

  @override
  String toString() {
    return 'ArcMilestone(id: $id, title: $title, description: $description, order: $order, status: $status, chapterId: $chapterId, metadata: $metadata, achievedAt: $achievedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ArcMilestoneImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.chapterId, chapterId) ||
                other.chapterId == chapterId) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.achievedAt, achievedAt) ||
                other.achievedAt == achievedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    title,
    description,
    order,
    status,
    chapterId,
    const DeepCollectionEquality().hash(_metadata),
    achievedAt,
  );

  /// Create a copy of ArcMilestone
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ArcMilestoneImplCopyWith<_$ArcMilestoneImpl> get copyWith =>
      __$$ArcMilestoneImplCopyWithImpl<_$ArcMilestoneImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ArcMilestoneImplToJson(this);
  }
}

abstract class _ArcMilestone implements ArcMilestone {
  const factory _ArcMilestone({
    required final String id,
    required final String title,
    final String? description,
    required final int order,
    final MilestoneStatus? status,
    final String? chapterId,
    final Map<String, dynamic> metadata,
    final DateTime? achievedAt,
  }) = _$ArcMilestoneImpl;

  factory _ArcMilestone.fromJson(Map<String, dynamic> json) =
      _$ArcMilestoneImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String? get description;
  @override
  int get order;
  @override
  MilestoneStatus? get status;
  @override
  String? get chapterId;
  @override
  Map<String, dynamic> get metadata;
  @override
  DateTime? get achievedAt;

  /// Create a copy of ArcMilestone
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ArcMilestoneImplCopyWith<_$ArcMilestoneImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CharacterStats _$CharacterStatsFromJson(Map<String, dynamic> json) {
  return _CharacterStats.fromJson(json);
}

/// @nodoc
mixin _$CharacterStats {
  String get characterId => throw _privateConstructorUsedError;
  int? get appearanceCount => throw _privateConstructorUsedError; // 出场次数
  int? get dialogueCount => throw _privateConstructorUsedError; // 对话次数
  int? get mentionCount => throw _privateConstructorUsedError; // 被提及次数
  List<String> get mostInteractedCharacters =>
      throw _privateConstructorUsedError;
  List<String> get frequentLocations => throw _privateConstructorUsedError;
  Map<String, int> get emotionFrequency => throw _privateConstructorUsedError;
  DateTime? get lastAppearance => throw _privateConstructorUsedError;
  Map<String, dynamic> get customStats => throw _privateConstructorUsedError;

  /// Serializes this CharacterStats to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CharacterStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CharacterStatsCopyWith<CharacterStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CharacterStatsCopyWith<$Res> {
  factory $CharacterStatsCopyWith(
    CharacterStats value,
    $Res Function(CharacterStats) then,
  ) = _$CharacterStatsCopyWithImpl<$Res, CharacterStats>;
  @useResult
  $Res call({
    String characterId,
    int? appearanceCount,
    int? dialogueCount,
    int? mentionCount,
    List<String> mostInteractedCharacters,
    List<String> frequentLocations,
    Map<String, int> emotionFrequency,
    DateTime? lastAppearance,
    Map<String, dynamic> customStats,
  });
}

/// @nodoc
class _$CharacterStatsCopyWithImpl<$Res, $Val extends CharacterStats>
    implements $CharacterStatsCopyWith<$Res> {
  _$CharacterStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CharacterStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? characterId = null,
    Object? appearanceCount = freezed,
    Object? dialogueCount = freezed,
    Object? mentionCount = freezed,
    Object? mostInteractedCharacters = null,
    Object? frequentLocations = null,
    Object? emotionFrequency = null,
    Object? lastAppearance = freezed,
    Object? customStats = null,
  }) {
    return _then(
      _value.copyWith(
            characterId: null == characterId
                ? _value.characterId
                : characterId // ignore: cast_nullable_to_non_nullable
                      as String,
            appearanceCount: freezed == appearanceCount
                ? _value.appearanceCount
                : appearanceCount // ignore: cast_nullable_to_non_nullable
                      as int?,
            dialogueCount: freezed == dialogueCount
                ? _value.dialogueCount
                : dialogueCount // ignore: cast_nullable_to_non_nullable
                      as int?,
            mentionCount: freezed == mentionCount
                ? _value.mentionCount
                : mentionCount // ignore: cast_nullable_to_non_nullable
                      as int?,
            mostInteractedCharacters: null == mostInteractedCharacters
                ? _value.mostInteractedCharacters
                : mostInteractedCharacters // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            frequentLocations: null == frequentLocations
                ? _value.frequentLocations
                : frequentLocations // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            emotionFrequency: null == emotionFrequency
                ? _value.emotionFrequency
                : emotionFrequency // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
            lastAppearance: freezed == lastAppearance
                ? _value.lastAppearance
                : lastAppearance // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            customStats: null == customStats
                ? _value.customStats
                : customStats // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CharacterStatsImplCopyWith<$Res>
    implements $CharacterStatsCopyWith<$Res> {
  factory _$$CharacterStatsImplCopyWith(
    _$CharacterStatsImpl value,
    $Res Function(_$CharacterStatsImpl) then,
  ) = __$$CharacterStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String characterId,
    int? appearanceCount,
    int? dialogueCount,
    int? mentionCount,
    List<String> mostInteractedCharacters,
    List<String> frequentLocations,
    Map<String, int> emotionFrequency,
    DateTime? lastAppearance,
    Map<String, dynamic> customStats,
  });
}

/// @nodoc
class __$$CharacterStatsImplCopyWithImpl<$Res>
    extends _$CharacterStatsCopyWithImpl<$Res, _$CharacterStatsImpl>
    implements _$$CharacterStatsImplCopyWith<$Res> {
  __$$CharacterStatsImplCopyWithImpl(
    _$CharacterStatsImpl _value,
    $Res Function(_$CharacterStatsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CharacterStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? characterId = null,
    Object? appearanceCount = freezed,
    Object? dialogueCount = freezed,
    Object? mentionCount = freezed,
    Object? mostInteractedCharacters = null,
    Object? frequentLocations = null,
    Object? emotionFrequency = null,
    Object? lastAppearance = freezed,
    Object? customStats = null,
  }) {
    return _then(
      _$CharacterStatsImpl(
        characterId: null == characterId
            ? _value.characterId
            : characterId // ignore: cast_nullable_to_non_nullable
                  as String,
        appearanceCount: freezed == appearanceCount
            ? _value.appearanceCount
            : appearanceCount // ignore: cast_nullable_to_non_nullable
                  as int?,
        dialogueCount: freezed == dialogueCount
            ? _value.dialogueCount
            : dialogueCount // ignore: cast_nullable_to_non_nullable
                  as int?,
        mentionCount: freezed == mentionCount
            ? _value.mentionCount
            : mentionCount // ignore: cast_nullable_to_non_nullable
                  as int?,
        mostInteractedCharacters: null == mostInteractedCharacters
            ? _value._mostInteractedCharacters
            : mostInteractedCharacters // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        frequentLocations: null == frequentLocations
            ? _value._frequentLocations
            : frequentLocations // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        emotionFrequency: null == emotionFrequency
            ? _value._emotionFrequency
            : emotionFrequency // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
        lastAppearance: freezed == lastAppearance
            ? _value.lastAppearance
            : lastAppearance // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        customStats: null == customStats
            ? _value._customStats
            : customStats // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CharacterStatsImpl implements _CharacterStats {
  const _$CharacterStatsImpl({
    required this.characterId,
    this.appearanceCount,
    this.dialogueCount,
    this.mentionCount,
    final List<String> mostInteractedCharacters = const [],
    final List<String> frequentLocations = const [],
    final Map<String, int> emotionFrequency = const {},
    this.lastAppearance,
    final Map<String, dynamic> customStats = const {},
  }) : _mostInteractedCharacters = mostInteractedCharacters,
       _frequentLocations = frequentLocations,
       _emotionFrequency = emotionFrequency,
       _customStats = customStats;

  factory _$CharacterStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$CharacterStatsImplFromJson(json);

  @override
  final String characterId;
  @override
  final int? appearanceCount;
  // 出场次数
  @override
  final int? dialogueCount;
  // 对话次数
  @override
  final int? mentionCount;
  // 被提及次数
  final List<String> _mostInteractedCharacters;
  // 被提及次数
  @override
  @JsonKey()
  List<String> get mostInteractedCharacters {
    if (_mostInteractedCharacters is EqualUnmodifiableListView)
      return _mostInteractedCharacters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_mostInteractedCharacters);
  }

  final List<String> _frequentLocations;
  @override
  @JsonKey()
  List<String> get frequentLocations {
    if (_frequentLocations is EqualUnmodifiableListView)
      return _frequentLocations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_frequentLocations);
  }

  final Map<String, int> _emotionFrequency;
  @override
  @JsonKey()
  Map<String, int> get emotionFrequency {
    if (_emotionFrequency is EqualUnmodifiableMapView) return _emotionFrequency;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_emotionFrequency);
  }

  @override
  final DateTime? lastAppearance;
  final Map<String, dynamic> _customStats;
  @override
  @JsonKey()
  Map<String, dynamic> get customStats {
    if (_customStats is EqualUnmodifiableMapView) return _customStats;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customStats);
  }

  @override
  String toString() {
    return 'CharacterStats(characterId: $characterId, appearanceCount: $appearanceCount, dialogueCount: $dialogueCount, mentionCount: $mentionCount, mostInteractedCharacters: $mostInteractedCharacters, frequentLocations: $frequentLocations, emotionFrequency: $emotionFrequency, lastAppearance: $lastAppearance, customStats: $customStats)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CharacterStatsImpl &&
            (identical(other.characterId, characterId) ||
                other.characterId == characterId) &&
            (identical(other.appearanceCount, appearanceCount) ||
                other.appearanceCount == appearanceCount) &&
            (identical(other.dialogueCount, dialogueCount) ||
                other.dialogueCount == dialogueCount) &&
            (identical(other.mentionCount, mentionCount) ||
                other.mentionCount == mentionCount) &&
            const DeepCollectionEquality().equals(
              other._mostInteractedCharacters,
              _mostInteractedCharacters,
            ) &&
            const DeepCollectionEquality().equals(
              other._frequentLocations,
              _frequentLocations,
            ) &&
            const DeepCollectionEquality().equals(
              other._emotionFrequency,
              _emotionFrequency,
            ) &&
            (identical(other.lastAppearance, lastAppearance) ||
                other.lastAppearance == lastAppearance) &&
            const DeepCollectionEquality().equals(
              other._customStats,
              _customStats,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    characterId,
    appearanceCount,
    dialogueCount,
    mentionCount,
    const DeepCollectionEquality().hash(_mostInteractedCharacters),
    const DeepCollectionEquality().hash(_frequentLocations),
    const DeepCollectionEquality().hash(_emotionFrequency),
    lastAppearance,
    const DeepCollectionEquality().hash(_customStats),
  );

  /// Create a copy of CharacterStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CharacterStatsImplCopyWith<_$CharacterStatsImpl> get copyWith =>
      __$$CharacterStatsImplCopyWithImpl<_$CharacterStatsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CharacterStatsImplToJson(this);
  }
}

abstract class _CharacterStats implements CharacterStats {
  const factory _CharacterStats({
    required final String characterId,
    final int? appearanceCount,
    final int? dialogueCount,
    final int? mentionCount,
    final List<String> mostInteractedCharacters,
    final List<String> frequentLocations,
    final Map<String, int> emotionFrequency,
    final DateTime? lastAppearance,
    final Map<String, dynamic> customStats,
  }) = _$CharacterStatsImpl;

  factory _CharacterStats.fromJson(Map<String, dynamic> json) =
      _$CharacterStatsImpl.fromJson;

  @override
  String get characterId;
  @override
  int? get appearanceCount; // 出场次数
  @override
  int? get dialogueCount; // 对话次数
  @override
  int? get mentionCount; // 被提及次数
  @override
  List<String> get mostInteractedCharacters;
  @override
  List<String> get frequentLocations;
  @override
  Map<String, int> get emotionFrequency;
  @override
  DateTime? get lastAppearance;
  @override
  Map<String, dynamic> get customStats;

  /// Create a copy of CharacterStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CharacterStatsImplCopyWith<_$CharacterStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
