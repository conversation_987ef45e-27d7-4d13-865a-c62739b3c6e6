import 'package:freezed_annotation/freezed_annotation.dart';

part 'version_diff.freezed.dart';
part 'version_diff.g.dart';

/// 版本差异实体
/// 表示两个版本之间的差异信息
@freezed
class VersionDiff with _$VersionDiff {
  const factory VersionDiff({
    /// 差异唯一标识符
    required String id,
    
    /// 源版本ID
    required String sourceVersionId,
    
    /// 目标版本ID
    required String targetVersionId,
    
    /// 差异创建时间
    required DateTime createdAt,
    
    /// 差异类型
    @Default(DiffType.textDiff) DiffType diffType,
    
    /// 差异操作列表
    @Default([]) List<DiffOperation> operations,
    
    /// 统计信息
    required DiffStatistics statistics,
    
    /// 差异元数据
    @Default({}) Map<String, dynamic> metadata,
  }) = _VersionDiff;

  factory VersionDiff.fromJson(Map<String, dynamic> json) => _$VersionDiffFromJson(json);
}

/// 差异类型枚举
enum DiffType {
  /// 文本差异
  textDiff,
  
  /// 行差异
  lineDiff,
  
  /// 单词差异
  wordDiff,
  
  /// 字符差异
  characterDiff,
  
  /// 语义差异
  semanticDiff,
}

/// 差异操作实体
@freezed
class DiffOperation with _$DiffOperation {
  const factory DiffOperation({
    /// 操作类型
    required OperationType type,
    
    /// 操作位置（在源文本中的位置）
    required int position,
    
    /// 操作长度
    @Default(0) int length,
    
    /// 操作内容
    @Default('') String content,
    
    /// 行号（如果适用）
    int? lineNumber,
    
    /// 列号（如果适用）
    int? columnNumber,
    
    /// 操作元数据
    @Default({}) Map<String, dynamic> metadata,
  }) = _DiffOperation;

  factory DiffOperation.fromJson(Map<String, dynamic> json) => _$DiffOperationFromJson(json);
}

/// 操作类型枚举
enum OperationType {
  /// 插入
  insert,
  
  /// 删除
  delete,
  
  /// 替换
  replace,
  
  /// 移动
  move,
  
  /// 无变化（用于上下文）
  equal,
}

/// 差异统计信息
@freezed
class DiffStatistics with _$DiffStatistics {
  const factory DiffStatistics({
    /// 插入的字符数
    @Default(0) int insertedCharacters,
    
    /// 删除的字符数
    @Default(0) int deletedCharacters,
    
    /// 插入的单词数
    @Default(0) int insertedWords,
    
    /// 删除的单词数
    @Default(0) int deletedWords,
    
    /// 插入的行数
    @Default(0) int insertedLines,
    
    /// 删除的行数
    @Default(0) int deletedLines,
    
    /// 修改的行数
    @Default(0) int modifiedLines,
    
    /// 相似度（0-1之间）
    @Default(0.0) double similarity,
    
    /// 变更密度（变更数/总长度）
    @Default(0.0) double changeDensity,
    
    /// 复杂度评分（1-10）
    @Default(1) int complexityScore,
  }) = _DiffStatistics;

  factory DiffStatistics.fromJson(Map<String, dynamic> json) => _$DiffStatisticsFromJson(json);
}

/// 操作类型扩展
extension OperationTypeExtension on OperationType {
  /// 获取操作显示名称
  String get displayName {
    switch (this) {
      case OperationType.insert:
        return '插入';
      case OperationType.delete:
        return '删除';
      case OperationType.replace:
        return '替换';
      case OperationType.move:
        return '移动';
      case OperationType.equal:
        return '无变化';
    }
  }
  
  /// 获取操作颜色
  String get colorHex {
    switch (this) {
      case OperationType.insert:
        return '#10B981'; // 绿色
      case OperationType.delete:
        return '#EF4444'; // 红色
      case OperationType.replace:
        return '#F59E0B'; // 黄色
      case OperationType.move:
        return '#3B82F6'; // 蓝色
      case OperationType.equal:
        return '#6B7280'; // 灰色
    }
  }
  
  /// 获取操作符号
  String get symbol {
    switch (this) {
      case OperationType.insert:
        return '+';
      case OperationType.delete:
        return '-';
      case OperationType.replace:
        return '~';
      case OperationType.move:
        return '→';
      case OperationType.equal:
        return '=';
    }
  }
}

/// 版本差异扩展方法
extension VersionDiffExtension on VersionDiff {
  /// 是否有变更
  bool get hasChanges {
    return operations.any((op) => op.type != OperationType.equal);
  }
  
  /// 获取变更操作
  List<DiffOperation> get changeOperations {
    return operations.where((op) => op.type != OperationType.equal).toList();
  }
  
  /// 获取插入操作
  List<DiffOperation> get insertOperations {
    return operations.where((op) => op.type == OperationType.insert).toList();
  }
  
  /// 获取删除操作
  List<DiffOperation> get deleteOperations {
    return operations.where((op) => op.type == OperationType.delete).toList();
  }
  
  /// 获取替换操作
  List<DiffOperation> get replaceOperations {
    return operations.where((op) => op.type == OperationType.replace).toList();
  }
  
  /// 获取总变更数
  int get totalChanges {
    return changeOperations.length;
  }
  
  /// 获取变更摘要
  String get changeSummary {
    if (!hasChanges) return '无变更';
    
    final inserts = insertOperations.length;
    final deletes = deleteOperations.length;
    final replaces = replaceOperations.length;
    
    final parts = <String>[];
    if (inserts > 0) parts.add('$inserts处插入');
    if (deletes > 0) parts.add('$deletes处删除');
    if (replaces > 0) parts.add('$replaces处替换');
    
    return parts.join('，');
  }
  
  /// 获取变更类型
  String get changeType {
    if (!hasChanges) return '无变更';
    
    final inserts = statistics.insertedCharacters;
    final deletes = statistics.deletedCharacters;
    
    if (inserts > deletes * 2) return '大量新增';
    if (deletes > inserts * 2) return '大量删除';
    if (inserts > 0 && deletes > 0) return '修改';
    if (inserts > 0) return '新增';
    if (deletes > 0) return '删除';
    
    return '未知';
  }
  
  /// 应用差异到文本
  String applyToText(String sourceText) {
    String result = sourceText;
    int offset = 0;
    
    for (final operation in operations) {
      switch (operation.type) {
        case OperationType.insert:
          result = result.substring(0, operation.position + offset) +
                  operation.content +
                  result.substring(operation.position + offset);
          offset += operation.content.length;
          break;
          
        case OperationType.delete:
          final start = operation.position + offset;
          final end = start + operation.length;
          result = result.substring(0, start) + result.substring(end);
          offset -= operation.length;
          break;
          
        case OperationType.replace:
          final start = operation.position + offset;
          final end = start + operation.length;
          result = result.substring(0, start) +
                  operation.content +
                  result.substring(end);
          offset += operation.content.length - operation.length;
          break;
          
        case OperationType.move:
        case OperationType.equal:
          // 这些操作不改变文本内容
          break;
      }
    }
    
    return result;
  }
  
  /// 反向应用差异
  String reverseApplyToText(String targetText) {
    String result = targetText;
    int offset = 0;
    
    // 反向遍历操作列表
    for (final operation in operations.reversed) {
      switch (operation.type) {
        case OperationType.insert:
          // 插入变成删除
          final start = operation.position + offset;
          final end = start + operation.content.length;
          result = result.substring(0, start) + result.substring(end);
          offset -= operation.content.length;
          break;
          
        case OperationType.delete:
          // 删除变成插入
          result = result.substring(0, operation.position + offset) +
                  operation.content +
                  result.substring(operation.position + offset);
          offset += operation.content.length;
          break;
          
        case OperationType.replace:
          // 替换需要知道原始内容，这里简化处理
          break;
          
        case OperationType.move:
        case OperationType.equal:
          break;
      }
    }
    
    return result;
  }
}

/// 差异统计扩展方法
extension DiffStatisticsExtension on DiffStatistics {
  /// 总插入数
  int get totalInsertions => insertedCharacters + insertedWords + insertedLines;
  
  /// 总删除数
  int get totalDeletions => deletedCharacters + deletedWords + deletedLines;
  
  /// 总变更数
  int get totalChanges => totalInsertions + totalDeletions + modifiedLines;
  
  /// 是否为重大变更
  bool get isMajorChange => similarity < 0.7 || complexityScore > 7;
  
  /// 是否为轻微变更
  bool get isMinorChange => similarity > 0.9 && complexityScore < 4;
  
  /// 变更级别
  String get changeLevel {
    if (isMinorChange) return '轻微';
    if (isMajorChange) return '重大';
    return '中等';
  }
  
  /// 获取相似度百分比
  String get similarityPercentage => '${(similarity * 100).toStringAsFixed(1)}%';
}

/// 差异构建器
class DiffBuilder {
  /// 计算两个文本之间的差异
  static VersionDiff calculateDiff({
    required String sourceVersionId,
    required String targetVersionId,
    required String sourceText,
    required String targetText,
    DiffType diffType = DiffType.lineDiff,
  }) {
    final operations = <DiffOperation>[];
    final statistics = _calculateStatistics(sourceText, targetText);
    
    switch (diffType) {
      case DiffType.lineDiff:
        operations.addAll(_calculateLineDiff(sourceText, targetText));
        break;
      case DiffType.wordDiff:
        operations.addAll(_calculateWordDiff(sourceText, targetText));
        break;
      case DiffType.characterDiff:
        operations.addAll(_calculateCharacterDiff(sourceText, targetText));
        break;
      default:
        operations.addAll(_calculateLineDiff(sourceText, targetText));
    }
    
    return VersionDiff(
      id: _generateDiffId(),
      sourceVersionId: sourceVersionId,
      targetVersionId: targetVersionId,
      createdAt: DateTime.now(),
      diffType: diffType,
      operations: operations,
      statistics: statistics,
    );
  }
  
  /// 计算行级差异
  static List<DiffOperation> _calculateLineDiff(String sourceText, String targetText) {
    final sourceLines = sourceText.split('\n');
    final targetLines = targetText.split('\n');
    final operations = <DiffOperation>[];
    
    // 简化的差异算法（实际项目中应使用更复杂的算法如Myers算法）
    int sourceIndex = 0;
    int targetIndex = 0;
    int position = 0;
    
    while (sourceIndex < sourceLines.length || targetIndex < targetLines.length) {
      if (sourceIndex >= sourceLines.length) {
        // 只剩目标行，全部插入
        operations.add(DiffOperation(
          type: OperationType.insert,
          position: position,
          content: '${targetLines[targetIndex]}\n',
          lineNumber: targetIndex + 1,
        ));
        position += targetLines[targetIndex].length + 1;
        targetIndex++;
      } else if (targetIndex >= targetLines.length) {
        // 只剩源行，全部删除
        operations.add(DiffOperation(
          type: OperationType.delete,
          position: position,
          length: sourceLines[sourceIndex].length + 1,
          content: '${sourceLines[sourceIndex]}\n',
          lineNumber: sourceIndex + 1,
        ));
        position += sourceLines[sourceIndex].length + 1;
        sourceIndex++;
      } else if (sourceLines[sourceIndex] == targetLines[targetIndex]) {
        // 行相同
        operations.add(DiffOperation(
          type: OperationType.equal,
          position: position,
          length: sourceLines[sourceIndex].length + 1,
          content: '${sourceLines[sourceIndex]}\n',
          lineNumber: sourceIndex + 1,
        ));
        position += sourceLines[sourceIndex].length + 1;
        sourceIndex++;
        targetIndex++;
      } else {
        // 行不同，标记为替换
        operations.add(DiffOperation(
          type: OperationType.replace,
          position: position,
          length: sourceLines[sourceIndex].length + 1,
          content: '${targetLines[targetIndex]}\n',
          lineNumber: sourceIndex + 1,
        ));
        position += sourceLines[sourceIndex].length + 1;
        sourceIndex++;
        targetIndex++;
      }
    }
    
    return operations;
  }
  
  /// 计算单词级差异
  static List<DiffOperation> _calculateWordDiff(String sourceText, String targetText) {
    // 简化实现，实际项目中需要更复杂的算法
    return _calculateCharacterDiff(sourceText, targetText);
  }
  
  /// 计算字符级差异
  static List<DiffOperation> _calculateCharacterDiff(String sourceText, String targetText) {
    final operations = <DiffOperation>[];
    
    // 简化的字符差异算法
    int i = 0, j = 0;
    while (i < sourceText.length || j < targetText.length) {
      if (i >= sourceText.length) {
        operations.add(DiffOperation(
          type: OperationType.insert,
          position: i,
          content: targetText[j],
        ));
        j++;
      } else if (j >= targetText.length) {
        operations.add(DiffOperation(
          type: OperationType.delete,
          position: i,
          length: 1,
          content: sourceText[i],
        ));
        i++;
      } else if (sourceText[i] == targetText[j]) {
        operations.add(DiffOperation(
          type: OperationType.equal,
          position: i,
          length: 1,
          content: sourceText[i],
        ));
        i++;
        j++;
      } else {
        operations.add(DiffOperation(
          type: OperationType.replace,
          position: i,
          length: 1,
          content: targetText[j],
        ));
        i++;
        j++;
      }
    }
    
    return operations;
  }
  
  /// 计算统计信息
  static DiffStatistics _calculateStatistics(String sourceText, String targetText) {
    final sourceLines = sourceText.split('\n');
    final targetLines = targetText.split('\n');
    
    final sourceWords = sourceText.split(RegExp(r'\s+'));
    final targetWords = targetText.split(RegExp(r'\s+'));
    
    final insertedChars = targetText.length - sourceText.length;
    final insertedWords = targetWords.length - sourceWords.length;
    final insertedLines = targetLines.length - sourceLines.length;
    
    // 简化的相似度计算
    final similarity = _calculateSimilarity(sourceText, targetText);
    
    return DiffStatistics(
      insertedCharacters: insertedChars > 0 ? insertedChars : 0,
      deletedCharacters: insertedChars < 0 ? -insertedChars : 0,
      insertedWords: insertedWords > 0 ? insertedWords : 0,
      deletedWords: insertedWords < 0 ? -insertedWords : 0,
      insertedLines: insertedLines > 0 ? insertedLines : 0,
      deletedLines: insertedLines < 0 ? -insertedLines : 0,
      similarity: similarity,
      changeDensity: 1.0 - similarity,
      complexityScore: _calculateComplexityScore(similarity, insertedChars.abs()),
    );
  }
  
  /// 计算相似度
  static double _calculateSimilarity(String text1, String text2) {
    if (text1 == text2) return 1.0;
    if (text1.isEmpty || text2.isEmpty) return 0.0;
    
    final maxLength = text1.length > text2.length ? text1.length : text2.length;
    final distance = _levenshteinDistance(text1, text2);
    
    return 1.0 - (distance / maxLength);
  }
  
  /// 计算编辑距离
  static int _levenshteinDistance(String s1, String s2) {
    final matrix = List.generate(
      s1.length + 1,
      (i) => List.filled(s2.length + 1, 0),
    );
    
    for (int i = 0; i <= s1.length; i++) {
      matrix[i][0] = i;
    }
    
    for (int j = 0; j <= s2.length; j++) {
      matrix[0][j] = j;
    }
    
    for (int i = 1; i <= s1.length; i++) {
      for (int j = 1; j <= s2.length; j++) {
        final cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost,
        ].reduce((a, b) => a < b ? a : b);
      }
    }
    
    return matrix[s1.length][s2.length];
  }
  
  /// 计算复杂度评分
  static int _calculateComplexityScore(double similarity, int changeAmount) {
    if (similarity > 0.9) return 1;
    if (similarity > 0.8) return 2;
    if (similarity > 0.7) return 3;
    if (similarity > 0.6) return 4;
    if (similarity > 0.5) return 5;
    if (similarity > 0.4) return 6;
    if (similarity > 0.3) return 7;
    if (similarity > 0.2) return 8;
    if (similarity > 0.1) return 9;
    return 10;
  }
  
  /// 生成差异ID
  static String _generateDiffId() {
    return 'diff_${DateTime.now().millisecondsSinceEpoch}_${(1000 + (999 * (DateTime.now().microsecond / 1000000))).round()}';
  }
}