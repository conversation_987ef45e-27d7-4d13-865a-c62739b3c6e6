# BambooFall App Release Build Script
# Build release versions for different platforms

param(
    [string]$Platform = "all",
    [switch]$Clean = $false
)

Write-Host "=== BambooFall App Build Script ===" -ForegroundColor Green

# Clean build cache
if ($Clean) {
    Write-Host "Cleaning build cache..." -ForegroundColor Yellow
    flutter clean
    flutter pub get
}

# Create release directory
$releaseDir = ".\release"
if (!(Test-Path $releaseDir)) {
    New-Item -ItemType Directory -Path $releaseDir
}

# Build Windows version
if ($Platform -eq "all" -or $Platform -eq "windows") {
    Write-Host "Building Windows version..." -ForegroundColor Cyan
    flutter build windows --release
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Windows build successful" -ForegroundColor Green
        
        # Copy Windows release files
        $windowsReleaseDir = "$releaseDir\windows"
        if (Test-Path $windowsReleaseDir) {
            Remove-Item -Recurse -Force $windowsReleaseDir
        }
        Copy-Item -Recurse ".\build\windows\x64\runner\Release" $windowsReleaseDir
        
        Write-Host "Windows release package copied to: $windowsReleaseDir" -ForegroundColor Green
    } else {
        Write-Host "Windows build failed" -ForegroundColor Red
        exit 1
    }
}

# Build macOS version (if running on macOS)
if ($Platform -eq "all" -or $Platform -eq "macos") {
    if ($IsLinux -or $IsMacOS) {
        Write-Host "Building macOS version..." -ForegroundColor Cyan
        flutter build macos --release
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "macOS build successful" -ForegroundColor Green
            
            # Copy macOS release files
            $macosReleaseDir = "$releaseDir\macos"
            if (Test-Path $macosReleaseDir) {
                Remove-Item -Recurse -Force $macosReleaseDir
            }
            Copy-Item -Recurse ".\build\macos\Build\Products\Release" $macosReleaseDir
            
            Write-Host "macOS release package copied to: $macosReleaseDir" -ForegroundColor Green
        } else {
            Write-Host "macOS build failed" -ForegroundColor Red
        }
    } else {
        Write-Host "Skipping macOS build (only supported on macOS)" -ForegroundColor Yellow
    }
}

# Build Linux version (if running on Linux)
if ($Platform -eq "all" -or $Platform -eq "linux") {
    if ($IsLinux) {
        Write-Host "Building Linux version..." -ForegroundColor Cyan
        flutter build linux --release
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Linux build successful" -ForegroundColor Green
            
            # Copy Linux release files
            $linuxReleaseDir = "$releaseDir\linux"
            if (Test-Path $linuxReleaseDir) {
                Remove-Item -Recurse -Force $linuxReleaseDir
            }
            Copy-Item -Recurse ".\build\linux\x64\release\bundle" $linuxReleaseDir
            
            Write-Host "Linux release package copied to: $linuxReleaseDir" -ForegroundColor Green
        } else {
            Write-Host "Linux build failed" -ForegroundColor Red
        }
    } else {
        Write-Host "Skipping Linux build (only supported on Linux)" -ForegroundColor Yellow
    }
}

Write-Host "=== Build Complete ===" -ForegroundColor Green
Write-Host "Release files location: $releaseDir" -ForegroundColor Cyan

# Display build information
Write-Host "`nBuild Information:" -ForegroundColor Yellow
Write-Host "App Name: BambooFall"
Write-Host "Version: 1.0.0+1"
Write-Host "Build Time: $(Get-Date)"
$flutterVersion = flutter --version | Select-String 'Flutter' | Select-Object -First 1
Write-Host "Flutter Version: $flutterVersion"