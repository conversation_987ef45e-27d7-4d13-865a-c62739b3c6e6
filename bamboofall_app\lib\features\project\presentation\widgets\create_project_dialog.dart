import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

import '../../domain/entities/project.dart';
import '../../domain/usecases/manage_projects.dart';

/// 创建项目对话框
/// 提供创建新项目的界面
class CreateProjectDialog extends ConsumerStatefulWidget {
  const CreateProjectDialog({super.key});

  @override
  ConsumerState<CreateProjectDialog> createState() => _CreateProjectDialogState();
}

class _CreateProjectDialogState extends ConsumerState<CreateProjectDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _tagsController = TextEditingController();
  
  ProjectType _selectedType = ProjectType.novel;
  int _targetWordCount = 80000;
  int _dailyGoal = 1000;
  String? _selectedTemplateId;
  bool _isLoading = false;
  bool _useTemplate = false;

  @override
  void initState() {
    super.initState();
    _updateTargetWordCount();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return fluent.ContentDialog(
      title: const Text('创建新项目'),
      content: SizedBox(
        width: 500,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 创建方式选择
              _buildCreationModeSelector(),
              
              const SizedBox(height: 16),
              
              // 模板选择（如果使用模板）
              if (_useTemplate) ...[
                _buildTemplateSelector(),
                const SizedBox(height: 16),
              ],
              
              // 项目基本信息
              _buildBasicInfo(),
              
              const SizedBox(height: 16),
              
              // 项目配置（如果不使用模板）
              if (!_useTemplate) ...[
                _buildProjectConfig(),
                const SizedBox(height: 16),
              ],
              
              // 标签
              _buildTagsInput(),
            ],
          ),
        ),
      ),
      actions: [
        fluent.Button(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(false),
          child: const Text('取消'),
        ),
        fluent.FilledButton(
          onPressed: _isLoading ? null : _createProject,
          child: _isLoading 
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: fluent.ProgressRing(),
                )
              : const Text('创建'),
        ),
      ],
    );
  }

  Widget _buildCreationModeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '创建方式',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: fluent.RadioButton(
                checked: !_useTemplate,
                onChanged: (value) => setState(() => _useTemplate = false),
                content: const Text('从头开始'),
              ),
            ),
            Expanded(
              child: fluent.RadioButton(
                checked: _useTemplate,
                onChanged: (value) => setState(() => _useTemplate = true),
                content: const Text('使用模板'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTemplateSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '选择模板',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        FutureBuilder<List<Map<String, dynamic>>>(
          future: Future.value([]), // TODO: 实现项目模板功能
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const fluent.ProgressRing();
            }

            if (snapshot.hasError) {
              return Text('加载模板失败: ${snapshot.error}');
            }

            final templates = snapshot.data ?? [];
            
            return fluent.ComboBox<String>(
              placeholder: const Text('选择一个模板'),
              value: _selectedTemplateId,
              items: templates.map((template) {
                return fluent.ComboBoxItem<String>(
                  value: template['id'] as String,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(template['name'] as String),
                      Text(
                        template['description'] as String,
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedTemplateId = value;
                  if (value != null) {
                    final template = templates.firstWhere((t) => t['id'] == value);
                    final typeString = template['type'] as String;
                    _selectedType = ProjectType.values.firstWhere(
                      (e) => e.name == typeString,
                      orElse: () => ProjectType.novel,
                    );
                    _targetWordCount = (template['config'] as Map)['targetWordCount'] as int;
                    _dailyGoal = (template['config'] as Map)['dailyWritingGoal'] as int;
                  }
                });
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildBasicInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 项目名称
        const Text(
          '项目名称 *',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        fluent.TextBox(
          controller: _nameController,
          placeholder: '输入项目名称',
        ),
        
        const SizedBox(height: 16),
        
        // 项目描述
        const Text(
          '项目描述',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        fluent.TextBox(
          controller: _descriptionController,
          placeholder: '输入项目描述（可选）',
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildProjectConfig() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 项目类型
        const Text(
          '项目类型',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        fluent.ComboBox<ProjectType>(
          value: _selectedType,
          items: ProjectType.values.map((type) {
            return fluent.ComboBoxItem<ProjectType>(
              value: type,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(type.icon),
                  const SizedBox(width: 8),
                  Text(type.displayName),
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedType = value;
                _updateTargetWordCount();
              });
            }
          },
        ),
        
        const SizedBox(height: 16),
        
        // 目标字数和每日目标
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '目标字数',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 8),
                  fluent.NumberBox<int>(
                    value: _targetWordCount,
                    min: 1000,
                    max: 1000000,
                    mode: fluent.SpinButtonPlacementMode.inline,
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _targetWordCount = value);
                      }
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '每日目标',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 8),
                  fluent.NumberBox<int>(
                    value: _dailyGoal,
                    min: 100,
                    max: 10000,
                    mode: fluent.SpinButtonPlacementMode.inline,
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _dailyGoal = value);
                      }
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTagsInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '标签',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        fluent.TextBox(
          controller: _tagsController,
          placeholder: '输入标签，用逗号分隔（可选）',
        ),
        const SizedBox(height: 4),
        Text(
          '例如：小说,奇幻,长篇',
          style: TextStyle(
            fontSize: 11,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  void _updateTargetWordCount() {
    _targetWordCount = _selectedType.defaultTargetWordCount;
    _dailyGoal = (_targetWordCount / 80).round().clamp(100, 2000);
  }

  Future<void> _createProject() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      final useCase = ref.read(manageProjectsUseCaseProvider);
      
      // 解析标签
      final tags = _tagsController.text
          .split(',')
          .map((tag) => tag.trim())
          .where((tag) => tag.isNotEmpty)
          .toList();

      Project project;
      
      if (_useTemplate && _selectedTemplateId != null) {
        // 使用模板创建项目
        project = await useCase.createProjectFromTemplate(
          templateId: _selectedTemplateId!,
          projectName: _nameController.text.trim(),
          createdBy: 'user', // TODO: 从用户上下文获取
        );
        
        // 更新项目描述和标签
        if (_descriptionController.text.trim().isNotEmpty || tags.isNotEmpty) {
          project = await useCase.updateProjectInfo(
            projectId: project.id,
            description: _descriptionController.text.trim().isNotEmpty 
                ? _descriptionController.text.trim() 
                : null,
          );
          
          // 添加标签
          for (final tag in tags) {
            project = await useCase.addProjectTag(project.id, tag);
          }
        }
      } else {
        // 从头创建项目
        final config = ProjectConfig(
          targetWordCount: _targetWordCount,
          dailyWritingGoal: _dailyGoal,
        );
        
        project = await useCase.createProject(
          name: _nameController.text.trim(),
          createdBy: 'user', // TODO: 从用户上下文获取
          description: _descriptionController.text.trim(),
          type: _selectedType,
          tags: tags,
          config: config,
        );
      }

      if (mounted) {
        Navigator.of(context).pop(true);
        
        // 显示成功消息
        fluent.showDialog(
          context: context,
          builder: (context) => fluent.ContentDialog(
            title: const Text('创建成功'),
            content: Text('项目"${project.name}"已创建成功！'),
            actions: [
              fluent.FilledButton(
                child: const Text('确定'),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        
        fluent.showDialog(
          context: context,
          builder: (context) => fluent.ContentDialog(
            title: const Text('创建失败'),
            content: Text('无法创建项目: $e'),
            actions: [
              fluent.Button(
                child: const Text('确定'),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
        );
      }
    }
  }
}