// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$WorldSettingsImpl _$$WorldSettingsImplFromJson(Map<String, dynamic> json) =>
    _$WorldSettingsImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      type: $enumDecode(_$WorldTypeEnumMap, json['type']),
      technologyLevel: $enumDecode(
        _$TechnologyLevelEnumMap,
        json['technologyLevel'],
      ),
      magicLevel: $enumDecode(_$<PERSON>evel<PERSON>numMap, json['magicLevel']),
      physics: json['physics'] == null
          ? null
          : WorldPhysics.fromJson(json['physics'] as Map<String, dynamic>),
      history: json['history'] == null
          ? null
          : WorldHistory.fromJson(json['history'] as Map<String, dynamic>),
      geography: json['geography'] == null
          ? null
          : WorldGeography.fromJson(json['geography'] as Map<String, dynamic>),
      society: json['society'] == null
          ? null
          : WorldSociety.fromJson(json['society'] as Map<String, dynamic>),
      economy: json['economy'] == null
          ? null
          : WorldEconomy.fromJson(json['economy'] as Map<String, dynamic>),
      religion: json['religion'] == null
          ? null
          : WorldReligion.fromJson(json['religion'] as Map<String, dynamic>),
      rules:
          (json['rules'] as List<dynamic>?)
              ?.map((e) => WorldRule.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      majorEvents:
          (json['majorEvents'] as List<dynamic>?)
              ?.map((e) => WorldEvent.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      species:
          (json['species'] as List<dynamic>?)
              ?.map((e) => Species.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      languages:
          (json['languages'] as List<dynamic>?)
              ?.map((e) => Language.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      calendars:
          (json['calendars'] as List<dynamic>?)
              ?.map((e) => Calendar.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      currencies:
          (json['currencies'] as List<dynamic>?)
              ?.map((e) => Currency.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      customSettings:
          json['customSettings'] as Map<String, dynamic>? ?? const {},
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$WorldSettingsImplToJson(_$WorldSettingsImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$WorldTypeEnumMap[instance.type]!,
      'technologyLevel': _$TechnologyLevelEnumMap[instance.technologyLevel]!,
      'magicLevel': _$MagicLevelEnumMap[instance.magicLevel]!,
      'physics': instance.physics,
      'history': instance.history,
      'geography': instance.geography,
      'society': instance.society,
      'economy': instance.economy,
      'religion': instance.religion,
      'rules': instance.rules,
      'majorEvents': instance.majorEvents,
      'species': instance.species,
      'languages': instance.languages,
      'calendars': instance.calendars,
      'currencies': instance.currencies,
      'customSettings': instance.customSettings,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$WorldTypeEnumMap = {
  WorldType.fantasy: 'fantasy',
  WorldType.sciFi: 'sciFi',
  WorldType.modern: 'modern',
  WorldType.historical: 'historical',
  WorldType.postApocalyptic: 'postApocalyptic',
  WorldType.steampunk: 'steampunk',
  WorldType.cyberpunk: 'cyberpunk',
  WorldType.alternate: 'alternate',
  WorldType.mixed: 'mixed',
};

const _$TechnologyLevelEnumMap = {
  TechnologyLevel.primitive: 'primitive',
  TechnologyLevel.ancient: 'ancient',
  TechnologyLevel.medieval: 'medieval',
  TechnologyLevel.renaissance: 'renaissance',
  TechnologyLevel.industrial: 'industrial',
  TechnologyLevel.modern: 'modern',
  TechnologyLevel.futuristic: 'futuristic',
  TechnologyLevel.postApocalyptic: 'postApocalyptic',
  TechnologyLevel.mixed: 'mixed',
};

const _$MagicLevelEnumMap = {
  MagicLevel.none: 'none',
  MagicLevel.rare: 'rare',
  MagicLevel.uncommon: 'uncommon',
  MagicLevel.common: 'common',
  MagicLevel.abundant: 'abundant',
  MagicLevel.omnipresent: 'omnipresent',
};

_$WorldPhysicsImpl _$$WorldPhysicsImplFromJson(Map<String, dynamic> json) =>
    _$WorldPhysicsImpl(
      laws:
          (json['laws'] as List<dynamic>?)
              ?.map((e) => PhysicalLaw.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      constants:
          (json['constants'] as List<dynamic>?)
              ?.map((e) => PhysicalConstant.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      forces:
          (json['forces'] as List<dynamic>?)
              ?.map((e) => PhysicalForce.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      gravityDescription: json['gravityDescription'] as String?,
      timeDescription: json['timeDescription'] as String?,
      spaceDescription: json['spaceDescription'] as String?,
      dimensions:
          (json['dimensions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      customPhysics: json['customPhysics'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$WorldPhysicsImplToJson(_$WorldPhysicsImpl instance) =>
    <String, dynamic>{
      'laws': instance.laws,
      'constants': instance.constants,
      'forces': instance.forces,
      'gravityDescription': instance.gravityDescription,
      'timeDescription': instance.timeDescription,
      'spaceDescription': instance.spaceDescription,
      'dimensions': instance.dimensions,
      'customPhysics': instance.customPhysics,
    };

_$PhysicalLawImpl _$$PhysicalLawImplFromJson(Map<String, dynamic> json) =>
    _$PhysicalLawImpl(
      name: json['name'] as String,
      description: json['description'] as String,
      effects:
          (json['effects'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      exceptions:
          (json['exceptions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      parameters: json['parameters'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$PhysicalLawImplToJson(_$PhysicalLawImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'effects': instance.effects,
      'exceptions': instance.exceptions,
      'parameters': instance.parameters,
    };

_$PhysicalConstantImpl _$$PhysicalConstantImplFromJson(
  Map<String, dynamic> json,
) => _$PhysicalConstantImpl(
  name: json['name'] as String,
  value: json['value'] as String,
  unit: json['unit'] as String?,
  description: json['description'] as String?,
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$PhysicalConstantImplToJson(
  _$PhysicalConstantImpl instance,
) => <String, dynamic>{
  'name': instance.name,
  'value': instance.value,
  'unit': instance.unit,
  'description': instance.description,
  'customAttributes': instance.customAttributes,
};

_$PhysicalForceImpl _$$PhysicalForceImplFromJson(Map<String, dynamic> json) =>
    _$PhysicalForceImpl(
      name: json['name'] as String,
      description: json['description'] as String,
      strength: json['strength'] as String?,
      range: json['range'] as String?,
      effects:
          (json['effects'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      customAttributes:
          json['customAttributes'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$PhysicalForceImplToJson(_$PhysicalForceImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'strength': instance.strength,
      'range': instance.range,
      'effects': instance.effects,
      'customAttributes': instance.customAttributes,
    };

_$WorldHistoryImpl _$$WorldHistoryImplFromJson(Map<String, dynamic> json) =>
    _$WorldHistoryImpl(
      eras:
          (json['eras'] as List<dynamic>?)
              ?.map((e) => HistoricalEra.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      majorEvents:
          (json['majorEvents'] as List<dynamic>?)
              ?.map((e) => HistoricalEvent.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      civilizations:
          (json['civilizations'] as List<dynamic>?)
              ?.map((e) => Civilization.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      wars:
          (json['wars'] as List<dynamic>?)
              ?.map((e) => War.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      discoveries:
          (json['discoveries'] as List<dynamic>?)
              ?.map((e) => Discovery.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      creationMyth: json['creationMyth'] as String?,
      customHistory: json['customHistory'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$WorldHistoryImplToJson(_$WorldHistoryImpl instance) =>
    <String, dynamic>{
      'eras': instance.eras,
      'majorEvents': instance.majorEvents,
      'civilizations': instance.civilizations,
      'wars': instance.wars,
      'discoveries': instance.discoveries,
      'creationMyth': instance.creationMyth,
      'customHistory': instance.customHistory,
    };

_$HistoricalEraImpl _$$HistoricalEraImplFromJson(Map<String, dynamic> json) =>
    _$HistoricalEraImpl(
      name: json['name'] as String,
      description: json['description'] as String?,
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      characteristics:
          (json['characteristics'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      majorEvents:
          (json['majorEvents'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      importantFigures:
          (json['importantFigures'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      customAttributes:
          json['customAttributes'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$HistoricalEraImplToJson(_$HistoricalEraImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'startDate': instance.startDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'characteristics': instance.characteristics,
      'majorEvents': instance.majorEvents,
      'importantFigures': instance.importantFigures,
      'customAttributes': instance.customAttributes,
    };

_$HistoricalEventImpl _$$HistoricalEventImplFromJson(
  Map<String, dynamic> json,
) => _$HistoricalEventImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  date: DateTime.parse(json['date'] as String),
  type: $enumDecodeNullable(_$HistoricalEventTypeEnumMap, json['type']),
  involvedCivilizations:
      (json['involvedCivilizations'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  involvedFigures:
      (json['involvedFigures'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  consequences:
      (json['consequences'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  location: json['location'] as String?,
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$HistoricalEventImplToJson(
  _$HistoricalEventImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'description': instance.description,
  'date': instance.date.toIso8601String(),
  'type': _$HistoricalEventTypeEnumMap[instance.type],
  'involvedCivilizations': instance.involvedCivilizations,
  'involvedFigures': instance.involvedFigures,
  'consequences': instance.consequences,
  'location': instance.location,
  'customAttributes': instance.customAttributes,
};

const _$HistoricalEventTypeEnumMap = {
  HistoricalEventType.war: 'war',
  HistoricalEventType.discovery: 'discovery',
  HistoricalEventType.invention: 'invention',
  HistoricalEventType.disaster: 'disaster',
  HistoricalEventType.founding: 'founding',
  HistoricalEventType.collapse: 'collapse',
  HistoricalEventType.treaty: 'treaty',
  HistoricalEventType.revolution: 'revolution',
  HistoricalEventType.other: 'other',
};

_$CivilizationImpl _$$CivilizationImplFromJson(Map<String, dynamic> json) =>
    _$CivilizationImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      status: $enumDecodeNullable(_$CivilizationStatusEnumMap, json['status']),
      foundedDate: json['foundedDate'] == null
          ? null
          : DateTime.parse(json['foundedDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      territories:
          (json['territories'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      achievements:
          (json['achievements'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      technologies:
          (json['technologies'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      government: json['government'] as String?,
      culture: json['culture'] as String?,
      customAttributes:
          json['customAttributes'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$CivilizationImplToJson(_$CivilizationImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'status': _$CivilizationStatusEnumMap[instance.status],
      'foundedDate': instance.foundedDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'territories': instance.territories,
      'achievements': instance.achievements,
      'technologies': instance.technologies,
      'government': instance.government,
      'culture': instance.culture,
      'customAttributes': instance.customAttributes,
    };

const _$CivilizationStatusEnumMap = {
  CivilizationStatus.thriving: 'thriving',
  CivilizationStatus.stable: 'stable',
  CivilizationStatus.declining: 'declining',
  CivilizationStatus.extinct: 'extinct',
  CivilizationStatus.unknown: 'unknown',
};

_$WarImpl _$$WarImplFromJson(Map<String, dynamic> json) => _$WarImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String?,
  startDate: json['startDate'] == null
      ? null
      : DateTime.parse(json['startDate'] as String),
  endDate: json['endDate'] == null
      ? null
      : DateTime.parse(json['endDate'] as String),
  participants:
      (json['participants'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  causes:
      (json['causes'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  consequences:
      (json['consequences'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  outcome: json['outcome'] as String?,
  majorBattles:
      (json['majorBattles'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$WarImplToJson(_$WarImpl instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'description': instance.description,
  'startDate': instance.startDate?.toIso8601String(),
  'endDate': instance.endDate?.toIso8601String(),
  'participants': instance.participants,
  'causes': instance.causes,
  'consequences': instance.consequences,
  'outcome': instance.outcome,
  'majorBattles': instance.majorBattles,
  'customAttributes': instance.customAttributes,
};

_$DiscoveryImpl _$$DiscoveryImplFromJson(
  Map<String, dynamic> json,
) => _$DiscoveryImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  date: json['date'] == null ? null : DateTime.parse(json['date'] as String),
  discoverer: json['discoverer'] as String?,
  type: $enumDecodeNullable(_$DiscoveryTypeEnumMap, json['type']),
  impacts:
      (json['impacts'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  location: json['location'] as String?,
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$DiscoveryImplToJson(_$DiscoveryImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'date': instance.date?.toIso8601String(),
      'discoverer': instance.discoverer,
      'type': _$DiscoveryTypeEnumMap[instance.type],
      'impacts': instance.impacts,
      'location': instance.location,
      'customAttributes': instance.customAttributes,
    };

const _$DiscoveryTypeEnumMap = {
  DiscoveryType.scientific: 'scientific',
  DiscoveryType.magical: 'magical',
  DiscoveryType.geographical: 'geographical',
  DiscoveryType.archaeological: 'archaeological',
  DiscoveryType.technological: 'technological',
  DiscoveryType.other: 'other',
};

_$WorldGeographyImpl _$$WorldGeographyImplFromJson(Map<String, dynamic> json) =>
    _$WorldGeographyImpl(
      continents:
          (json['continents'] as List<dynamic>?)
              ?.map((e) => Continent.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      oceans:
          (json['oceans'] as List<dynamic>?)
              ?.map((e) => Ocean.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      mountainRanges:
          (json['mountainRanges'] as List<dynamic>?)
              ?.map((e) => MountainRange.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      rivers:
          (json['rivers'] as List<dynamic>?)
              ?.map((e) => River.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      deserts:
          (json['deserts'] as List<dynamic>?)
              ?.map((e) => Desert.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      forests:
          (json['forests'] as List<dynamic>?)
              ?.map((e) => Forest.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      worldShape: json['worldShape'] as String?,
      size: json['size'] as String?,
      customGeography:
          json['customGeography'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$WorldGeographyImplToJson(
  _$WorldGeographyImpl instance,
) => <String, dynamic>{
  'continents': instance.continents,
  'oceans': instance.oceans,
  'mountainRanges': instance.mountainRanges,
  'rivers': instance.rivers,
  'deserts': instance.deserts,
  'forests': instance.forests,
  'worldShape': instance.worldShape,
  'size': instance.size,
  'customGeography': instance.customGeography,
};

_$ContinentImpl _$$ContinentImplFromJson(Map<String, dynamic> json) =>
    _$ContinentImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      area: (json['area'] as num?)?.toDouble(),
      countries:
          (json['countries'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      majorFeatures:
          (json['majorFeatures'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      climate: json['climate'] as String?,
      customAttributes:
          json['customAttributes'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$ContinentImplToJson(_$ContinentImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'area': instance.area,
      'countries': instance.countries,
      'majorFeatures': instance.majorFeatures,
      'climate': instance.climate,
      'customAttributes': instance.customAttributes,
    };

_$OceanImpl _$$OceanImplFromJson(Map<String, dynamic> json) => _$OceanImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String?,
  area: (json['area'] as num?)?.toDouble(),
  averageDepth: (json['averageDepth'] as num?)?.toDouble(),
  borderingContinents:
      (json['borderingContinents'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  islands:
      (json['islands'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  dangers:
      (json['dangers'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$OceanImplToJson(_$OceanImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'area': instance.area,
      'averageDepth': instance.averageDepth,
      'borderingContinents': instance.borderingContinents,
      'islands': instance.islands,
      'dangers': instance.dangers,
      'customAttributes': instance.customAttributes,
    };

_$MountainRangeImpl _$$MountainRangeImplFromJson(
  Map<String, dynamic> json,
) => _$MountainRangeImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String?,
  length: (json['length'] as num?)?.toDouble(),
  highestPeak: (json['highestPeak'] as num?)?.toDouble(),
  countries:
      (json['countries'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  resources:
      (json['resources'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  dangers:
      (json['dangers'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$MountainRangeImplToJson(_$MountainRangeImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'length': instance.length,
      'highestPeak': instance.highestPeak,
      'countries': instance.countries,
      'resources': instance.resources,
      'dangers': instance.dangers,
      'customAttributes': instance.customAttributes,
    };

_$RiverImpl _$$RiverImplFromJson(Map<String, dynamic> json) => _$RiverImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String?,
  length: (json['length'] as num?)?.toDouble(),
  source: json['source'] as String?,
  mouth: json['mouth'] as String?,
  cities:
      (json['cities'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  tributaries:
      (json['tributaries'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$RiverImplToJson(_$RiverImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'length': instance.length,
      'source': instance.source,
      'mouth': instance.mouth,
      'cities': instance.cities,
      'tributaries': instance.tributaries,
      'customAttributes': instance.customAttributes,
    };

_$DesertImpl _$$DesertImplFromJson(Map<String, dynamic> json) => _$DesertImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String?,
  area: (json['area'] as num?)?.toDouble(),
  type: $enumDecodeNullable(_$DesertTypeEnumMap, json['type']),
  oases:
      (json['oases'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  dangers:
      (json['dangers'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  resources:
      (json['resources'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$DesertImplToJson(_$DesertImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'area': instance.area,
      'type': _$DesertTypeEnumMap[instance.type],
      'oases': instance.oases,
      'dangers': instance.dangers,
      'resources': instance.resources,
      'customAttributes': instance.customAttributes,
    };

const _$DesertTypeEnumMap = {
  DesertType.hot: 'hot',
  DesertType.cold: 'cold',
  DesertType.coastal: 'coastal',
  DesertType.semiarid: 'semiarid',
};

_$ForestImpl _$$ForestImplFromJson(Map<String, dynamic> json) => _$ForestImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String?,
  area: (json['area'] as num?)?.toDouble(),
  type: $enumDecodeNullable(_$ForestTypeEnumMap, json['type']),
  wildlife:
      (json['wildlife'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  resources:
      (json['resources'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  dangers:
      (json['dangers'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$ForestImplToJson(_$ForestImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'area': instance.area,
      'type': _$ForestTypeEnumMap[instance.type],
      'wildlife': instance.wildlife,
      'resources': instance.resources,
      'dangers': instance.dangers,
      'customAttributes': instance.customAttributes,
    };

const _$ForestTypeEnumMap = {
  ForestType.tropical: 'tropical',
  ForestType.temperate: 'temperate',
  ForestType.boreal: 'boreal',
  ForestType.magical: 'magical',
};

_$WorldSocietyImpl _$$WorldSocietyImplFromJson(Map<String, dynamic> json) =>
    _$WorldSocietyImpl(
      socialClasses:
          (json['socialClasses'] as List<dynamic>?)
              ?.map((e) => SocialClass.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      institutions:
          (json['institutions'] as List<dynamic>?)
              ?.map((e) => Institution.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      norms:
          (json['norms'] as List<dynamic>?)
              ?.map((e) => SocialNorm.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      values:
          (json['values'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      familyStructure: json['familyStructure'] as String?,
      educationSystem: json['educationSystem'] as String?,
      customSociety: json['customSociety'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$WorldSocietyImplToJson(_$WorldSocietyImpl instance) =>
    <String, dynamic>{
      'socialClasses': instance.socialClasses,
      'institutions': instance.institutions,
      'norms': instance.norms,
      'values': instance.values,
      'familyStructure': instance.familyStructure,
      'educationSystem': instance.educationSystem,
      'customSociety': instance.customSociety,
    };

_$SocialClassImpl _$$SocialClassImplFromJson(Map<String, dynamic> json) =>
    _$SocialClassImpl(
      name: json['name'] as String,
      description: json['description'] as String?,
      rank: (json['rank'] as num?)?.toInt(),
      privileges:
          (json['privileges'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      responsibilities:
          (json['responsibilities'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      mobility: json['mobility'] as String?,
      customAttributes:
          json['customAttributes'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$SocialClassImplToJson(_$SocialClassImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'rank': instance.rank,
      'privileges': instance.privileges,
      'responsibilities': instance.responsibilities,
      'mobility': instance.mobility,
      'customAttributes': instance.customAttributes,
    };

_$InstitutionImpl _$$InstitutionImplFromJson(Map<String, dynamic> json) =>
    _$InstitutionImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      type: $enumDecodeNullable(_$InstitutionTypeEnumMap, json['type']),
      purpose: json['purpose'] as String?,
      functions:
          (json['functions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      structure: json['structure'] as String?,
      customAttributes:
          json['customAttributes'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$InstitutionImplToJson(_$InstitutionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$InstitutionTypeEnumMap[instance.type],
      'purpose': instance.purpose,
      'functions': instance.functions,
      'structure': instance.structure,
      'customAttributes': instance.customAttributes,
    };

const _$InstitutionTypeEnumMap = {
  InstitutionType.government: 'government',
  InstitutionType.religious: 'religious',
  InstitutionType.educational: 'educational',
  InstitutionType.military: 'military',
  InstitutionType.economic: 'economic',
  InstitutionType.social: 'social',
  InstitutionType.other: 'other',
};

_$SocialNormImpl _$$SocialNormImplFromJson(Map<String, dynamic> json) =>
    _$SocialNormImpl(
      name: json['name'] as String,
      description: json['description'] as String,
      type: $enumDecodeNullable(_$NormTypeEnumMap, json['type']),
      strength: $enumDecodeNullable(_$NormStrengthEnumMap, json['strength']),
      violations:
          (json['violations'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      consequences:
          (json['consequences'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      customAttributes:
          json['customAttributes'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$SocialNormImplToJson(_$SocialNormImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'type': _$NormTypeEnumMap[instance.type],
      'strength': _$NormStrengthEnumMap[instance.strength],
      'violations': instance.violations,
      'consequences': instance.consequences,
      'customAttributes': instance.customAttributes,
    };

const _$NormTypeEnumMap = {
  NormType.moral: 'moral',
  NormType.legal: 'legal',
  NormType.cultural: 'cultural',
  NormType.religious: 'religious',
  NormType.etiquette: 'etiquette',
};

const _$NormStrengthEnumMap = {
  NormStrength.weak: 'weak',
  NormStrength.moderate: 'moderate',
  NormStrength.strong: 'strong',
  NormStrength.absolute: 'absolute',
};

_$WorldEconomyImpl _$$WorldEconomyImplFromJson(Map<String, dynamic> json) =>
    _$WorldEconomyImpl(
      systems:
          (json['systems'] as List<dynamic>?)
              ?.map((e) => EconomicSystem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      tradeNetworks:
          (json['tradeNetworks'] as List<dynamic>?)
              ?.map((e) => TradeNetwork.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      resources:
          (json['resources'] as List<dynamic>?)
              ?.map((e) => Resource.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      industries:
          (json['industries'] as List<dynamic>?)
              ?.map((e) => Industry.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      primaryCurrency: json['primaryCurrency'] as String?,
      customEconomy: json['customEconomy'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$WorldEconomyImplToJson(_$WorldEconomyImpl instance) =>
    <String, dynamic>{
      'systems': instance.systems,
      'tradeNetworks': instance.tradeNetworks,
      'resources': instance.resources,
      'industries': instance.industries,
      'primaryCurrency': instance.primaryCurrency,
      'customEconomy': instance.customEconomy,
    };

_$EconomicSystemImpl _$$EconomicSystemImplFromJson(Map<String, dynamic> json) =>
    _$EconomicSystemImpl(
      name: json['name'] as String,
      description: json['description'] as String?,
      type: $enumDecodeNullable(_$EconomicSystemTypeEnumMap, json['type']),
      characteristics:
          (json['characteristics'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      advantages:
          (json['advantages'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      disadvantages:
          (json['disadvantages'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      customAttributes:
          json['customAttributes'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$EconomicSystemImplToJson(
  _$EconomicSystemImpl instance,
) => <String, dynamic>{
  'name': instance.name,
  'description': instance.description,
  'type': _$EconomicSystemTypeEnumMap[instance.type],
  'characteristics': instance.characteristics,
  'advantages': instance.advantages,
  'disadvantages': instance.disadvantages,
  'customAttributes': instance.customAttributes,
};

const _$EconomicSystemTypeEnumMap = {
  EconomicSystemType.feudal: 'feudal',
  EconomicSystemType.capitalist: 'capitalist',
  EconomicSystemType.socialist: 'socialist',
  EconomicSystemType.barter: 'barter',
  EconomicSystemType.gift: 'gift',
  EconomicSystemType.mixed: 'mixed',
  EconomicSystemType.other: 'other',
};

_$TradeNetworkImpl _$$TradeNetworkImplFromJson(Map<String, dynamic> json) =>
    _$TradeNetworkImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      participants:
          (json['participants'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      tradedGoods:
          (json['tradedGoods'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      routes:
          (json['routes'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      customAttributes:
          json['customAttributes'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$TradeNetworkImplToJson(_$TradeNetworkImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'participants': instance.participants,
      'tradedGoods': instance.tradedGoods,
      'routes': instance.routes,
      'customAttributes': instance.customAttributes,
    };

_$ResourceImpl _$$ResourceImplFromJson(Map<String, dynamic> json) =>
    _$ResourceImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      type: $enumDecodeNullable(_$ResourceTypeEnumMap, json['type']),
      rarity: $enumDecodeNullable(_$ResourceRarityEnumMap, json['rarity']),
      locations:
          (json['locations'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      uses:
          (json['uses'] as List<dynamic>?)?.map((e) => e as String).toList() ??
          const [],
      extractionMethod: json['extractionMethod'] as String?,
      customAttributes:
          json['customAttributes'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$ResourceImplToJson(_$ResourceImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$ResourceTypeEnumMap[instance.type],
      'rarity': _$ResourceRarityEnumMap[instance.rarity],
      'locations': instance.locations,
      'uses': instance.uses,
      'extractionMethod': instance.extractionMethod,
      'customAttributes': instance.customAttributes,
    };

const _$ResourceTypeEnumMap = {
  ResourceType.mineral: 'mineral',
  ResourceType.biological: 'biological',
  ResourceType.energy: 'energy',
  ResourceType.magical: 'magical',
  ResourceType.technological: 'technological',
  ResourceType.other: 'other',
};

const _$ResourceRarityEnumMap = {
  ResourceRarity.abundant: 'abundant',
  ResourceRarity.common: 'common',
  ResourceRarity.uncommon: 'uncommon',
  ResourceRarity.rare: 'rare',
  ResourceRarity.legendary: 'legendary',
};

_$IndustryImpl _$$IndustryImplFromJson(
  Map<String, dynamic> json,
) => _$IndustryImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String?,
  type: $enumDecodeNullable(_$IndustryTypeEnumMap, json['type']),
  products:
      (json['products'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  requiredResources:
      (json['requiredResources'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  locations:
      (json['locations'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$IndustryImplToJson(_$IndustryImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$IndustryTypeEnumMap[instance.type],
      'products': instance.products,
      'requiredResources': instance.requiredResources,
      'locations': instance.locations,
      'customAttributes': instance.customAttributes,
    };

const _$IndustryTypeEnumMap = {
  IndustryType.agriculture: 'agriculture',
  IndustryType.manufacturing: 'manufacturing',
  IndustryType.mining: 'mining',
  IndustryType.services: 'services',
  IndustryType.crafts: 'crafts',
  IndustryType.magic: 'magic',
  IndustryType.other: 'other',
};

_$WorldReligionImpl _$$WorldReligionImplFromJson(Map<String, dynamic> json) =>
    _$WorldReligionImpl(
      religions:
          (json['religions'] as List<dynamic>?)
              ?.map((e) => Religion.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      deities:
          (json['deities'] as List<dynamic>?)
              ?.map((e) => Deity.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      orders:
          (json['orders'] as List<dynamic>?)
              ?.map((e) => ReligiousOrder.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      prophecies:
          (json['prophecies'] as List<dynamic>?)
              ?.map((e) => Prophecy.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      customReligion:
          json['customReligion'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$WorldReligionImplToJson(_$WorldReligionImpl instance) =>
    <String, dynamic>{
      'religions': instance.religions,
      'deities': instance.deities,
      'orders': instance.orders,
      'prophecies': instance.prophecies,
      'customReligion': instance.customReligion,
    };

_$ReligionImpl _$$ReligionImplFromJson(
  Map<String, dynamic> json,
) => _$ReligionImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String?,
  type: $enumDecodeNullable(_$ReligionTypeEnumMap, json['type']),
  beliefs:
      (json['beliefs'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  practices:
      (json['practices'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  deityIds:
      (json['deityIds'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  holyTexts:
      (json['holyTexts'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  holyPlaces:
      (json['holyPlaces'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  afterlife: json['afterlife'] as String?,
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$ReligionImplToJson(_$ReligionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$ReligionTypeEnumMap[instance.type],
      'beliefs': instance.beliefs,
      'practices': instance.practices,
      'deityIds': instance.deityIds,
      'holyTexts': instance.holyTexts,
      'holyPlaces': instance.holyPlaces,
      'afterlife': instance.afterlife,
      'customAttributes': instance.customAttributes,
    };

const _$ReligionTypeEnumMap = {
  ReligionType.monotheistic: 'monotheistic',
  ReligionType.polytheistic: 'polytheistic',
  ReligionType.pantheistic: 'pantheistic',
  ReligionType.animistic: 'animistic',
  ReligionType.philosophical: 'philosophical',
  ReligionType.other: 'other',
};

_$DeityImpl _$$DeityImplFromJson(Map<String, dynamic> json) => _$DeityImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String?,
  domains:
      (json['domains'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  symbols:
      (json['symbols'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  alignment: json['alignment'] as String?,
  power: $enumDecodeNullable(_$DeityPowerEnumMap, json['power']),
  followers:
      (json['followers'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  enemies:
      (json['enemies'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$DeityImplToJson(_$DeityImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'domains': instance.domains,
      'symbols': instance.symbols,
      'alignment': instance.alignment,
      'power': _$DeityPowerEnumMap[instance.power],
      'followers': instance.followers,
      'enemies': instance.enemies,
      'customAttributes': instance.customAttributes,
    };

const _$DeityPowerEnumMap = {
  DeityPower.lesser: 'lesser',
  DeityPower.intermediate: 'intermediate',
  DeityPower.greater: 'greater',
  DeityPower.overdeity: 'overdeity',
};

_$ReligiousOrderImpl _$$ReligiousOrderImplFromJson(Map<String, dynamic> json) =>
    _$ReligiousOrderImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      religionId: json['religionId'] as String?,
      type: $enumDecodeNullable(_$OrderTypeEnumMap, json['type']),
      goals:
          (json['goals'] as List<dynamic>?)?.map((e) => e as String).toList() ??
          const [],
      practices:
          (json['practices'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      hierarchy: json['hierarchy'] as String?,
      customAttributes:
          json['customAttributes'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$ReligiousOrderImplToJson(
  _$ReligiousOrderImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'description': instance.description,
  'religionId': instance.religionId,
  'type': _$OrderTypeEnumMap[instance.type],
  'goals': instance.goals,
  'practices': instance.practices,
  'hierarchy': instance.hierarchy,
  'customAttributes': instance.customAttributes,
};

const _$OrderTypeEnumMap = {
  OrderType.monastic: 'monastic',
  OrderType.militant: 'militant',
  OrderType.scholarly: 'scholarly',
  OrderType.missionary: 'missionary',
  OrderType.mystical: 'mystical',
  OrderType.other: 'other',
};

_$ProphecyImpl _$$ProphecyImplFromJson(Map<String, dynamic> json) =>
    _$ProphecyImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      text: json['text'] as String,
      interpretation: json['interpretation'] as String?,
      source: json['source'] as String?,
      status: $enumDecodeNullable(_$ProphecyStatusEnumMap, json['status']),
      conditions:
          (json['conditions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      prophesiedDate: json['prophesiedDate'] == null
          ? null
          : DateTime.parse(json['prophesiedDate'] as String),
      customAttributes:
          json['customAttributes'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$ProphecyImplToJson(_$ProphecyImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'text': instance.text,
      'interpretation': instance.interpretation,
      'source': instance.source,
      'status': _$ProphecyStatusEnumMap[instance.status],
      'conditions': instance.conditions,
      'prophesiedDate': instance.prophesiedDate?.toIso8601String(),
      'customAttributes': instance.customAttributes,
    };

const _$ProphecyStatusEnumMap = {
  ProphecyStatus.unfulfilled: 'unfulfilled',
  ProphecyStatus.inProgress: 'inProgress',
  ProphecyStatus.fulfilled: 'fulfilled',
  ProphecyStatus.failed: 'failed',
  ProphecyStatus.unknown: 'unknown',
};

_$WorldRuleImpl _$$WorldRuleImplFromJson(Map<String, dynamic> json) =>
    _$WorldRuleImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$WorldRuleTypeEnumMap, json['type']),
      severity: $enumDecode(_$WorldRuleSeverityEnumMap, json['severity']),
      effects:
          (json['effects'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      exceptions:
          (json['exceptions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      parameters: json['parameters'] as Map<String, dynamic>? ?? const {},
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$WorldRuleImplToJson(_$WorldRuleImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$WorldRuleTypeEnumMap[instance.type]!,
      'severity': _$WorldRuleSeverityEnumMap[instance.severity]!,
      'effects': instance.effects,
      'exceptions': instance.exceptions,
      'parameters': instance.parameters,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$WorldRuleTypeEnumMap = {
  WorldRuleType.physical: 'physical',
  WorldRuleType.magical: 'magical',
  WorldRuleType.social: 'social',
  WorldRuleType.economic: 'economic',
  WorldRuleType.political: 'political',
  WorldRuleType.religious: 'religious',
  WorldRuleType.other: 'other',
};

const _$WorldRuleSeverityEnumMap = {
  WorldRuleSeverity.guideline: 'guideline',
  WorldRuleSeverity.rule: 'rule',
  WorldRuleSeverity.law: 'law',
  WorldRuleSeverity.absolute: 'absolute',
};

_$WorldEventImpl _$$WorldEventImplFromJson(Map<String, dynamic> json) =>
    _$WorldEventImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      date: DateTime.parse(json['date'] as String),
      type: $enumDecodeNullable(_$WorldEventTypeEnumMap, json['type']),
      scope: $enumDecodeNullable(_$WorldEventScopeEnumMap, json['scope']),
      consequences:
          (json['consequences'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      involvedEntities:
          (json['involvedEntities'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      customAttributes:
          json['customAttributes'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$WorldEventImplToJson(_$WorldEventImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'date': instance.date.toIso8601String(),
      'type': _$WorldEventTypeEnumMap[instance.type],
      'scope': _$WorldEventScopeEnumMap[instance.scope],
      'consequences': instance.consequences,
      'involvedEntities': instance.involvedEntities,
      'customAttributes': instance.customAttributes,
    };

const _$WorldEventTypeEnumMap = {
  WorldEventType.natural: 'natural',
  WorldEventType.magical: 'magical',
  WorldEventType.political: 'political',
  WorldEventType.social: 'social',
  WorldEventType.economic: 'economic',
  WorldEventType.religious: 'religious',
  WorldEventType.technological: 'technological',
  WorldEventType.other: 'other',
};

const _$WorldEventScopeEnumMap = {
  WorldEventScope.local: 'local',
  WorldEventScope.regional: 'regional',
  WorldEventScope.national: 'national',
  WorldEventScope.continental: 'continental',
  WorldEventScope.global: 'global',
  WorldEventScope.cosmic: 'cosmic',
};

_$SpeciesImpl _$$SpeciesImplFromJson(
  Map<String, dynamic> json,
) => _$SpeciesImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String?,
  type: $enumDecodeNullable(_$SpeciesTypeEnumMap, json['type']),
  characteristics:
      (json['characteristics'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  abilities:
      (json['abilities'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  habitats:
      (json['habitats'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  lifespan: json['lifespan'] as String?,
  intelligence: json['intelligence'] as String?,
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$SpeciesImplToJson(_$SpeciesImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$SpeciesTypeEnumMap[instance.type],
      'characteristics': instance.characteristics,
      'abilities': instance.abilities,
      'habitats': instance.habitats,
      'lifespan': instance.lifespan,
      'intelligence': instance.intelligence,
      'customAttributes': instance.customAttributes,
    };

const _$SpeciesTypeEnumMap = {
  SpeciesType.humanoid: 'humanoid',
  SpeciesType.beast: 'beast',
  SpeciesType.magical: 'magical',
  SpeciesType.elemental: 'elemental',
  SpeciesType.undead: 'undead',
  SpeciesType.construct: 'construct',
  SpeciesType.plant: 'plant',
  SpeciesType.other: 'other',
};

_$LanguageImpl _$$LanguageImplFromJson(
  Map<String, dynamic> json,
) => _$LanguageImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String?,
  type: $enumDecodeNullable(_$LanguageTypeEnumMap, json['type']),
  speakers:
      (json['speakers'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  writingSystem: json['writingSystem'] as String?,
  dialects:
      (json['dialects'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  origin: json['origin'] as String?,
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$LanguageImplToJson(_$LanguageImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$LanguageTypeEnumMap[instance.type],
      'speakers': instance.speakers,
      'writingSystem': instance.writingSystem,
      'dialects': instance.dialects,
      'origin': instance.origin,
      'customAttributes': instance.customAttributes,
    };

const _$LanguageTypeEnumMap = {
  LanguageType.spoken: 'spoken',
  LanguageType.written: 'written',
  LanguageType.sign: 'sign',
  LanguageType.telepathic: 'telepathic',
  LanguageType.magical: 'magical',
  LanguageType.other: 'other',
};

_$CalendarImpl _$$CalendarImplFromJson(Map<String, dynamic> json) =>
    _$CalendarImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      daysPerYear: (json['daysPerYear'] as num?)?.toInt(),
      monthsPerYear: (json['monthsPerYear'] as num?)?.toInt(),
      daysPerMonth: (json['daysPerMonth'] as num?)?.toInt(),
      monthNames:
          (json['monthNames'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      dayNames:
          (json['dayNames'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      holidays:
          (json['holidays'] as List<dynamic>?)
              ?.map((e) => Holiday.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      customAttributes:
          json['customAttributes'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$CalendarImplToJson(_$CalendarImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'daysPerYear': instance.daysPerYear,
      'monthsPerYear': instance.monthsPerYear,
      'daysPerMonth': instance.daysPerMonth,
      'monthNames': instance.monthNames,
      'dayNames': instance.dayNames,
      'holidays': instance.holidays,
      'customAttributes': instance.customAttributes,
    };

_$HolidayImpl _$$HolidayImplFromJson(Map<String, dynamic> json) =>
    _$HolidayImpl(
      name: json['name'] as String,
      description: json['description'] as String?,
      date: json['date'] as String?,
      type: $enumDecodeNullable(_$HolidayTypeEnumMap, json['type']),
      traditions:
          (json['traditions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      customAttributes:
          json['customAttributes'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$HolidayImplToJson(_$HolidayImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'date': instance.date,
      'type': _$HolidayTypeEnumMap[instance.type],
      'traditions': instance.traditions,
      'customAttributes': instance.customAttributes,
    };

const _$HolidayTypeEnumMap = {
  HolidayType.religious: 'religious',
  HolidayType.seasonal: 'seasonal',
  HolidayType.historical: 'historical',
  HolidayType.cultural: 'cultural',
  HolidayType.personal: 'personal',
  HolidayType.other: 'other',
};

_$CurrencyImpl _$$CurrencyImplFromJson(Map<String, dynamic> json) =>
    _$CurrencyImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      symbol: json['symbol'] as String?,
      denominations:
          (json['denominations'] as List<dynamic>?)
              ?.map((e) => Denomination.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      acceptedRegions:
          (json['acceptedRegions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      backingSystem: json['backingSystem'] as String?,
      customAttributes:
          json['customAttributes'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$CurrencyImplToJson(_$CurrencyImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'symbol': instance.symbol,
      'denominations': instance.denominations,
      'acceptedRegions': instance.acceptedRegions,
      'backingSystem': instance.backingSystem,
      'customAttributes': instance.customAttributes,
    };

_$DenominationImpl _$$DenominationImplFromJson(Map<String, dynamic> json) =>
    _$DenominationImpl(
      name: json['name'] as String,
      value: (json['value'] as num).toDouble(),
      material: json['material'] as String?,
      description: json['description'] as String?,
      customAttributes:
          json['customAttributes'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$DenominationImplToJson(_$DenominationImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'value': instance.value,
      'material': instance.material,
      'description': instance.description,
      'customAttributes': instance.customAttributes,
    };
