import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:bamboofall_app/features/ai_integration/domain/entities/ai_model.dart';
import 'package:bamboofall_app/features/ai_integration/domain/repositories/llm_repository.dart';
import 'package:bamboofall_app/features/ai_integration/domain/usecases/generate_content.dart';

import 'generate_content_test.mocks.dart';

@GenerateMocks([LLMRepository])
void main() {
  late GenerateContentUseCase useCase;
  late MockLLMRepository mockRepository;

  setUp(() {
    mockRepository = MockLLMRepository();
    useCase = GenerateContentUseCase(mockRepository);
  });

  group('GenerateContentUseCase', () {
    const testModelId = 'gpt-4';
    const testPrompt = 'Write a story about a dragon';
    const testMaxTokens = 1000;
    const testTemperature = 0.7;

    test('should generate text content successfully', () async {
      // Arrange
      final expectedResponse = AIResponse(
        id: 'test-id',
        content: 'Once upon a time, there was a dragon...',
        model: testModelId,
        usage: const AIUsage(
          promptTokens: 10,
          completionTokens: 50,
          totalTokens: 60,
        ),
        finishReason: 'stop',
        createdAt: DateTime.now(),
      );

      when(mockRepository.completion(any)).thenAnswer((_) async => expectedResponse);

      // Act
      final result = await useCase.generateText(
        modelId: testModelId,
        prompt: testPrompt,
        maxTokens: testMaxTokens,
        temperature: testTemperature,
      );

      // Assert
      expect(result.isSuccess, true);
      expect(result.data?.content, expectedResponse.content);
      verify(mockRepository.completion(any)).called(1);
    });

    test('should handle generation failure', () async {
      // Arrange
      when(mockRepository.completion(any)).thenThrow(Exception('API Error'));

      // Act
      final result = await useCase.generateText(
        modelId: testModelId,
        prompt: testPrompt,
      );

      // Assert
      expect(result.isSuccess, false);
      expect(result.error, isNotNull);
      expect(result.error!.type, GenerateContentErrorType.apiError);
    });

    test('should validate input parameters', () async {
      // Act & Assert
      expect(
        () => useCase.generateText(modelId: '', prompt: testPrompt),
        throwsA(isA<ArgumentError>()),
      );

      expect(
        () => useCase.generateText(modelId: testModelId, prompt: ''),
        throwsA(isA<ArgumentError>()),
      );
    });

    test('should generate story outline successfully', () async {
      // Arrange
      final expectedResponse = AIResponse(
        id: 'outline-id',
        content: '1. Introduction\n2. Rising Action\n3. Climax\n4. Resolution',
        model: testModelId,
        usage: const AIUsage(
          promptTokens: 20,
          completionTokens: 100,
          totalTokens: 120,
        ),
        finishReason: 'stop',
        createdAt: DateTime.now(),
      );

      when(mockRepository.completion(any)).thenAnswer((_) async => expectedResponse);

      // Act
      final result = await useCase.generateOutline(
        modelId: testModelId,
        genre: 'Fantasy',
        theme: 'Good vs Evil',
        length: 'Novel',
      );

      // Assert
      expect(result.isSuccess, true);
      expect(result.data?.content, contains('Introduction'));
      expect(result.data?.content, contains('Climax'));
      verify(mockRepository.completion(any)).called(1);
    });

    test('should generate character description successfully', () async {
      // Arrange
      final expectedResponse = AIResponse(
        id: 'character-id',
        content: 'A brave knight with golden hair and piercing blue eyes...',
        model: testModelId,
        usage: const AIUsage(
          promptTokens: 15,
          completionTokens: 80,
          totalTokens: 95,
        ),
        finishReason: 'stop',
        createdAt: DateTime.now(),
      );

      when(mockRepository.completion(any)).thenAnswer((_) async => expectedResponse);

      // Act
      final result = await useCase.generateCharacterDescription(
        modelId: testModelId,
        characterName: 'Sir Galahad',
        characterType: 'Hero',
        context: 'Medieval fantasy setting',
      );

      // Assert
      expect(result.isSuccess, true);
      expect(result.data?.content, contains('knight'));
      verify(mockRepository.completion(any)).called(1);
    });

    test('should handle streaming content generation', () async {
      // Arrange
      final streamResponses = [
        AIResponse(
          id: 'stream-1',
          content: 'Once',
          model: testModelId,
          usage: const AIUsage(promptTokens: 5, completionTokens: 1, totalTokens: 6),
          finishReason: null,
          createdAt: DateTime.now(),
        ),
        AIResponse(
          id: 'stream-2',
          content: ' upon',
          model: testModelId,
          usage: const AIUsage(promptTokens: 5, completionTokens: 2, totalTokens: 7),
          finishReason: null,
          createdAt: DateTime.now(),
        ),
        AIResponse(
          id: 'stream-3',
          content: ' a time',
          model: testModelId,
          usage: const AIUsage(promptTokens: 5, completionTokens: 3, totalTokens: 8),
          finishReason: 'stop',
          createdAt: DateTime.now(),
        ),
      ];

      when(mockRepository.completionStream(any))
          .thenAnswer((_) => Stream.fromIterable(streamResponses));

      // Act
      final resultStream = useCase.generateTextStream(
        modelId: testModelId,
        prompt: testPrompt,
      );

      // Assert
      final results = await resultStream.toList();
      expect(results.length, 3);
      expect(results[0].data?.content, 'Once');
      expect(results[1].data?.content, ' upon');
      expect(results[2].data?.content, ' a time');
      verify(mockRepository.completionStream(any)).called(1);
    });
  });
}