import 'package:flutter/material.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

/// 快捷键设置面板
class ShortcutsSettingsPanel extends StatelessWidget {
  final Map<String, String> shortcuts;

  const ShortcutsSettingsPanel({
    super.key,
    required this.shortcuts,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '快捷键设置',
            style: fluent.FluentTheme.of(context).typography.title,
          ),
          const SizedBox(height: 16),
          
          // 快捷键设置选项
          fluent.Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '键盘快捷键',
                    style: fluent.FluentTheme.of(context).typography.subtitle,
                  ),
                  const SizedBox(height: 8),
                  const Text('快捷键设置功能正在开发中...'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}