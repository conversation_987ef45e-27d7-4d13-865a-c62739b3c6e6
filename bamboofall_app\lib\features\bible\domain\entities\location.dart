import 'package:freezed_annotation/freezed_annotation.dart';

part 'location.freezed.dart';
part 'location.g.dart';

/// 地点实体
@freezed
class Location with _$Location {
  const factory Location({
    required String id,
    required String name,
    @Default([]) List<String> aliases, // 别名
    String? description,
    required LocationType type,
    required LocationStatus status,
    LocationGeography? geography,
    LocationClimate? climate,
    LocationCulture? culture,
    LocationEconomy? economy,
    LocationPolitics? politics,
    @Default([]) List<String> parentLocationIds, // 父级地点ID
    @Default([]) List<String> childLocationIds, // 子级地点ID
    @Default([]) List<String> connectedLocationIds, // 连接的地点ID
    @Default([]) List<LocationFeature> features,
    @Default([]) List<String> residentCharacterIds, // 居民角色ID
    @Default([]) List<String> visitingCharacterIds, // 访问角色ID
    @Default([]) List<LocationEvent> events,
    @Default([]) List<String> availableItemIds, // 可获得的物品ID
    @Default([]) List<LocationSecret> secrets,
    String? imageUrl,
    @Default({}) Map<String, dynamic> customAttributes,
    @Default({}) Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _Location;

  factory Location.fromJson(Map<String, dynamic> json) => _$LocationFromJson(json);
}

/// 地点类型
enum LocationType {
  continent('大陆'),
  country('国家'),
  region('地区'),
  city('城市'),
  town('城镇'),
  village('村庄'),
  district('区域'),
  building('建筑'),
  room('房间'),
  natural('自然地点'),
  dungeon('地下城'),
  fortress('要塞'),
  temple('神庙'),
  market('市场'),
  tavern('酒馆'),
  forest('森林'),
  mountain('山脉'),
  river('河流'),
  ocean('海洋'),
  desert('沙漠'),
  other('其他');

  const LocationType(this.displayName);
  final String displayName;
}

/// 地点状态
enum LocationStatus {
  active('活跃'),
  abandoned('废弃'),
  destroyed('被毁'),
  hidden('隐藏'),
  restricted('限制进入'),
  unknown('未知');

  const LocationStatus(this.displayName);
  final String displayName;
}

/// 地理信息
@freezed
class LocationGeography with _$LocationGeography {
  const factory LocationGeography({
    LocationCoordinates? coordinates,
    String? terrain, // 地形
    double? elevation, // 海拔
    double? area, // 面积
    @Default([]) List<String> naturalFeatures, // 自然特征
    @Default([]) List<String> landmarks, // 地标
    String? waterSources, // 水源
    String? vegetation, // 植被
    @Default([]) List<String> naturalResources, // 自然资源
    @Default({}) Map<String, dynamic> customGeography,
  }) = _LocationGeography;

  factory LocationGeography.fromJson(Map<String, dynamic> json) => _$LocationGeographyFromJson(json);
}

/// 坐标
@freezed
class LocationCoordinates with _$LocationCoordinates {
  const factory LocationCoordinates({
    required double latitude, // 纬度
    required double longitude, // 经度
    String? coordinateSystem, // 坐标系统
    @Default({}) Map<String, dynamic> customCoordinates,
  }) = _LocationCoordinates;

  factory LocationCoordinates.fromJson(Map<String, dynamic> json) => _$LocationCoordinatesFromJson(json);
}

/// 气候信息
@freezed
class LocationClimate with _$LocationClimate {
  const factory LocationClimate({
    String? climateType, // 气候类型
    @Default([]) List<Season> seasons,
    double? averageTemperature, // 平均温度
    double? averageRainfall, // 平均降雨量
    @Default([]) List<String> weatherPatterns, // 天气模式
    @Default([]) List<WeatherEvent> extremeWeather, // 极端天气
    @Default({}) Map<String, dynamic> customClimate,
  }) = _LocationClimate;

  factory LocationClimate.fromJson(Map<String, dynamic> json) => _$LocationClimateFromJson(json);
}

/// 季节
@freezed
class Season with _$Season {
  const factory Season({
    required String name,
    String? description,
    int? durationDays, // 持续天数
    double? averageTemperature,
    double? averageRainfall,
    @Default([]) List<String> characteristics,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _Season;

  factory Season.fromJson(Map<String, dynamic> json) => _$SeasonFromJson(json);
}

/// 天气事件
@freezed
class WeatherEvent with _$WeatherEvent {
  const factory WeatherEvent({
    required String name,
    String? description,
    WeatherEventType? type,
    WeatherEventSeverity? severity,
    int? frequency, // 发生频率 (每年次数)
    int? durationDays, // 持续天数
    @Default([]) List<String> effects,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _WeatherEvent;

  factory WeatherEvent.fromJson(Map<String, dynamic> json) => _$WeatherEventFromJson(json);
}

/// 天气事件类型
enum WeatherEventType {
  storm('风暴'),
  drought('干旱'),
  flood('洪水'),
  blizzard('暴雪'),
  heatwave('热浪'),
  earthquake('地震'),
  volcanic('火山'),
  magical('魔法天气');

  const WeatherEventType(this.displayName);
  final String displayName;
}

/// 天气事件严重程度
enum WeatherEventSeverity {
  mild('轻微'),
  moderate('中等'),
  severe('严重'),
  catastrophic('灾难性');

  const WeatherEventSeverity(this.displayName);
  final String displayName;
}

/// 文化信息
@freezed
class LocationCulture with _$LocationCulture {
  const factory LocationCulture({
    @Default([]) List<String> ethnicGroups, // 民族群体
    @Default([]) List<String> languages, // 语言
    @Default([]) List<String> religions, // 宗教
    @Default([]) List<CulturalTradition> traditions, // 传统
    @Default([]) List<String> festivals, // 节日
    @Default([]) List<String> customs, // 习俗
    String? architecture, // 建筑风格
    String? artStyle, // 艺术风格
    @Default([]) List<String> cuisine, // 美食
    @Default([]) List<String> clothing, // 服装
    @Default({}) Map<String, dynamic> customCulture,
  }) = _LocationCulture;

  factory LocationCulture.fromJson(Map<String, dynamic> json) => _$LocationCultureFromJson(json);
}

/// 文化传统
@freezed
class CulturalTradition with _$CulturalTradition {
  const factory CulturalTradition({
    required String name,
    String? description,
    String? origin,
    TraditionType? type,
    @Default([]) List<String> practices,
    String? significance,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _CulturalTradition;

  factory CulturalTradition.fromJson(Map<String, dynamic> json) => _$CulturalTraditionFromJson(json);
}

/// 传统类型
enum TraditionType {
  religious('宗教'),
  social('社会'),
  ceremonial('仪式'),
  seasonal('季节性'),
  lifecycle('生命周期'),
  occupational('职业'),
  other('其他');

  const TraditionType(this.displayName);
  final String displayName;
}

/// 经济信息
@freezed
class LocationEconomy with _$LocationEconomy {
  const factory LocationEconomy({
    String? economicSystem, // 经济制度
    @Default([]) List<String> primaryIndustries, // 主要产业
    @Default([]) List<String> exports, // 出口商品
    @Default([]) List<String> imports, // 进口商品
    String? currency, // 货币
    EconomicStatus? economicStatus,
    @Default([]) List<TradeRoute> tradeRoutes, // 贸易路线
    @Default([]) List<String> marketplaces, // 市场
    @Default({}) Map<String, dynamic> customEconomy,
  }) = _LocationEconomy;

  factory LocationEconomy.fromJson(Map<String, dynamic> json) => _$LocationEconomyFromJson(json);
}

/// 经济状况
enum EconomicStatus {
  prosperous('繁荣'),
  stable('稳定'),
  declining('衰退'),
  poor('贫困'),
  recovering('恢复中');

  const EconomicStatus(this.displayName);
  final String displayName;
}

/// 贸易路线
@freezed
class TradeRoute with _$TradeRoute {
  const factory TradeRoute({
    required String name,
    String? description,
    @Default([]) List<String> connectedLocationIds,
    @Default([]) List<String> tradedGoods,
    TradeRouteStatus? status,
    @Default([]) List<String> dangers, // 危险
    int? travelTimeDays, // 旅行时间
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _TradeRoute;

  factory TradeRoute.fromJson(Map<String, dynamic> json) => _$TradeRouteFromJson(json);
}

/// 贸易路线状态
enum TradeRouteStatus {
  active('活跃'),
  inactive('不活跃'),
  dangerous('危险'),
  blocked('阻塞'),
  seasonal('季节性');

  const TradeRouteStatus(this.displayName);
  final String displayName;
}

/// 政治信息
@freezed
class LocationPolitics with _$LocationPolitics {
  const factory LocationPolitics({
    String? governmentType, // 政府类型
    @Default([]) List<PoliticalFigure> leaders, // 领导者
    @Default([]) List<PoliticalFaction> factions, // 派系
    @Default([]) List<String> laws, // 法律
    @Default([]) List<String> conflicts, // 冲突
    @Default([]) List<String> alliances, // 联盟
    String? militaryStrength, // 军事力量
    @Default({}) Map<String, dynamic> customPolitics,
  }) = _LocationPolitics;

  factory LocationPolitics.fromJson(Map<String, dynamic> json) => _$LocationPoliticsFromJson(json);
}

/// 政治人物
@freezed
class PoliticalFigure with _$PoliticalFigure {
  const factory PoliticalFigure({
    required String characterId,
    required String position, // 职位
    String? title, // 头衔
    int? influence, // 影响力 1-10
    @Default([]) List<String> responsibilities, // 职责
    DateTime? termStart,
    DateTime? termEnd,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _PoliticalFigure;

  factory PoliticalFigure.fromJson(Map<String, dynamic> json) => _$PoliticalFigureFromJson(json);
}

/// 政治派系
@freezed
class PoliticalFaction with _$PoliticalFaction {
  const factory PoliticalFaction({
    required String id,
    required String name,
    String? description,
    @Default([]) List<String> memberCharacterIds,
    String? ideology, // 意识形态
    int? power, // 权力 1-10
    @Default([]) List<String> goals,
    @Default([]) List<String> enemies,
    @Default([]) List<String> allies,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _PoliticalFaction;

  factory PoliticalFaction.fromJson(Map<String, dynamic> json) => _$PoliticalFactionFromJson(json);
}

/// 地点特征
@freezed
class LocationFeature with _$LocationFeature {
  const factory LocationFeature({
    required String name,
    String? description,
    required FeatureType type,
    FeatureImportance? importance,
    @Default([]) List<String> effects, // 影响
    @Default([]) List<String> requirements, // 访问要求
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _LocationFeature;

  factory LocationFeature.fromJson(Map<String, dynamic> json) => _$LocationFeatureFromJson(json);
}

/// 特征类型
enum FeatureType {
  architectural('建筑'),
  natural('自然'),
  magical('魔法'),
  historical('历史'),
  cultural('文化'),
  defensive('防御'),
  commercial('商业'),
  religious('宗教');

  const FeatureType(this.displayName);
  final String displayName;
}

/// 特征重要性
enum FeatureImportance {
  minor('次要'),
  moderate('中等'),
  major('重要'),
  critical('关键');

  const FeatureImportance(this.displayName);
  final String displayName;
}

/// 地点事件
@freezed
class LocationEvent with _$LocationEvent {
  const factory LocationEvent({
    required String id,
    required String title,
    String? description,
    required DateTime eventDate,
    LocationEventType? type,
    @Default([]) List<String> involvedCharacterIds,
    @Default([]) List<String> consequences, // 后果
    String? chapterId,
    @Default({}) Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _LocationEvent;

  factory LocationEvent.fromJson(Map<String, dynamic> json) => _$LocationEventFromJson(json);
}

/// 地点事件类型
enum LocationEventType {
  battle('战斗'),
  celebration('庆典'),
  disaster('灾难'),
  discovery('发现'),
  construction('建设'),
  destruction('破坏'),
  meeting('会议'),
  ceremony('仪式'),
  other('其他');

  const LocationEventType(this.displayName);
  final String displayName;
}

/// 地点秘密
@freezed
class LocationSecret with _$LocationSecret {
  const factory LocationSecret({
    required String id,
    required String title,
    required String description,
    required SecretType type,
    required SecretSeverity severity,
    @Default([]) List<String> knownByCharacterIds,
    String? discoveryCondition, // 发现条件
    String? consequence, // 发现后果
    bool? isDiscovered,
    DateTime? discoveredAt,
    @Default({}) Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _LocationSecret;

  factory LocationSecret.fromJson(Map<String, dynamic> json) => _$LocationSecretFromJson(json);
}

/// 秘密类型
enum SecretType {
  treasure('宝藏'),
  passage('通道'),
  history('历史'),
  danger('危险'),
  resource('资源'),
  magic('魔法'),
  conspiracy('阴谋'),
  other('其他');

  const SecretType(this.displayName);
  final String displayName;
}

/// 秘密严重程度
enum SecretSeverity {
  minor('轻微'),
  moderate('中等'),
  major('重大'),
  critical('致命');

  const SecretSeverity(this.displayName);
  final String displayName;
}

/// 地点统计
@freezed
class LocationStats with _$LocationStats {
  const factory LocationStats({
    required String locationId,
    int? visitCount, // 访问次数
    int? eventCount, // 事件次数
    @Default([]) List<String> frequentVisitors, // 常客
    @Default([]) List<String> significantEvents, // 重要事件
    DateTime? lastVisit,
    @Default({}) Map<String, int> characterVisitCounts,
    @Default({}) Map<String, dynamic> customStats,
  }) = _LocationStats;

  factory LocationStats.fromJson(Map<String, dynamic> json) => _$LocationStatsFromJson(json);
}