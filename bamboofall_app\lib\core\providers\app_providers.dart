import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../di/injection.dart';
import '../storage/database.dart';
import '../storage/file_storage.dart';
import '../storage/secure_storage.dart';

// Logger Provider
final loggerProvider = Provider<Logger>((ref) {
  return getIt<Logger>();
});

// SharedPreferences Provider
final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  return getIt<SharedPreferences>();
});

// Secure Storage Provider
final secureStorageProvider = Provider<FlutterSecureStorage>((ref) {
  return getIt<FlutterSecureStorage>();
});

// Storage Managers Providers
final localDatabaseProvider = Provider<LocalDatabase>((ref) {
  return getIt<LocalDatabase>();
});

final fileStorageProvider = Provider<FileStorage>((ref) {
  return getIt<FileStorage>();
});

final secureStorageManagerProvider = Provider<SecureStorageManager>((ref) {
  return getIt<SecureStorageManager>();
});

// Theme Mode Provider
final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  return ThemeModeNotifier(prefs);
});

// Theme Mode Notifier
class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  final SharedPreferences _prefs;
  static const String _key = 'theme_mode';

  ThemeModeNotifier(this._prefs) : super(ThemeMode.system) {
    _loadThemeMode();
  }

  void _loadThemeMode() {
    final savedMode = _prefs.getString(_key);
    if (savedMode != null) {
      switch (savedMode) {
        case 'light':
          state = ThemeMode.light;
          break;
        case 'dark':
          state = ThemeMode.dark;
          break;
        default:
          state = ThemeMode.system;
      }
    }
  }

  Future<void> setThemeMode(ThemeMode mode) async {
    state = mode;
    String modeString;
    switch (mode) {
      case ThemeMode.light:
        modeString = 'light';
        break;
      case ThemeMode.dark:
        modeString = 'dark';
        break;
      case ThemeMode.system:
        modeString = 'system';
        break;
    }
    await _prefs.setString(_key, modeString);
  }
}

// Application State Provider
final appStateProvider = StateNotifierProvider<AppStateNotifier, AppState>((ref) {
  final fileStorage = ref.watch(fileStorageProvider);
  return AppStateNotifier(fileStorage);
});

// Application State
class AppState {
  final bool isInitialized;
  final String? currentProjectPath;
  final List<Map<String, dynamic>> recentProjects;

  const AppState({
    this.isInitialized = false,
    this.currentProjectPath,
    this.recentProjects = const [],
  });

  AppState copyWith({
    bool? isInitialized,
    String? currentProjectPath,
    List<Map<String, dynamic>>? recentProjects,
  }) {
    return AppState(
      isInitialized: isInitialized ?? this.isInitialized,
      currentProjectPath: currentProjectPath ?? this.currentProjectPath,
      recentProjects: recentProjects ?? this.recentProjects,
    );
  }
}

// Application State Notifier
class AppStateNotifier extends StateNotifier<AppState> {
  final FileStorage _fileStorage;
  
  AppStateNotifier(this._fileStorage) : super(const AppState()) {
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      // 加载最近项目
      final projects = await _fileStorage.getProjects();
      
      state = state.copyWith(
        isInitialized: true,
        recentProjects: projects,
      );
    } catch (e) {
      // 即使加载失败也要标记为已初始化
      state = state.copyWith(isInitialized: true);
    }
  }

  void setCurrentProject(String? projectPath) {
    state = state.copyWith(currentProjectPath: projectPath);
  }

  Future<void> refreshProjects() async {
    try {
      final projects = await _fileStorage.getProjects();
      state = state.copyWith(recentProjects: projects);
    } catch (e) {
      // 刷新失败时保持当前状态
    }
  }

  Future<void> createProject(String projectName) async {
    try {
      await _fileStorage.createProject(projectName);
      await refreshProjects();
    } catch (e) {
      rethrow;
    }
  }

  Future<void> deleteProject(String projectName) async {
    try {
      await _fileStorage.deleteProject(projectName);
      await refreshProjects();
      
      // 如果删除的是当前项目，清除当前项目路径
      if (state.currentProjectPath?.contains(projectName) == true) {
        state = state.copyWith(currentProjectPath: null);
      }
    } catch (e) {
      rethrow;
    }
  }
}