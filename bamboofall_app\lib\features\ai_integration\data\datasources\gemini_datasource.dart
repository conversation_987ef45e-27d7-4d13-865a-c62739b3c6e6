import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

import '../../domain/entities/ai_model.dart';
import '../../domain/repositories/llm_repository.dart';

/// Gemini数据源
/// 实现Google Gemini API的具体集成
class GeminiDataSource {
  final Dio _dio;
  final Logger _logger;
  
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
  
  GeminiDataSource({
    Dio? dio,
    Logger? logger,
  }) : _dio = dio ?? Dio(),
       _logger = logger ?? Logger() {
    _setupDio();
  }
  
  /// 配置Dio实例
  void _setupDio() {
    _dio.options.baseUrl = _baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 60);
    _dio.options.sendTimeout = const Duration(seconds: 30);
    
    // 添加拦截器
    _dio.interceptors.add(LogInterceptor(
      requestBody: false, // 不记录请求体，避免泄露敏感信息
      responseBody: false, // 不记录响应体，避免日志过长
      logPrint: (obj) => _logger.d(obj),
    ));
    
    // 添加重试拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onError: (error, handler) async {
        if (_shouldRetry(error)) {
          try {
            final response = await _dio.fetch(error.requestOptions);
            handler.resolve(response);
            return;
          } catch (e) {
            // 重试失败，继续抛出原错误
          }
        }
        handler.next(error);
      },
    ));
  }
  
  /// 判断是否应该重试
  bool _shouldRetry(DioException error) {
    if (error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.receiveTimeout ||
        error.type == DioExceptionType.sendTimeout) {
      return true;
    }
    
    if (error.response?.statusCode == 429 || // Rate limit
        error.response?.statusCode == 502 || // Bad gateway
        error.response?.statusCode == 503 || // Service unavailable
        error.response?.statusCode == 504) { // Gateway timeout
      return true;
    }
    
    return false;
  }
  
  /// 设置API密钥
  void setApiKey(String apiKey) {
    // Gemini API使用查询参数传递API密钥
    _dio.options.queryParameters['key'] = apiKey;
  }
  
  /// 生成内容
  Future<AIResponse> generateContent(AIRequest request) async {
    try {
      _logger.d('Sending generate content request to Gemini');
      
      final endpoint = '/models/${request.modelId}:generateContent';
      final requestData = _buildGenerateContentRequest(request);
      final response = await _dio.post(endpoint, data: requestData);
      
      return _parseGenerateContentResponse(response.data, request.requestId);
    } on DioException catch (e) {
      _logger.e('Gemini generate content error: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      _logger.e('Unexpected error in generate content: $e');
      throw LLMRepositoryException('Unexpected error: $e');
    }
  }
  
  /// 流式生成内容
  Stream<AIResponse> generateContentStream(AIRequest request) async* {
    try {
      _logger.d('Sending streaming generate content request to Gemini');
      
      final endpoint = '/models/${request.modelId}:streamGenerateContent';
      final requestData = _buildGenerateContentRequest(request);
      
      final response = await _dio.post(
        endpoint,
        data: requestData,
        options: Options(
          responseType: ResponseType.stream,
          headers: {'Accept': 'application/json'},
        ),
      );
      
      await for (final chunk in _parseStreamResponse(response.data.stream)) {
        if (chunk != null) {
          yield chunk;
        }
      }
    } on DioException catch (e) {
      _logger.e('Gemini streaming generate content error: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      _logger.e('Unexpected error in streaming generate content: $e');
      throw LLMRepositoryException('Unexpected error: $e');
    }
  }
  
  /// 获取可用模型
  Future<List<AIModel>> getModels() async {
    try {
      _logger.d('Fetching available models from Gemini');
      
      final response = await _dio.get('/models');
      return _parseModelsResponse(response.data);
    } on DioException catch (e) {
      _logger.e('Gemini get models error: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      _logger.e('Unexpected error in get models: $e');
      throw LLMRepositoryException('Unexpected error: $e');
    }
  }
  
  /// 构建生成内容请求
  Map<String, dynamic> _buildGenerateContentRequest(AIRequest request) {
    // 构建内容部分
    final contents = <Map<String, dynamic>>[];
    
    if (request.messages != null && request.messages!.isNotEmpty) {
      // 使用提供的消息列表
      for (final msg in request.messages!) {
        contents.add({
          'role': _convertRole(msg.role),
          'parts': [
            {'text': msg.content}
          ],
        });
      }
    } else {
      // 使用单个prompt
      if (request.systemPrompt != null) {
        contents.add({
          'role': 'user',
          'parts': [
            {'text': '${request.systemPrompt}\n\n${request.prompt}'}
          ],
        });
      } else {
        contents.add({
          'role': 'user',
          'parts': [
            {'text': request.prompt}
          ],
        });
      }
    }
    
    final generationConfig = {
      'temperature': request.temperature,
      'topP': request.topP,
      'maxOutputTokens': request.maxTokens,
      if (request.stop != null && request.stop!.isNotEmpty) 'stopSequences': request.stop,
    };
    
    return {
      'contents': contents,
      'generationConfig': generationConfig,
    };
  }
  
  /// 转换角色名称
  String _convertRole(String role) {
    switch (role) {
      case 'assistant':
        return 'model';
      case 'user':
        return 'user';
      case 'system':
        return 'user'; // Gemini没有system角色，合并到user中
      default:
        return 'user';
    }
  }
  
  /// 解析生成内容响应
  AIResponse _parseGenerateContentResponse(Map<String, dynamic> data, String? requestId) {
    final candidates = data['candidates'] as List;
    if (candidates.isEmpty) {
      throw LLMRepositoryException('No candidates in response');
    }
    
    final candidate = candidates[0];
    final content = candidate['content'];
    final parts = content['parts'] as List;
    
    String text = '';
    if (parts.isNotEmpty && parts[0]['text'] != null) {
      text = parts[0]['text'];
    }
    
    // Gemini API不直接提供token使用统计，需要估算
    final promptTokens = _estimateTokens(requestId ?? '');
    final completionTokens = _estimateTokens(text);
    
    return AIResponse(
      id: requestId ?? DateTime.now().millisecondsSinceEpoch.toString(),
      modelId: data['modelVersion'] ?? 'gemini',
      content: text,
      usage: AIUsage(
        promptTokens: promptTokens,
        completionTokens: completionTokens,
        totalTokens: promptTokens + completionTokens,
      ),
      finishReason: candidate['finishReason'],
      choices: candidates.map((candidate) {
        final content = candidate['content'];
        final parts = content['parts'] as List;
        String text = '';
        if (parts.isNotEmpty && parts[0]['text'] != null) {
          text = parts[0]['text'];
        }
        
        return AIChoice(
          index: candidates.indexOf(candidate),
          message: AIMessage(
            role: 'assistant',
            content: text,
          ),
          finishReason: candidate['finishReason'],
        );
      }).toList(),
      createdAt: DateTime.now(),
    );
  }
  
  /// 解析模型列表响应
  List<AIModel> _parseModelsResponse(Map<String, dynamic> data) {
    final models = data['models'] as List;
    return models.map((model) {
      final name = model['name'] as String;
      final modelId = name.split('/').last; // 提取模型ID
      
      return AIModel(
        id: modelId,
        name: modelId,
        displayName: model['displayName'] ?? modelId,
        provider: AIProvider.google,
        type: AIModelType.chat,
        status: AIModelStatus.available,
        description: model['description'],
        maxTokens: _getMaxTokens(modelId),
        supportedFeatures: _getSupportedFeatures(modelId),
      );
    }).toList();
  }
  
  /// 解析流式响应
  Stream<AIResponse?> _parseStreamResponse(Stream<List<int>> stream) async* {
    String buffer = '';
    
    await for (final chunk in stream) {
      buffer += utf8.decode(chunk);
      final lines = buffer.split('\n');
      buffer = lines.removeLast(); // 保留最后一行（可能不完整）
      
      for (final line in lines) {
        final trimmedLine = line.trim();
        if (trimmedLine.isEmpty) continue;
        
        try {
          final json = jsonDecode(trimmedLine);
          
          if (json['candidates'] != null) {
            final candidates = json['candidates'] as List;
            if (candidates.isNotEmpty) {
              final candidate = candidates[0];
              final content = candidate['content'];
              if (content != null && content['parts'] != null) {
                final parts = content['parts'] as List;
                if (parts.isNotEmpty && parts[0]['text'] != null) {
                  yield AIResponse(
                    id: 'stream',
                    modelId: 'gemini',
                    content: parts[0]['text'],
                    usage: const AIUsage(
                      promptTokens: 0,
                      completionTokens: 0,
                      totalTokens: 0,
                    ),
                    finishReason: candidate['finishReason'],
                    createdAt: DateTime.now(),
                  );
                }
              }
            }
          }
        } catch (e) {
          _logger.w('Failed to parse streaming chunk: $e');
        }
      }
    }
  }
  
  /// 估算token数量（简单实现）
  int _estimateTokens(String text) {
    // 简单的token估算：大约4个字符等于1个token
    return (text.length / 4).ceil();
  }
  
  /// 获取模型最大token数
  int _getMaxTokens(String modelId) {
    if (modelId.contains('gemini-2.0')) {
      return 1048576; // 1M tokens
    } else if (modelId.contains('gemini-1.5-pro')) {
      return 2097152; // 2M tokens
    } else if (modelId.contains('gemini-1.5-flash')) {
      return 1048576; // 1M tokens
    } else {
      return 32768; // 默认32K tokens
    }
  }
  
  /// 获取支持的功能
  List<String> _getSupportedFeatures(String modelId) {
    final features = <String>['chat'];
    
    if (modelId.contains('gemini-2.0')) {
      features.addAll(['vision', 'audio', 'code_execution']);
    } else if (modelId.contains('gemini-1.5')) {
      features.addAll(['vision', 'audio']);
    }
    
    return features;
  }
  
  /// 获取支持的模型列表
  List<AIModel> getSupportedModels() {
    return [
      const AIModel(
        id: 'gemini-2.0-flash-exp',
        name: 'gemini-2.0-flash-exp',
        displayName: 'Gemini 2.0 Flash',
        provider: AIProvider.google,
        type: AIModelType.chat,
        status: AIModelStatus.beta,
        description: 'Google最新的实验性多模态模型',
        maxTokens: 1048576,
        supportedFeatures: ['chat', 'vision', 'audio', 'code_execution'],
      ),
      const AIModel(
        id: 'gemini-1.5-pro',
        name: 'gemini-1.5-pro',
        displayName: 'Gemini 1.5 Pro',
        provider: AIProvider.google,
        type: AIModelType.chat,
        status: AIModelStatus.available,
        description: 'Google的专业级多模态模型',
        maxTokens: 2097152,
        supportedFeatures: ['chat', 'vision', 'audio'],
      ),
      const AIModel(
        id: 'gemini-1.5-flash',
        name: 'gemini-1.5-flash',
        displayName: 'Gemini 1.5 Flash',
        provider: AIProvider.google,
        type: AIModelType.chat,
        status: AIModelStatus.available,
        description: 'Google的快速多模态模型',
        maxTokens: 1048576,
        supportedFeatures: ['chat', 'vision', 'audio'],
      ),
      const AIModel(
        id: 'gemini-pro',
        name: 'gemini-pro',
        displayName: 'Gemini Pro',
        provider: AIProvider.google,
        type: AIModelType.chat,
        status: AIModelStatus.available,
        description: 'Google的专业级对话模型',
        maxTokens: 32768,
        supportedFeatures: ['chat'],
      ),
    ];
  }
  
  /// 处理Dio异常
  LLMRepositoryException _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return TimeoutException('Request timeout: ${e.message}');
      
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final responseData = e.response?.data;
        
        switch (statusCode) {
          case 401:
            return AuthenticationException('Invalid API key');
          case 403:
            return AuthenticationException('API key does not have permission');
          case 429:
            return QuotaExceededException('Rate limit exceeded');
          case 404:
            return ModelUnavailableException('Model not found');
          case 400:
            String message = 'Bad request';
            if (responseData is Map && responseData['error'] != null) {
              message = responseData['error']['message'] ?? message;
            }
            return ValidationException(message);
          case 500:
          case 502:
          case 503:
          case 504:
            return NetworkException('Server error: $statusCode');
          default:
            String message = 'HTTP error: $statusCode';
            if (responseData is Map && responseData['error'] != null) {
              message = responseData['error']['message'] ?? message;
            }
            return LLMRepositoryException(message, code: statusCode.toString());
        }
      
      case DioExceptionType.connectionError:
        return NetworkException('Connection error: ${e.message}');
      
      case DioExceptionType.badCertificate:
        return NetworkException('SSL certificate error: ${e.message}');
      
      case DioExceptionType.cancel:
        return LLMRepositoryException('Request cancelled');
      
      default:
        return LLMRepositoryException('Unknown error: ${e.message}');
    }
  }
}