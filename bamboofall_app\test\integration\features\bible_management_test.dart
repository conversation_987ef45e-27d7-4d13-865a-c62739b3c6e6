import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

// 导入应用相关的类
import 'package:bamboofall_app/features/bible/domain/entities/character.dart';
import 'package:bamboofall_app/features/bible/domain/entities/location.dart';
import 'package:bamboofall_app/features/bible/domain/entities/story_bible.dart';
import 'package:bamboofall_app/features/bible/domain/repositories/bible_repository.dart';

// Mock类生成
@GenerateMocks([
  BibleRepository,
])

/// 圣经管理系统集成测试
/// 测试世界观设定、角色管理、地点管理等功能
void main() {
  group('Bible Management Integration Tests', () {
    late MockBibleRepository mockBibleRepository;

    setUp(() {
      mockBibleRepository = MockBibleRepository();
    });

    group('Character Management', () {
      test('should create new character', () async {
        // Arrange
        final newCharacter = Character(
          id: 'char-1',
          projectId: 'project-1',
          name: '李明',
          description: '一位勇敢的年轻剑客',
          age: 25,
          gender: Gender.male,
          occupation: '剑客',
          personality: ['勇敢', '正直', '冲动'],
          appearance: '身材高大，黑发黑眼，总是佩戴着祖传的长剑',
          background: '出生在一个武术世家，从小习武',
          relationships: {},
          tags: ['主角', '武者'],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        when(mockBibleRepository.createCharacter(any))
            .thenAnswer((_) async => newCharacter);

        // Act
        final result = await mockBibleRepository.createCharacter(newCharacter);

        // Assert
        expect(result.id, equals('char-1'));
        expect(result.name, equals('李明'));
        expect(result.age, equals(25));
        expect(result.gender, equals(Gender.male));
        expect(result.personality.contains('勇敢'), isTrue);
        verify(mockBibleRepository.createCharacter(any)).called(1);
      });

      test('should get characters by project', () async {
        // Arrange
        const projectId = 'project-1';
        final characters = [
          Character(
            id: 'char-1',
            projectId: projectId,
            name: '李明',
            description: '主角',
            age: 25,
            gender: Gender.male,
            occupation: '剑客',
            personality: ['勇敢'],
            appearance: '高大',
            background: '武术世家',
            relationships: {},
            tags: ['主角'],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Character(
            id: 'char-2',
            projectId: projectId,
            name: '王小美',
            description: '女主角',
            age: 22,
            gender: Gender.female,
            occupation: '法师',
            personality: ['聪明'],
            appearance: '美丽',
            background: '魔法学院',
            relationships: {},
            tags: ['女主'],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        when(mockBibleRepository.getCharacters(projectId))
            .thenAnswer((_) async => characters);

        // Act
        final result = await mockBibleRepository.getCharacters(projectId);

        // Assert
        expect(result.length, equals(2));
        expect(result.first.name, equals('李明'));
        expect(result.last.name, equals('王小美'));
        verify(mockBibleRepository.getCharacters(projectId)).called(1);
      });

      test('should update character relationships', () async {
        // Arrange
        final character = Character(
          id: 'char-1',
          projectId: 'project-1',
          name: '李明',
          description: '主角',
          age: 25,
          gender: Gender.male,
          occupation: '剑客',
          personality: ['勇敢'],
          appearance: '高大',
          background: '武术世家',
          relationships: {},
          tags: ['主角'],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final updatedCharacter = character.copyWith(
          relationships: {
            'char-2': CharacterRelationship(
              targetId: 'char-2',
              type: RelationshipType.romantic,
              description: '恋人关系',
              strength: 0.9,
            ),
            'char-3': CharacterRelationship(
              targetId: 'char-3',
              type: RelationshipType.friend,
              description: '好友',
              strength: 0.8,
            ),
          },
          updatedAt: DateTime.now(),
        );

        when(mockBibleRepository.updateCharacter(any))
            .thenAnswer((_) async => updatedCharacter);

        // Act
        final result = await mockBibleRepository.updateCharacter(updatedCharacter);

        // Assert
        expect(result.relationships.length, equals(2));
        expect(result.relationships['char-2']?.type, equals(RelationshipType.romantic));
        expect(result.relationships['char-3']?.type, equals(RelationshipType.friend));
        verify(mockBibleRepository.updateCharacter(any)).called(1);
      });

      test('should search characters by tags', () async {
        // Arrange
        const projectId = 'project-1';
        final characters = [
          Character(
            id: 'char-1',
            projectId: projectId,
            name: '李明',
            description: '主角',
            age: 25,
            gender: Gender.male,
            occupation: '剑客',
            personality: ['勇敢'],
            appearance: '高大',
            background: '武术世家',
            relationships: {},
            tags: ['主角', '武者'],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Character(
            id: 'char-2',
            projectId: projectId,
            name: '王小美',
            description: '女主角',
            age: 22,
            gender: Gender.female,
            occupation: '法师',
            personality: ['聪明'],
            appearance: '美丽',
            background: '魔法学院',
            relationships: {},
            tags: ['女主', '法师'],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Character(
            id: 'char-3',
            projectId: projectId,
            name: '张三',
            description: '配角',
            age: 30,
            gender: Gender.male,
            occupation: '商人',
            personality: ['狡猾'],
            appearance: '普通',
            background: '商人家庭',
            relationships: {},
            tags: ['配角', '商人'],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        when(mockBibleRepository.getCharacters(projectId))
            .thenAnswer((_) async => characters);

        // Act
        final allCharacters = await mockBibleRepository.getCharacters(projectId);
        
        // 模拟按标签搜索
        final mainCharacters = allCharacters.where((char) => 
          char.tags.contains('主角') || char.tags.contains('女主')
        ).toList();

        // Assert
        expect(mainCharacters.length, equals(2));
        expect(mainCharacters.map((c) => c.name), containsAll(['李明', '王小美']));
      });
    });

    group('Location Management', () {
      test('should create new location', () async {
        // Arrange
        final newLocation = Location(
          id: 'loc-1',
          projectId: 'project-1',
          name: '青云城',
          description: '一座繁华的古代城市',
          type: LocationType.city,
          climate: '温带',
          geography: '位于山谷中，三面环山',
          culture: '以武术和商业闻名',
          history: '建城已有千年历史',
          population: 100000,
          government: '城主制',
          economy: '商业贸易',
          landmarks: ['青云塔', '武术学院', '中央广场'],
          connections: {},
          tags: ['主城', '繁华'],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        when(mockBibleRepository.createLocation(any))
            .thenAnswer((_) async => newLocation);

        // Act
        final result = await mockBibleRepository.createLocation(newLocation);

        // Assert
        expect(result.id, equals('loc-1'));
        expect(result.name, equals('青云城'));
        expect(result.type, equals(LocationType.city));
        expect(result.population, equals(100000));
        expect(result.landmarks.contains('青云塔'), isTrue);
        verify(mockBibleRepository.createLocation(any)).called(1);
      });

      test('should get locations by project', () async {
        // Arrange
        const projectId = 'project-1';
        final locations = [
          Location(
            id: 'loc-1',
            projectId: projectId,
            name: '青云城',
            description: '主城',
            type: LocationType.city,
            climate: '温带',
            geography: '山谷',
            culture: '武术',
            history: '千年',
            population: 100000,
            government: '城主制',
            economy: '商业',
            landmarks: ['青云塔'],
            connections: {},
            tags: ['主城'],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Location(
            id: 'loc-2',
            projectId: projectId,
            name: '暗影森林',
            description: '神秘森林',
            type: LocationType.forest,
            climate: '阴冷',
            geography: '密林',
            culture: '精灵',
            history: '远古',
            population: 1000,
            government: '长老会',
            economy: '自给自足',
            landmarks: ['生命之树'],
            connections: {},
            tags: ['森林', '神秘'],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        when(mockBibleRepository.getLocations(projectId))
            .thenAnswer((_) async => locations);

        // Act
        final result = await mockBibleRepository.getLocations(projectId);

        // Assert
        expect(result.length, equals(2));
        expect(result.first.name, equals('青云城'));
        expect(result.last.name, equals('暗影森林'));
        expect(result.first.type, equals(LocationType.city));
        expect(result.last.type, equals(LocationType.forest));
        verify(mockBibleRepository.getLocations(projectId)).called(1);
      });

      test('should establish location connections', () async {
        // Arrange
        final location = Location(
          id: 'loc-1',
          projectId: 'project-1',
          name: '青云城',
          description: '主城',
          type: LocationType.city,
          climate: '温带',
          geography: '山谷',
          culture: '武术',
          history: '千年',
          population: 100000,
          government: '城主制',
          economy: '商业',
          landmarks: ['青云塔'],
          connections: {},
          tags: ['主城'],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final updatedLocation = location.copyWith(
          connections: {
            'loc-2': LocationConnection(
              targetId: 'loc-2',
              type: ConnectionType.road,
              distance: 50,
              travelTime: '2天',
              description: '通往暗影森林的古道',
            ),
            'loc-3': LocationConnection(
              targetId: 'loc-3',
              type: ConnectionType.river,
              distance: 30,
              travelTime: '1天',
              description: '青云河水路',
            ),
          },
          updatedAt: DateTime.now(),
        );

        when(mockBibleRepository.updateLocation(any))
            .thenAnswer((_) async => updatedLocation);

        // Act
        final result = await mockBibleRepository.updateLocation(updatedLocation);

        // Assert
        expect(result.connections.length, equals(2));
        expect(result.connections['loc-2']?.type, equals(ConnectionType.road));
        expect(result.connections['loc-3']?.type, equals(ConnectionType.river));
        verify(mockBibleRepository.updateLocation(any)).called(1);
      });
    });

    group('Story Bible Management', () {
      test('should create story bible', () async {
        // Arrange
        final storyBible = StoryBible(
          id: 'bible-1',
          projectId: 'project-1',
          title: '青云传说世界观',
          description: '一个充满武侠和魔法的世界',
          worldSettings: WorldSettings(
            name: '青云大陆',
            timeSystem: '青云历',
            magicSystem: '内力与法术并存',
            technology: '古代科技水平',
            socialStructure: '封建制度',
            religions: ['天道教', '武神宗'],
            languages: ['通用语', '古语'],
            currencies: ['金币', '银币', '铜币'],
          ),
          themes: ['正义与邪恶', '成长与友情', '爱情与牺牲'],
          rules: [
            '内力修炼需要天赋和努力',
            '魔法需要消耗精神力',
            '死亡是永久的，没有复活',
          ],
          constraints: [
            '主角不能无敌',
            '魔法有明确的限制',
            '角色行为要符合性格设定',
          ],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        when(mockBibleRepository.createStoryBible(any))
            .thenAnswer((_) async => storyBible);

        // Act
        final result = await mockBibleRepository.createStoryBible(storyBible);

        // Assert
        expect(result.id, equals('bible-1'));
        expect(result.title, equals('青云传说世界观'));
        expect(result.worldSettings.name, equals('青云大陆'));
        expect(result.themes.length, equals(3));
        expect(result.rules.length, equals(3));
        verify(mockBibleRepository.createStoryBible(any)).called(1);
      });

      test('should validate story consistency', () async {
        // Arrange
        const projectId = 'project-1';
        final characters = [
          Character(
            id: 'char-1',
            projectId: projectId,
            name: '李明',
            description: '主角',
            age: 25,
            gender: Gender.male,
            occupation: '剑客',
            personality: ['勇敢'],
            appearance: '高大',
            background: '武术世家',
            relationships: {
              'char-2': CharacterRelationship(
                targetId: 'char-2',
                type: RelationshipType.romantic,
                description: '恋人',
                strength: 0.9,
              ),
            },
            tags: ['主角'],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        final validationResults = [
          ValidationResult(
            type: ViolationType.invalidReference,
            message: '角色 char-1 引用了不存在的角色 char-2',
            severity: ValidationSeverity.error,
            elementId: 'char-1',
            elementType: 'Character',
          ),
        ];

        when(mockBibleRepository.getCharacters(projectId))
            .thenAnswer((_) async => characters);
        when(mockBibleRepository.validateConstraints(projectId))
            .thenAnswer((_) async => validationResults);

        // Act
        final charactersResult = await mockBibleRepository.getCharacters(projectId);
        final validationResult = await mockBibleRepository.validateConstraints(projectId);

        // Assert
        expect(charactersResult.length, equals(1));
        expect(charactersResult.first.relationships.containsKey('char-2'), isTrue);
        
        expect(validationResult.length, equals(1));
        expect(validationResult.first.type, equals(ViolationType.invalidReference));
        expect(validationResult.first.message, contains('char-2'));

        verify(mockBibleRepository.getCharacters(projectId)).called(1);
        verify(mockBibleRepository.validateConstraints(projectId)).called(1);
      });

      test('should export and import bible data', () async {
        // Arrange
        const projectId = 'project-1';
        final exportData = {
          'characters': [
            {
              'id': 'char-1',
              'name': '李明',
              'age': 25,
              'gender': 'male',
            }
          ],
          'locations': [
            {
              'id': 'loc-1',
              'name': '青云城',
              'type': 'city',
            }
          ],
          'storyBible': {
            'id': 'bible-1',
            'title': '世界观设定',
          }
        };

        when(mockBibleRepository.exportBibleData(projectId))
            .thenAnswer((_) async => exportData);
        when(mockBibleRepository.importBibleData(projectId, exportData))
            .thenAnswer((_) async => true);

        // Act
        final exported = await mockBibleRepository.exportBibleData(projectId);
        final imported = await mockBibleRepository.importBibleData(projectId, exported);

        // Assert
        expect(exported['characters'], isA<List>());
        expect(exported['locations'], isA<List>());
        expect(exported['storyBible'], isA<Map>());
        expect(imported, isTrue);

        verify(mockBibleRepository.exportBibleData(projectId)).called(1);
        verify(mockBibleRepository.importBibleData(projectId, exported)).called(1);
      });
    });

    group('Advanced Bible Features', () {
      test('should generate character relationship graph', () async {
        // Arrange
        const projectId = 'project-1';
        final characters = [
          Character(
            id: 'char-1',
            projectId: projectId,
            name: '李明',
            description: '主角',
            age: 25,
            gender: Gender.male,
            occupation: '剑客',
            personality: ['勇敢'],
            appearance: '高大',
            background: '武术世家',
            relationships: {
              'char-2': CharacterRelationship(
                targetId: 'char-2',
                type: RelationshipType.romantic,
                description: '恋人',
                strength: 0.9,
              ),
              'char-3': CharacterRelationship(
                targetId: 'char-3',
                type: RelationshipType.friend,
                description: '好友',
                strength: 0.8,
              ),
            },
            tags: ['主角'],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        when(mockBibleRepository.getCharacters(projectId))
            .thenAnswer((_) async => characters);

        // Act
        final result = await mockBibleRepository.getCharacters(projectId);
        
        // 分析关系图
        final relationshipGraph = <String, List<String>>{};
        for (final character in result) {
          relationshipGraph[character.id] = character.relationships.keys.toList();
        }

        // Assert
        expect(relationshipGraph['char-1']?.length, equals(2));
        expect(relationshipGraph['char-1'], containsAll(['char-2', 'char-3']));
      });

      test('should track bible element usage in chapters', () async {
        // Arrange
        const projectId = 'project-1';
        final usageStats = {
          'characters': {
            'char-1': 15, // 在15个章节中出现
            'char-2': 8,
            'char-3': 3,
          },
          'locations': {
            'loc-1': 12,
            'loc-2': 5,
          }
        };

        when(mockBibleRepository.getBibleElementUsage(projectId))
            .thenAnswer((_) async => usageStats);

        // Act
        final result = await mockBibleRepository.getBibleElementUsage(projectId);

        // Assert
        expect(result['characters']['char-1'], equals(15));
        expect(result['locations']['loc-1'], equals(12));
        verify(mockBibleRepository.getBibleElementUsage(projectId)).called(1);
      });
    });
  });
}

// 辅助枚举和类定义
enum Gender { male, female, other }
enum RelationshipType { family, friend, romantic, enemy, mentor, rival }
enum LocationType { city, village, forest, mountain, river, building, other }
enum ConnectionType { road, river, bridge, tunnel, portal, other }
enum ViolationType { invalidReference, inconsistentData, missingRequired }
enum ValidationSeverity { error, warning, info }

// 辅助类定义
class CharacterRelationship {
  final String targetId;
  final RelationshipType type;
  final String description;
  final double strength;

  const CharacterRelationship({
    required this.targetId,
    required this.type,
    required this.description,
    required this.strength,
  });
}

class LocationConnection {
  final String targetId;
  final ConnectionType type;
  final int distance;
  final String travelTime;
  final String description;

  const LocationConnection({
    required this.targetId,
    required this.type,
    required this.distance,
    required this.travelTime,
    required this.description,
  });
}

class WorldSettings {
  final String name;
  final String timeSystem;
  final String magicSystem;
  final String technology;
  final String socialStructure;
  final List<String> religions;
  final List<String> languages;
  final List<String> currencies;

  const WorldSettings({
    required this.name,
    required this.timeSystem,
    required this.magicSystem,
    required this.technology,
    required this.socialStructure,
    required this.religions,
    required this.languages,
    required this.currencies,
  });
}

class ValidationResult {
  final ViolationType type;
  final String message;
  final ValidationSeverity severity;
  final String elementId;
  final String elementType;

  const ValidationResult({
    required this.type,
    required this.message,
    required this.severity,
    required this.elementId,
    required this.elementType,
  });
}

// Mock类
class MockBibleRepository extends Mock implements BibleRepository {}