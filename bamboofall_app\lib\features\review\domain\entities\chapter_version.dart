import 'dart:math' as math;

import 'package:freezed_annotation/freezed_annotation.dart';

part 'chapter_version.freezed.dart';
part 'chapter_version.g.dart';

/// 章节版本实体
/// 表示章节的一个历史版本，用于版本控制和审阅
@freezed
class ChapterVersion with _$ChapterVersion {
  const factory ChapterVersion({
    /// 版本唯一标识符
    required String id,
    
    /// 关联的章节ID
    required String chapterId,
    
    /// 版本号（递增）
    required int versionNumber,
    
    /// 版本标题
    required String title,
    
    /// 版本内容（Markdown格式）
    required String content,
    
    /// 版本创建时间
    required DateTime createdAt,
    
    /// 版本创建者
    required String createdBy,
    
    /// 版本描述/备注
    @Default('') String description,
    
    /// 版本标签
    @Default([]) List<String> tags,
    
    /// 版本状态
    @Default(VersionStatus.draft) VersionStatus status,
    
    /// 字数统计
    @Default(0) int wordCount,
    
    /// 字符数统计
    @Default(0) int characterCount,
    
    /// 段落数统计
    @Default(0) int paragraphCount,
    
    /// 版本大小（字节）
    @Default(0) int sizeInBytes,
    
    /// 是否为主版本
    @Default(false) bool isMainVersion,
    
    /// 是否为自动保存版本
    @Default(false) bool isAutoSave,
    
    /// 父版本ID（基于哪个版本创建）
    String? parentVersionId,
    
    /// 合并的源版本ID列表（如果是合并版本）
    @Default([]) List<String> mergedFromVersionIds,
    
    /// 版本元数据
    @Default({}) Map<String, dynamic> metadata,
    
    /// 校验和（用于完整性检查）
    String? checksum,
  }) = _ChapterVersion;

  factory ChapterVersion.fromJson(Map<String, dynamic> json) => _$ChapterVersionFromJson(json);
}

/// 版本状态枚举
enum VersionStatus {
  /// 草稿
  draft,
  
  /// 待审阅
  pendingReview,
  
  /// 审阅中
  inReview,
  
  /// 已批准
  approved,
  
  /// 已拒绝
  rejected,
  
  /// 已发布
  published,
  
  /// 已归档
  archived,
  
  /// 已删除
  deleted,
}

/// 版本状态扩展
extension VersionStatusExtension on VersionStatus {
  /// 获取状态显示名称
  String get displayName {
    switch (this) {
      case VersionStatus.draft:
        return '草稿';
      case VersionStatus.pendingReview:
        return '待审阅';
      case VersionStatus.inReview:
        return '审阅中';
      case VersionStatus.approved:
        return '已批准';
      case VersionStatus.rejected:
        return '已拒绝';
      case VersionStatus.published:
        return '已发布';
      case VersionStatus.archived:
        return '已归档';
      case VersionStatus.deleted:
        return '已删除';
    }
  }
  
  /// 获取状态颜色
  String get colorHex {
    switch (this) {
      case VersionStatus.draft:
        return '#9CA3AF'; // 灰色
      case VersionStatus.pendingReview:
        return '#F59E0B'; // 黄色
      case VersionStatus.inReview:
        return '#3B82F6'; // 蓝色
      case VersionStatus.approved:
        return '#10B981'; // 绿色
      case VersionStatus.rejected:
        return '#EF4444'; // 红色
      case VersionStatus.published:
        return '#8B5CF6'; // 紫色
      case VersionStatus.archived:
        return '#6B7280'; // 深灰色
      case VersionStatus.deleted:
        return '#374151'; // 更深灰色
    }
  }
  
  /// 是否可以编辑
  bool get canEdit {
    switch (this) {
      case VersionStatus.draft:
      case VersionStatus.rejected:
        return true;
      default:
        return false;
    }
  }
  
  /// 是否可以删除
  bool get canDelete {
    switch (this) {
      case VersionStatus.draft:
      case VersionStatus.rejected:
      case VersionStatus.archived:
        return true;
      default:
        return false;
    }
  }
}

/// 章节版本扩展方法
extension ChapterVersionExtension on ChapterVersion {
  /// 是否为最新版本
  bool isLatest(List<ChapterVersion> allVersions) {
    final chapterVersions = allVersions.where((v) => v.chapterId == chapterId).toList();
    if (chapterVersions.isEmpty) return true;
    
    final maxVersion = chapterVersions.map((v) => v.versionNumber).reduce(math.max);
    return versionNumber == maxVersion;
  }
  
  /// 获取版本标识符（用于显示）
  String get versionLabel => 'v$versionNumber';
  
  /// 获取简短描述
  String get shortDescription {
    if (description.isEmpty) {
      return isAutoSave ? '自动保存' : '版本 $versionNumber';
    }
    return description.length > 50 ? '${description.substring(0, 50)}...' : description;
  }
  
  /// 计算内容统计信息
  ChapterVersion calculateStats() {
    final lines = content.split('\n');
    final paragraphs = lines.where((line) => line.trim().isNotEmpty).length;
    final words = _countWords(content);
    final characters = content.length;
    final bytes = content.codeUnits.length;
    
    return copyWith(
      wordCount: words,
      characterCount: characters,
      paragraphCount: paragraphs,
      sizeInBytes: bytes,
      checksum: _calculateChecksum(content),
    );
  }
  
  /// 创建新版本
  ChapterVersion createNewVersion({
    required String newContent,
    String? newTitle,
    String? description,
    String? createdBy,
    List<String>? tags,
    VersionStatus? status,
  }) {
    final now = DateTime.now();
    final newVersionNumber = versionNumber + 1;
    
    return ChapterVersion(
      id: _generateVersionId(),
      chapterId: chapterId,
      versionNumber: newVersionNumber,
      title: newTitle ?? title,
      content: newContent,
      createdAt: now,
      createdBy: createdBy ?? this.createdBy,
      description: description ?? '',
      tags: tags ?? this.tags,
      status: status ?? VersionStatus.draft,
      parentVersionId: id,
    ).calculateStats();
  }
  
  /// 创建分支版本
  ChapterVersion createBranch({
    required String newContent,
    String? description,
    String? createdBy,
    List<String>? tags,
  }) {
    final now = DateTime.now();
    
    return ChapterVersion(
      id: _generateVersionId(),
      chapterId: chapterId,
      versionNumber: versionNumber, // 保持相同版本号，但ID不同
      title: title,
      content: newContent,
      createdAt: now,
      createdBy: createdBy ?? this.createdBy,
      description: description ?? '分支版本',
      tags: tags ?? [...this.tags, 'branch'],
      status: VersionStatus.draft,
      parentVersionId: id,
    ).calculateStats();
  }
  
  /// 创建合并版本
  static ChapterVersion createMergedVersion({
    required String chapterId,
    required List<ChapterVersion> sourceVersions,
    required String mergedContent,
    required String title,
    required String createdBy,
    String? description,
    List<String>? tags,
  }) {
    if (sourceVersions.isEmpty) {
      throw ArgumentError('Source versions cannot be empty');
    }
    
    final now = DateTime.now();
    final maxVersionNumber = sourceVersions.map((v) => v.versionNumber).reduce(math.max);
    final newVersionNumber = maxVersionNumber + 1;
    
    return ChapterVersion(
      id: _generateVersionId(),
      chapterId: chapterId,
      versionNumber: newVersionNumber,
      title: title,
      content: mergedContent,
      createdAt: now,
      createdBy: createdBy,
      description: description ?? '合并版本',
      tags: tags ?? ['merged'],
      status: VersionStatus.draft,
      mergedFromVersionIds: sourceVersions.map((v) => v.id).toList(),
    ).calculateStats();
  }
  
  /// 创建自动保存版本
  ChapterVersion createAutoSave({
    required String newContent,
    String? createdBy,
  }) {
    final now = DateTime.now();
    
    return ChapterVersion(
      id: _generateVersionId(),
      chapterId: chapterId,
      versionNumber: versionNumber,
      title: title,
      content: newContent,
      createdAt: now,
      createdBy: createdBy ?? this.createdBy,
      description: '自动保存 ${now.toString()}',
      tags: [...tags, 'autosave'],
      status: VersionStatus.draft,
      isAutoSave: true,
      parentVersionId: id,
    ).calculateStats();
  }
  
  /// 验证版本完整性
  bool validateIntegrity() {
    if (checksum == null) return false;
    return checksum == _calculateChecksum(content);
  }
  
  /// 获取版本大小（人类可读格式）
  String get readableSize {
    if (sizeInBytes < 1024) {
      return '${sizeInBytes}B';
    } else if (sizeInBytes < 1024 * 1024) {
      return '${(sizeInBytes / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(sizeInBytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }
  
  /// 统计字数
  int _countWords(String text) {
    if (text.isEmpty) return 0;
    
    // 简单的中英文字数统计
    final chineseChars = text.replaceAll(RegExp(r'[^\u4e00-\u9fa5]'), '').length;
    final englishWords = text
        .replaceAll(RegExp(r'[\u4e00-\u9fa5]'), ' ')
        .split(RegExp(r'\s+'))
        .where((word) => word.isNotEmpty)
        .length;
    
    return chineseChars + englishWords;
  }
  
  /// 计算校验和
  String _calculateChecksum(String content) {
    // 简单的校验和计算（实际项目中应使用更强的哈希算法）
    int hash = 0;
    for (int i = 0; i < content.length; i++) {
      hash = ((hash << 5) - hash + content.codeUnitAt(i)) & 0xffffffff;
    }
    return hash.toRadixString(16);
  }
  
  /// 生成版本ID
  static String _generateVersionId() {
    return 'version_${DateTime.now().millisecondsSinceEpoch}_${(1000 + (999 * (DateTime.now().microsecond / 1000000))).round()}';
  }
}

/// 版本比较结果
class VersionComparison {
  final ChapterVersion oldVersion;
  final ChapterVersion newVersion;
  final int addedLines;
  final int removedLines;
  final int modifiedLines;
  final double similarity;
  
  const VersionComparison({
    required this.oldVersion,
    required this.newVersion,
    required this.addedLines,
    required this.removedLines,
    required this.modifiedLines,
    required this.similarity,
  });
  
  /// 总变更行数
  int get totalChanges => addedLines + removedLines + modifiedLines;
  
  /// 是否有变更
  bool get hasChanges => totalChanges > 0;
  
  /// 变更类型
  String get changeType {
    if (!hasChanges) return '无变更';
    if (addedLines > removedLines) return '主要新增';
    if (removedLines > addedLines) return '主要删除';
    return '修改';
  }
}