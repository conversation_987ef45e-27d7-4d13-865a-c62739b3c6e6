import 'package:freezed_annotation/freezed_annotation.dart';

part 'character.freezed.dart';
part 'character.g.dart';

/// 角色实体
@freezed
class Character with _$Character {
  const factory Character({
    required String id,
    required String name,
    @Default([]) List<String> aliases, // 别名
    String? description,
    required CharacterType type,
    required CharacterStatus status,
    CharacterAppearance? appearance,
    CharacterPersonality? personality,
    CharacterBackground? background,
    @Default([]) List<CharacterAbility> abilities,
    @Default([]) List<String> skillIds,
    @Default([]) List<String> itemIds,
    @Default([]) List<String> relationshipIds,
    String? currentLocationId,
    @Default([]) List<CharacterGoal> goals,
    @Default([]) List<CharacterSecret> secrets,
    @Default([]) List<CharacterArc> characterArcs,
    String? voiceStyle, // 说话风格
    @Default({}) Map<String, dynamic> customAttributes,
    String? imageUrl,
    @Default({}) Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _Character;

  factory Character.fromJson(Map<String, dynamic> json) => _$CharacterFromJson(json);
}

/// 角色类型
enum CharacterType {
  protagonist('主角'),
  antagonist('反派'),
  supporting('配角'),
  minor('次要角色'),
  background('背景角色'),
  narrator('叙述者');

  const CharacterType(this.displayName);
  final String displayName;
}

/// 角色状态
enum CharacterStatus {
  alive('存活'),
  dead('死亡'),
  missing('失踪'),
  unknown('未知'),
  inactive('非活跃');

  const CharacterStatus(this.displayName);
  final String displayName;
}

/// 角色外貌
@freezed
class CharacterAppearance with _$CharacterAppearance {
  const factory CharacterAppearance({
    int? age,
    String? gender,
    String? height,
    String? weight,
    String? build, // 体型
    String? hairColor,
    String? hairStyle,
    String? eyeColor,
    String? skinTone,
    @Default([]) List<String> distinguishingFeatures, // 显著特征
    String? clothing,
    @Default([]) List<String> accessories,
    String? overallDescription,
    @Default({}) Map<String, dynamic> customFeatures,
  }) = _CharacterAppearance;

  factory CharacterAppearance.fromJson(Map<String, dynamic> json) => _$CharacterAppearanceFromJson(json);
}

/// 角色性格
@freezed
class CharacterPersonality with _$CharacterPersonality {
  const factory CharacterPersonality({
    @Default([]) List<String> traits, // 性格特征
    @Default([]) List<String> strengths, // 优点
    @Default([]) List<String> weaknesses, // 缺点
    @Default([]) List<String> fears, // 恐惧
    @Default([]) List<String> desires, // 欲望
    @Default([]) List<String> habits, // 习惯
    @Default([]) List<String> quirks, // 怪癖
    String? moralAlignment, // 道德倾向
    String? temperament, // 气质
    String? worldview, // 世界观
    @Default({}) Map<String, int> personalityScores, // 性格评分 (如五大人格)
    @Default({}) Map<String, dynamic> customTraits,
  }) = _CharacterPersonality;

  factory CharacterPersonality.fromJson(Map<String, dynamic> json) => _$CharacterPersonalityFromJson(json);
}

/// 角色背景
@freezed
class CharacterBackground with _$CharacterBackground {
  const factory CharacterBackground({
    String? birthPlace,
    DateTime? birthDate,
    String? family,
    String? education,
    String? occupation,
    String? socialClass,
    @Default([]) List<String> languages,
    @Default([]) List<BackgroundEvent> lifeEvents,
    String? currentResidence,
    String? backstory,
    @Default({}) Map<String, dynamic> customBackground,
  }) = _CharacterBackground;

  factory CharacterBackground.fromJson(Map<String, dynamic> json) => _$CharacterBackgroundFromJson(json);
}

/// 背景事件
@freezed
class BackgroundEvent with _$BackgroundEvent {
  const factory BackgroundEvent({
    required String id,
    required String title,
    String? description,
    DateTime? date,
    String? location,
    @Default([]) List<String> involvedCharacterIds,
    BackgroundEventType? type,
    int? impactLevel, // 影响程度 1-10
    @Default({}) Map<String, dynamic> metadata,
  }) = _BackgroundEvent;

  factory BackgroundEvent.fromJson(Map<String, dynamic> json) => _$BackgroundEventFromJson(json);
}

/// 背景事件类型
enum BackgroundEventType {
  birth('出生'),
  education('教育'),
  career('职业'),
  relationship('关系'),
  trauma('创伤'),
  achievement('成就'),
  loss('失去'),
  discovery('发现'),
  other('其他');

  const BackgroundEventType(this.displayName);
  final String displayName;
}

/// 角色能力
@freezed
class CharacterAbility with _$CharacterAbility {
  const factory CharacterAbility({
    required String name,
    String? description,
    required AbilityType type,
    required int level, // 1-10
    String? source, // 能力来源
    @Default([]) List<String> limitations,
    @Default({}) Map<String, dynamic> parameters,
  }) = _CharacterAbility;

  factory CharacterAbility.fromJson(Map<String, dynamic> json) => _$CharacterAbilityFromJson(json);
}

/// 能力类型
enum AbilityType {
  physical('物理'),
  mental('精神'),
  magical('魔法'),
  supernatural('超自然'),
  technological('科技'),
  social('社交'),
  other('其他');

  const AbilityType(this.displayName);
  final String displayName;
}

/// 角色目标
@freezed
class CharacterGoal with _$CharacterGoal {
  const factory CharacterGoal({
    required String id,
    required String title,
    String? description,
    required GoalType type,
    required GoalPriority priority,
    required GoalStatus status,
    @Default([]) List<String> obstacles, // 障碍
    @Default([]) List<String> steps, // 实现步骤
    String? motivation, // 动机
    DateTime? deadline,
    @Default({}) Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _CharacterGoal;

  factory CharacterGoal.fromJson(Map<String, dynamic> json) => _$CharacterGoalFromJson(json);
}

/// 目标类型
enum GoalType {
  shortTerm('短期'),
  longTerm('长期'),
  lifeGoal('人生目标'),
  immediate('即时'),
  conditional('条件性');

  const GoalType(this.displayName);
  final String displayName;
}

/// 目标优先级
enum GoalPriority {
  low('低'),
  medium('中'),
  high('高'),
  critical('关键');

  const GoalPriority(this.displayName);
  final String displayName;
}

/// 目标状态
enum GoalStatus {
  active('进行中'),
  completed('已完成'),
  failed('已失败'),
  abandoned('已放弃'),
  paused('暂停');

  const GoalStatus(this.displayName);
  final String displayName;
}

/// 角色秘密
@freezed
class CharacterSecret with _$CharacterSecret {
  const factory CharacterSecret({
    required String id,
    required String title,
    required String description,
    required SecretType type,
    required SecretSeverity severity,
    @Default([]) List<String> knownByCharacterIds, // 知道这个秘密的角色
    String? revealCondition, // 揭露条件
    String? consequence, // 揭露后果
    bool? isRevealed,
    DateTime? revealedAt,
    @Default({}) Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _CharacterSecret;

  factory CharacterSecret.fromJson(Map<String, dynamic> json) => _$CharacterSecretFromJson(json);
}

/// 秘密类型
enum SecretType {
  identity('身份'),
  past('过去'),
  relationship('关系'),
  ability('能力'),
  knowledge('知识'),
  crime('罪行'),
  shame('羞耻'),
  other('其他');

  const SecretType(this.displayName);
  final String displayName;
}

/// 秘密严重程度
enum SecretSeverity {
  minor('轻微'),
  moderate('中等'),
  major('重大'),
  critical('致命');

  const SecretSeverity(this.displayName);
  final String displayName;
}

/// 角色弧线
@freezed
class CharacterArc with _$CharacterArc {
  const factory CharacterArc({
    required String id,
    required String title,
    String? description,
    required ArcType type,
    required ArcStatus status,
    @Default([]) List<ArcMilestone> milestones,
    String? startingPoint, // 起点描述
    String? endingPoint, // 终点描述
    @Default([]) List<String> relatedPlotLineIds,
    @Default({}) Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _CharacterArc;

  factory CharacterArc.fromJson(Map<String, dynamic> json) => _$CharacterArcFromJson(json);
}

/// 弧线类型
enum ArcType {
  growth('成长'),
  fall('堕落'),
  redemption('救赎'),
  transformation('转变'),
  discovery('发现'),
  revenge('复仇'),
  love('爱情'),
  sacrifice('牺牲');

  const ArcType(this.displayName);
  final String displayName;
}

/// 弧线状态
enum ArcStatus {
  planned('计划中'),
  active('进行中'),
  completed('已完成'),
  paused('暂停'),
  cancelled('已取消');

  const ArcStatus(this.displayName);
  final String displayName;
}

/// 弧线里程碑
@freezed
class ArcMilestone with _$ArcMilestone {
  const factory ArcMilestone({
    required String id,
    required String title,
    String? description,
    required int order,
    MilestoneStatus? status,
    String? chapterId,
    @Default({}) Map<String, dynamic> metadata,
    DateTime? achievedAt,
  }) = _ArcMilestone;

  factory ArcMilestone.fromJson(Map<String, dynamic> json) => _$ArcMilestoneFromJson(json);
}

/// 里程碑状态
enum MilestoneStatus {
  pending('待完成'),
  achieved('已达成'),
  skipped('已跳过'),
  modified('已修改');

  const MilestoneStatus(this.displayName);
  final String displayName;
}

/// 角色统计
@freezed
class CharacterStats with _$CharacterStats {
  const factory CharacterStats({
    required String characterId,
    int? appearanceCount, // 出场次数
    int? dialogueCount, // 对话次数
    int? mentionCount, // 被提及次数
    @Default([]) List<String> mostInteractedCharacters,
    @Default([]) List<String> frequentLocations,
    @Default({}) Map<String, int> emotionFrequency,
    DateTime? lastAppearance,
    @Default({}) Map<String, dynamic> customStats,
  }) = _CharacterStats;

  factory CharacterStats.fromJson(Map<String, dynamic> json) => _$CharacterStatsFromJson(json);
}