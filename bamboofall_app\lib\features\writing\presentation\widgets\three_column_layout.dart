import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

/// 三栏布局配置状态
class ThreeColumnLayoutState {
  final double leftWidth;
  final double rightWidth;
  final bool leftVisible;
  final bool rightVisible;

  const ThreeColumnLayoutState({
    this.leftWidth = 300.0,
    this.rightWidth = 350.0,
    this.leftVisible = true,
    this.rightVisible = true,
  });

  ThreeColumnLayoutState copyWith({
    double? leftWidth,
    double? rightWidth,
    bool? leftVisible,
    bool? rightVisible,
  }) {
    return ThreeColumnLayoutState(
      leftWidth: leftWidth ?? this.leftWidth,
      rightWidth: rightWidth ?? this.rightWidth,
      leftVisible: leftVisible ?? this.leftVisible,
      rightVisible: rightVisible ?? this.rightVisible,
    );
  }
}

/// 三栏布局状态管理
final threeColumnLayoutProvider = StateNotifierProvider<ThreeColumnLayoutNotifier, ThreeColumnLayoutState>((ref) {
  return ThreeColumnLayoutNotifier();
});

class ThreeColumnLayoutNotifier extends StateNotifier<ThreeColumnLayoutState> {
  ThreeColumnLayoutNotifier() : super(const ThreeColumnLayoutState());

  void updateLeftWidth(double width) {
    state = state.copyWith(leftWidth: width.clamp(200.0, 500.0));
  }

  void updateRightWidth(double width) {
    state = state.copyWith(rightWidth: width.clamp(250.0, 600.0));
  }

  void toggleLeftPanel() {
    state = state.copyWith(leftVisible: !state.leftVisible);
  }

  void toggleRightPanel() {
    state = state.copyWith(rightVisible: !state.rightVisible);
  }
}

/// 三栏布局组件
/// 提供可调整大小的三栏布局：左栏、中栏、右栏
class ThreeColumnLayout extends ConsumerWidget {
  final Widget leftPanel;
  final Widget centerPanel;
  final Widget rightPanel;

  const ThreeColumnLayout({
    super.key,
    required this.leftPanel,
    required this.centerPanel,
    required this.rightPanel,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final layoutState = ref.watch(threeColumnLayoutProvider);
    final layoutNotifier = ref.read(threeColumnLayoutProvider.notifier);

    return LayoutBuilder(
      builder: (context, constraints) {
        final totalWidth = constraints.maxWidth;
        final minCenterWidth = 400.0;
        
        // 计算实际宽度
        double leftWidth = layoutState.leftVisible ? layoutState.leftWidth : 0;
        double rightWidth = layoutState.rightVisible ? layoutState.rightWidth : 0;
        double centerWidth = totalWidth - leftWidth - rightWidth - (layoutState.leftVisible ? 4 : 0) - (layoutState.rightVisible ? 4 : 0);
        
        // 确保中栏有最小宽度
        if (centerWidth < minCenterWidth) {
          final excess = minCenterWidth - centerWidth;
          if (layoutState.leftVisible && layoutState.rightVisible) {
            leftWidth = (leftWidth - excess / 2).clamp(200.0, leftWidth);
            rightWidth = (rightWidth - excess / 2).clamp(250.0, rightWidth);
          } else if (layoutState.leftVisible) {
            leftWidth = (leftWidth - excess).clamp(200.0, leftWidth);
          } else if (layoutState.rightVisible) {
            rightWidth = (rightWidth - excess).clamp(250.0, rightWidth);
          }
          centerWidth = totalWidth - leftWidth - rightWidth - (layoutState.leftVisible ? 4 : 0) - (layoutState.rightVisible ? 4 : 0);
        }

        return Row(
          children: [
            // 左栏
            if (layoutState.leftVisible) ...[
              SizedBox(
                width: leftWidth,
                child: _PanelContainer(
                  title: '大纲',
                  onToggle: layoutNotifier.toggleLeftPanel,
                  child: leftPanel,
                ),
              ),
              _ResizeHandle(
                isVertical: false,
                onDrag: (delta) {
                  layoutNotifier.updateLeftWidth(leftWidth + delta);
                },
              ),
            ],
            
            // 中栏
            Expanded(
              child: _PanelContainer(
                title: '编辑器',
                showToggle: false,
                child: centerPanel,
              ),
            ),
            
            // 右栏
            if (layoutState.rightVisible) ...[
              _ResizeHandle(
                isVertical: false,
                onDrag: (delta) {
                  layoutNotifier.updateRightWidth(rightWidth - delta);
                },
              ),
              SizedBox(
                width: rightWidth,
                child: _PanelContainer(
                  title: 'AI助手',
                  onToggle: layoutNotifier.toggleRightPanel,
                  child: rightPanel,
                ),
              ),
            ],
          ],
        );
      },
    );
  }
}

/// 面板容器
class _PanelContainer extends StatelessWidget {
  final String title;
  final Widget child;
  final VoidCallback? onToggle;
  final bool showToggle;

  const _PanelContainer({
    required this.title,
    required this.child,
    this.onToggle,
    this.showToggle = true,
  });

  @override
  Widget build(BuildContext context) {
    return fluent.Card(
      child: Column(
        children: [
          // 面板标题栏
          Container(
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              color: fluent.FluentTheme.of(context).cardColor,
              border: Border(
                bottom: BorderSide(
                  color: fluent.FluentTheme.of(context).resources.dividerStrokeColorDefault,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: fluent.FluentTheme.of(context).typography.bodyStrong,
                  ),
                ),
                if (showToggle && onToggle != null)
                  fluent.IconButton(
                    icon: const fluent.Icon(fluent.FluentIcons.chrome_minimize, size: 12),
                    onPressed: onToggle,
                  ),
              ],
            ),
          ),
          
          // 面板内容
          Expanded(child: child),
        ],
      ),
    );
  }
}

/// 调整大小手柄
class _ResizeHandle extends StatefulWidget {
  final bool isVertical;
  final ValueChanged<double> onDrag;

  const _ResizeHandle({
    required this.isVertical,
    required this.onDrag,
  });

  @override
  State<_ResizeHandle> createState() => _ResizeHandleState();
}

class _ResizeHandleState extends State<_ResizeHandle> {
  bool _isHovering = false;
  bool _isDragging = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovering = true),
      onExit: (_) => setState(() => _isHovering = false),
      cursor: widget.isVertical ? SystemMouseCursors.resizeRow : SystemMouseCursors.resizeColumn,
      child: GestureDetector(
        onPanStart: (_) => setState(() => _isDragging = true),
        onPanEnd: (_) => setState(() => _isDragging = false),
        onPanUpdate: (details) {
          final delta = widget.isVertical ? details.delta.dy : details.delta.dx;
          widget.onDrag(delta);
        },
        child: Container(
          width: widget.isVertical ? double.infinity : 4,
          height: widget.isVertical ? 4 : double.infinity,
          color: _isDragging || _isHovering
              ? fluent.FluentTheme.of(context).accentColor.withValues(alpha: 0.6)
              : Colors.transparent,
        ),
      ),
    );
  }
}