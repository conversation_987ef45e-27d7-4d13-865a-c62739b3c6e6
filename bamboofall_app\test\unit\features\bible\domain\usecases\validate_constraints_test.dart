import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:logger/logger.dart';

import 'package:bamboofall_app/features/bible/domain/entities/character.dart';
import 'package:bamboofall_app/features/bible/domain/entities/location.dart';
import 'package:bamboofall_app/features/bible/domain/entities/story_bible.dart';
import 'package:bamboofall_app/features/bible/domain/repositories/bible_repository.dart';
import 'package:bamboofall_app/features/bible/domain/usecases/validate_constraints.dart';

import 'validate_constraints_test.mocks.dart';

@GenerateMocks([BibleRepository, Logger])
void main() {
  late ValidateConstraintsUseCase useCase;
  late MockBibleRepository mockRepository;
  late MockLogger mockLogger;

  setUp(() {
    mockRepository = MockBibleRepository();
    mockLogger = MockLogger();
    useCase = ValidateConstraintsUseCase(
      repository: mockRepository,
      logger: mockLogger,
    );
  });

  group('ValidateConstraintsUseCase - Character Validation', () {
    final testCharacter = Character(
      id: 'char-1',
      name: 'Test Character',
      type: CharacterType.protagonist,
      description: 'A test character',
      appearance: const CharacterAppearance(
        age: 25,
        gender: 'Male',
        height: '180cm',
        build: 'Athletic',
        hairColor: 'Brown',
        eyeColor: 'Blue',
      ),
      personality: const CharacterPersonality(
        traits: ['Brave', 'Kind'],
        motivations: ['Save the world'],
        fears: ['Spiders'],
        goals: ['Defeat evil'],
      ),
      background: const CharacterBackground(
        birthplace: 'Village',
        family: 'Orphan',
        education: 'Self-taught',
        occupation: 'Knight',
        history: 'Found as a baby',
      ),
      status: CharacterStatus.active,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    test('should validate character successfully when no conflicts exist', () async {
      // Arrange
      when(mockRepository.searchCharacters(any)).thenAnswer((_) async => []);
      when(mockRepository.getLocationsByType(any)).thenAnswer((_) async => []);

      // Act
      final result = await useCase.validateCharacter(testCharacter);

      // Assert
      expect(result.isValid, true);
      expect(result.conflicts, isEmpty);
      expect(result.warnings, isEmpty);
    });

    test('should detect character name conflicts', () async {
      // Arrange
      final conflictingCharacter = testCharacter.copyWith(
        id: 'char-2',
        name: testCharacter.name, // Same name
      );
      when(mockRepository.searchCharacters(any)).thenAnswer((_) async => [conflictingCharacter]);

      // Act
      final result = await useCase.validateCharacter(testCharacter);

      // Assert
      expect(result.isValid, false);
      expect(result.conflicts, isNotEmpty);
      expect(result.conflicts.first.type, ConstraintViolationType.duplicateName);
    });

    test('should detect invalid character location', () async {
      // Arrange
      final characterWithLocation = testCharacter.copyWith(
        currentLocationId: 'invalid-location',
      );
      when(mockRepository.searchCharacters(any)).thenAnswer((_) async => []);
      when(mockRepository.getLocation('invalid-location')).thenAnswer((_) async => null);

      // Act
      final result = await useCase.validateCharacter(characterWithLocation);

      // Assert
      expect(result.isValid, false);
      expect(result.conflicts, isNotEmpty);
      expect(result.conflicts.first.type, ConstraintViolationType.invalidReference);
    });

    test('should validate character age constraints', () async {
      // Arrange
      final youngCharacter = testCharacter.copyWith(
        appearance: testCharacter.appearance?.copyWith(age: -5), // Invalid age
      );
      when(mockRepository.searchCharacters(any)).thenAnswer((_) async => []);

      // Act
      final result = await useCase.validateCharacter(youngCharacter);

      // Assert
      expect(result.isValid, false);
      expect(result.conflicts, isNotEmpty);
      expect(result.conflicts.first.type, ConstraintViolationType.invalidValue);
    });

    test('should generate warnings for potential issues', () async {
      // Arrange
      final characterWithLongName = testCharacter.copyWith(
        name: 'A' * 100, // Very long name
      );
      when(mockRepository.searchCharacters(any)).thenAnswer((_) async => []);

      // Act
      final result = await useCase.validateCharacter(characterWithLongName);

      // Assert
      expect(result.isValid, true); // Still valid but has warnings
      expect(result.warnings, isNotEmpty);
      expect(result.warnings.first.type, ConstraintWarningType.unusualValue);
    });
  });

  group('ValidateConstraintsUseCase - Location Validation', () {
    final testLocation = Location(
      id: 'loc-1',
      name: 'Test City',
      type: LocationType.city,
      description: 'A test city',
      geography: const LocationGeography(
        climate: 'Temperate',
        terrain: 'Plains',
        naturalResources: ['Water', 'Fertile soil'],
        landmarks: ['Central Plaza'],
      ),
      culture: const LocationCulture(
        population: 10000,
        government: 'Democracy',
        economy: 'Trade',
        religion: 'Multiple',
        customs: ['Annual festival'],
        languages: ['Common'],
      ),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    test('should validate location successfully when no conflicts exist', () async {
      // Arrange
      when(mockRepository.getLocationsByType(any)).thenAnswer((_) async => []);
      when(mockRepository.getChildLocations(any)).thenAnswer((_) async => []);

      // Act
      final result = await useCase.validateLocation(testLocation);

      // Assert
      expect(result.isValid, true);
      expect(result.conflicts, isEmpty);
    });

    test('should detect location name conflicts within same type', () async {
      // Arrange
      final conflictingLocation = testLocation.copyWith(
        id: 'loc-2',
        name: testLocation.name, // Same name and type
      );
      when(mockRepository.getLocationsByType(LocationType.city))
          .thenAnswer((_) async => [conflictingLocation]);

      // Act
      final result = await useCase.validateLocation(testLocation);

      // Assert
      expect(result.isValid, false);
      expect(result.conflicts, isNotEmpty);
      expect(result.conflicts.first.type, ConstraintViolationType.duplicateName);
    });

    test('should detect circular parent-child relationships', () async {
      // Arrange
      final parentLocation = testLocation.copyWith(
        id: 'parent-loc',
        parentLocationId: testLocation.id, // Creates circular reference
      );
      
      when(mockRepository.getLocationsByType(any)).thenAnswer((_) async => []);
      when(mockRepository.getLocation(testLocation.id)).thenAnswer((_) async => testLocation);
      when(mockRepository.getLocation('parent-loc')).thenAnswer((_) async => parentLocation);

      final locationWithParent = testLocation.copyWith(
        parentLocationId: 'parent-loc',
      );

      // Act
      final result = await useCase.validateLocation(locationWithParent);

      // Assert
      expect(result.isValid, false);
      expect(result.conflicts, isNotEmpty);
      expect(result.conflicts.first.type, ConstraintViolationType.circularReference);
    });

    test('should validate location hierarchy depth', () async {
      // Arrange
      final deepLocation = testLocation.copyWith(
        parentLocationId: 'deep-parent',
      );
      
      // Create a deep hierarchy chain
      final locations = List.generate(10, (index) => testLocation.copyWith(
        id: 'loc-$index',
        parentLocationId: index > 0 ? 'loc-${index - 1}' : null,
      ));
      
      when(mockRepository.getLocationsByType(any)).thenAnswer((_) async => []);
      for (int i = 0; i < locations.length; i++) {
        when(mockRepository.getLocation('loc-$i')).thenAnswer((_) async => locations[i]);
      }

      // Act
      final result = await useCase.validateLocation(deepLocation);

      // Assert
      expect(result.warnings, isNotEmpty);
      expect(result.warnings.first.type, ConstraintWarningType.deepHierarchy);
    });

    test('should validate population constraints for location types', () async {
      // Arrange
      final invalidVillage = testLocation.copyWith(
        type: LocationType.village,
        culture: testLocation.culture?.copyWith(
          population: 100000, // Too large for a village
        ),
      );
      
      when(mockRepository.getLocationsByType(any)).thenAnswer((_) async => []);

      // Act
      final result = await useCase.validateLocation(invalidVillage);

      // Assert
      expect(result.isValid, false);
      expect(result.conflicts, isNotEmpty);
      expect(result.conflicts.first.type, ConstraintViolationType.invalidValue);
    });
  });

  group('ValidateConstraintsUseCase - Story Bible Validation', () {
    final testStoryBible = StoryBible(
      id: 'bible-1',
      projectId: 'project-1',
      title: 'Test Story',
      genre: 'Fantasy',
      theme: 'Good vs Evil',
      setting: 'Medieval world',
      tone: 'Epic',
      targetAudience: 'Young Adult',
      synopsis: 'A story about heroes',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    test('should validate story bible successfully', () async {
      // Arrange
      when(mockRepository.getStoryBibleByProjectId(any)).thenAnswer((_) async => null);

      // Act
      final result = await useCase.validateStoryBible(testStoryBible);

      // Assert
      expect(result.isValid, true);
      expect(result.conflicts, isEmpty);
    });

    test('should detect duplicate story bible for project', () async {
      // Arrange
      final existingBible = testStoryBible.copyWith(
        id: 'bible-2',
        projectId: testStoryBible.projectId, // Same project
      );
      when(mockRepository.getStoryBibleByProjectId(testStoryBible.projectId))
          .thenAnswer((_) async => existingBible);

      // Act
      final result = await useCase.validateStoryBible(testStoryBible);

      // Assert
      expect(result.isValid, false);
      expect(result.conflicts, isNotEmpty);
      expect(result.conflicts.first.type, ConstraintViolationType.duplicateProjectBible);
    });

    test('should validate required fields', () async {
      // Arrange
      final incompleteStoryBible = testStoryBible.copyWith(
        title: '', // Empty title
        genre: '', // Empty genre
      );
      when(mockRepository.getStoryBibleByProjectId(any)).thenAnswer((_) async => null);

      // Act
      final result = await useCase.validateStoryBible(incompleteStoryBible);

      // Assert
      expect(result.isValid, false);
      expect(result.conflicts.length, greaterThanOrEqualTo(2)); // Title and genre
      expect(result.conflicts.any((c) => c.type == ConstraintViolationType.missingRequiredField), true);
    });
  });

  group('ValidateConstraintsUseCase - Cross-Entity Validation', () {
    test('should validate character-location relationships', () async {
      // Arrange
      final character = Character(
        id: 'char-1',
        name: 'Test Character',
        type: CharacterType.protagonist,
        currentLocationId: 'loc-1',
        status: CharacterStatus.active,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final location = Location(
        id: 'loc-1',
        name: 'Test Location',
        type: LocationType.city,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      when(mockRepository.getLocation('loc-1')).thenAnswer((_) async => location);
      when(mockRepository.searchCharacters(any)).thenAnswer((_) async => []);

      // Act
      final result = await useCase.validateCharacterLocationRelationship(character);

      // Assert
      expect(result.isValid, true);
      expect(result.conflicts, isEmpty);
    });

    test('should detect orphaned character references', () async {
      // Arrange
      final character = Character(
        id: 'char-1',
        name: 'Test Character',
        type: CharacterType.protagonist,
        currentLocationId: 'non-existent-location',
        status: CharacterStatus.active,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      when(mockRepository.getLocation('non-existent-location')).thenAnswer((_) async => null);
      when(mockRepository.searchCharacters(any)).thenAnswer((_) async => []);

      // Act
      final result = await useCase.validateCharacterLocationRelationship(character);

      // Assert
      expect(result.isValid, false);
      expect(result.conflicts, isNotEmpty);
      expect(result.conflicts.first.type, ConstraintViolationType.orphanedReference);
    });
  });

  group('ValidateConstraintsUseCase - Batch Validation', () {
    test('should validate multiple entities in batch', () async {
      // Arrange
      final characters = [
        Character(
          id: 'char-1',
          name: 'Character 1',
          type: CharacterType.protagonist,
          status: CharacterStatus.active,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Character(
          id: 'char-2',
          name: 'Character 2',
          type: CharacterType.antagonist,
          status: CharacterStatus.active,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      when(mockRepository.searchCharacters(any)).thenAnswer((_) async => []);

      // Act
      final result = await useCase.validateBatch(characters: characters);

      // Assert
      expect(result.overallValid, true);
      expect(result.characterResults.length, 2);
      expect(result.characterResults.every((r) => r.isValid), true);
    });

    test('should report batch validation summary', () async {
      // Arrange
      final validCharacter = Character(
        id: 'char-1',
        name: 'Valid Character',
        type: CharacterType.protagonist,
        status: CharacterStatus.active,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final invalidCharacter = Character(
        id: 'char-2',
        name: '', // Invalid empty name
        type: CharacterType.protagonist,
        status: CharacterStatus.active,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      when(mockRepository.searchCharacters(any)).thenAnswer((_) async => []);

      // Act
      final result = await useCase.validateBatch(
        characters: [validCharacter, invalidCharacter],
      );

      // Assert
      expect(result.overallValid, false);
      expect(result.totalEntities, 2);
      expect(result.validEntities, 1);
      expect(result.invalidEntities, 1);
      expect(result.totalConflicts, greaterThan(0));
    });
  });
}