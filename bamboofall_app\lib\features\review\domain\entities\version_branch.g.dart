// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'version_branch.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VersionBranchImpl _$$VersionBranchImplFromJson(Map<String, dynamic> json) =>
    _$VersionBranchImpl(
      version: ChapterVersion.fromJson(json['version'] as Map<String, dynamic>),
      children:
          (json['children'] as List<dynamic>?)
              ?.map((e) => VersionBranch.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      depth: (json['depth'] as num?)?.toInt() ?? 0,
      isExpanded: json['isExpanded'] as bool? ?? true,
      branchType:
          $enumDecodeNullable(_$BranchTypeEnumMap, json['branchType']) ??
          BranchType.main,
      branchColor: json['branchColor'] as String?,
    );

Map<String, dynamic> _$$VersionBranchImplToJson(_$VersionBranchImpl instance) =>
    <String, dynamic>{
      'version': instance.version,
      'children': instance.children,
      'depth': instance.depth,
      'isExpanded': instance.isExpanded,
      'branchType': _$BranchTypeEnumMap[instance.branchType]!,
      'branchColor': instance.branchColor,
    };

const _$BranchTypeEnumMap = {
  BranchType.main: 'main',
  BranchType.feature: 'feature',
  BranchType.hotfix: 'hotfix',
  BranchType.experimental: 'experimental',
  BranchType.merge: 'merge',
};
