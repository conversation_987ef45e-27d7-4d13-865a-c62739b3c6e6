import 'package:flutter/material.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

/// 编辑器工具栏
/// 提供格式化按钮和编辑功能
class EditorToolbar extends StatelessWidget {
  final VoidCallback? onBoldPressed;
  final VoidCallback? onItalicPressed;
  final VoidCallback? onUnderlinePressed;
  final VoidCallback? onStrikethroughPressed;
  final VoidCallback? onHeader1Pressed;
  final VoidCallback? onHeader2Pressed;
  final VoidCallback? onHeader3Pressed;
  final VoidCallback? onBulletListPressed;
  final VoidCallback? onNumberedListPressed;
  final VoidCallback? onQuotePressed;
  final VoidCallback? onCodePressed;
  final VoidCallback? onLinkPressed;
  final VoidCallback? onImagePressed;
  final VoidCallback? onTablePressed;
  final VoidCallback? onPreviewToggled;
  final bool isPreviewMode;

  const EditorToolbar({
    super.key,
    this.onBoldPressed,
    this.onItalicPressed,
    this.onUnderlinePressed,
    this.onStrikethroughPressed,
    this.onHeader1Pressed,
    this.onHeader2Pressed,
    this.onHeader3Pressed,
    this.onBulletListPressed,
    this.onNumberedListPressed,
    this.onQuotePressed,
    this.onCodePressed,
    this.onLinkPressed,
    this.onImagePressed,
    this.onTablePressed,
    this.onPreviewToggled,
    this.isPreviewMode = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: fluent.FluentTheme.of(context).resources.layerFillColorDefault,
        border: Border(
          bottom: BorderSide(
            color: fluent.FluentTheme.of(context).resources.dividerStrokeColorDefault,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 文本格式化按钮组
          _buildButtonGroup([
            _ToolbarButton(
              icon: fluent.FluentIcons.bold,
              tooltip: '粗体 (Ctrl+B)',
              onPressed: onBoldPressed,
            ),
            _ToolbarButton(
              icon: fluent.FluentIcons.italic,
              tooltip: '斜体 (Ctrl+I)',
              onPressed: onItalicPressed,
            ),
            _ToolbarButton(
              icon: fluent.FluentIcons.underline,
              tooltip: '下划线 (Ctrl+U)',
              onPressed: onUnderlinePressed,
            ),
            _ToolbarButton(
              icon: fluent.FluentIcons.strikethrough,
              tooltip: '删除线',
              onPressed: onStrikethroughPressed,
            ),
          ]),
          
          _buildDivider(context),
          
          // 标题按钮组
          _buildButtonGroup([
            _ToolbarButton(
              icon: fluent.FluentIcons.header1,
              tooltip: '一级标题',
              onPressed: onHeader1Pressed,
            ),
            _ToolbarButton(
              icon: fluent.FluentIcons.header2,
              tooltip: '二级标题',
              onPressed: onHeader2Pressed,
            ),
            _ToolbarButton(
              icon: fluent.FluentIcons.header3,
              tooltip: '三级标题',
              onPressed: onHeader3Pressed,
            ),
          ]),
          
          _buildDivider(context),
          
          // 列表按钮组
          _buildButtonGroup([
            _ToolbarButton(
              icon: fluent.FluentIcons.bulleted_list,
              tooltip: '无序列表',
              onPressed: onBulletListPressed,
            ),
            _ToolbarButton(
              icon: fluent.FluentIcons.numbered_list,
              tooltip: '有序列表',
              onPressed: onNumberedListPressed,
            ),
          ]),
          
          _buildDivider(context),
          
          // 其他格式按钮组
          _buildButtonGroup([
            _ToolbarButton(
              icon: fluent.FluentIcons.text_field,
              tooltip: '引用',
              onPressed: onQuotePressed,
            ),
            _ToolbarButton(
              icon: fluent.FluentIcons.code,
              tooltip: '代码',
              onPressed: onCodePressed,
            ),
          ]),
          
          _buildDivider(context),
          
          // 插入内容按钮组
          _buildButtonGroup([
            _ToolbarButton(
              icon: fluent.FluentIcons.link,
              tooltip: '插入链接',
              onPressed: onLinkPressed,
            ),
            _ToolbarButton(
              icon: fluent.FluentIcons.photo2,
              tooltip: '插入图片',
              onPressed: onImagePressed,
            ),
            _ToolbarButton(
              icon: fluent.FluentIcons.table,
              tooltip: '插入表格',
              onPressed: onTablePressed,
            ),
          ]),
          
          const Spacer(),
          
          // 预览切换按钮
          _buildDivider(context),
          _ToolbarButton(
            icon: isPreviewMode ? fluent.FluentIcons.edit : fluent.FluentIcons.preview,
            tooltip: isPreviewMode ? '编辑模式' : '预览模式',
            onPressed: onPreviewToggled,
            isActive: isPreviewMode,
          ),
        ],
      ),
    );
  }

  Widget _buildButtonGroup(List<Widget> buttons) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: buttons,
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Container(
      width: 1,
      height: 24,
      margin: const EdgeInsets.symmetric(horizontal: 8),
      color: fluent.FluentTheme.of(context).resources.dividerStrokeColorDefault,
    );
  }
}

/// 工具栏按钮
class _ToolbarButton extends StatefulWidget {
  final IconData icon;
  final String tooltip;
  final VoidCallback? onPressed;
  final bool isActive;

  const _ToolbarButton({
    required this.icon,
    required this.tooltip,
    this.onPressed,
    this.isActive = false,
  });

  @override
  State<_ToolbarButton> createState() => _ToolbarButtonState();
}

class _ToolbarButtonState extends State<_ToolbarButton> {
  bool _isHovering = false;

  @override
  Widget build(BuildContext context) {
    final theme = fluent.FluentTheme.of(context);
    
    return Tooltip(
      message: widget.tooltip,
      child: MouseRegion(
        onEnter: (_) => setState(() => _isHovering = true),
        onExit: (_) => setState(() => _isHovering = false),
        child: GestureDetector(
          onTap: widget.onPressed,
          child: Container(
            width: 32,
            height: 32,
            margin: const EdgeInsets.symmetric(horizontal: 2),
            decoration: BoxDecoration(
              color: _getBackgroundColor(theme),
              borderRadius: BorderRadius.circular(4),
              border: widget.isActive
                  ? Border.all(
                      color: theme.accentColor,
                      width: 1,
                    )
                  : null,
            ),
            child: Icon(
              widget.icon,
              size: 16,
              color: _getIconColor(theme),
            ),
          ),
        ),
      ),
    );
  }

  Color _getBackgroundColor(fluent.FluentThemeData theme) {
    if (widget.isActive) {
      return theme.accentColor.withValues(alpha: 0.1);
    } else if (_isHovering) {
      return theme.resources.subtleFillColorSecondary;
    } else {
      return Colors.transparent;
    }
  }

  Color _getIconColor(fluent.FluentThemeData theme) {
    if (widget.onPressed == null) {
      return theme.resources.textFillColorDisabled;
    } else if (widget.isActive) {
      return theme.accentColor;
    } else {
      return theme.resources.textFillColorPrimary;
    }
  }
}

/// 工具栏分隔符
class ToolbarDivider extends StatelessWidget {
  const ToolbarDivider({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1,
      height: 24,
      margin: const EdgeInsets.symmetric(horizontal: 8),
      color: fluent.FluentTheme.of(context).resources.dividerStrokeColorDefault,
    );
  }
}

/// 工具栏下拉菜单按钮
class ToolbarDropdownButton extends StatelessWidget {
  final IconData icon;
  final String tooltip;
  final List<ToolbarMenuItem> items;

  const ToolbarDropdownButton({
    super.key,
    required this.icon,
    required this.tooltip,
    required this.items,
  });

  @override
  Widget build(BuildContext context) {
    return fluent.DropDownButton(
      title: Icon(icon, size: 16),
      items: items.map((item) {
        return fluent.MenuFlyoutItem(
          text: Text(item.title),
          onPressed: item.onPressed,
          leading: item.icon != null ? Icon(item.icon, size: 16) : null,
        );
      }).toList(),
    );
  }
}

/// 工具栏菜单项
class ToolbarMenuItem {
  final String title;
  final IconData? icon;
  final VoidCallback? onPressed;

  const ToolbarMenuItem({
    required this.title,
    this.icon,
    this.onPressed,
  });
}