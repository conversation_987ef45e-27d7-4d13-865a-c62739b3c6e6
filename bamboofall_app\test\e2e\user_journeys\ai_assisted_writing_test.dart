import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import 'package:bamboofall_app/main.dart' as app;

/// AI辅助写作端到端测试
/// 测试AI集成功能在真实用户场景中的表现
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('AI Assisted Writing E2E Tests', () {
    testWidgets('Complete AI-powered content generation workflow', (WidgetTester tester) async {
      // 启动应用
      app.main();
      await tester.pumpAndSettle();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // ==================== 项目设置 ====================
      
      // 创建新项目
      await tester.tap(find.byKey(const Key('create_project_button')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('project_name_field')), 'AI辅助写作测试');
      await tester.enterText(find.byKey(const Key('project_description_field')), '测试AI辅助功能的项目');
      await tester.tap(find.by<PERSON>ey(const Key('project_type_novel')));
      await tester.tap(find.byKey(const Key('confirm_create_project')));
      await tester.pumpAndSettle();

      // ==================== AI配置和设置 ====================
      
      // 打开AI设置
      await tester.tap(find.byKey(const Key('ai_assistant_panel')));
      await tester.pumpAndSettle();

      await tester.tap(find.byKey(const Key('ai_settings_button')));
      await tester.pumpAndSettle();

      // 配置API密钥（模拟）
      await tester.enterText(find.byKey(const Key('openai_api_key_field')), 'sk-test-key-for-testing');
      await tester.tap(find.byKey(const Key('validate_api_key_button')));
      await tester.pumpAndSettle();

      // 验证API密钥验证成功
      expect(find.text('API密钥验证成功'), findsOneWidget);

      // 选择AI模型
      await tester.tap(find.byKey(const Key('ai_model_dropdown')));
      await tester.pumpAndSettle();
      await tester.tap(find.text('GPT-4'));
      await tester.pumpAndSettle();

      // 调整AI参数
      await tester.drag(find.byKey(const Key('temperature_slider')), const Offset(20, 0));
      await tester.drag(find.byKey(const Key('max_tokens_slider')), const Offset(30, 0));
      await tester.pumpAndSettle();

      // 保存AI设置
      await tester.tap(find.byKey(const Key('save_ai_settings_button')));
      await tester.pumpAndSettle();

      expect(find.text('AI设置已保存'), findsOneWidget);

      // ==================== 基础内容生成 ====================
      
      // 创建章节
      await tester.tap(find.byKey(const Key('add_chapter_button')));
      await tester.pumpAndSettle();
      await tester.enterText(find.byKey(const Key('chapter_title_field')), '第一章：AI的力量');
      await tester.tap(find.byKey(const Key('confirm_add_chapter')));
      await tester.pumpAndSettle();

      // 使用AI生成章节开头
      await tester.enterText(
        find.byKey(const Key('ai_prompt_input')),
        '请为一个科幻小说写一个引人入胜的开头，主题是人工智能与人类的关系。'
      );

      await tester.tap(find.byKey(const Key('generate_content_button')));
      await tester.pumpAndSettle();

      // 等待AI生成内容
      await _waitForAIGeneration(tester);

      // 验证生成的内容
      expect(find.byKey(const Key('ai_generated_content')), findsOneWidget);
      expect(find.textContaining('人工智能'), findsOneWidget);

      // 插入生成的内容
      await tester.tap(find.byKey(const Key('insert_ai_content_button')));
      await tester.pumpAndSettle();

      // 验证内容已插入编辑器
      expect(find.textContaining('人工智能'), findsOneWidget);

      // ==================== 角色生成和发展 ====================
      
      // 切换到圣经管理
      await tester.tap(find.byKey(const Key('bible_management_tab')));
      await tester.pumpAndSettle();

      // 使用AI生成角色
      await tester.tap(find.byKey(const Key('ai_generate_character_button')));
      await tester.pumpAndSettle();

      await tester.enterText(
        find.byKey(const Key('character_generation_prompt')),
        '创建一个复杂的科幻小说主角，是一名AI研究员，有着复杂的道德困境。'
      );

      await tester.tap(find.byKey(const Key('generate_character_button')));
      await tester.pumpAndSettle();

      await _waitForAIGeneration(tester);

      // 验证角色生成结果
      expect(find.byKey(const Key('generated_character_card')), findsOneWidget);
      expect(find.textContaining('研究员'), findsOneWidget);

      // 编辑和完善AI生成的角色
      await tester.tap(find.byKey(const Key('edit_generated_character')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('character_name_field')), '艾莉克斯·陈');
      await tester.enterText(find.byKey(const Key('character_age_field')), '35');

      // 使用AI生成更详细的背景故事
      await tester.tap(find.byKey(const Key('ai_expand_background_button')));
      await tester.pumpAndSettle();

      await _waitForAIGeneration(tester);

      // 保存角色
      await tester.tap(find.byKey(const Key('save_character_button')));
      await tester.pumpAndSettle();

      expect(find.text('角色保存成功'), findsOneWidget);

      // ==================== 情节发展辅助 ====================
      
      // 返回写作工作台
      await tester.tap(find.byKey(const Key('writing_workspace_tab')));
      await tester.pumpAndSettle();

      // 使用AI分析当前情节
      await tester.tap(find.byKey(const Key('ai_plot_analysis_button')));
      await tester.pumpAndSettle();

      await _waitForAIGeneration(tester);

      // 验证情节分析结果
      expect(find.byKey(const Key('plot_analysis_result')), findsOneWidget);
      expect(find.textContaining('情节建议'), findsOneWidget);

      // 根据AI建议生成下一段内容
      await tester.tap(find.byKey(const Key('generate_next_scene_button')));
      await tester.pumpAndSettle();

      await _waitForAIGeneration(tester);

      // 验证场景生成
      expect(find.byKey(const Key('generated_scene_content')), findsOneWidget);

      // 选择性插入内容
      await tester.tap(find.byKey(const Key('selective_insert_button')));
      await tester.pumpAndSettle();

      // 选择要插入的段落
      await tester.tap(find.byKey(const Key('select_paragraph_1')));
      await tester.tap(find.byKey(const Key('select_paragraph_3')));
      await tester.tap(find.byKey(const Key('confirm_selective_insert')));
      await tester.pumpAndSettle();

      // ==================== 对话生成 ====================
      
      // 生成角色对话
      await tester.enterText(
        find.byKey(const Key('ai_prompt_input')),
        '为艾莉克斯·陈和她的AI助手创建一段关于道德困境的对话。'
      );

      await tester.tap(find.byKey(const Key('generate_dialogue_button')));
      await tester.pumpAndSettle();

      await _waitForAIGeneration(tester);

      // 验证对话生成
      expect(find.textContaining('艾莉克斯'), findsOneWidget);
      expect(find.textContaining('"'), findsWidgets); // 对话标记

      // 调整对话风格
      await tester.tap(find.byKey(const Key('dialogue_style_button')));
      await tester.pumpAndSettle();

      await tester.tap(find.byKey(const Key('formal_style_option')));
      await tester.tap(find.byKey(const Key('regenerate_dialogue_button')));
      await tester.pumpAndSettle();

      await _waitForAIGeneration(tester);

      // ==================== 描述增强 ====================
      
      // 选择需要增强的文本
      await tester.longPress(find.textContaining('实验室'));
      await tester.pumpAndSettle();

      // 使用AI增强描述
      await tester.tap(find.byKey(const Key('ai_enhance_description_button')));
      await tester.pumpAndSettle();

      await _waitForAIGeneration(tester);

      // 验证描述增强结果
      expect(find.byKey(const Key('enhanced_description_preview')), findsOneWidget);

      // 应用增强的描述
      await tester.tap(find.byKey(const Key('apply_enhanced_description')));
      await tester.pumpAndSettle();

      // ==================== 风格一致性检查 ====================
      
      // 运行AI风格分析
      await tester.tap(find.byKey(const Key('ai_style_analysis_button')));
      await tester.pumpAndSettle();

      await _waitForAIGeneration(tester);

      // 验证风格分析结果
      expect(find.byKey(const Key('style_analysis_report')), findsOneWidget);
      expect(find.textContaining('风格一致性'), findsOneWidget);

      // 查看风格建议
      await tester.tap(find.byKey(const Key('view_style_suggestions')));
      await tester.pumpAndSettle();

      expect(find.byKey(const Key('style_suggestions_list')), findsOneWidget);

      // 应用风格修正
      await tester.tap(find.byKey(const Key('apply_style_correction_1')));
      await tester.pumpAndSettle();

      // ==================== 创意激发 ====================
      
      // 使用AI创意助手
      await tester.tap(find.byKey(const Key('ai_creativity_assistant')));
      await tester.pumpAndSettle();

      // 请求情节转折建议
      await tester.tap(find.byKey(const Key('plot_twist_suggestions')));
      await tester.pumpAndSettle();

      await _waitForAIGeneration(tester);

      // 验证创意建议
      expect(find.byKey(const Key('creativity_suggestions')), findsOneWidget);
      expect(find.textContaining('转折'), findsOneWidget);

      // 选择一个建议并展开
      await tester.tap(find.byKey(const Key('expand_suggestion_1')));
      await tester.pumpAndSettle();

      await _waitForAIGeneration(tester);

      // 验证详细建议
      expect(find.byKey(const Key('detailed_suggestion')), findsOneWidget);

      // ==================== 多语言支持 ====================
      
      // 切换AI语言设置
      await tester.tap(find.byKey(const Key('ai_language_settings')));
      await tester.pumpAndSettle();

      await tester.tap(find.byKey(const Key('language_english')));
      await tester.pumpAndSettle();

      // 用英文生成内容
      await tester.enterText(
        find.byKey(const Key('ai_prompt_input')),
        'Generate a dramatic scene in English for the AI research lab.'
      );

      await tester.tap(find.byKey(const Key('generate_content_button')));
      await tester.pumpAndSettle();

      await _waitForAIGeneration(tester);

      // 验证英文内容生成
      expect(find.textContaining('laboratory'), findsOneWidget);

      // 翻译回中文
      await tester.tap(find.byKey(const Key('translate_to_chinese_button')));
      await tester.pumpAndSettle();

      await _waitForAIGeneration(tester);

      // ==================== AI使用统计 ====================
      
      // 查看AI使用统计
      await tester.tap(find.byKey(const Key('ai_usage_statistics')));
      await tester.pumpAndSettle();

      // 验证统计信息
      expect(find.text('AI使用统计'), findsOneWidget);
      expect(find.textContaining('生成次数'), findsOneWidget);
      expect(find.textContaining('Token使用量'), findsOneWidget);
      expect(find.textContaining('成本估算'), findsOneWidget);

      // 查看详细使用历史
      await tester.tap(find.byKey(const Key('detailed_usage_history')));
      await tester.pumpAndSettle();

      expect(find.byKey(const Key('usage_history_list')), findsOneWidget);

      // ==================== 批量处理 ====================
      
      // 批量生成章节大纲
      await tester.tap(find.byKey(const Key('batch_generation_button')));
      await tester.pumpAndSettle();

      await tester.enterText(
        find.byKey(const Key('batch_prompt_input')),
        '为接下来的5个章节生成详细大纲，每章聚焦不同的AI伦理问题。'
      );

      await tester.tap(find.byKey(const Key('start_batch_generation')));
      await tester.pumpAndSettle();

      // 等待批量生成完成
      await _waitForBatchGeneration(tester);

      // 验证批量生成结果
      expect(find.byKey(const Key('batch_results_list')), findsOneWidget);
      expect(find.textContaining('第二章'), findsOneWidget);
      expect(find.textContaining('第三章'), findsOneWidget);

      // 选择性应用结果
      await tester.tap(find.byKey(const Key('select_batch_result_2')));
      await tester.tap(find.byKey(const Key('select_batch_result_4')));
      await tester.tap(find.byKey(const Key('apply_selected_results')));
      await tester.pumpAndSettle();

      print('✅ AI辅助写作完整工作流程测试通过！');
    });

    testWidgets('AI model switching and performance comparison', (WidgetTester tester) async {
      // 启动应用
      app.main();
      await tester.pumpAndSettle();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // 创建测试项目
      await _createTestProject(tester, 'AI模型对比测试');

      // ==================== 模型配置 ====================
      
      // 配置多个AI模型
      await tester.tap(find.byKey(const Key('ai_settings_button')));
      await tester.pumpAndSettle();

      // 配置GPT-4
      await tester.tap(find.byKey(const Key('add_ai_model_button')));
      await tester.pumpAndSettle();
      await tester.tap(find.byKey(const Key('model_type_gpt4')));
      await tester.enterText(find.byKey(const Key('api_key_field')), 'sk-gpt4-test-key');
      await tester.tap(find.byKey(const Key('save_model_config')));
      await tester.pumpAndSettle();

      // 配置Claude
      await tester.tap(find.byKey(const Key('add_ai_model_button')));
      await tester.pumpAndSettle();
      await tester.tap(find.byKey(const Key('model_type_claude')));
      await tester.enterText(find.byKey(const Key('api_key_field')), 'claude-test-key');
      await tester.tap(find.byKey(const Key('save_model_config')));
      await tester.pumpAndSettle();

      // 配置Gemini
      await tester.tap(find.byKey(const Key('add_ai_model_button')));
      await tester.pumpAndSettle();
      await tester.tap(find.byKey(const Key('model_type_gemini')));
      await tester.enterText(find.byKey(const Key('api_key_field')), 'gemini-test-key');
      await tester.tap(find.byKey(const Key('save_model_config')));
      await tester.pumpAndSettle();

      // ==================== 模型性能对比 ====================
      
      const testPrompt = '写一个关于时间旅行的短篇故事开头，要求有悬念和吸引力。';

      // 使用GPT-4生成
      await tester.tap(find.byKey(const Key('select_model_gpt4')));
      await tester.pumpAndSettle();
      
      await tester.enterText(find.byKey(const Key('ai_prompt_input')), testPrompt);
      await tester.tap(find.byKey(const Key('generate_content_button')));
      await tester.pumpAndSettle();
      await _waitForAIGeneration(tester);

      // 保存GPT-4结果
      await tester.tap(find.byKey(const Key('save_generation_result')));
      await tester.enterText(find.byKey(const Key('result_name_field')), 'GPT-4结果');
      await tester.tap(find.byKey(const Key('confirm_save_result')));
      await tester.pumpAndSettle();

      // 使用Claude生成
      await tester.tap(find.byKey(const Key('select_model_claude')));
      await tester.pumpAndSettle();
      
      await tester.tap(find.byKey(const Key('generate_content_button')));
      await tester.pumpAndSettle();
      await _waitForAIGeneration(tester);

      // 保存Claude结果
      await tester.tap(find.byKey(const Key('save_generation_result')));
      await tester.enterText(find.byKey(const Key('result_name_field')), 'Claude结果');
      await tester.tap(find.byKey(const Key('confirm_save_result')));
      await tester.pumpAndSettle();

      // 使用Gemini生成
      await tester.tap(find.byKey(const Key('select_model_gemini')));
      await tester.pumpAndSettle();
      
      await tester.tap(find.byKey(const Key('generate_content_button')));
      await tester.pumpAndSettle();
      await _waitForAIGeneration(tester);

      // 保存Gemini结果
      await tester.tap(find.byKey(const Key('save_generation_result')));
      await tester.enterText(find.byKey(const Key('result_name_field')), 'Gemini结果');
      await tester.tap(find.byKey(const Key('confirm_save_result')));
      await tester.pumpAndSettle();

      // ==================== 结果对比分析 ====================
      
      // 打开结果对比界面
      await tester.tap(find.byKey(const Key('compare_results_button')));
      await tester.pumpAndSettle();

      // 验证所有结果都显示
      expect(find.text('GPT-4结果'), findsOneWidget);
      expect(find.text('Claude结果'), findsOneWidget);
      expect(find.text('Gemini结果'), findsOneWidget);

      // 查看性能指标
      await tester.tap(find.byKey(const Key('performance_metrics_tab')));
      await tester.pumpAndSettle();

      // 验证性能指标显示
      expect(find.textContaining('生成时间'), findsOneWidget);
      expect(find.textContaining('Token使用量'), findsOneWidget);
      expect(find.textContaining('成本'), findsOneWidget);

      // 选择最佳结果
      await tester.tap(find.byKey(const Key('select_best_result_gpt4')));
      await tester.tap(find.byKey(const Key('use_selected_result')));
      await tester.pumpAndSettle();

      expect(find.text('已应用选中的结果'), findsOneWidget);

      print('✅ AI模型切换和性能对比测试通过！');
    });

    testWidgets('AI error handling and fallback mechanisms', (WidgetTester tester) async {
      // 启动应用
      app.main();
      await tester.pumpAndSettle();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // 创建测试项目
      await _createTestProject(tester, 'AI错误处理测试');

      // ==================== API错误处理 ====================
      
      // 配置无效的API密钥
      await tester.tap(find.byKey(const Key('ai_settings_button')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('openai_api_key_field')), 'invalid-key');
      await tester.tap(find.byKey(const Key('validate_api_key_button')));
      await tester.pumpAndSettle();

      // 验证错误处理
      expect(find.text('API密钥无效'), findsOneWidget);
      expect(find.byKey(const Key('api_error_details')), findsOneWidget);

      // 测试网络错误处理
      await tester.tap(find.byKey(const Key('simulate_network_error')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('ai_prompt_input')), '测试网络错误');
      await tester.tap(find.byKey(const Key('generate_content_button')));
      await tester.pumpAndSettle();

      // 验证网络错误处理
      expect(find.text('网络连接失败'), findsOneWidget);
      expect(find.byKey(const Key('retry_button')), findsOneWidget);

      // 测试重试机制
      await tester.tap(find.byKey(const Key('retry_button')));
      await tester.pumpAndSettle();

      // ==================== 配额限制处理 ====================
      
      // 模拟API配额用完
      await tester.tap(find.byKey(const Key('simulate_quota_exceeded')));
      await tester.pumpAndSettle();

      await tester.tap(find.byKey(const Key('generate_content_button')));
      await tester.pumpAndSettle();

      // 验证配额错误处理
      expect(find.text('API配额已用完'), findsOneWidget);
      expect(find.byKey(const Key('upgrade_plan_button')), findsOneWidget);
      expect(find.byKey(const Key('use_fallback_model_button')), findsOneWidget);

      // 测试降级到备用模型
      await tester.tap(find.byKey(const Key('use_fallback_model_button')));
      await tester.pumpAndSettle();

      expect(find.text('已切换到备用模型'), findsOneWidget);

      // ==================== 内容过滤处理 ====================
      
      // 测试敏感内容过滤
      await tester.enterText(
        find.byKey(const Key('ai_prompt_input')),
        '包含敏感内容的提示词'
      );

      await tester.tap(find.byKey(const Key('generate_content_button')));
      await tester.pumpAndSettle();

      // 验证内容过滤
      expect(find.text('内容被过滤'), findsOneWidget);
      expect(find.byKey(const Key('content_filter_explanation')), findsOneWidget);

      // 提供替代建议
      expect(find.byKey(const Key('alternative_suggestions')), findsOneWidget);

      print('✅ AI错误处理和降级机制测试通过！');
    });
  });
}

/// 等待AI生成完成
Future<void> _waitForAIGeneration(WidgetTester tester) async {
  // 等待生成开始
  await tester.pump(const Duration(milliseconds: 500));
  
  // 等待生成完成（最多30秒）
  for (int i = 0; i < 60; i++) {
    await tester.pump(const Duration(milliseconds: 500));
    
    // 检查是否有生成完成的标志
    if (tester.any(find.byKey(const Key('ai_generation_complete'))) ||
        tester.any(find.byKey(const Key('ai_generated_content')))) {
      break;
    }
    
    // 检查是否有错误
    if (tester.any(find.textContaining('错误')) ||
        tester.any(find.textContaining('失败'))) {
      break;
    }
  }
  
  await tester.pumpAndSettle();
}

/// 等待批量生成完成
Future<void> _waitForBatchGeneration(WidgetTester tester) async {
  // 等待批量生成完成（最多2分钟）
  for (int i = 0; i < 240; i++) {
    await tester.pump(const Duration(milliseconds: 500));
    
    if (tester.any(find.byKey(const Key('batch_generation_complete')))) {
      break;
    }
  }
  
  await tester.pumpAndSettle();
}

/// 创建测试项目
Future<void> _createTestProject(WidgetTester tester, String projectName) async {
  await tester.tap(find.byKey(const Key('create_project_button')));
  await tester.pumpAndSettle();

  await tester.enterText(find.byKey(const Key('project_name_field')), projectName);
  await tester.tap(find.byKey(const Key('project_type_novel')));
  await tester.tap(find.byKey(const Key('confirm_create_project')));
  await tester.pumpAndSettle();
}