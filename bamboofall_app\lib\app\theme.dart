import 'package:flutter/material.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

/// 应用主题配置
class AppTheme {
  // 私有构造函数，防止实例化
  AppTheme._();

  // 主色调
  static const Color primaryColor = Color(0xFF2E7D32); // 竹绿色
  static const Color secondaryColor = Color(0xFF4CAF50); // 浅绿色
  static const Color accentColor = Color(0xFF81C784); // 淡绿色

  // 语义颜色
  static const Color successColor = Color(0xFF4CAF50);
  static const Color warningColor = Color(0xFFFF9800);
  static const Color errorColor = Color(0xFFF44336);
  static const Color infoColor = Color(0xFF2196F3);

  // 中性颜色
  static const Color surfaceLight = Color(0xFFFAFAFA);
  static const Color surfaceDark = Color(0xFF121212);
  static const Color backgroundLight = Color(0xFFFFFFFF);
  static const Color backgroundDark = Color(0xFF1E1E1E);

  /// 获取亮色主题
  static fluent.FluentThemeData get lightTheme {
    return fluent.FluentThemeData(
      brightness: Brightness.light,
      accentColor: fluent.AccentColor.swatch({
        'darkest': primaryColor.withValues(alpha: 0.9),
        'darker': primaryColor.withValues(alpha: 0.8),
        'dark': primaryColor.withValues(alpha: 0.7),
        'normal': primaryColor,
        'light': primaryColor.withValues(alpha: 0.6),
        'lighter': primaryColor.withValues(alpha: 0.4),
        'lightest': primaryColor.withValues(alpha: 0.2),
      }),
      scaffoldBackgroundColor: backgroundLight,
      cardColor: surfaceLight,
      visualDensity: VisualDensity.standard,
      typography: _buildTypography(Brightness.light),
      navigationPaneTheme: _buildNavigationPaneTheme(Brightness.light),
      buttonTheme: _buildButtonTheme(Brightness.light),
      checkboxTheme: _buildCheckboxTheme(Brightness.light),
      toggleSwitchTheme: _buildToggleSwitchTheme(Brightness.light),
      sliderTheme: _buildSliderTheme(Brightness.light),
      tooltipTheme: _buildTooltipTheme(Brightness.light),
      dividerTheme: _buildDividerTheme(Brightness.light),
      scrollbarTheme: _buildScrollbarTheme(Brightness.light),
    );
  }

  /// 获取暗色主题
  static fluent.FluentThemeData get darkTheme {
    return fluent.FluentThemeData(
      brightness: Brightness.dark,
      accentColor: fluent.AccentColor.swatch({
        'darkest': accentColor.withValues(alpha: 0.9),
        'darker': accentColor.withValues(alpha: 0.8),
        'dark': accentColor.withValues(alpha: 0.7),
        'normal': accentColor,
        'light': accentColor.withValues(alpha: 0.6),
        'lighter': accentColor.withValues(alpha: 0.4),
        'lightest': accentColor.withValues(alpha: 0.2),
      }),
      scaffoldBackgroundColor: backgroundDark,
      cardColor: surfaceDark,
      visualDensity: VisualDensity.standard,
      typography: _buildTypography(Brightness.dark),
      navigationPaneTheme: _buildNavigationPaneTheme(Brightness.dark),
      buttonTheme: _buildButtonTheme(Brightness.dark),
      checkboxTheme: _buildCheckboxTheme(Brightness.dark),
      toggleSwitchTheme: _buildToggleSwitchTheme(Brightness.dark),
      sliderTheme: _buildSliderTheme(Brightness.dark),
      tooltipTheme: _buildTooltipTheme(Brightness.dark),
      dividerTheme: _buildDividerTheme(Brightness.dark),
      scrollbarTheme: _buildScrollbarTheme(Brightness.dark),
    );
  }

  /// 构建字体主题
  static fluent.Typography _buildTypography(Brightness brightness) {
    final baseColor = brightness == Brightness.light 
        ? Colors.black87 
        : Colors.white;
    
    return fluent.Typography.fromBrightness(
      brightness: brightness,
      color: baseColor,
    );
  }

  /// 构建导航面板主题
  static fluent.NavigationPaneThemeData _buildNavigationPaneTheme(Brightness brightness) {
    return fluent.NavigationPaneThemeData(
      backgroundColor: brightness == Brightness.light 
          ? surfaceLight 
          : surfaceDark,
      highlightColor: brightness == Brightness.light
          ? primaryColor.withValues(alpha: 0.1)
          : accentColor.withValues(alpha: 0.2),
    );
  }

  /// 构建按钮主题
  static fluent.ButtonThemeData _buildButtonTheme(Brightness brightness) {
    return fluent.ButtonThemeData(
      defaultButtonStyle: fluent.ButtonStyle(
        backgroundColor: fluent.WidgetStateProperty.resolveWith((states) {
          if (states.isPressed) {
            return brightness == Brightness.light 
                ? primaryColor.withValues(alpha: 0.8) 
                : accentColor.withValues(alpha: 0.8);
          }
          if (states.isHovered) {
            return brightness == Brightness.light 
                ? primaryColor.withValues(alpha: 0.1) 
                : accentColor.withValues(alpha: 0.2);
          }
          return Colors.transparent;
        }),
        foregroundColor: fluent.WidgetStateProperty.resolveWith((states) {
          if (states.isPressed || states.isHovered) {
            return brightness == Brightness.light 
                ? primaryColor 
                : accentColor;
          }
          return brightness == Brightness.light 
              ? Colors.black87 
              : Colors.white;
        }),
      ),
    );
  }

  /// 构建复选框主题
  static fluent.CheckboxThemeData _buildCheckboxTheme(Brightness brightness) {
    return fluent.CheckboxThemeData(
      checkedDecoration: fluent.WidgetStateProperty.all(BoxDecoration(
        color: brightness == Brightness.light ? primaryColor : accentColor,
        borderRadius: BorderRadius.circular(4),
      )),
      uncheckedDecoration: fluent.WidgetStateProperty.all(BoxDecoration(
        border: Border.all(
          color: brightness == Brightness.light 
              ? Colors.grey.shade400 
              : Colors.grey.shade600,
        ),
        borderRadius: BorderRadius.circular(4),
      )),
    );
  }

  /// 构建切换开关主题
  static fluent.ToggleSwitchThemeData _buildToggleSwitchTheme(Brightness brightness) {
    return fluent.ToggleSwitchThemeData(
      // 使用基本的颜色配置，避免使用不兼容的装饰参数
    );
  }

  /// 构建滑块主题
  static fluent.SliderThemeData _buildSliderTheme(Brightness brightness) {
    return fluent.SliderThemeData(
      activeColor: WidgetStateProperty.all(brightness == Brightness.light ? primaryColor : accentColor),
      inactiveColor: WidgetStateProperty.all((brightness == Brightness.light ? primaryColor : accentColor)
          .withValues(alpha: 0.3)),
    );
  }

  /// 构建工具提示主题
  static fluent.TooltipThemeData _buildTooltipTheme(Brightness brightness) {
    return fluent.TooltipThemeData(
      decoration: BoxDecoration(
        color: brightness == Brightness.light 
            ? Colors.grey.shade800 
            : Colors.grey.shade200,
        borderRadius: BorderRadius.circular(6),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      textStyle: TextStyle(
        color: brightness == Brightness.light 
            ? Colors.white 
            : Colors.black87,
        fontSize: 12,
      ),
    );
  }

  /// 构建分割线主题
  static fluent.DividerThemeData _buildDividerTheme(Brightness brightness) {
    return fluent.DividerThemeData(
      decoration: BoxDecoration(
        color: brightness == Brightness.light 
            ? Colors.grey.shade300 
            : Colors.grey.shade700,
      ),
      thickness: 1,
    );
  }

  /// 构建滚动条主题
  static fluent.ScrollbarThemeData _buildScrollbarTheme(Brightness brightness) {
    return fluent.ScrollbarThemeData(
      thickness: 8,
      scrollbarColor: brightness == Brightness.light 
          ? Colors.grey.shade400 
          : Colors.grey.shade600,
    );
  }
}

/// 主题模式枚举
enum AppThemeMode {
  /// 系统主题
  system,
  /// 亮色主题
  light,
  /// 暗色主题
  dark,
}

/// 主题模式扩展
extension AppThemeModeExtension on AppThemeMode {
  /// 获取显示名称
  String get displayName {
    switch (this) {
      case AppThemeMode.system:
        return '跟随系统';
      case AppThemeMode.light:
        return '亮色主题';
      case AppThemeMode.dark:
        return '暗色主题';
    }
  }

  /// 获取图标
  IconData get icon {
    switch (this) {
      case AppThemeMode.system:
        return fluent.FluentIcons.settings;
      case AppThemeMode.light:
        return fluent.FluentIcons.sunny;
      case AppThemeMode.dark:
        return fluent.FluentIcons.clear_night;
    }
  }

  /// 转换为Flutter主题模式
  ThemeMode get fluentThemeMode {
    switch (this) {
      case AppThemeMode.system:
        return ThemeMode.system;
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
    }
  }
}

/// 自定义颜色
class AppColors {
  AppColors._();

  // 写作相关颜色
  static const Color writingPrimary = Color(0xFF2E7D32);
  static const Color writingSecondary = Color(0xFF4CAF50);
  static const Color writingAccent = Color(0xFF81C784);

  // AI相关颜色
  static const Color aiPrimary = Color(0xFF1976D2);
  static const Color aiSecondary = Color(0xFF2196F3);
  static const Color aiAccent = Color(0xFF64B5F6);

  // 圣经系统颜色
  static const Color biblePrimary = Color(0xFF7B1FA2);
  static const Color bibleSecondary = Color(0xFF9C27B0);
  static const Color bibleAccent = Color(0xFFBA68C8);

  // 版本控制颜色
  static const Color versionPrimary = Color(0xFFD32F2F);
  static const Color versionSecondary = Color(0xFFF44336);
  static const Color versionAccent = Color(0xFFEF5350);

  // 项目管理颜色
  static const Color projectPrimary = Color(0xFFF57C00);
  static const Color projectSecondary = Color(0xFFFF9800);
  static const Color projectAccent = Color(0xFFFFB74D);

  // 模板系统颜色
  static const Color templatePrimary = Color(0xFF5D4037);
  static const Color templateSecondary = Color(0xFF795548);
  static const Color templateAccent = Color(0xFFA1887F);

  // 状态颜色
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // 中性颜色
  static const Color neutral50 = Color(0xFFFAFAFA);
  static const Color neutral100 = Color(0xFFF5F5F5);
  static const Color neutral200 = Color(0xFFEEEEEE);
  static const Color neutral300 = Color(0xFFE0E0E0);
  static const Color neutral400 = Color(0xFFBDBDBD);
  static const Color neutral500 = Color(0xFF9E9E9E);
  static const Color neutral600 = Color(0xFF757575);
  static const Color neutral700 = Color(0xFF616161);
  static const Color neutral800 = Color(0xFF424242);
  static const Color neutral900 = Color(0xFF212121);
}

/// 自定义文本样式
class AppTextStyles {
  AppTextStyles._();

  // 标题样式
  static const TextStyle displayLarge = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.w700,
    letterSpacing: -0.5,
    height: 1.2,
  );

  static const TextStyle displayMedium = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.w600,
    letterSpacing: -0.25,
    height: 1.3,
  );

  static const TextStyle displaySmall = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    height: 1.3,
  );

  // 标题样式
  static const TextStyle headlineLarge = TextStyle(
    fontSize: 22,
    fontWeight: FontWeight.w600,
    height: 1.3,
  );

  static const TextStyle headlineMedium = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    height: 1.4,
  );

  static const TextStyle headlineSmall = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    height: 1.4,
  );

  // 正文样式
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    height: 1.5,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    height: 1.5,
  );

  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    height: 1.4,
  );

  // 标签样式
  static const TextStyle labelLarge = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.4,
  );

  static const TextStyle labelMedium = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    height: 1.3,
  );

  static const TextStyle labelSmall = TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.w500,
    height: 1.3,
  );
}