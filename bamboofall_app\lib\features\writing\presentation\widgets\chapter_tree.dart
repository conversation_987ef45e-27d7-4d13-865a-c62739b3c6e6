import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

import '../../domain/entities/chapter.dart';
import '../../domain/usecases/manage_chapters.dart';

/// 章节树状态
class ChapterTreeState {
  final List<Chapter> chapters;
  final String? selectedChapterId;
  final bool isLoading;
  final String? error;
  final bool isReordering;

  const ChapterTreeState({
    this.chapters = const [],
    this.selectedChapterId,
    this.isLoading = false,
    this.error,
    this.isReordering = false,
  });

  ChapterTreeState copyWith({
    List<Chapter>? chapters,
    String? selectedChapterId,
    bool? isLoading,
    String? error,
    bool? isReordering,
  }) {
    return ChapterTreeState(
      chapters: chapters ?? this.chapters,
      selectedChapterId: selectedChapterId ?? this.selectedChapterId,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isReordering: isReordering ?? this.isReordering,
    );
  }
}

/// 章节树状态管理
final chapterTreeProvider = StateNotifierProvider<ChapterTreeNotifier, ChapterTreeState>((ref) {
  final useCase = ref.read(manageChaptersUseCaseProvider);
  return ChapterTreeNotifier(useCase);
});

class ChapterTreeNotifier extends StateNotifier<ChapterTreeState> {
  final ManageChaptersUseCase _useCase;

  ChapterTreeNotifier(this._useCase) : super(const ChapterTreeState()) {
    loadChapters();
  }

  /// 加载章节列表
  Future<void> loadChapters() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final chapters = await _useCase.buildChapterTree();
      state = state.copyWith(
        chapters: chapters,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 选择章节
  void selectChapter(String? chapterId) {
    state = state.copyWith(selectedChapterId: chapterId);
  }

  /// 创建新章节
  Future<void> createChapter({
    required String title,
    String? parentId,
    String content = '',
  }) async {
    try {
      await _useCase.createChapter(
        title: title,
        parentId: parentId,
        content: content,
      );
      await loadChapters();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 更新章节
  Future<void> updateChapter(Chapter chapter) async {
    try {
      await _useCase.updateChapter(chapter);
      await loadChapters();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 删除章节
  Future<void> deleteChapter(String chapterId) async {
    try {
      await _useCase.deleteChapter(chapterId);
      await loadChapters();
      
      // 如果删除的是当前选中的章节，清除选择
      if (state.selectedChapterId == chapterId) {
        state = state.copyWith(selectedChapterId: null);
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 移动章节
  Future<void> moveChapter(String chapterId, String? newParentId) async {
    try {
      await _useCase.moveChapter(chapterId, newParentId);
      await loadChapters();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 重新排序章节
  Future<void> reorderChapters(String? parentId, List<String> newOrder) async {
    state = state.copyWith(isReordering: true);
    
    try {
      await _useCase.reorderChapters(parentId, newOrder);
      await loadChapters();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    } finally {
      state = state.copyWith(isReordering: false);
    }
  }

  /// 切换章节展开状态
  void toggleChapterExpansion(String chapterId) {
    final updatedChapters = _updateChapterInTree(
      state.chapters,
      chapterId,
      (chapter) => chapter.copyWith(isExpanded: !chapter.isExpanded),
    );
    
    state = state.copyWith(chapters: updatedChapters);
  }

  /// 在树中更新章节
  List<Chapter> _updateChapterInTree(
    List<Chapter> chapters,
    String chapterId,
    Chapter Function(Chapter) updater,
  ) {
    return chapters.map((chapter) {
      if (chapter.id == chapterId) {
        return updater(chapter);
      } else if (chapter.hasChildren) {
        return chapter.copyWith(
          children: _updateChapterInTree(chapter.children, chapterId, updater),
        );
      }
      return chapter;
    }).toList();
  }
}

/// 章节树组件
/// 显示章节的树形结构，支持拖拽排序和层级管理
class ChapterTree extends ConsumerWidget {
  final Function(String chapterId)? onChapterSelected;
  final Function(String chapterId)? onChapterEdit;
  final Function(String chapterId)? onChapterDelete;

  const ChapterTree({
    super.key,
    this.onChapterSelected,
    this.onChapterEdit,
    this.onChapterDelete,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(chapterTreeProvider);
    final notifier = ref.read(chapterTreeProvider.notifier);

    if (state.isLoading) {
      return const Center(
        child: fluent.ProgressRing(),
      );
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const fluent.Icon(
              fluent.FluentIcons.error,
              size: 48,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: fluent.FluentTheme.of(context).typography.subtitle,
            ),
            const SizedBox(height: 8),
            Text(
              state.error!,
              style: fluent.FluentTheme.of(context).typography.caption,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            fluent.Button(
              onPressed: notifier.loadChapters,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (state.chapters.isEmpty) {
      return _buildEmptyState(context, notifier);
    }

    return Column(
      children: [
        // 工具栏
        _buildToolbar(context, notifier),
        
        // 章节列表
        Expanded(
          child: ReorderableListView.builder(
            itemCount: state.chapters.length,
            onReorder: (oldIndex, newIndex) {
              if (oldIndex < newIndex) newIndex--;
              final newOrder = List<String>.from(
                state.chapters.map((c) => c.id),
              );
              final item = newOrder.removeAt(oldIndex);
              newOrder.insert(newIndex, item);
              notifier.reorderChapters(null, newOrder);
            },
            itemBuilder: (context, index) {
              final chapter = state.chapters[index];
              return _buildChapterItem(
                context,
                chapter,
                notifier,
                state.selectedChapterId,
                key: ValueKey(chapter.id),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context, ChapterTreeNotifier notifier) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const fluent.Icon(
            fluent.FluentIcons.document,
            size: 48,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            '暂无章节',
            style: fluent.FluentTheme.of(context).typography.subtitle,
          ),
          const SizedBox(height: 8),
          Text(
            '点击下方按钮创建第一个章节',
            style: fluent.FluentTheme.of(context).typography.caption,
          ),
          const SizedBox(height: 24),
          fluent.FilledButton(
            onPressed: () => _showCreateChapterDialog(context, notifier),
            child: const Text('创建章节'),
          ),
        ],
      ),
    );
  }

  Widget _buildToolbar(BuildContext context, ChapterTreeNotifier notifier) {
    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          fluent.IconButton(
            icon: const fluent.Icon(fluent.FluentIcons.add, size: 16),
            onPressed: () => _showCreateChapterDialog(context, notifier),
          ),
          fluent.IconButton(
            icon: const fluent.Icon(fluent.FluentIcons.refresh, size: 16),
            onPressed: notifier.loadChapters,
          ),
          const Spacer(),
          fluent.IconButton(
            icon: const fluent.Icon(fluent.FluentIcons.more, size: 16),
            onPressed: () => _showMoreOptions(context, notifier),
          ),
        ],
      ),
    );
  }

  Widget _buildChapterItem(
    BuildContext context,
    Chapter chapter,
    ChapterTreeNotifier notifier,
    String? selectedChapterId, {
    required Key key,
  }) {
    final isSelected = chapter.id == selectedChapterId;
    
    return Container(
      key: key,
      margin: EdgeInsets.only(left: chapter.level * 16.0),
      child: Column(
        children: [
          _ChapterTile(
            chapter: chapter,
            isSelected: isSelected,
            onTap: () {
              notifier.selectChapter(chapter.id);
              onChapterSelected?.call(chapter.id);
            },
            onToggleExpansion: () {
              notifier.toggleChapterExpansion(chapter.id);
            },
            onEdit: () => onChapterEdit?.call(chapter.id),
            onDelete: () => _confirmDeleteChapter(context, notifier, chapter),
            onAddChild: () => _showCreateChapterDialog(
              context,
              notifier,
              parentId: chapter.id,
            ),
          ),
          
          // 子章节
          if (chapter.isExpanded && chapter.hasChildren)
            ...chapter.children.map((child) => _buildChapterItem(
                  context,
                  child,
                  notifier,
                  selectedChapterId,
                  key: ValueKey(child.id),
                )),
        ],
      ),
    );
  }

  void _showCreateChapterDialog(
    BuildContext context,
    ChapterTreeNotifier notifier, {
    String? parentId,
  }) {
    final titleController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: Text(parentId != null ? '创建子章节' : '创建章节'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('章节标题:'),
            const SizedBox(height: 8),
            fluent.TextBox(
              controller: titleController,
              placeholder: '输入章节标题',
              autofocus: true,
            ),
          ],
        ),
        actions: [
          fluent.Button(
            child: const Text('取消'),
            onPressed: () => Navigator.pop(context),
          ),
          fluent.FilledButton(
            child: const Text('创建'),
            onPressed: () {
              final title = titleController.text.trim();
              if (title.isNotEmpty) {
                Navigator.pop(context);
                notifier.createChapter(
                  title: title,
                  parentId: parentId,
                );
              }
            },
          ),
        ],
      ),
    );
  }

  void _confirmDeleteChapter(
    BuildContext context,
    ChapterTreeNotifier notifier,
    Chapter chapter,
  ) {
    showDialog(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: const Text('删除章节'),
        content: Text('确定要删除章节 "${chapter.title}" 吗？\n\n此操作不可撤销。'),
        actions: [
          fluent.Button(
            child: const Text('取消'),
            onPressed: () => Navigator.pop(context),
          ),
          fluent.FilledButton(
            child: const Text('删除'),
            onPressed: () {
              Navigator.pop(context);
              notifier.deleteChapter(chapter.id);
            },
          ),
        ],
      ),
    );
  }

  void _showMoreOptions(BuildContext context, ChapterTreeNotifier notifier) {
    // TODO: 实现更多选项菜单
  }
}

/// 章节项组件
class _ChapterTile extends StatefulWidget {
  final Chapter chapter;
  final bool isSelected;
  final VoidCallback onTap;
  final VoidCallback onToggleExpansion;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onAddChild;

  const _ChapterTile({
    required this.chapter,
    required this.isSelected,
    required this.onTap,
    required this.onToggleExpansion,
    this.onEdit,
    this.onDelete,
    this.onAddChild,
  });

  @override
  State<_ChapterTile> createState() => _ChapterTileState();
}

class _ChapterTileState extends State<_ChapterTile> {
  bool _isHovering = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovering = true),
      onExit: (_) => setState(() => _isHovering = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          height: 36,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          decoration: BoxDecoration(
            color: _getBackgroundColor(context),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            children: [
              // 展开/折叠按钮
              if (widget.chapter.hasChildren)
                fluent.IconButton(
                  icon: fluent.Icon(
                    widget.chapter.isExpanded
                        ? fluent.FluentIcons.chevron_down
                        : fluent.FluentIcons.chevron_right,
                    size: 12,
                  ),
                  onPressed: widget.onToggleExpansion,
                )
              else
                const SizedBox(width: 32),

              // 状态指示器
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: _getStatusColor(),
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),

              // 章节标题
              Expanded(
                child: Text(
                  widget.chapter.title,
                  style: fluent.FluentTheme.of(context).typography.body?.copyWith(
                        fontWeight: widget.isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              // 字数统计
              if (widget.chapter.wordCount > 0)
                Text(
                  '${widget.chapter.wordCount}字',
                  style: fluent.FluentTheme.of(context).typography.caption?.copyWith(
                        color: fluent.FluentTheme.of(context).resources.textFillColorSecondary,
                      ),
                ),

              // 操作按钮（悬停时显示）
              if (_isHovering) ...[
                const SizedBox(width: 8),
                fluent.IconButton(
                  icon: const fluent.Icon(fluent.FluentIcons.add, size: 12),
                  onPressed: widget.onAddChild,
                ),
                fluent.IconButton(
                  icon: const fluent.Icon(fluent.FluentIcons.edit, size: 12),
                  onPressed: widget.onEdit,
                ),
                fluent.IconButton(
                  icon: const fluent.Icon(fluent.FluentIcons.delete, size: 12),
                  onPressed: widget.onDelete,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getBackgroundColor(BuildContext context) {
    if (widget.isSelected) {
      return fluent.FluentTheme.of(context).accentColor.withValues(alpha: 0.1);
    } else if (_isHovering) {
      return fluent.FluentTheme.of(context).resources.subtleFillColorSecondary;
    } else {
      return Colors.transparent;
    }
  }

  Color _getStatusColor() {
    switch (widget.chapter.status) {
      case ChapterStatus.draft:
        return Colors.grey;
      case ChapterStatus.inProgress:
        return Colors.blue;
      case ChapterStatus.completed:
        return Colors.green;
      case ChapterStatus.published:
        return Colors.purple;
      case ChapterStatus.archived:
        return Colors.grey.shade600;
    }
  }
}