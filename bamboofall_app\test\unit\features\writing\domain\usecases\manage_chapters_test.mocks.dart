// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in bamboofall_app/test/unit/features/writing/domain/usecases/manage_chapters_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:bamboofall_app/features/writing/domain/entities/chapter.dart'
    as _i2;
import 'package:bamboofall_app/features/writing/domain/repositories/chapter_repository.dart'
    as _i3;
import 'package:logger/src/log_level.dart' as _i6;
import 'package:logger/src/logger.dart' as _i5;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeChapter_0 extends _i1.SmartFake implements _i2.Chapter {
  _FakeChapter_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [ChapterRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockChapterRepository extends _i1.Mock implements _i3.ChapterRepository {
  MockChapterRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<List<_i2.Chapter>> getAllChapters() =>
      (super.noSuchMethod(
            Invocation.method(#getAllChapters, []),
            returnValue: _i4.Future<List<_i2.Chapter>>.value(<_i2.Chapter>[]),
          )
          as _i4.Future<List<_i2.Chapter>>);

  @override
  _i4.Future<_i2.Chapter?> getChapterById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getChapterById, [id]),
            returnValue: _i4.Future<_i2.Chapter?>.value(),
          )
          as _i4.Future<_i2.Chapter?>);

  @override
  _i4.Future<List<_i2.Chapter>> getChildChapters(String? parentId) =>
      (super.noSuchMethod(
            Invocation.method(#getChildChapters, [parentId]),
            returnValue: _i4.Future<List<_i2.Chapter>>.value(<_i2.Chapter>[]),
          )
          as _i4.Future<List<_i2.Chapter>>);

  @override
  _i4.Future<_i2.Chapter> createChapter(_i2.Chapter? chapter) =>
      (super.noSuchMethod(
            Invocation.method(#createChapter, [chapter]),
            returnValue: _i4.Future<_i2.Chapter>.value(
              _FakeChapter_0(
                this,
                Invocation.method(#createChapter, [chapter]),
              ),
            ),
          )
          as _i4.Future<_i2.Chapter>);

  @override
  _i4.Future<_i2.Chapter> updateChapter(_i2.Chapter? chapter) =>
      (super.noSuchMethod(
            Invocation.method(#updateChapter, [chapter]),
            returnValue: _i4.Future<_i2.Chapter>.value(
              _FakeChapter_0(
                this,
                Invocation.method(#updateChapter, [chapter]),
              ),
            ),
          )
          as _i4.Future<_i2.Chapter>);

  @override
  _i4.Future<void> deleteChapter(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteChapter, [id]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> updateChapters(List<_i2.Chapter>? chapters) =>
      (super.noSuchMethod(
            Invocation.method(#updateChapters, [chapters]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<_i2.Chapter>> getChaptersByStatus(
    _i2.ChapterStatus? status,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getChaptersByStatus, [status]),
            returnValue: _i4.Future<List<_i2.Chapter>>.value(<_i2.Chapter>[]),
          )
          as _i4.Future<List<_i2.Chapter>>);

  @override
  _i4.Future<List<_i2.Chapter>> getChaptersByTag(String? tag) =>
      (super.noSuchMethod(
            Invocation.method(#getChaptersByTag, [tag]),
            returnValue: _i4.Future<List<_i2.Chapter>>.value(<_i2.Chapter>[]),
          )
          as _i4.Future<List<_i2.Chapter>>);

  @override
  _i4.Future<List<_i2.Chapter>> searchChapters(String? query) =>
      (super.noSuchMethod(
            Invocation.method(#searchChapters, [query]),
            returnValue: _i4.Future<List<_i2.Chapter>>.value(<_i2.Chapter>[]),
          )
          as _i4.Future<List<_i2.Chapter>>);

  @override
  _i4.Future<int> getChapterCount() =>
      (super.noSuchMethod(
            Invocation.method(#getChapterCount, []),
            returnValue: _i4.Future<int>.value(0),
          )
          as _i4.Future<int>);

  @override
  _i4.Future<void> clearAllChapters() =>
      (super.noSuchMethod(
            Invocation.method(#clearAllChapters, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}

/// A class which mocks [Logger].
///
/// See the documentation for Mockito's code generation for more information.
class MockLogger extends _i1.Mock implements _i5.Logger {
  MockLogger() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> get init =>
      (super.noSuchMethod(
            Invocation.getter(#init),
            returnValue: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  void v(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #v,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void t(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #t,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void d(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #d,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void i(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #i,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void w(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #w,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void e(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #e,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void wtf(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #wtf,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void f(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #f,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void log(
    _i6.Level? level,
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #log,
      [level, message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  bool isClosed() =>
      (super.noSuchMethod(Invocation.method(#isClosed, []), returnValue: false)
          as bool);

  @override
  _i4.Future<void> close() =>
      (super.noSuchMethod(
            Invocation.method(#close, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}
