// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'world_settings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

WorldSettings _$WorldSettingsFromJson(Map<String, dynamic> json) {
  return _WorldSettings.fromJson(json);
}

/// @nodoc
mixin _$WorldSettings {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  WorldType get type => throw _privateConstructorUsedError;
  TechnologyLevel get technologyLevel => throw _privateConstructorUsedError;
  MagicLevel get magicLevel => throw _privateConstructorUsedError;
  WorldPhysics? get physics => throw _privateConstructorUsedError;
  WorldHistory? get history => throw _privateConstructorUsedError;
  WorldGeography? get geography => throw _privateConstructorUsedError;
  WorldSociety? get society => throw _privateConstructorUsedError;
  WorldEconomy? get economy => throw _privateConstructorUsedError;
  WorldReligion? get religion => throw _privateConstructorUsedError;
  List<WorldRule> get rules => throw _privateConstructorUsedError;
  List<WorldEvent> get majorEvents => throw _privateConstructorUsedError;
  List<Species> get species => throw _privateConstructorUsedError;
  List<Language> get languages => throw _privateConstructorUsedError;
  List<Calendar> get calendars => throw _privateConstructorUsedError;
  List<Currency> get currencies => throw _privateConstructorUsedError;
  Map<String, dynamic> get customSettings => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this WorldSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WorldSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WorldSettingsCopyWith<WorldSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorldSettingsCopyWith<$Res> {
  factory $WorldSettingsCopyWith(
    WorldSettings value,
    $Res Function(WorldSettings) then,
  ) = _$WorldSettingsCopyWithImpl<$Res, WorldSettings>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    WorldType type,
    TechnologyLevel technologyLevel,
    MagicLevel magicLevel,
    WorldPhysics? physics,
    WorldHistory? history,
    WorldGeography? geography,
    WorldSociety? society,
    WorldEconomy? economy,
    WorldReligion? religion,
    List<WorldRule> rules,
    List<WorldEvent> majorEvents,
    List<Species> species,
    List<Language> languages,
    List<Calendar> calendars,
    List<Currency> currencies,
    Map<String, dynamic> customSettings,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });

  $WorldPhysicsCopyWith<$Res>? get physics;
  $WorldHistoryCopyWith<$Res>? get history;
  $WorldGeographyCopyWith<$Res>? get geography;
  $WorldSocietyCopyWith<$Res>? get society;
  $WorldEconomyCopyWith<$Res>? get economy;
  $WorldReligionCopyWith<$Res>? get religion;
}

/// @nodoc
class _$WorldSettingsCopyWithImpl<$Res, $Val extends WorldSettings>
    implements $WorldSettingsCopyWith<$Res> {
  _$WorldSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WorldSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = null,
    Object? technologyLevel = null,
    Object? magicLevel = null,
    Object? physics = freezed,
    Object? history = freezed,
    Object? geography = freezed,
    Object? society = freezed,
    Object? economy = freezed,
    Object? religion = freezed,
    Object? rules = null,
    Object? majorEvents = null,
    Object? species = null,
    Object? languages = null,
    Object? calendars = null,
    Object? currencies = null,
    Object? customSettings = null,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as WorldType,
            technologyLevel: null == technologyLevel
                ? _value.technologyLevel
                : technologyLevel // ignore: cast_nullable_to_non_nullable
                      as TechnologyLevel,
            magicLevel: null == magicLevel
                ? _value.magicLevel
                : magicLevel // ignore: cast_nullable_to_non_nullable
                      as MagicLevel,
            physics: freezed == physics
                ? _value.physics
                : physics // ignore: cast_nullable_to_non_nullable
                      as WorldPhysics?,
            history: freezed == history
                ? _value.history
                : history // ignore: cast_nullable_to_non_nullable
                      as WorldHistory?,
            geography: freezed == geography
                ? _value.geography
                : geography // ignore: cast_nullable_to_non_nullable
                      as WorldGeography?,
            society: freezed == society
                ? _value.society
                : society // ignore: cast_nullable_to_non_nullable
                      as WorldSociety?,
            economy: freezed == economy
                ? _value.economy
                : economy // ignore: cast_nullable_to_non_nullable
                      as WorldEconomy?,
            religion: freezed == religion
                ? _value.religion
                : religion // ignore: cast_nullable_to_non_nullable
                      as WorldReligion?,
            rules: null == rules
                ? _value.rules
                : rules // ignore: cast_nullable_to_non_nullable
                      as List<WorldRule>,
            majorEvents: null == majorEvents
                ? _value.majorEvents
                : majorEvents // ignore: cast_nullable_to_non_nullable
                      as List<WorldEvent>,
            species: null == species
                ? _value.species
                : species // ignore: cast_nullable_to_non_nullable
                      as List<Species>,
            languages: null == languages
                ? _value.languages
                : languages // ignore: cast_nullable_to_non_nullable
                      as List<Language>,
            calendars: null == calendars
                ? _value.calendars
                : calendars // ignore: cast_nullable_to_non_nullable
                      as List<Calendar>,
            currencies: null == currencies
                ? _value.currencies
                : currencies // ignore: cast_nullable_to_non_nullable
                      as List<Currency>,
            customSettings: null == customSettings
                ? _value.customSettings
                : customSettings // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }

  /// Create a copy of WorldSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WorldPhysicsCopyWith<$Res>? get physics {
    if (_value.physics == null) {
      return null;
    }

    return $WorldPhysicsCopyWith<$Res>(_value.physics!, (value) {
      return _then(_value.copyWith(physics: value) as $Val);
    });
  }

  /// Create a copy of WorldSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WorldHistoryCopyWith<$Res>? get history {
    if (_value.history == null) {
      return null;
    }

    return $WorldHistoryCopyWith<$Res>(_value.history!, (value) {
      return _then(_value.copyWith(history: value) as $Val);
    });
  }

  /// Create a copy of WorldSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WorldGeographyCopyWith<$Res>? get geography {
    if (_value.geography == null) {
      return null;
    }

    return $WorldGeographyCopyWith<$Res>(_value.geography!, (value) {
      return _then(_value.copyWith(geography: value) as $Val);
    });
  }

  /// Create a copy of WorldSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WorldSocietyCopyWith<$Res>? get society {
    if (_value.society == null) {
      return null;
    }

    return $WorldSocietyCopyWith<$Res>(_value.society!, (value) {
      return _then(_value.copyWith(society: value) as $Val);
    });
  }

  /// Create a copy of WorldSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WorldEconomyCopyWith<$Res>? get economy {
    if (_value.economy == null) {
      return null;
    }

    return $WorldEconomyCopyWith<$Res>(_value.economy!, (value) {
      return _then(_value.copyWith(economy: value) as $Val);
    });
  }

  /// Create a copy of WorldSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WorldReligionCopyWith<$Res>? get religion {
    if (_value.religion == null) {
      return null;
    }

    return $WorldReligionCopyWith<$Res>(_value.religion!, (value) {
      return _then(_value.copyWith(religion: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$WorldSettingsImplCopyWith<$Res>
    implements $WorldSettingsCopyWith<$Res> {
  factory _$$WorldSettingsImplCopyWith(
    _$WorldSettingsImpl value,
    $Res Function(_$WorldSettingsImpl) then,
  ) = __$$WorldSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    WorldType type,
    TechnologyLevel technologyLevel,
    MagicLevel magicLevel,
    WorldPhysics? physics,
    WorldHistory? history,
    WorldGeography? geography,
    WorldSociety? society,
    WorldEconomy? economy,
    WorldReligion? religion,
    List<WorldRule> rules,
    List<WorldEvent> majorEvents,
    List<Species> species,
    List<Language> languages,
    List<Calendar> calendars,
    List<Currency> currencies,
    Map<String, dynamic> customSettings,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });

  @override
  $WorldPhysicsCopyWith<$Res>? get physics;
  @override
  $WorldHistoryCopyWith<$Res>? get history;
  @override
  $WorldGeographyCopyWith<$Res>? get geography;
  @override
  $WorldSocietyCopyWith<$Res>? get society;
  @override
  $WorldEconomyCopyWith<$Res>? get economy;
  @override
  $WorldReligionCopyWith<$Res>? get religion;
}

/// @nodoc
class __$$WorldSettingsImplCopyWithImpl<$Res>
    extends _$WorldSettingsCopyWithImpl<$Res, _$WorldSettingsImpl>
    implements _$$WorldSettingsImplCopyWith<$Res> {
  __$$WorldSettingsImplCopyWithImpl(
    _$WorldSettingsImpl _value,
    $Res Function(_$WorldSettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of WorldSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = null,
    Object? technologyLevel = null,
    Object? magicLevel = null,
    Object? physics = freezed,
    Object? history = freezed,
    Object? geography = freezed,
    Object? society = freezed,
    Object? economy = freezed,
    Object? religion = freezed,
    Object? rules = null,
    Object? majorEvents = null,
    Object? species = null,
    Object? languages = null,
    Object? calendars = null,
    Object? currencies = null,
    Object? customSettings = null,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$WorldSettingsImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as WorldType,
        technologyLevel: null == technologyLevel
            ? _value.technologyLevel
            : technologyLevel // ignore: cast_nullable_to_non_nullable
                  as TechnologyLevel,
        magicLevel: null == magicLevel
            ? _value.magicLevel
            : magicLevel // ignore: cast_nullable_to_non_nullable
                  as MagicLevel,
        physics: freezed == physics
            ? _value.physics
            : physics // ignore: cast_nullable_to_non_nullable
                  as WorldPhysics?,
        history: freezed == history
            ? _value.history
            : history // ignore: cast_nullable_to_non_nullable
                  as WorldHistory?,
        geography: freezed == geography
            ? _value.geography
            : geography // ignore: cast_nullable_to_non_nullable
                  as WorldGeography?,
        society: freezed == society
            ? _value.society
            : society // ignore: cast_nullable_to_non_nullable
                  as WorldSociety?,
        economy: freezed == economy
            ? _value.economy
            : economy // ignore: cast_nullable_to_non_nullable
                  as WorldEconomy?,
        religion: freezed == religion
            ? _value.religion
            : religion // ignore: cast_nullable_to_non_nullable
                  as WorldReligion?,
        rules: null == rules
            ? _value._rules
            : rules // ignore: cast_nullable_to_non_nullable
                  as List<WorldRule>,
        majorEvents: null == majorEvents
            ? _value._majorEvents
            : majorEvents // ignore: cast_nullable_to_non_nullable
                  as List<WorldEvent>,
        species: null == species
            ? _value._species
            : species // ignore: cast_nullable_to_non_nullable
                  as List<Species>,
        languages: null == languages
            ? _value._languages
            : languages // ignore: cast_nullable_to_non_nullable
                  as List<Language>,
        calendars: null == calendars
            ? _value._calendars
            : calendars // ignore: cast_nullable_to_non_nullable
                  as List<Calendar>,
        currencies: null == currencies
            ? _value._currencies
            : currencies // ignore: cast_nullable_to_non_nullable
                  as List<Currency>,
        customSettings: null == customSettings
            ? _value._customSettings
            : customSettings // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$WorldSettingsImpl implements _WorldSettings {
  const _$WorldSettingsImpl({
    required this.id,
    required this.name,
    this.description,
    required this.type,
    required this.technologyLevel,
    required this.magicLevel,
    this.physics,
    this.history,
    this.geography,
    this.society,
    this.economy,
    this.religion,
    final List<WorldRule> rules = const [],
    final List<WorldEvent> majorEvents = const [],
    final List<Species> species = const [],
    final List<Language> languages = const [],
    final List<Calendar> calendars = const [],
    final List<Currency> currencies = const [],
    final Map<String, dynamic> customSettings = const {},
    final Map<String, dynamic> metadata = const {},
    this.createdAt,
    this.updatedAt,
  }) : _rules = rules,
       _majorEvents = majorEvents,
       _species = species,
       _languages = languages,
       _calendars = calendars,
       _currencies = currencies,
       _customSettings = customSettings,
       _metadata = metadata;

  factory _$WorldSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorldSettingsImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final WorldType type;
  @override
  final TechnologyLevel technologyLevel;
  @override
  final MagicLevel magicLevel;
  @override
  final WorldPhysics? physics;
  @override
  final WorldHistory? history;
  @override
  final WorldGeography? geography;
  @override
  final WorldSociety? society;
  @override
  final WorldEconomy? economy;
  @override
  final WorldReligion? religion;
  final List<WorldRule> _rules;
  @override
  @JsonKey()
  List<WorldRule> get rules {
    if (_rules is EqualUnmodifiableListView) return _rules;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_rules);
  }

  final List<WorldEvent> _majorEvents;
  @override
  @JsonKey()
  List<WorldEvent> get majorEvents {
    if (_majorEvents is EqualUnmodifiableListView) return _majorEvents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_majorEvents);
  }

  final List<Species> _species;
  @override
  @JsonKey()
  List<Species> get species {
    if (_species is EqualUnmodifiableListView) return _species;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_species);
  }

  final List<Language> _languages;
  @override
  @JsonKey()
  List<Language> get languages {
    if (_languages is EqualUnmodifiableListView) return _languages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_languages);
  }

  final List<Calendar> _calendars;
  @override
  @JsonKey()
  List<Calendar> get calendars {
    if (_calendars is EqualUnmodifiableListView) return _calendars;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_calendars);
  }

  final List<Currency> _currencies;
  @override
  @JsonKey()
  List<Currency> get currencies {
    if (_currencies is EqualUnmodifiableListView) return _currencies;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_currencies);
  }

  final Map<String, dynamic> _customSettings;
  @override
  @JsonKey()
  Map<String, dynamic> get customSettings {
    if (_customSettings is EqualUnmodifiableMapView) return _customSettings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customSettings);
  }

  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'WorldSettings(id: $id, name: $name, description: $description, type: $type, technologyLevel: $technologyLevel, magicLevel: $magicLevel, physics: $physics, history: $history, geography: $geography, society: $society, economy: $economy, religion: $religion, rules: $rules, majorEvents: $majorEvents, species: $species, languages: $languages, calendars: $calendars, currencies: $currencies, customSettings: $customSettings, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorldSettingsImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.technologyLevel, technologyLevel) ||
                other.technologyLevel == technologyLevel) &&
            (identical(other.magicLevel, magicLevel) ||
                other.magicLevel == magicLevel) &&
            (identical(other.physics, physics) || other.physics == physics) &&
            (identical(other.history, history) || other.history == history) &&
            (identical(other.geography, geography) ||
                other.geography == geography) &&
            (identical(other.society, society) || other.society == society) &&
            (identical(other.economy, economy) || other.economy == economy) &&
            (identical(other.religion, religion) ||
                other.religion == religion) &&
            const DeepCollectionEquality().equals(other._rules, _rules) &&
            const DeepCollectionEquality().equals(
              other._majorEvents,
              _majorEvents,
            ) &&
            const DeepCollectionEquality().equals(other._species, _species) &&
            const DeepCollectionEquality().equals(
              other._languages,
              _languages,
            ) &&
            const DeepCollectionEquality().equals(
              other._calendars,
              _calendars,
            ) &&
            const DeepCollectionEquality().equals(
              other._currencies,
              _currencies,
            ) &&
            const DeepCollectionEquality().equals(
              other._customSettings,
              _customSettings,
            ) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    name,
    description,
    type,
    technologyLevel,
    magicLevel,
    physics,
    history,
    geography,
    society,
    economy,
    religion,
    const DeepCollectionEquality().hash(_rules),
    const DeepCollectionEquality().hash(_majorEvents),
    const DeepCollectionEquality().hash(_species),
    const DeepCollectionEquality().hash(_languages),
    const DeepCollectionEquality().hash(_calendars),
    const DeepCollectionEquality().hash(_currencies),
    const DeepCollectionEquality().hash(_customSettings),
    const DeepCollectionEquality().hash(_metadata),
    createdAt,
    updatedAt,
  ]);

  /// Create a copy of WorldSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WorldSettingsImplCopyWith<_$WorldSettingsImpl> get copyWith =>
      __$$WorldSettingsImplCopyWithImpl<_$WorldSettingsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorldSettingsImplToJson(this);
  }
}

abstract class _WorldSettings implements WorldSettings {
  const factory _WorldSettings({
    required final String id,
    required final String name,
    final String? description,
    required final WorldType type,
    required final TechnologyLevel technologyLevel,
    required final MagicLevel magicLevel,
    final WorldPhysics? physics,
    final WorldHistory? history,
    final WorldGeography? geography,
    final WorldSociety? society,
    final WorldEconomy? economy,
    final WorldReligion? religion,
    final List<WorldRule> rules,
    final List<WorldEvent> majorEvents,
    final List<Species> species,
    final List<Language> languages,
    final List<Calendar> calendars,
    final List<Currency> currencies,
    final Map<String, dynamic> customSettings,
    final Map<String, dynamic> metadata,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$WorldSettingsImpl;

  factory _WorldSettings.fromJson(Map<String, dynamic> json) =
      _$WorldSettingsImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  WorldType get type;
  @override
  TechnologyLevel get technologyLevel;
  @override
  MagicLevel get magicLevel;
  @override
  WorldPhysics? get physics;
  @override
  WorldHistory? get history;
  @override
  WorldGeography? get geography;
  @override
  WorldSociety? get society;
  @override
  WorldEconomy? get economy;
  @override
  WorldReligion? get religion;
  @override
  List<WorldRule> get rules;
  @override
  List<WorldEvent> get majorEvents;
  @override
  List<Species> get species;
  @override
  List<Language> get languages;
  @override
  List<Calendar> get calendars;
  @override
  List<Currency> get currencies;
  @override
  Map<String, dynamic> get customSettings;
  @override
  Map<String, dynamic> get metadata;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of WorldSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WorldSettingsImplCopyWith<_$WorldSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WorldPhysics _$WorldPhysicsFromJson(Map<String, dynamic> json) {
  return _WorldPhysics.fromJson(json);
}

/// @nodoc
mixin _$WorldPhysics {
  List<PhysicalLaw> get laws => throw _privateConstructorUsedError;
  List<PhysicalConstant> get constants => throw _privateConstructorUsedError;
  List<PhysicalForce> get forces => throw _privateConstructorUsedError;
  String? get gravityDescription => throw _privateConstructorUsedError;
  String? get timeDescription => throw _privateConstructorUsedError;
  String? get spaceDescription => throw _privateConstructorUsedError;
  List<String> get dimensions => throw _privateConstructorUsedError;
  Map<String, dynamic> get customPhysics => throw _privateConstructorUsedError;

  /// Serializes this WorldPhysics to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WorldPhysics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WorldPhysicsCopyWith<WorldPhysics> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorldPhysicsCopyWith<$Res> {
  factory $WorldPhysicsCopyWith(
    WorldPhysics value,
    $Res Function(WorldPhysics) then,
  ) = _$WorldPhysicsCopyWithImpl<$Res, WorldPhysics>;
  @useResult
  $Res call({
    List<PhysicalLaw> laws,
    List<PhysicalConstant> constants,
    List<PhysicalForce> forces,
    String? gravityDescription,
    String? timeDescription,
    String? spaceDescription,
    List<String> dimensions,
    Map<String, dynamic> customPhysics,
  });
}

/// @nodoc
class _$WorldPhysicsCopyWithImpl<$Res, $Val extends WorldPhysics>
    implements $WorldPhysicsCopyWith<$Res> {
  _$WorldPhysicsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WorldPhysics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? laws = null,
    Object? constants = null,
    Object? forces = null,
    Object? gravityDescription = freezed,
    Object? timeDescription = freezed,
    Object? spaceDescription = freezed,
    Object? dimensions = null,
    Object? customPhysics = null,
  }) {
    return _then(
      _value.copyWith(
            laws: null == laws
                ? _value.laws
                : laws // ignore: cast_nullable_to_non_nullable
                      as List<PhysicalLaw>,
            constants: null == constants
                ? _value.constants
                : constants // ignore: cast_nullable_to_non_nullable
                      as List<PhysicalConstant>,
            forces: null == forces
                ? _value.forces
                : forces // ignore: cast_nullable_to_non_nullable
                      as List<PhysicalForce>,
            gravityDescription: freezed == gravityDescription
                ? _value.gravityDescription
                : gravityDescription // ignore: cast_nullable_to_non_nullable
                      as String?,
            timeDescription: freezed == timeDescription
                ? _value.timeDescription
                : timeDescription // ignore: cast_nullable_to_non_nullable
                      as String?,
            spaceDescription: freezed == spaceDescription
                ? _value.spaceDescription
                : spaceDescription // ignore: cast_nullable_to_non_nullable
                      as String?,
            dimensions: null == dimensions
                ? _value.dimensions
                : dimensions // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customPhysics: null == customPhysics
                ? _value.customPhysics
                : customPhysics // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$WorldPhysicsImplCopyWith<$Res>
    implements $WorldPhysicsCopyWith<$Res> {
  factory _$$WorldPhysicsImplCopyWith(
    _$WorldPhysicsImpl value,
    $Res Function(_$WorldPhysicsImpl) then,
  ) = __$$WorldPhysicsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    List<PhysicalLaw> laws,
    List<PhysicalConstant> constants,
    List<PhysicalForce> forces,
    String? gravityDescription,
    String? timeDescription,
    String? spaceDescription,
    List<String> dimensions,
    Map<String, dynamic> customPhysics,
  });
}

/// @nodoc
class __$$WorldPhysicsImplCopyWithImpl<$Res>
    extends _$WorldPhysicsCopyWithImpl<$Res, _$WorldPhysicsImpl>
    implements _$$WorldPhysicsImplCopyWith<$Res> {
  __$$WorldPhysicsImplCopyWithImpl(
    _$WorldPhysicsImpl _value,
    $Res Function(_$WorldPhysicsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of WorldPhysics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? laws = null,
    Object? constants = null,
    Object? forces = null,
    Object? gravityDescription = freezed,
    Object? timeDescription = freezed,
    Object? spaceDescription = freezed,
    Object? dimensions = null,
    Object? customPhysics = null,
  }) {
    return _then(
      _$WorldPhysicsImpl(
        laws: null == laws
            ? _value._laws
            : laws // ignore: cast_nullable_to_non_nullable
                  as List<PhysicalLaw>,
        constants: null == constants
            ? _value._constants
            : constants // ignore: cast_nullable_to_non_nullable
                  as List<PhysicalConstant>,
        forces: null == forces
            ? _value._forces
            : forces // ignore: cast_nullable_to_non_nullable
                  as List<PhysicalForce>,
        gravityDescription: freezed == gravityDescription
            ? _value.gravityDescription
            : gravityDescription // ignore: cast_nullable_to_non_nullable
                  as String?,
        timeDescription: freezed == timeDescription
            ? _value.timeDescription
            : timeDescription // ignore: cast_nullable_to_non_nullable
                  as String?,
        spaceDescription: freezed == spaceDescription
            ? _value.spaceDescription
            : spaceDescription // ignore: cast_nullable_to_non_nullable
                  as String?,
        dimensions: null == dimensions
            ? _value._dimensions
            : dimensions // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customPhysics: null == customPhysics
            ? _value._customPhysics
            : customPhysics // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$WorldPhysicsImpl implements _WorldPhysics {
  const _$WorldPhysicsImpl({
    final List<PhysicalLaw> laws = const [],
    final List<PhysicalConstant> constants = const [],
    final List<PhysicalForce> forces = const [],
    this.gravityDescription,
    this.timeDescription,
    this.spaceDescription,
    final List<String> dimensions = const [],
    final Map<String, dynamic> customPhysics = const {},
  }) : _laws = laws,
       _constants = constants,
       _forces = forces,
       _dimensions = dimensions,
       _customPhysics = customPhysics;

  factory _$WorldPhysicsImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorldPhysicsImplFromJson(json);

  final List<PhysicalLaw> _laws;
  @override
  @JsonKey()
  List<PhysicalLaw> get laws {
    if (_laws is EqualUnmodifiableListView) return _laws;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_laws);
  }

  final List<PhysicalConstant> _constants;
  @override
  @JsonKey()
  List<PhysicalConstant> get constants {
    if (_constants is EqualUnmodifiableListView) return _constants;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_constants);
  }

  final List<PhysicalForce> _forces;
  @override
  @JsonKey()
  List<PhysicalForce> get forces {
    if (_forces is EqualUnmodifiableListView) return _forces;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_forces);
  }

  @override
  final String? gravityDescription;
  @override
  final String? timeDescription;
  @override
  final String? spaceDescription;
  final List<String> _dimensions;
  @override
  @JsonKey()
  List<String> get dimensions {
    if (_dimensions is EqualUnmodifiableListView) return _dimensions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dimensions);
  }

  final Map<String, dynamic> _customPhysics;
  @override
  @JsonKey()
  Map<String, dynamic> get customPhysics {
    if (_customPhysics is EqualUnmodifiableMapView) return _customPhysics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customPhysics);
  }

  @override
  String toString() {
    return 'WorldPhysics(laws: $laws, constants: $constants, forces: $forces, gravityDescription: $gravityDescription, timeDescription: $timeDescription, spaceDescription: $spaceDescription, dimensions: $dimensions, customPhysics: $customPhysics)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorldPhysicsImpl &&
            const DeepCollectionEquality().equals(other._laws, _laws) &&
            const DeepCollectionEquality().equals(
              other._constants,
              _constants,
            ) &&
            const DeepCollectionEquality().equals(other._forces, _forces) &&
            (identical(other.gravityDescription, gravityDescription) ||
                other.gravityDescription == gravityDescription) &&
            (identical(other.timeDescription, timeDescription) ||
                other.timeDescription == timeDescription) &&
            (identical(other.spaceDescription, spaceDescription) ||
                other.spaceDescription == spaceDescription) &&
            const DeepCollectionEquality().equals(
              other._dimensions,
              _dimensions,
            ) &&
            const DeepCollectionEquality().equals(
              other._customPhysics,
              _customPhysics,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_laws),
    const DeepCollectionEquality().hash(_constants),
    const DeepCollectionEquality().hash(_forces),
    gravityDescription,
    timeDescription,
    spaceDescription,
    const DeepCollectionEquality().hash(_dimensions),
    const DeepCollectionEquality().hash(_customPhysics),
  );

  /// Create a copy of WorldPhysics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WorldPhysicsImplCopyWith<_$WorldPhysicsImpl> get copyWith =>
      __$$WorldPhysicsImplCopyWithImpl<_$WorldPhysicsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorldPhysicsImplToJson(this);
  }
}

abstract class _WorldPhysics implements WorldPhysics {
  const factory _WorldPhysics({
    final List<PhysicalLaw> laws,
    final List<PhysicalConstant> constants,
    final List<PhysicalForce> forces,
    final String? gravityDescription,
    final String? timeDescription,
    final String? spaceDescription,
    final List<String> dimensions,
    final Map<String, dynamic> customPhysics,
  }) = _$WorldPhysicsImpl;

  factory _WorldPhysics.fromJson(Map<String, dynamic> json) =
      _$WorldPhysicsImpl.fromJson;

  @override
  List<PhysicalLaw> get laws;
  @override
  List<PhysicalConstant> get constants;
  @override
  List<PhysicalForce> get forces;
  @override
  String? get gravityDescription;
  @override
  String? get timeDescription;
  @override
  String? get spaceDescription;
  @override
  List<String> get dimensions;
  @override
  Map<String, dynamic> get customPhysics;

  /// Create a copy of WorldPhysics
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WorldPhysicsImplCopyWith<_$WorldPhysicsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PhysicalLaw _$PhysicalLawFromJson(Map<String, dynamic> json) {
  return _PhysicalLaw.fromJson(json);
}

/// @nodoc
mixin _$PhysicalLaw {
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  List<String> get effects => throw _privateConstructorUsedError;
  List<String> get exceptions => throw _privateConstructorUsedError;
  Map<String, dynamic> get parameters => throw _privateConstructorUsedError;

  /// Serializes this PhysicalLaw to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PhysicalLaw
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PhysicalLawCopyWith<PhysicalLaw> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PhysicalLawCopyWith<$Res> {
  factory $PhysicalLawCopyWith(
    PhysicalLaw value,
    $Res Function(PhysicalLaw) then,
  ) = _$PhysicalLawCopyWithImpl<$Res, PhysicalLaw>;
  @useResult
  $Res call({
    String name,
    String description,
    List<String> effects,
    List<String> exceptions,
    Map<String, dynamic> parameters,
  });
}

/// @nodoc
class _$PhysicalLawCopyWithImpl<$Res, $Val extends PhysicalLaw>
    implements $PhysicalLawCopyWith<$Res> {
  _$PhysicalLawCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PhysicalLaw
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = null,
    Object? effects = null,
    Object? exceptions = null,
    Object? parameters = null,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            effects: null == effects
                ? _value.effects
                : effects // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            exceptions: null == exceptions
                ? _value.exceptions
                : exceptions // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            parameters: null == parameters
                ? _value.parameters
                : parameters // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PhysicalLawImplCopyWith<$Res>
    implements $PhysicalLawCopyWith<$Res> {
  factory _$$PhysicalLawImplCopyWith(
    _$PhysicalLawImpl value,
    $Res Function(_$PhysicalLawImpl) then,
  ) = __$$PhysicalLawImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    String description,
    List<String> effects,
    List<String> exceptions,
    Map<String, dynamic> parameters,
  });
}

/// @nodoc
class __$$PhysicalLawImplCopyWithImpl<$Res>
    extends _$PhysicalLawCopyWithImpl<$Res, _$PhysicalLawImpl>
    implements _$$PhysicalLawImplCopyWith<$Res> {
  __$$PhysicalLawImplCopyWithImpl(
    _$PhysicalLawImpl _value,
    $Res Function(_$PhysicalLawImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PhysicalLaw
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = null,
    Object? effects = null,
    Object? exceptions = null,
    Object? parameters = null,
  }) {
    return _then(
      _$PhysicalLawImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        effects: null == effects
            ? _value._effects
            : effects // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        exceptions: null == exceptions
            ? _value._exceptions
            : exceptions // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        parameters: null == parameters
            ? _value._parameters
            : parameters // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PhysicalLawImpl implements _PhysicalLaw {
  const _$PhysicalLawImpl({
    required this.name,
    required this.description,
    final List<String> effects = const [],
    final List<String> exceptions = const [],
    final Map<String, dynamic> parameters = const {},
  }) : _effects = effects,
       _exceptions = exceptions,
       _parameters = parameters;

  factory _$PhysicalLawImpl.fromJson(Map<String, dynamic> json) =>
      _$$PhysicalLawImplFromJson(json);

  @override
  final String name;
  @override
  final String description;
  final List<String> _effects;
  @override
  @JsonKey()
  List<String> get effects {
    if (_effects is EqualUnmodifiableListView) return _effects;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_effects);
  }

  final List<String> _exceptions;
  @override
  @JsonKey()
  List<String> get exceptions {
    if (_exceptions is EqualUnmodifiableListView) return _exceptions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_exceptions);
  }

  final Map<String, dynamic> _parameters;
  @override
  @JsonKey()
  Map<String, dynamic> get parameters {
    if (_parameters is EqualUnmodifiableMapView) return _parameters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_parameters);
  }

  @override
  String toString() {
    return 'PhysicalLaw(name: $name, description: $description, effects: $effects, exceptions: $exceptions, parameters: $parameters)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PhysicalLawImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(other._effects, _effects) &&
            const DeepCollectionEquality().equals(
              other._exceptions,
              _exceptions,
            ) &&
            const DeepCollectionEquality().equals(
              other._parameters,
              _parameters,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    description,
    const DeepCollectionEquality().hash(_effects),
    const DeepCollectionEquality().hash(_exceptions),
    const DeepCollectionEquality().hash(_parameters),
  );

  /// Create a copy of PhysicalLaw
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PhysicalLawImplCopyWith<_$PhysicalLawImpl> get copyWith =>
      __$$PhysicalLawImplCopyWithImpl<_$PhysicalLawImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PhysicalLawImplToJson(this);
  }
}

abstract class _PhysicalLaw implements PhysicalLaw {
  const factory _PhysicalLaw({
    required final String name,
    required final String description,
    final List<String> effects,
    final List<String> exceptions,
    final Map<String, dynamic> parameters,
  }) = _$PhysicalLawImpl;

  factory _PhysicalLaw.fromJson(Map<String, dynamic> json) =
      _$PhysicalLawImpl.fromJson;

  @override
  String get name;
  @override
  String get description;
  @override
  List<String> get effects;
  @override
  List<String> get exceptions;
  @override
  Map<String, dynamic> get parameters;

  /// Create a copy of PhysicalLaw
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PhysicalLawImplCopyWith<_$PhysicalLawImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PhysicalConstant _$PhysicalConstantFromJson(Map<String, dynamic> json) {
  return _PhysicalConstant.fromJson(json);
}

/// @nodoc
mixin _$PhysicalConstant {
  String get name => throw _privateConstructorUsedError;
  String get value => throw _privateConstructorUsedError;
  String? get unit => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this PhysicalConstant to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PhysicalConstant
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PhysicalConstantCopyWith<PhysicalConstant> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PhysicalConstantCopyWith<$Res> {
  factory $PhysicalConstantCopyWith(
    PhysicalConstant value,
    $Res Function(PhysicalConstant) then,
  ) = _$PhysicalConstantCopyWithImpl<$Res, PhysicalConstant>;
  @useResult
  $Res call({
    String name,
    String value,
    String? unit,
    String? description,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$PhysicalConstantCopyWithImpl<$Res, $Val extends PhysicalConstant>
    implements $PhysicalConstantCopyWith<$Res> {
  _$PhysicalConstantCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PhysicalConstant
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? value = null,
    Object? unit = freezed,
    Object? description = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            value: null == value
                ? _value.value
                : value // ignore: cast_nullable_to_non_nullable
                      as String,
            unit: freezed == unit
                ? _value.unit
                : unit // ignore: cast_nullable_to_non_nullable
                      as String?,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PhysicalConstantImplCopyWith<$Res>
    implements $PhysicalConstantCopyWith<$Res> {
  factory _$$PhysicalConstantImplCopyWith(
    _$PhysicalConstantImpl value,
    $Res Function(_$PhysicalConstantImpl) then,
  ) = __$$PhysicalConstantImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    String value,
    String? unit,
    String? description,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$PhysicalConstantImplCopyWithImpl<$Res>
    extends _$PhysicalConstantCopyWithImpl<$Res, _$PhysicalConstantImpl>
    implements _$$PhysicalConstantImplCopyWith<$Res> {
  __$$PhysicalConstantImplCopyWithImpl(
    _$PhysicalConstantImpl _value,
    $Res Function(_$PhysicalConstantImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PhysicalConstant
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? value = null,
    Object? unit = freezed,
    Object? description = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _$PhysicalConstantImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        value: null == value
            ? _value.value
            : value // ignore: cast_nullable_to_non_nullable
                  as String,
        unit: freezed == unit
            ? _value.unit
            : unit // ignore: cast_nullable_to_non_nullable
                  as String?,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PhysicalConstantImpl implements _PhysicalConstant {
  const _$PhysicalConstantImpl({
    required this.name,
    required this.value,
    this.unit,
    this.description,
    final Map<String, dynamic> customAttributes = const {},
  }) : _customAttributes = customAttributes;

  factory _$PhysicalConstantImpl.fromJson(Map<String, dynamic> json) =>
      _$$PhysicalConstantImplFromJson(json);

  @override
  final String name;
  @override
  final String value;
  @override
  final String? unit;
  @override
  final String? description;
  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'PhysicalConstant(name: $name, value: $value, unit: $unit, description: $description, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PhysicalConstantImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.unit, unit) || other.unit == unit) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    value,
    unit,
    description,
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of PhysicalConstant
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PhysicalConstantImplCopyWith<_$PhysicalConstantImpl> get copyWith =>
      __$$PhysicalConstantImplCopyWithImpl<_$PhysicalConstantImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$PhysicalConstantImplToJson(this);
  }
}

abstract class _PhysicalConstant implements PhysicalConstant {
  const factory _PhysicalConstant({
    required final String name,
    required final String value,
    final String? unit,
    final String? description,
    final Map<String, dynamic> customAttributes,
  }) = _$PhysicalConstantImpl;

  factory _PhysicalConstant.fromJson(Map<String, dynamic> json) =
      _$PhysicalConstantImpl.fromJson;

  @override
  String get name;
  @override
  String get value;
  @override
  String? get unit;
  @override
  String? get description;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of PhysicalConstant
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PhysicalConstantImplCopyWith<_$PhysicalConstantImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PhysicalForce _$PhysicalForceFromJson(Map<String, dynamic> json) {
  return _PhysicalForce.fromJson(json);
}

/// @nodoc
mixin _$PhysicalForce {
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String? get strength => throw _privateConstructorUsedError;
  String? get range => throw _privateConstructorUsedError;
  List<String> get effects => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this PhysicalForce to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PhysicalForce
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PhysicalForceCopyWith<PhysicalForce> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PhysicalForceCopyWith<$Res> {
  factory $PhysicalForceCopyWith(
    PhysicalForce value,
    $Res Function(PhysicalForce) then,
  ) = _$PhysicalForceCopyWithImpl<$Res, PhysicalForce>;
  @useResult
  $Res call({
    String name,
    String description,
    String? strength,
    String? range,
    List<String> effects,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$PhysicalForceCopyWithImpl<$Res, $Val extends PhysicalForce>
    implements $PhysicalForceCopyWith<$Res> {
  _$PhysicalForceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PhysicalForce
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = null,
    Object? strength = freezed,
    Object? range = freezed,
    Object? effects = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            strength: freezed == strength
                ? _value.strength
                : strength // ignore: cast_nullable_to_non_nullable
                      as String?,
            range: freezed == range
                ? _value.range
                : range // ignore: cast_nullable_to_non_nullable
                      as String?,
            effects: null == effects
                ? _value.effects
                : effects // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PhysicalForceImplCopyWith<$Res>
    implements $PhysicalForceCopyWith<$Res> {
  factory _$$PhysicalForceImplCopyWith(
    _$PhysicalForceImpl value,
    $Res Function(_$PhysicalForceImpl) then,
  ) = __$$PhysicalForceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    String description,
    String? strength,
    String? range,
    List<String> effects,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$PhysicalForceImplCopyWithImpl<$Res>
    extends _$PhysicalForceCopyWithImpl<$Res, _$PhysicalForceImpl>
    implements _$$PhysicalForceImplCopyWith<$Res> {
  __$$PhysicalForceImplCopyWithImpl(
    _$PhysicalForceImpl _value,
    $Res Function(_$PhysicalForceImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PhysicalForce
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = null,
    Object? strength = freezed,
    Object? range = freezed,
    Object? effects = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$PhysicalForceImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        strength: freezed == strength
            ? _value.strength
            : strength // ignore: cast_nullable_to_non_nullable
                  as String?,
        range: freezed == range
            ? _value.range
            : range // ignore: cast_nullable_to_non_nullable
                  as String?,
        effects: null == effects
            ? _value._effects
            : effects // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PhysicalForceImpl implements _PhysicalForce {
  const _$PhysicalForceImpl({
    required this.name,
    required this.description,
    this.strength,
    this.range,
    final List<String> effects = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _effects = effects,
       _customAttributes = customAttributes;

  factory _$PhysicalForceImpl.fromJson(Map<String, dynamic> json) =>
      _$$PhysicalForceImplFromJson(json);

  @override
  final String name;
  @override
  final String description;
  @override
  final String? strength;
  @override
  final String? range;
  final List<String> _effects;
  @override
  @JsonKey()
  List<String> get effects {
    if (_effects is EqualUnmodifiableListView) return _effects;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_effects);
  }

  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'PhysicalForce(name: $name, description: $description, strength: $strength, range: $range, effects: $effects, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PhysicalForceImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.strength, strength) ||
                other.strength == strength) &&
            (identical(other.range, range) || other.range == range) &&
            const DeepCollectionEquality().equals(other._effects, _effects) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    description,
    strength,
    range,
    const DeepCollectionEquality().hash(_effects),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of PhysicalForce
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PhysicalForceImplCopyWith<_$PhysicalForceImpl> get copyWith =>
      __$$PhysicalForceImplCopyWithImpl<_$PhysicalForceImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PhysicalForceImplToJson(this);
  }
}

abstract class _PhysicalForce implements PhysicalForce {
  const factory _PhysicalForce({
    required final String name,
    required final String description,
    final String? strength,
    final String? range,
    final List<String> effects,
    final Map<String, dynamic> customAttributes,
  }) = _$PhysicalForceImpl;

  factory _PhysicalForce.fromJson(Map<String, dynamic> json) =
      _$PhysicalForceImpl.fromJson;

  @override
  String get name;
  @override
  String get description;
  @override
  String? get strength;
  @override
  String? get range;
  @override
  List<String> get effects;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of PhysicalForce
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PhysicalForceImplCopyWith<_$PhysicalForceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WorldHistory _$WorldHistoryFromJson(Map<String, dynamic> json) {
  return _WorldHistory.fromJson(json);
}

/// @nodoc
mixin _$WorldHistory {
  List<HistoricalEra> get eras => throw _privateConstructorUsedError;
  List<HistoricalEvent> get majorEvents => throw _privateConstructorUsedError;
  List<Civilization> get civilizations => throw _privateConstructorUsedError;
  List<War> get wars => throw _privateConstructorUsedError;
  List<Discovery> get discoveries => throw _privateConstructorUsedError;
  String? get creationMyth => throw _privateConstructorUsedError;
  Map<String, dynamic> get customHistory => throw _privateConstructorUsedError;

  /// Serializes this WorldHistory to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WorldHistory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WorldHistoryCopyWith<WorldHistory> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorldHistoryCopyWith<$Res> {
  factory $WorldHistoryCopyWith(
    WorldHistory value,
    $Res Function(WorldHistory) then,
  ) = _$WorldHistoryCopyWithImpl<$Res, WorldHistory>;
  @useResult
  $Res call({
    List<HistoricalEra> eras,
    List<HistoricalEvent> majorEvents,
    List<Civilization> civilizations,
    List<War> wars,
    List<Discovery> discoveries,
    String? creationMyth,
    Map<String, dynamic> customHistory,
  });
}

/// @nodoc
class _$WorldHistoryCopyWithImpl<$Res, $Val extends WorldHistory>
    implements $WorldHistoryCopyWith<$Res> {
  _$WorldHistoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WorldHistory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? eras = null,
    Object? majorEvents = null,
    Object? civilizations = null,
    Object? wars = null,
    Object? discoveries = null,
    Object? creationMyth = freezed,
    Object? customHistory = null,
  }) {
    return _then(
      _value.copyWith(
            eras: null == eras
                ? _value.eras
                : eras // ignore: cast_nullable_to_non_nullable
                      as List<HistoricalEra>,
            majorEvents: null == majorEvents
                ? _value.majorEvents
                : majorEvents // ignore: cast_nullable_to_non_nullable
                      as List<HistoricalEvent>,
            civilizations: null == civilizations
                ? _value.civilizations
                : civilizations // ignore: cast_nullable_to_non_nullable
                      as List<Civilization>,
            wars: null == wars
                ? _value.wars
                : wars // ignore: cast_nullable_to_non_nullable
                      as List<War>,
            discoveries: null == discoveries
                ? _value.discoveries
                : discoveries // ignore: cast_nullable_to_non_nullable
                      as List<Discovery>,
            creationMyth: freezed == creationMyth
                ? _value.creationMyth
                : creationMyth // ignore: cast_nullable_to_non_nullable
                      as String?,
            customHistory: null == customHistory
                ? _value.customHistory
                : customHistory // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$WorldHistoryImplCopyWith<$Res>
    implements $WorldHistoryCopyWith<$Res> {
  factory _$$WorldHistoryImplCopyWith(
    _$WorldHistoryImpl value,
    $Res Function(_$WorldHistoryImpl) then,
  ) = __$$WorldHistoryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    List<HistoricalEra> eras,
    List<HistoricalEvent> majorEvents,
    List<Civilization> civilizations,
    List<War> wars,
    List<Discovery> discoveries,
    String? creationMyth,
    Map<String, dynamic> customHistory,
  });
}

/// @nodoc
class __$$WorldHistoryImplCopyWithImpl<$Res>
    extends _$WorldHistoryCopyWithImpl<$Res, _$WorldHistoryImpl>
    implements _$$WorldHistoryImplCopyWith<$Res> {
  __$$WorldHistoryImplCopyWithImpl(
    _$WorldHistoryImpl _value,
    $Res Function(_$WorldHistoryImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of WorldHistory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? eras = null,
    Object? majorEvents = null,
    Object? civilizations = null,
    Object? wars = null,
    Object? discoveries = null,
    Object? creationMyth = freezed,
    Object? customHistory = null,
  }) {
    return _then(
      _$WorldHistoryImpl(
        eras: null == eras
            ? _value._eras
            : eras // ignore: cast_nullable_to_non_nullable
                  as List<HistoricalEra>,
        majorEvents: null == majorEvents
            ? _value._majorEvents
            : majorEvents // ignore: cast_nullable_to_non_nullable
                  as List<HistoricalEvent>,
        civilizations: null == civilizations
            ? _value._civilizations
            : civilizations // ignore: cast_nullable_to_non_nullable
                  as List<Civilization>,
        wars: null == wars
            ? _value._wars
            : wars // ignore: cast_nullable_to_non_nullable
                  as List<War>,
        discoveries: null == discoveries
            ? _value._discoveries
            : discoveries // ignore: cast_nullable_to_non_nullable
                  as List<Discovery>,
        creationMyth: freezed == creationMyth
            ? _value.creationMyth
            : creationMyth // ignore: cast_nullable_to_non_nullable
                  as String?,
        customHistory: null == customHistory
            ? _value._customHistory
            : customHistory // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$WorldHistoryImpl implements _WorldHistory {
  const _$WorldHistoryImpl({
    final List<HistoricalEra> eras = const [],
    final List<HistoricalEvent> majorEvents = const [],
    final List<Civilization> civilizations = const [],
    final List<War> wars = const [],
    final List<Discovery> discoveries = const [],
    this.creationMyth,
    final Map<String, dynamic> customHistory = const {},
  }) : _eras = eras,
       _majorEvents = majorEvents,
       _civilizations = civilizations,
       _wars = wars,
       _discoveries = discoveries,
       _customHistory = customHistory;

  factory _$WorldHistoryImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorldHistoryImplFromJson(json);

  final List<HistoricalEra> _eras;
  @override
  @JsonKey()
  List<HistoricalEra> get eras {
    if (_eras is EqualUnmodifiableListView) return _eras;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_eras);
  }

  final List<HistoricalEvent> _majorEvents;
  @override
  @JsonKey()
  List<HistoricalEvent> get majorEvents {
    if (_majorEvents is EqualUnmodifiableListView) return _majorEvents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_majorEvents);
  }

  final List<Civilization> _civilizations;
  @override
  @JsonKey()
  List<Civilization> get civilizations {
    if (_civilizations is EqualUnmodifiableListView) return _civilizations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_civilizations);
  }

  final List<War> _wars;
  @override
  @JsonKey()
  List<War> get wars {
    if (_wars is EqualUnmodifiableListView) return _wars;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_wars);
  }

  final List<Discovery> _discoveries;
  @override
  @JsonKey()
  List<Discovery> get discoveries {
    if (_discoveries is EqualUnmodifiableListView) return _discoveries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_discoveries);
  }

  @override
  final String? creationMyth;
  final Map<String, dynamic> _customHistory;
  @override
  @JsonKey()
  Map<String, dynamic> get customHistory {
    if (_customHistory is EqualUnmodifiableMapView) return _customHistory;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customHistory);
  }

  @override
  String toString() {
    return 'WorldHistory(eras: $eras, majorEvents: $majorEvents, civilizations: $civilizations, wars: $wars, discoveries: $discoveries, creationMyth: $creationMyth, customHistory: $customHistory)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorldHistoryImpl &&
            const DeepCollectionEquality().equals(other._eras, _eras) &&
            const DeepCollectionEquality().equals(
              other._majorEvents,
              _majorEvents,
            ) &&
            const DeepCollectionEquality().equals(
              other._civilizations,
              _civilizations,
            ) &&
            const DeepCollectionEquality().equals(other._wars, _wars) &&
            const DeepCollectionEquality().equals(
              other._discoveries,
              _discoveries,
            ) &&
            (identical(other.creationMyth, creationMyth) ||
                other.creationMyth == creationMyth) &&
            const DeepCollectionEquality().equals(
              other._customHistory,
              _customHistory,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_eras),
    const DeepCollectionEquality().hash(_majorEvents),
    const DeepCollectionEquality().hash(_civilizations),
    const DeepCollectionEquality().hash(_wars),
    const DeepCollectionEquality().hash(_discoveries),
    creationMyth,
    const DeepCollectionEquality().hash(_customHistory),
  );

  /// Create a copy of WorldHistory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WorldHistoryImplCopyWith<_$WorldHistoryImpl> get copyWith =>
      __$$WorldHistoryImplCopyWithImpl<_$WorldHistoryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorldHistoryImplToJson(this);
  }
}

abstract class _WorldHistory implements WorldHistory {
  const factory _WorldHistory({
    final List<HistoricalEra> eras,
    final List<HistoricalEvent> majorEvents,
    final List<Civilization> civilizations,
    final List<War> wars,
    final List<Discovery> discoveries,
    final String? creationMyth,
    final Map<String, dynamic> customHistory,
  }) = _$WorldHistoryImpl;

  factory _WorldHistory.fromJson(Map<String, dynamic> json) =
      _$WorldHistoryImpl.fromJson;

  @override
  List<HistoricalEra> get eras;
  @override
  List<HistoricalEvent> get majorEvents;
  @override
  List<Civilization> get civilizations;
  @override
  List<War> get wars;
  @override
  List<Discovery> get discoveries;
  @override
  String? get creationMyth;
  @override
  Map<String, dynamic> get customHistory;

  /// Create a copy of WorldHistory
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WorldHistoryImplCopyWith<_$WorldHistoryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

HistoricalEra _$HistoricalEraFromJson(Map<String, dynamic> json) {
  return _HistoricalEra.fromJson(json);
}

/// @nodoc
mixin _$HistoricalEra {
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  DateTime? get startDate => throw _privateConstructorUsedError;
  DateTime? get endDate => throw _privateConstructorUsedError;
  List<String> get characteristics => throw _privateConstructorUsedError;
  List<String> get majorEvents => throw _privateConstructorUsedError;
  List<String> get importantFigures => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this HistoricalEra to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of HistoricalEra
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HistoricalEraCopyWith<HistoricalEra> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HistoricalEraCopyWith<$Res> {
  factory $HistoricalEraCopyWith(
    HistoricalEra value,
    $Res Function(HistoricalEra) then,
  ) = _$HistoricalEraCopyWithImpl<$Res, HistoricalEra>;
  @useResult
  $Res call({
    String name,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    List<String> characteristics,
    List<String> majorEvents,
    List<String> importantFigures,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$HistoricalEraCopyWithImpl<$Res, $Val extends HistoricalEra>
    implements $HistoricalEraCopyWith<$Res> {
  _$HistoricalEraCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HistoricalEra
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? characteristics = null,
    Object? majorEvents = null,
    Object? importantFigures = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            startDate: freezed == startDate
                ? _value.startDate
                : startDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            endDate: freezed == endDate
                ? _value.endDate
                : endDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            characteristics: null == characteristics
                ? _value.characteristics
                : characteristics // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            majorEvents: null == majorEvents
                ? _value.majorEvents
                : majorEvents // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            importantFigures: null == importantFigures
                ? _value.importantFigures
                : importantFigures // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$HistoricalEraImplCopyWith<$Res>
    implements $HistoricalEraCopyWith<$Res> {
  factory _$$HistoricalEraImplCopyWith(
    _$HistoricalEraImpl value,
    $Res Function(_$HistoricalEraImpl) then,
  ) = __$$HistoricalEraImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    List<String> characteristics,
    List<String> majorEvents,
    List<String> importantFigures,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$HistoricalEraImplCopyWithImpl<$Res>
    extends _$HistoricalEraCopyWithImpl<$Res, _$HistoricalEraImpl>
    implements _$$HistoricalEraImplCopyWith<$Res> {
  __$$HistoricalEraImplCopyWithImpl(
    _$HistoricalEraImpl _value,
    $Res Function(_$HistoricalEraImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of HistoricalEra
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? characteristics = null,
    Object? majorEvents = null,
    Object? importantFigures = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$HistoricalEraImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        startDate: freezed == startDate
            ? _value.startDate
            : startDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        endDate: freezed == endDate
            ? _value.endDate
            : endDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        characteristics: null == characteristics
            ? _value._characteristics
            : characteristics // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        majorEvents: null == majorEvents
            ? _value._majorEvents
            : majorEvents // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        importantFigures: null == importantFigures
            ? _value._importantFigures
            : importantFigures // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$HistoricalEraImpl implements _HistoricalEra {
  const _$HistoricalEraImpl({
    required this.name,
    this.description,
    this.startDate,
    this.endDate,
    final List<String> characteristics = const [],
    final List<String> majorEvents = const [],
    final List<String> importantFigures = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _characteristics = characteristics,
       _majorEvents = majorEvents,
       _importantFigures = importantFigures,
       _customAttributes = customAttributes;

  factory _$HistoricalEraImpl.fromJson(Map<String, dynamic> json) =>
      _$$HistoricalEraImplFromJson(json);

  @override
  final String name;
  @override
  final String? description;
  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;
  final List<String> _characteristics;
  @override
  @JsonKey()
  List<String> get characteristics {
    if (_characteristics is EqualUnmodifiableListView) return _characteristics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_characteristics);
  }

  final List<String> _majorEvents;
  @override
  @JsonKey()
  List<String> get majorEvents {
    if (_majorEvents is EqualUnmodifiableListView) return _majorEvents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_majorEvents);
  }

  final List<String> _importantFigures;
  @override
  @JsonKey()
  List<String> get importantFigures {
    if (_importantFigures is EqualUnmodifiableListView)
      return _importantFigures;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_importantFigures);
  }

  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'HistoricalEra(name: $name, description: $description, startDate: $startDate, endDate: $endDate, characteristics: $characteristics, majorEvents: $majorEvents, importantFigures: $importantFigures, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistoricalEraImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            const DeepCollectionEquality().equals(
              other._characteristics,
              _characteristics,
            ) &&
            const DeepCollectionEquality().equals(
              other._majorEvents,
              _majorEvents,
            ) &&
            const DeepCollectionEquality().equals(
              other._importantFigures,
              _importantFigures,
            ) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    description,
    startDate,
    endDate,
    const DeepCollectionEquality().hash(_characteristics),
    const DeepCollectionEquality().hash(_majorEvents),
    const DeepCollectionEquality().hash(_importantFigures),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of HistoricalEra
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HistoricalEraImplCopyWith<_$HistoricalEraImpl> get copyWith =>
      __$$HistoricalEraImplCopyWithImpl<_$HistoricalEraImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HistoricalEraImplToJson(this);
  }
}

abstract class _HistoricalEra implements HistoricalEra {
  const factory _HistoricalEra({
    required final String name,
    final String? description,
    final DateTime? startDate,
    final DateTime? endDate,
    final List<String> characteristics,
    final List<String> majorEvents,
    final List<String> importantFigures,
    final Map<String, dynamic> customAttributes,
  }) = _$HistoricalEraImpl;

  factory _HistoricalEra.fromJson(Map<String, dynamic> json) =
      _$HistoricalEraImpl.fromJson;

  @override
  String get name;
  @override
  String? get description;
  @override
  DateTime? get startDate;
  @override
  DateTime? get endDate;
  @override
  List<String> get characteristics;
  @override
  List<String> get majorEvents;
  @override
  List<String> get importantFigures;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of HistoricalEra
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HistoricalEraImplCopyWith<_$HistoricalEraImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

HistoricalEvent _$HistoricalEventFromJson(Map<String, dynamic> json) {
  return _HistoricalEvent.fromJson(json);
}

/// @nodoc
mixin _$HistoricalEvent {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  DateTime get date => throw _privateConstructorUsedError;
  HistoricalEventType? get type => throw _privateConstructorUsedError;
  List<String> get involvedCivilizations => throw _privateConstructorUsedError;
  List<String> get involvedFigures => throw _privateConstructorUsedError;
  List<String> get consequences => throw _privateConstructorUsedError;
  String? get location => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this HistoricalEvent to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of HistoricalEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HistoricalEventCopyWith<HistoricalEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HistoricalEventCopyWith<$Res> {
  factory $HistoricalEventCopyWith(
    HistoricalEvent value,
    $Res Function(HistoricalEvent) then,
  ) = _$HistoricalEventCopyWithImpl<$Res, HistoricalEvent>;
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    DateTime date,
    HistoricalEventType? type,
    List<String> involvedCivilizations,
    List<String> involvedFigures,
    List<String> consequences,
    String? location,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$HistoricalEventCopyWithImpl<$Res, $Val extends HistoricalEvent>
    implements $HistoricalEventCopyWith<$Res> {
  _$HistoricalEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HistoricalEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? date = null,
    Object? type = freezed,
    Object? involvedCivilizations = null,
    Object? involvedFigures = null,
    Object? consequences = null,
    Object? location = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            date: null == date
                ? _value.date
                : date // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as HistoricalEventType?,
            involvedCivilizations: null == involvedCivilizations
                ? _value.involvedCivilizations
                : involvedCivilizations // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            involvedFigures: null == involvedFigures
                ? _value.involvedFigures
                : involvedFigures // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            consequences: null == consequences
                ? _value.consequences
                : consequences // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            location: freezed == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as String?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$HistoricalEventImplCopyWith<$Res>
    implements $HistoricalEventCopyWith<$Res> {
  factory _$$HistoricalEventImplCopyWith(
    _$HistoricalEventImpl value,
    $Res Function(_$HistoricalEventImpl) then,
  ) = __$$HistoricalEventImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    DateTime date,
    HistoricalEventType? type,
    List<String> involvedCivilizations,
    List<String> involvedFigures,
    List<String> consequences,
    String? location,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$HistoricalEventImplCopyWithImpl<$Res>
    extends _$HistoricalEventCopyWithImpl<$Res, _$HistoricalEventImpl>
    implements _$$HistoricalEventImplCopyWith<$Res> {
  __$$HistoricalEventImplCopyWithImpl(
    _$HistoricalEventImpl _value,
    $Res Function(_$HistoricalEventImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of HistoricalEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? date = null,
    Object? type = freezed,
    Object? involvedCivilizations = null,
    Object? involvedFigures = null,
    Object? consequences = null,
    Object? location = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _$HistoricalEventImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        date: null == date
            ? _value.date
            : date // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as HistoricalEventType?,
        involvedCivilizations: null == involvedCivilizations
            ? _value._involvedCivilizations
            : involvedCivilizations // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        involvedFigures: null == involvedFigures
            ? _value._involvedFigures
            : involvedFigures // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        consequences: null == consequences
            ? _value._consequences
            : consequences // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        location: freezed == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as String?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$HistoricalEventImpl implements _HistoricalEvent {
  const _$HistoricalEventImpl({
    required this.id,
    required this.name,
    required this.description,
    required this.date,
    this.type,
    final List<String> involvedCivilizations = const [],
    final List<String> involvedFigures = const [],
    final List<String> consequences = const [],
    this.location,
    final Map<String, dynamic> customAttributes = const {},
  }) : _involvedCivilizations = involvedCivilizations,
       _involvedFigures = involvedFigures,
       _consequences = consequences,
       _customAttributes = customAttributes;

  factory _$HistoricalEventImpl.fromJson(Map<String, dynamic> json) =>
      _$$HistoricalEventImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final DateTime date;
  @override
  final HistoricalEventType? type;
  final List<String> _involvedCivilizations;
  @override
  @JsonKey()
  List<String> get involvedCivilizations {
    if (_involvedCivilizations is EqualUnmodifiableListView)
      return _involvedCivilizations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_involvedCivilizations);
  }

  final List<String> _involvedFigures;
  @override
  @JsonKey()
  List<String> get involvedFigures {
    if (_involvedFigures is EqualUnmodifiableListView) return _involvedFigures;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_involvedFigures);
  }

  final List<String> _consequences;
  @override
  @JsonKey()
  List<String> get consequences {
    if (_consequences is EqualUnmodifiableListView) return _consequences;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_consequences);
  }

  @override
  final String? location;
  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'HistoricalEvent(id: $id, name: $name, description: $description, date: $date, type: $type, involvedCivilizations: $involvedCivilizations, involvedFigures: $involvedFigures, consequences: $consequences, location: $location, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistoricalEventImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(
              other._involvedCivilizations,
              _involvedCivilizations,
            ) &&
            const DeepCollectionEquality().equals(
              other._involvedFigures,
              _involvedFigures,
            ) &&
            const DeepCollectionEquality().equals(
              other._consequences,
              _consequences,
            ) &&
            (identical(other.location, location) ||
                other.location == location) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    date,
    type,
    const DeepCollectionEquality().hash(_involvedCivilizations),
    const DeepCollectionEquality().hash(_involvedFigures),
    const DeepCollectionEquality().hash(_consequences),
    location,
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of HistoricalEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HistoricalEventImplCopyWith<_$HistoricalEventImpl> get copyWith =>
      __$$HistoricalEventImplCopyWithImpl<_$HistoricalEventImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$HistoricalEventImplToJson(this);
  }
}

abstract class _HistoricalEvent implements HistoricalEvent {
  const factory _HistoricalEvent({
    required final String id,
    required final String name,
    required final String description,
    required final DateTime date,
    final HistoricalEventType? type,
    final List<String> involvedCivilizations,
    final List<String> involvedFigures,
    final List<String> consequences,
    final String? location,
    final Map<String, dynamic> customAttributes,
  }) = _$HistoricalEventImpl;

  factory _HistoricalEvent.fromJson(Map<String, dynamic> json) =
      _$HistoricalEventImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  DateTime get date;
  @override
  HistoricalEventType? get type;
  @override
  List<String> get involvedCivilizations;
  @override
  List<String> get involvedFigures;
  @override
  List<String> get consequences;
  @override
  String? get location;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of HistoricalEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HistoricalEventImplCopyWith<_$HistoricalEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Civilization _$CivilizationFromJson(Map<String, dynamic> json) {
  return _Civilization.fromJson(json);
}

/// @nodoc
mixin _$Civilization {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  CivilizationStatus? get status => throw _privateConstructorUsedError;
  DateTime? get foundedDate => throw _privateConstructorUsedError;
  DateTime? get endDate => throw _privateConstructorUsedError;
  List<String> get territories => throw _privateConstructorUsedError;
  List<String> get achievements => throw _privateConstructorUsedError;
  List<String> get technologies => throw _privateConstructorUsedError;
  String? get government => throw _privateConstructorUsedError;
  String? get culture => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this Civilization to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Civilization
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CivilizationCopyWith<Civilization> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CivilizationCopyWith<$Res> {
  factory $CivilizationCopyWith(
    Civilization value,
    $Res Function(Civilization) then,
  ) = _$CivilizationCopyWithImpl<$Res, Civilization>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    CivilizationStatus? status,
    DateTime? foundedDate,
    DateTime? endDate,
    List<String> territories,
    List<String> achievements,
    List<String> technologies,
    String? government,
    String? culture,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$CivilizationCopyWithImpl<$Res, $Val extends Civilization>
    implements $CivilizationCopyWith<$Res> {
  _$CivilizationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Civilization
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? status = freezed,
    Object? foundedDate = freezed,
    Object? endDate = freezed,
    Object? territories = null,
    Object? achievements = null,
    Object? technologies = null,
    Object? government = freezed,
    Object? culture = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            status: freezed == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as CivilizationStatus?,
            foundedDate: freezed == foundedDate
                ? _value.foundedDate
                : foundedDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            endDate: freezed == endDate
                ? _value.endDate
                : endDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            territories: null == territories
                ? _value.territories
                : territories // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            achievements: null == achievements
                ? _value.achievements
                : achievements // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            technologies: null == technologies
                ? _value.technologies
                : technologies // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            government: freezed == government
                ? _value.government
                : government // ignore: cast_nullable_to_non_nullable
                      as String?,
            culture: freezed == culture
                ? _value.culture
                : culture // ignore: cast_nullable_to_non_nullable
                      as String?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CivilizationImplCopyWith<$Res>
    implements $CivilizationCopyWith<$Res> {
  factory _$$CivilizationImplCopyWith(
    _$CivilizationImpl value,
    $Res Function(_$CivilizationImpl) then,
  ) = __$$CivilizationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    CivilizationStatus? status,
    DateTime? foundedDate,
    DateTime? endDate,
    List<String> territories,
    List<String> achievements,
    List<String> technologies,
    String? government,
    String? culture,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$CivilizationImplCopyWithImpl<$Res>
    extends _$CivilizationCopyWithImpl<$Res, _$CivilizationImpl>
    implements _$$CivilizationImplCopyWith<$Res> {
  __$$CivilizationImplCopyWithImpl(
    _$CivilizationImpl _value,
    $Res Function(_$CivilizationImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Civilization
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? status = freezed,
    Object? foundedDate = freezed,
    Object? endDate = freezed,
    Object? territories = null,
    Object? achievements = null,
    Object? technologies = null,
    Object? government = freezed,
    Object? culture = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _$CivilizationImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        status: freezed == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as CivilizationStatus?,
        foundedDate: freezed == foundedDate
            ? _value.foundedDate
            : foundedDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        endDate: freezed == endDate
            ? _value.endDate
            : endDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        territories: null == territories
            ? _value._territories
            : territories // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        achievements: null == achievements
            ? _value._achievements
            : achievements // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        technologies: null == technologies
            ? _value._technologies
            : technologies // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        government: freezed == government
            ? _value.government
            : government // ignore: cast_nullable_to_non_nullable
                  as String?,
        culture: freezed == culture
            ? _value.culture
            : culture // ignore: cast_nullable_to_non_nullable
                  as String?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CivilizationImpl implements _Civilization {
  const _$CivilizationImpl({
    required this.id,
    required this.name,
    this.description,
    this.status,
    this.foundedDate,
    this.endDate,
    final List<String> territories = const [],
    final List<String> achievements = const [],
    final List<String> technologies = const [],
    this.government,
    this.culture,
    final Map<String, dynamic> customAttributes = const {},
  }) : _territories = territories,
       _achievements = achievements,
       _technologies = technologies,
       _customAttributes = customAttributes;

  factory _$CivilizationImpl.fromJson(Map<String, dynamic> json) =>
      _$$CivilizationImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final CivilizationStatus? status;
  @override
  final DateTime? foundedDate;
  @override
  final DateTime? endDate;
  final List<String> _territories;
  @override
  @JsonKey()
  List<String> get territories {
    if (_territories is EqualUnmodifiableListView) return _territories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_territories);
  }

  final List<String> _achievements;
  @override
  @JsonKey()
  List<String> get achievements {
    if (_achievements is EqualUnmodifiableListView) return _achievements;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_achievements);
  }

  final List<String> _technologies;
  @override
  @JsonKey()
  List<String> get technologies {
    if (_technologies is EqualUnmodifiableListView) return _technologies;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_technologies);
  }

  @override
  final String? government;
  @override
  final String? culture;
  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'Civilization(id: $id, name: $name, description: $description, status: $status, foundedDate: $foundedDate, endDate: $endDate, territories: $territories, achievements: $achievements, technologies: $technologies, government: $government, culture: $culture, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CivilizationImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.foundedDate, foundedDate) ||
                other.foundedDate == foundedDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            const DeepCollectionEquality().equals(
              other._territories,
              _territories,
            ) &&
            const DeepCollectionEquality().equals(
              other._achievements,
              _achievements,
            ) &&
            const DeepCollectionEquality().equals(
              other._technologies,
              _technologies,
            ) &&
            (identical(other.government, government) ||
                other.government == government) &&
            (identical(other.culture, culture) || other.culture == culture) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    status,
    foundedDate,
    endDate,
    const DeepCollectionEquality().hash(_territories),
    const DeepCollectionEquality().hash(_achievements),
    const DeepCollectionEquality().hash(_technologies),
    government,
    culture,
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of Civilization
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CivilizationImplCopyWith<_$CivilizationImpl> get copyWith =>
      __$$CivilizationImplCopyWithImpl<_$CivilizationImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CivilizationImplToJson(this);
  }
}

abstract class _Civilization implements Civilization {
  const factory _Civilization({
    required final String id,
    required final String name,
    final String? description,
    final CivilizationStatus? status,
    final DateTime? foundedDate,
    final DateTime? endDate,
    final List<String> territories,
    final List<String> achievements,
    final List<String> technologies,
    final String? government,
    final String? culture,
    final Map<String, dynamic> customAttributes,
  }) = _$CivilizationImpl;

  factory _Civilization.fromJson(Map<String, dynamic> json) =
      _$CivilizationImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  CivilizationStatus? get status;
  @override
  DateTime? get foundedDate;
  @override
  DateTime? get endDate;
  @override
  List<String> get territories;
  @override
  List<String> get achievements;
  @override
  List<String> get technologies;
  @override
  String? get government;
  @override
  String? get culture;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of Civilization
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CivilizationImplCopyWith<_$CivilizationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

War _$WarFromJson(Map<String, dynamic> json) {
  return _War.fromJson(json);
}

/// @nodoc
mixin _$War {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  DateTime? get startDate => throw _privateConstructorUsedError;
  DateTime? get endDate => throw _privateConstructorUsedError;
  List<String> get participants => throw _privateConstructorUsedError;
  List<String> get causes => throw _privateConstructorUsedError;
  List<String> get consequences => throw _privateConstructorUsedError;
  String? get outcome => throw _privateConstructorUsedError;
  List<String> get majorBattles => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this War to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of War
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WarCopyWith<War> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WarCopyWith<$Res> {
  factory $WarCopyWith(War value, $Res Function(War) then) =
      _$WarCopyWithImpl<$Res, War>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    List<String> participants,
    List<String> causes,
    List<String> consequences,
    String? outcome,
    List<String> majorBattles,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$WarCopyWithImpl<$Res, $Val extends War> implements $WarCopyWith<$Res> {
  _$WarCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of War
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? participants = null,
    Object? causes = null,
    Object? consequences = null,
    Object? outcome = freezed,
    Object? majorBattles = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            startDate: freezed == startDate
                ? _value.startDate
                : startDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            endDate: freezed == endDate
                ? _value.endDate
                : endDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            participants: null == participants
                ? _value.participants
                : participants // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            causes: null == causes
                ? _value.causes
                : causes // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            consequences: null == consequences
                ? _value.consequences
                : consequences // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            outcome: freezed == outcome
                ? _value.outcome
                : outcome // ignore: cast_nullable_to_non_nullable
                      as String?,
            majorBattles: null == majorBattles
                ? _value.majorBattles
                : majorBattles // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$WarImplCopyWith<$Res> implements $WarCopyWith<$Res> {
  factory _$$WarImplCopyWith(_$WarImpl value, $Res Function(_$WarImpl) then) =
      __$$WarImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    List<String> participants,
    List<String> causes,
    List<String> consequences,
    String? outcome,
    List<String> majorBattles,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$WarImplCopyWithImpl<$Res> extends _$WarCopyWithImpl<$Res, _$WarImpl>
    implements _$$WarImplCopyWith<$Res> {
  __$$WarImplCopyWithImpl(_$WarImpl _value, $Res Function(_$WarImpl) _then)
    : super(_value, _then);

  /// Create a copy of War
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? participants = null,
    Object? causes = null,
    Object? consequences = null,
    Object? outcome = freezed,
    Object? majorBattles = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$WarImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        startDate: freezed == startDate
            ? _value.startDate
            : startDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        endDate: freezed == endDate
            ? _value.endDate
            : endDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        participants: null == participants
            ? _value._participants
            : participants // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        causes: null == causes
            ? _value._causes
            : causes // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        consequences: null == consequences
            ? _value._consequences
            : consequences // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        outcome: freezed == outcome
            ? _value.outcome
            : outcome // ignore: cast_nullable_to_non_nullable
                  as String?,
        majorBattles: null == majorBattles
            ? _value._majorBattles
            : majorBattles // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$WarImpl implements _War {
  const _$WarImpl({
    required this.id,
    required this.name,
    this.description,
    this.startDate,
    this.endDate,
    final List<String> participants = const [],
    final List<String> causes = const [],
    final List<String> consequences = const [],
    this.outcome,
    final List<String> majorBattles = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _participants = participants,
       _causes = causes,
       _consequences = consequences,
       _majorBattles = majorBattles,
       _customAttributes = customAttributes;

  factory _$WarImpl.fromJson(Map<String, dynamic> json) =>
      _$$WarImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;
  final List<String> _participants;
  @override
  @JsonKey()
  List<String> get participants {
    if (_participants is EqualUnmodifiableListView) return _participants;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_participants);
  }

  final List<String> _causes;
  @override
  @JsonKey()
  List<String> get causes {
    if (_causes is EqualUnmodifiableListView) return _causes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_causes);
  }

  final List<String> _consequences;
  @override
  @JsonKey()
  List<String> get consequences {
    if (_consequences is EqualUnmodifiableListView) return _consequences;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_consequences);
  }

  @override
  final String? outcome;
  final List<String> _majorBattles;
  @override
  @JsonKey()
  List<String> get majorBattles {
    if (_majorBattles is EqualUnmodifiableListView) return _majorBattles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_majorBattles);
  }

  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'War(id: $id, name: $name, description: $description, startDate: $startDate, endDate: $endDate, participants: $participants, causes: $causes, consequences: $consequences, outcome: $outcome, majorBattles: $majorBattles, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WarImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            const DeepCollectionEquality().equals(
              other._participants,
              _participants,
            ) &&
            const DeepCollectionEquality().equals(other._causes, _causes) &&
            const DeepCollectionEquality().equals(
              other._consequences,
              _consequences,
            ) &&
            (identical(other.outcome, outcome) || other.outcome == outcome) &&
            const DeepCollectionEquality().equals(
              other._majorBattles,
              _majorBattles,
            ) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    startDate,
    endDate,
    const DeepCollectionEquality().hash(_participants),
    const DeepCollectionEquality().hash(_causes),
    const DeepCollectionEquality().hash(_consequences),
    outcome,
    const DeepCollectionEquality().hash(_majorBattles),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of War
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WarImplCopyWith<_$WarImpl> get copyWith =>
      __$$WarImplCopyWithImpl<_$WarImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WarImplToJson(this);
  }
}

abstract class _War implements War {
  const factory _War({
    required final String id,
    required final String name,
    final String? description,
    final DateTime? startDate,
    final DateTime? endDate,
    final List<String> participants,
    final List<String> causes,
    final List<String> consequences,
    final String? outcome,
    final List<String> majorBattles,
    final Map<String, dynamic> customAttributes,
  }) = _$WarImpl;

  factory _War.fromJson(Map<String, dynamic> json) = _$WarImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  DateTime? get startDate;
  @override
  DateTime? get endDate;
  @override
  List<String> get participants;
  @override
  List<String> get causes;
  @override
  List<String> get consequences;
  @override
  String? get outcome;
  @override
  List<String> get majorBattles;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of War
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WarImplCopyWith<_$WarImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Discovery _$DiscoveryFromJson(Map<String, dynamic> json) {
  return _Discovery.fromJson(json);
}

/// @nodoc
mixin _$Discovery {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  DateTime? get date => throw _privateConstructorUsedError;
  String? get discoverer => throw _privateConstructorUsedError;
  DiscoveryType? get type => throw _privateConstructorUsedError;
  List<String> get impacts => throw _privateConstructorUsedError;
  String? get location => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this Discovery to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Discovery
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DiscoveryCopyWith<Discovery> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DiscoveryCopyWith<$Res> {
  factory $DiscoveryCopyWith(Discovery value, $Res Function(Discovery) then) =
      _$DiscoveryCopyWithImpl<$Res, Discovery>;
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    DateTime? date,
    String? discoverer,
    DiscoveryType? type,
    List<String> impacts,
    String? location,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$DiscoveryCopyWithImpl<$Res, $Val extends Discovery>
    implements $DiscoveryCopyWith<$Res> {
  _$DiscoveryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Discovery
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? date = freezed,
    Object? discoverer = freezed,
    Object? type = freezed,
    Object? impacts = null,
    Object? location = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            date: freezed == date
                ? _value.date
                : date // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            discoverer: freezed == discoverer
                ? _value.discoverer
                : discoverer // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as DiscoveryType?,
            impacts: null == impacts
                ? _value.impacts
                : impacts // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            location: freezed == location
                ? _value.location
                : location // ignore: cast_nullable_to_non_nullable
                      as String?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DiscoveryImplCopyWith<$Res>
    implements $DiscoveryCopyWith<$Res> {
  factory _$$DiscoveryImplCopyWith(
    _$DiscoveryImpl value,
    $Res Function(_$DiscoveryImpl) then,
  ) = __$$DiscoveryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    DateTime? date,
    String? discoverer,
    DiscoveryType? type,
    List<String> impacts,
    String? location,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$DiscoveryImplCopyWithImpl<$Res>
    extends _$DiscoveryCopyWithImpl<$Res, _$DiscoveryImpl>
    implements _$$DiscoveryImplCopyWith<$Res> {
  __$$DiscoveryImplCopyWithImpl(
    _$DiscoveryImpl _value,
    $Res Function(_$DiscoveryImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Discovery
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? date = freezed,
    Object? discoverer = freezed,
    Object? type = freezed,
    Object? impacts = null,
    Object? location = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _$DiscoveryImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        date: freezed == date
            ? _value.date
            : date // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        discoverer: freezed == discoverer
            ? _value.discoverer
            : discoverer // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as DiscoveryType?,
        impacts: null == impacts
            ? _value._impacts
            : impacts // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        location: freezed == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as String?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$DiscoveryImpl implements _Discovery {
  const _$DiscoveryImpl({
    required this.id,
    required this.name,
    required this.description,
    this.date,
    this.discoverer,
    this.type,
    final List<String> impacts = const [],
    this.location,
    final Map<String, dynamic> customAttributes = const {},
  }) : _impacts = impacts,
       _customAttributes = customAttributes;

  factory _$DiscoveryImpl.fromJson(Map<String, dynamic> json) =>
      _$$DiscoveryImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final DateTime? date;
  @override
  final String? discoverer;
  @override
  final DiscoveryType? type;
  final List<String> _impacts;
  @override
  @JsonKey()
  List<String> get impacts {
    if (_impacts is EqualUnmodifiableListView) return _impacts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_impacts);
  }

  @override
  final String? location;
  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'Discovery(id: $id, name: $name, description: $description, date: $date, discoverer: $discoverer, type: $type, impacts: $impacts, location: $location, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DiscoveryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.discoverer, discoverer) ||
                other.discoverer == discoverer) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other._impacts, _impacts) &&
            (identical(other.location, location) ||
                other.location == location) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    date,
    discoverer,
    type,
    const DeepCollectionEquality().hash(_impacts),
    location,
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of Discovery
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DiscoveryImplCopyWith<_$DiscoveryImpl> get copyWith =>
      __$$DiscoveryImplCopyWithImpl<_$DiscoveryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DiscoveryImplToJson(this);
  }
}

abstract class _Discovery implements Discovery {
  const factory _Discovery({
    required final String id,
    required final String name,
    required final String description,
    final DateTime? date,
    final String? discoverer,
    final DiscoveryType? type,
    final List<String> impacts,
    final String? location,
    final Map<String, dynamic> customAttributes,
  }) = _$DiscoveryImpl;

  factory _Discovery.fromJson(Map<String, dynamic> json) =
      _$DiscoveryImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  DateTime? get date;
  @override
  String? get discoverer;
  @override
  DiscoveryType? get type;
  @override
  List<String> get impacts;
  @override
  String? get location;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of Discovery
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DiscoveryImplCopyWith<_$DiscoveryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WorldGeography _$WorldGeographyFromJson(Map<String, dynamic> json) {
  return _WorldGeography.fromJson(json);
}

/// @nodoc
mixin _$WorldGeography {
  List<Continent> get continents => throw _privateConstructorUsedError;
  List<Ocean> get oceans => throw _privateConstructorUsedError;
  List<MountainRange> get mountainRanges => throw _privateConstructorUsedError;
  List<River> get rivers => throw _privateConstructorUsedError;
  List<Desert> get deserts => throw _privateConstructorUsedError;
  List<Forest> get forests => throw _privateConstructorUsedError;
  String? get worldShape => throw _privateConstructorUsedError; // 世界形状
  String? get size => throw _privateConstructorUsedError; // 世界大小
  Map<String, dynamic> get customGeography =>
      throw _privateConstructorUsedError;

  /// Serializes this WorldGeography to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WorldGeography
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WorldGeographyCopyWith<WorldGeography> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorldGeographyCopyWith<$Res> {
  factory $WorldGeographyCopyWith(
    WorldGeography value,
    $Res Function(WorldGeography) then,
  ) = _$WorldGeographyCopyWithImpl<$Res, WorldGeography>;
  @useResult
  $Res call({
    List<Continent> continents,
    List<Ocean> oceans,
    List<MountainRange> mountainRanges,
    List<River> rivers,
    List<Desert> deserts,
    List<Forest> forests,
    String? worldShape,
    String? size,
    Map<String, dynamic> customGeography,
  });
}

/// @nodoc
class _$WorldGeographyCopyWithImpl<$Res, $Val extends WorldGeography>
    implements $WorldGeographyCopyWith<$Res> {
  _$WorldGeographyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WorldGeography
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? continents = null,
    Object? oceans = null,
    Object? mountainRanges = null,
    Object? rivers = null,
    Object? deserts = null,
    Object? forests = null,
    Object? worldShape = freezed,
    Object? size = freezed,
    Object? customGeography = null,
  }) {
    return _then(
      _value.copyWith(
            continents: null == continents
                ? _value.continents
                : continents // ignore: cast_nullable_to_non_nullable
                      as List<Continent>,
            oceans: null == oceans
                ? _value.oceans
                : oceans // ignore: cast_nullable_to_non_nullable
                      as List<Ocean>,
            mountainRanges: null == mountainRanges
                ? _value.mountainRanges
                : mountainRanges // ignore: cast_nullable_to_non_nullable
                      as List<MountainRange>,
            rivers: null == rivers
                ? _value.rivers
                : rivers // ignore: cast_nullable_to_non_nullable
                      as List<River>,
            deserts: null == deserts
                ? _value.deserts
                : deserts // ignore: cast_nullable_to_non_nullable
                      as List<Desert>,
            forests: null == forests
                ? _value.forests
                : forests // ignore: cast_nullable_to_non_nullable
                      as List<Forest>,
            worldShape: freezed == worldShape
                ? _value.worldShape
                : worldShape // ignore: cast_nullable_to_non_nullable
                      as String?,
            size: freezed == size
                ? _value.size
                : size // ignore: cast_nullable_to_non_nullable
                      as String?,
            customGeography: null == customGeography
                ? _value.customGeography
                : customGeography // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$WorldGeographyImplCopyWith<$Res>
    implements $WorldGeographyCopyWith<$Res> {
  factory _$$WorldGeographyImplCopyWith(
    _$WorldGeographyImpl value,
    $Res Function(_$WorldGeographyImpl) then,
  ) = __$$WorldGeographyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    List<Continent> continents,
    List<Ocean> oceans,
    List<MountainRange> mountainRanges,
    List<River> rivers,
    List<Desert> deserts,
    List<Forest> forests,
    String? worldShape,
    String? size,
    Map<String, dynamic> customGeography,
  });
}

/// @nodoc
class __$$WorldGeographyImplCopyWithImpl<$Res>
    extends _$WorldGeographyCopyWithImpl<$Res, _$WorldGeographyImpl>
    implements _$$WorldGeographyImplCopyWith<$Res> {
  __$$WorldGeographyImplCopyWithImpl(
    _$WorldGeographyImpl _value,
    $Res Function(_$WorldGeographyImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of WorldGeography
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? continents = null,
    Object? oceans = null,
    Object? mountainRanges = null,
    Object? rivers = null,
    Object? deserts = null,
    Object? forests = null,
    Object? worldShape = freezed,
    Object? size = freezed,
    Object? customGeography = null,
  }) {
    return _then(
      _$WorldGeographyImpl(
        continents: null == continents
            ? _value._continents
            : continents // ignore: cast_nullable_to_non_nullable
                  as List<Continent>,
        oceans: null == oceans
            ? _value._oceans
            : oceans // ignore: cast_nullable_to_non_nullable
                  as List<Ocean>,
        mountainRanges: null == mountainRanges
            ? _value._mountainRanges
            : mountainRanges // ignore: cast_nullable_to_non_nullable
                  as List<MountainRange>,
        rivers: null == rivers
            ? _value._rivers
            : rivers // ignore: cast_nullable_to_non_nullable
                  as List<River>,
        deserts: null == deserts
            ? _value._deserts
            : deserts // ignore: cast_nullable_to_non_nullable
                  as List<Desert>,
        forests: null == forests
            ? _value._forests
            : forests // ignore: cast_nullable_to_non_nullable
                  as List<Forest>,
        worldShape: freezed == worldShape
            ? _value.worldShape
            : worldShape // ignore: cast_nullable_to_non_nullable
                  as String?,
        size: freezed == size
            ? _value.size
            : size // ignore: cast_nullable_to_non_nullable
                  as String?,
        customGeography: null == customGeography
            ? _value._customGeography
            : customGeography // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$WorldGeographyImpl implements _WorldGeography {
  const _$WorldGeographyImpl({
    final List<Continent> continents = const [],
    final List<Ocean> oceans = const [],
    final List<MountainRange> mountainRanges = const [],
    final List<River> rivers = const [],
    final List<Desert> deserts = const [],
    final List<Forest> forests = const [],
    this.worldShape,
    this.size,
    final Map<String, dynamic> customGeography = const {},
  }) : _continents = continents,
       _oceans = oceans,
       _mountainRanges = mountainRanges,
       _rivers = rivers,
       _deserts = deserts,
       _forests = forests,
       _customGeography = customGeography;

  factory _$WorldGeographyImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorldGeographyImplFromJson(json);

  final List<Continent> _continents;
  @override
  @JsonKey()
  List<Continent> get continents {
    if (_continents is EqualUnmodifiableListView) return _continents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_continents);
  }

  final List<Ocean> _oceans;
  @override
  @JsonKey()
  List<Ocean> get oceans {
    if (_oceans is EqualUnmodifiableListView) return _oceans;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_oceans);
  }

  final List<MountainRange> _mountainRanges;
  @override
  @JsonKey()
  List<MountainRange> get mountainRanges {
    if (_mountainRanges is EqualUnmodifiableListView) return _mountainRanges;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_mountainRanges);
  }

  final List<River> _rivers;
  @override
  @JsonKey()
  List<River> get rivers {
    if (_rivers is EqualUnmodifiableListView) return _rivers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_rivers);
  }

  final List<Desert> _deserts;
  @override
  @JsonKey()
  List<Desert> get deserts {
    if (_deserts is EqualUnmodifiableListView) return _deserts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_deserts);
  }

  final List<Forest> _forests;
  @override
  @JsonKey()
  List<Forest> get forests {
    if (_forests is EqualUnmodifiableListView) return _forests;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_forests);
  }

  @override
  final String? worldShape;
  // 世界形状
  @override
  final String? size;
  // 世界大小
  final Map<String, dynamic> _customGeography;
  // 世界大小
  @override
  @JsonKey()
  Map<String, dynamic> get customGeography {
    if (_customGeography is EqualUnmodifiableMapView) return _customGeography;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customGeography);
  }

  @override
  String toString() {
    return 'WorldGeography(continents: $continents, oceans: $oceans, mountainRanges: $mountainRanges, rivers: $rivers, deserts: $deserts, forests: $forests, worldShape: $worldShape, size: $size, customGeography: $customGeography)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorldGeographyImpl &&
            const DeepCollectionEquality().equals(
              other._continents,
              _continents,
            ) &&
            const DeepCollectionEquality().equals(other._oceans, _oceans) &&
            const DeepCollectionEquality().equals(
              other._mountainRanges,
              _mountainRanges,
            ) &&
            const DeepCollectionEquality().equals(other._rivers, _rivers) &&
            const DeepCollectionEquality().equals(other._deserts, _deserts) &&
            const DeepCollectionEquality().equals(other._forests, _forests) &&
            (identical(other.worldShape, worldShape) ||
                other.worldShape == worldShape) &&
            (identical(other.size, size) || other.size == size) &&
            const DeepCollectionEquality().equals(
              other._customGeography,
              _customGeography,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_continents),
    const DeepCollectionEquality().hash(_oceans),
    const DeepCollectionEquality().hash(_mountainRanges),
    const DeepCollectionEquality().hash(_rivers),
    const DeepCollectionEquality().hash(_deserts),
    const DeepCollectionEquality().hash(_forests),
    worldShape,
    size,
    const DeepCollectionEquality().hash(_customGeography),
  );

  /// Create a copy of WorldGeography
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WorldGeographyImplCopyWith<_$WorldGeographyImpl> get copyWith =>
      __$$WorldGeographyImplCopyWithImpl<_$WorldGeographyImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$WorldGeographyImplToJson(this);
  }
}

abstract class _WorldGeography implements WorldGeography {
  const factory _WorldGeography({
    final List<Continent> continents,
    final List<Ocean> oceans,
    final List<MountainRange> mountainRanges,
    final List<River> rivers,
    final List<Desert> deserts,
    final List<Forest> forests,
    final String? worldShape,
    final String? size,
    final Map<String, dynamic> customGeography,
  }) = _$WorldGeographyImpl;

  factory _WorldGeography.fromJson(Map<String, dynamic> json) =
      _$WorldGeographyImpl.fromJson;

  @override
  List<Continent> get continents;
  @override
  List<Ocean> get oceans;
  @override
  List<MountainRange> get mountainRanges;
  @override
  List<River> get rivers;
  @override
  List<Desert> get deserts;
  @override
  List<Forest> get forests;
  @override
  String? get worldShape; // 世界形状
  @override
  String? get size; // 世界大小
  @override
  Map<String, dynamic> get customGeography;

  /// Create a copy of WorldGeography
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WorldGeographyImplCopyWith<_$WorldGeographyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Continent _$ContinentFromJson(Map<String, dynamic> json) {
  return _Continent.fromJson(json);
}

/// @nodoc
mixin _$Continent {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  double? get area => throw _privateConstructorUsedError;
  List<String> get countries => throw _privateConstructorUsedError;
  List<String> get majorFeatures => throw _privateConstructorUsedError;
  String? get climate => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this Continent to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Continent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ContinentCopyWith<Continent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContinentCopyWith<$Res> {
  factory $ContinentCopyWith(Continent value, $Res Function(Continent) then) =
      _$ContinentCopyWithImpl<$Res, Continent>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    double? area,
    List<String> countries,
    List<String> majorFeatures,
    String? climate,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$ContinentCopyWithImpl<$Res, $Val extends Continent>
    implements $ContinentCopyWith<$Res> {
  _$ContinentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Continent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? area = freezed,
    Object? countries = null,
    Object? majorFeatures = null,
    Object? climate = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            area: freezed == area
                ? _value.area
                : area // ignore: cast_nullable_to_non_nullable
                      as double?,
            countries: null == countries
                ? _value.countries
                : countries // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            majorFeatures: null == majorFeatures
                ? _value.majorFeatures
                : majorFeatures // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            climate: freezed == climate
                ? _value.climate
                : climate // ignore: cast_nullable_to_non_nullable
                      as String?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ContinentImplCopyWith<$Res>
    implements $ContinentCopyWith<$Res> {
  factory _$$ContinentImplCopyWith(
    _$ContinentImpl value,
    $Res Function(_$ContinentImpl) then,
  ) = __$$ContinentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    double? area,
    List<String> countries,
    List<String> majorFeatures,
    String? climate,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$ContinentImplCopyWithImpl<$Res>
    extends _$ContinentCopyWithImpl<$Res, _$ContinentImpl>
    implements _$$ContinentImplCopyWith<$Res> {
  __$$ContinentImplCopyWithImpl(
    _$ContinentImpl _value,
    $Res Function(_$ContinentImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Continent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? area = freezed,
    Object? countries = null,
    Object? majorFeatures = null,
    Object? climate = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _$ContinentImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        area: freezed == area
            ? _value.area
            : area // ignore: cast_nullable_to_non_nullable
                  as double?,
        countries: null == countries
            ? _value._countries
            : countries // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        majorFeatures: null == majorFeatures
            ? _value._majorFeatures
            : majorFeatures // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        climate: freezed == climate
            ? _value.climate
            : climate // ignore: cast_nullable_to_non_nullable
                  as String?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ContinentImpl implements _Continent {
  const _$ContinentImpl({
    required this.id,
    required this.name,
    this.description,
    this.area,
    final List<String> countries = const [],
    final List<String> majorFeatures = const [],
    this.climate,
    final Map<String, dynamic> customAttributes = const {},
  }) : _countries = countries,
       _majorFeatures = majorFeatures,
       _customAttributes = customAttributes;

  factory _$ContinentImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContinentImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final double? area;
  final List<String> _countries;
  @override
  @JsonKey()
  List<String> get countries {
    if (_countries is EqualUnmodifiableListView) return _countries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_countries);
  }

  final List<String> _majorFeatures;
  @override
  @JsonKey()
  List<String> get majorFeatures {
    if (_majorFeatures is EqualUnmodifiableListView) return _majorFeatures;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_majorFeatures);
  }

  @override
  final String? climate;
  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'Continent(id: $id, name: $name, description: $description, area: $area, countries: $countries, majorFeatures: $majorFeatures, climate: $climate, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContinentImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.area, area) || other.area == area) &&
            const DeepCollectionEquality().equals(
              other._countries,
              _countries,
            ) &&
            const DeepCollectionEquality().equals(
              other._majorFeatures,
              _majorFeatures,
            ) &&
            (identical(other.climate, climate) || other.climate == climate) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    area,
    const DeepCollectionEquality().hash(_countries),
    const DeepCollectionEquality().hash(_majorFeatures),
    climate,
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of Continent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ContinentImplCopyWith<_$ContinentImpl> get copyWith =>
      __$$ContinentImplCopyWithImpl<_$ContinentImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContinentImplToJson(this);
  }
}

abstract class _Continent implements Continent {
  const factory _Continent({
    required final String id,
    required final String name,
    final String? description,
    final double? area,
    final List<String> countries,
    final List<String> majorFeatures,
    final String? climate,
    final Map<String, dynamic> customAttributes,
  }) = _$ContinentImpl;

  factory _Continent.fromJson(Map<String, dynamic> json) =
      _$ContinentImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  double? get area;
  @override
  List<String> get countries;
  @override
  List<String> get majorFeatures;
  @override
  String? get climate;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of Continent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ContinentImplCopyWith<_$ContinentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Ocean _$OceanFromJson(Map<String, dynamic> json) {
  return _Ocean.fromJson(json);
}

/// @nodoc
mixin _$Ocean {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  double? get area => throw _privateConstructorUsedError;
  double? get averageDepth => throw _privateConstructorUsedError;
  List<String> get borderingContinents => throw _privateConstructorUsedError;
  List<String> get islands => throw _privateConstructorUsedError;
  List<String> get dangers => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this Ocean to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Ocean
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OceanCopyWith<Ocean> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OceanCopyWith<$Res> {
  factory $OceanCopyWith(Ocean value, $Res Function(Ocean) then) =
      _$OceanCopyWithImpl<$Res, Ocean>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    double? area,
    double? averageDepth,
    List<String> borderingContinents,
    List<String> islands,
    List<String> dangers,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$OceanCopyWithImpl<$Res, $Val extends Ocean>
    implements $OceanCopyWith<$Res> {
  _$OceanCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Ocean
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? area = freezed,
    Object? averageDepth = freezed,
    Object? borderingContinents = null,
    Object? islands = null,
    Object? dangers = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            area: freezed == area
                ? _value.area
                : area // ignore: cast_nullable_to_non_nullable
                      as double?,
            averageDepth: freezed == averageDepth
                ? _value.averageDepth
                : averageDepth // ignore: cast_nullable_to_non_nullable
                      as double?,
            borderingContinents: null == borderingContinents
                ? _value.borderingContinents
                : borderingContinents // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            islands: null == islands
                ? _value.islands
                : islands // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            dangers: null == dangers
                ? _value.dangers
                : dangers // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$OceanImplCopyWith<$Res> implements $OceanCopyWith<$Res> {
  factory _$$OceanImplCopyWith(
    _$OceanImpl value,
    $Res Function(_$OceanImpl) then,
  ) = __$$OceanImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    double? area,
    double? averageDepth,
    List<String> borderingContinents,
    List<String> islands,
    List<String> dangers,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$OceanImplCopyWithImpl<$Res>
    extends _$OceanCopyWithImpl<$Res, _$OceanImpl>
    implements _$$OceanImplCopyWith<$Res> {
  __$$OceanImplCopyWithImpl(
    _$OceanImpl _value,
    $Res Function(_$OceanImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Ocean
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? area = freezed,
    Object? averageDepth = freezed,
    Object? borderingContinents = null,
    Object? islands = null,
    Object? dangers = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$OceanImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        area: freezed == area
            ? _value.area
            : area // ignore: cast_nullable_to_non_nullable
                  as double?,
        averageDepth: freezed == averageDepth
            ? _value.averageDepth
            : averageDepth // ignore: cast_nullable_to_non_nullable
                  as double?,
        borderingContinents: null == borderingContinents
            ? _value._borderingContinents
            : borderingContinents // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        islands: null == islands
            ? _value._islands
            : islands // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        dangers: null == dangers
            ? _value._dangers
            : dangers // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$OceanImpl implements _Ocean {
  const _$OceanImpl({
    required this.id,
    required this.name,
    this.description,
    this.area,
    this.averageDepth,
    final List<String> borderingContinents = const [],
    final List<String> islands = const [],
    final List<String> dangers = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _borderingContinents = borderingContinents,
       _islands = islands,
       _dangers = dangers,
       _customAttributes = customAttributes;

  factory _$OceanImpl.fromJson(Map<String, dynamic> json) =>
      _$$OceanImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final double? area;
  @override
  final double? averageDepth;
  final List<String> _borderingContinents;
  @override
  @JsonKey()
  List<String> get borderingContinents {
    if (_borderingContinents is EqualUnmodifiableListView)
      return _borderingContinents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_borderingContinents);
  }

  final List<String> _islands;
  @override
  @JsonKey()
  List<String> get islands {
    if (_islands is EqualUnmodifiableListView) return _islands;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_islands);
  }

  final List<String> _dangers;
  @override
  @JsonKey()
  List<String> get dangers {
    if (_dangers is EqualUnmodifiableListView) return _dangers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dangers);
  }

  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'Ocean(id: $id, name: $name, description: $description, area: $area, averageDepth: $averageDepth, borderingContinents: $borderingContinents, islands: $islands, dangers: $dangers, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OceanImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.area, area) || other.area == area) &&
            (identical(other.averageDepth, averageDepth) ||
                other.averageDepth == averageDepth) &&
            const DeepCollectionEquality().equals(
              other._borderingContinents,
              _borderingContinents,
            ) &&
            const DeepCollectionEquality().equals(other._islands, _islands) &&
            const DeepCollectionEquality().equals(other._dangers, _dangers) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    area,
    averageDepth,
    const DeepCollectionEquality().hash(_borderingContinents),
    const DeepCollectionEquality().hash(_islands),
    const DeepCollectionEquality().hash(_dangers),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of Ocean
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OceanImplCopyWith<_$OceanImpl> get copyWith =>
      __$$OceanImplCopyWithImpl<_$OceanImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OceanImplToJson(this);
  }
}

abstract class _Ocean implements Ocean {
  const factory _Ocean({
    required final String id,
    required final String name,
    final String? description,
    final double? area,
    final double? averageDepth,
    final List<String> borderingContinents,
    final List<String> islands,
    final List<String> dangers,
    final Map<String, dynamic> customAttributes,
  }) = _$OceanImpl;

  factory _Ocean.fromJson(Map<String, dynamic> json) = _$OceanImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  double? get area;
  @override
  double? get averageDepth;
  @override
  List<String> get borderingContinents;
  @override
  List<String> get islands;
  @override
  List<String> get dangers;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of Ocean
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OceanImplCopyWith<_$OceanImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MountainRange _$MountainRangeFromJson(Map<String, dynamic> json) {
  return _MountainRange.fromJson(json);
}

/// @nodoc
mixin _$MountainRange {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  double? get length => throw _privateConstructorUsedError;
  double? get highestPeak => throw _privateConstructorUsedError;
  List<String> get countries => throw _privateConstructorUsedError;
  List<String> get resources => throw _privateConstructorUsedError;
  List<String> get dangers => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this MountainRange to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MountainRange
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MountainRangeCopyWith<MountainRange> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MountainRangeCopyWith<$Res> {
  factory $MountainRangeCopyWith(
    MountainRange value,
    $Res Function(MountainRange) then,
  ) = _$MountainRangeCopyWithImpl<$Res, MountainRange>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    double? length,
    double? highestPeak,
    List<String> countries,
    List<String> resources,
    List<String> dangers,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$MountainRangeCopyWithImpl<$Res, $Val extends MountainRange>
    implements $MountainRangeCopyWith<$Res> {
  _$MountainRangeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MountainRange
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? length = freezed,
    Object? highestPeak = freezed,
    Object? countries = null,
    Object? resources = null,
    Object? dangers = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            length: freezed == length
                ? _value.length
                : length // ignore: cast_nullable_to_non_nullable
                      as double?,
            highestPeak: freezed == highestPeak
                ? _value.highestPeak
                : highestPeak // ignore: cast_nullable_to_non_nullable
                      as double?,
            countries: null == countries
                ? _value.countries
                : countries // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            resources: null == resources
                ? _value.resources
                : resources // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            dangers: null == dangers
                ? _value.dangers
                : dangers // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$MountainRangeImplCopyWith<$Res>
    implements $MountainRangeCopyWith<$Res> {
  factory _$$MountainRangeImplCopyWith(
    _$MountainRangeImpl value,
    $Res Function(_$MountainRangeImpl) then,
  ) = __$$MountainRangeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    double? length,
    double? highestPeak,
    List<String> countries,
    List<String> resources,
    List<String> dangers,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$MountainRangeImplCopyWithImpl<$Res>
    extends _$MountainRangeCopyWithImpl<$Res, _$MountainRangeImpl>
    implements _$$MountainRangeImplCopyWith<$Res> {
  __$$MountainRangeImplCopyWithImpl(
    _$MountainRangeImpl _value,
    $Res Function(_$MountainRangeImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MountainRange
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? length = freezed,
    Object? highestPeak = freezed,
    Object? countries = null,
    Object? resources = null,
    Object? dangers = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$MountainRangeImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        length: freezed == length
            ? _value.length
            : length // ignore: cast_nullable_to_non_nullable
                  as double?,
        highestPeak: freezed == highestPeak
            ? _value.highestPeak
            : highestPeak // ignore: cast_nullable_to_non_nullable
                  as double?,
        countries: null == countries
            ? _value._countries
            : countries // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        resources: null == resources
            ? _value._resources
            : resources // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        dangers: null == dangers
            ? _value._dangers
            : dangers // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$MountainRangeImpl implements _MountainRange {
  const _$MountainRangeImpl({
    required this.id,
    required this.name,
    this.description,
    this.length,
    this.highestPeak,
    final List<String> countries = const [],
    final List<String> resources = const [],
    final List<String> dangers = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _countries = countries,
       _resources = resources,
       _dangers = dangers,
       _customAttributes = customAttributes;

  factory _$MountainRangeImpl.fromJson(Map<String, dynamic> json) =>
      _$$MountainRangeImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final double? length;
  @override
  final double? highestPeak;
  final List<String> _countries;
  @override
  @JsonKey()
  List<String> get countries {
    if (_countries is EqualUnmodifiableListView) return _countries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_countries);
  }

  final List<String> _resources;
  @override
  @JsonKey()
  List<String> get resources {
    if (_resources is EqualUnmodifiableListView) return _resources;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_resources);
  }

  final List<String> _dangers;
  @override
  @JsonKey()
  List<String> get dangers {
    if (_dangers is EqualUnmodifiableListView) return _dangers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dangers);
  }

  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'MountainRange(id: $id, name: $name, description: $description, length: $length, highestPeak: $highestPeak, countries: $countries, resources: $resources, dangers: $dangers, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MountainRangeImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.length, length) || other.length == length) &&
            (identical(other.highestPeak, highestPeak) ||
                other.highestPeak == highestPeak) &&
            const DeepCollectionEquality().equals(
              other._countries,
              _countries,
            ) &&
            const DeepCollectionEquality().equals(
              other._resources,
              _resources,
            ) &&
            const DeepCollectionEquality().equals(other._dangers, _dangers) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    length,
    highestPeak,
    const DeepCollectionEquality().hash(_countries),
    const DeepCollectionEquality().hash(_resources),
    const DeepCollectionEquality().hash(_dangers),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of MountainRange
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MountainRangeImplCopyWith<_$MountainRangeImpl> get copyWith =>
      __$$MountainRangeImplCopyWithImpl<_$MountainRangeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MountainRangeImplToJson(this);
  }
}

abstract class _MountainRange implements MountainRange {
  const factory _MountainRange({
    required final String id,
    required final String name,
    final String? description,
    final double? length,
    final double? highestPeak,
    final List<String> countries,
    final List<String> resources,
    final List<String> dangers,
    final Map<String, dynamic> customAttributes,
  }) = _$MountainRangeImpl;

  factory _MountainRange.fromJson(Map<String, dynamic> json) =
      _$MountainRangeImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  double? get length;
  @override
  double? get highestPeak;
  @override
  List<String> get countries;
  @override
  List<String> get resources;
  @override
  List<String> get dangers;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of MountainRange
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MountainRangeImplCopyWith<_$MountainRangeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

River _$RiverFromJson(Map<String, dynamic> json) {
  return _River.fromJson(json);
}

/// @nodoc
mixin _$River {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  double? get length => throw _privateConstructorUsedError;
  String? get source => throw _privateConstructorUsedError;
  String? get mouth => throw _privateConstructorUsedError;
  List<String> get cities => throw _privateConstructorUsedError;
  List<String> get tributaries => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this River to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of River
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RiverCopyWith<River> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RiverCopyWith<$Res> {
  factory $RiverCopyWith(River value, $Res Function(River) then) =
      _$RiverCopyWithImpl<$Res, River>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    double? length,
    String? source,
    String? mouth,
    List<String> cities,
    List<String> tributaries,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$RiverCopyWithImpl<$Res, $Val extends River>
    implements $RiverCopyWith<$Res> {
  _$RiverCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of River
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? length = freezed,
    Object? source = freezed,
    Object? mouth = freezed,
    Object? cities = null,
    Object? tributaries = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            length: freezed == length
                ? _value.length
                : length // ignore: cast_nullable_to_non_nullable
                      as double?,
            source: freezed == source
                ? _value.source
                : source // ignore: cast_nullable_to_non_nullable
                      as String?,
            mouth: freezed == mouth
                ? _value.mouth
                : mouth // ignore: cast_nullable_to_non_nullable
                      as String?,
            cities: null == cities
                ? _value.cities
                : cities // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            tributaries: null == tributaries
                ? _value.tributaries
                : tributaries // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$RiverImplCopyWith<$Res> implements $RiverCopyWith<$Res> {
  factory _$$RiverImplCopyWith(
    _$RiverImpl value,
    $Res Function(_$RiverImpl) then,
  ) = __$$RiverImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    double? length,
    String? source,
    String? mouth,
    List<String> cities,
    List<String> tributaries,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$RiverImplCopyWithImpl<$Res>
    extends _$RiverCopyWithImpl<$Res, _$RiverImpl>
    implements _$$RiverImplCopyWith<$Res> {
  __$$RiverImplCopyWithImpl(
    _$RiverImpl _value,
    $Res Function(_$RiverImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of River
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? length = freezed,
    Object? source = freezed,
    Object? mouth = freezed,
    Object? cities = null,
    Object? tributaries = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$RiverImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        length: freezed == length
            ? _value.length
            : length // ignore: cast_nullable_to_non_nullable
                  as double?,
        source: freezed == source
            ? _value.source
            : source // ignore: cast_nullable_to_non_nullable
                  as String?,
        mouth: freezed == mouth
            ? _value.mouth
            : mouth // ignore: cast_nullable_to_non_nullable
                  as String?,
        cities: null == cities
            ? _value._cities
            : cities // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        tributaries: null == tributaries
            ? _value._tributaries
            : tributaries // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$RiverImpl implements _River {
  const _$RiverImpl({
    required this.id,
    required this.name,
    this.description,
    this.length,
    this.source,
    this.mouth,
    final List<String> cities = const [],
    final List<String> tributaries = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _cities = cities,
       _tributaries = tributaries,
       _customAttributes = customAttributes;

  factory _$RiverImpl.fromJson(Map<String, dynamic> json) =>
      _$$RiverImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final double? length;
  @override
  final String? source;
  @override
  final String? mouth;
  final List<String> _cities;
  @override
  @JsonKey()
  List<String> get cities {
    if (_cities is EqualUnmodifiableListView) return _cities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_cities);
  }

  final List<String> _tributaries;
  @override
  @JsonKey()
  List<String> get tributaries {
    if (_tributaries is EqualUnmodifiableListView) return _tributaries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tributaries);
  }

  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'River(id: $id, name: $name, description: $description, length: $length, source: $source, mouth: $mouth, cities: $cities, tributaries: $tributaries, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RiverImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.length, length) || other.length == length) &&
            (identical(other.source, source) || other.source == source) &&
            (identical(other.mouth, mouth) || other.mouth == mouth) &&
            const DeepCollectionEquality().equals(other._cities, _cities) &&
            const DeepCollectionEquality().equals(
              other._tributaries,
              _tributaries,
            ) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    length,
    source,
    mouth,
    const DeepCollectionEquality().hash(_cities),
    const DeepCollectionEquality().hash(_tributaries),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of River
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RiverImplCopyWith<_$RiverImpl> get copyWith =>
      __$$RiverImplCopyWithImpl<_$RiverImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RiverImplToJson(this);
  }
}

abstract class _River implements River {
  const factory _River({
    required final String id,
    required final String name,
    final String? description,
    final double? length,
    final String? source,
    final String? mouth,
    final List<String> cities,
    final List<String> tributaries,
    final Map<String, dynamic> customAttributes,
  }) = _$RiverImpl;

  factory _River.fromJson(Map<String, dynamic> json) = _$RiverImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  double? get length;
  @override
  String? get source;
  @override
  String? get mouth;
  @override
  List<String> get cities;
  @override
  List<String> get tributaries;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of River
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RiverImplCopyWith<_$RiverImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Desert _$DesertFromJson(Map<String, dynamic> json) {
  return _Desert.fromJson(json);
}

/// @nodoc
mixin _$Desert {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  double? get area => throw _privateConstructorUsedError;
  DesertType? get type => throw _privateConstructorUsedError;
  List<String> get oases => throw _privateConstructorUsedError;
  List<String> get dangers => throw _privateConstructorUsedError;
  List<String> get resources => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this Desert to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Desert
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DesertCopyWith<Desert> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DesertCopyWith<$Res> {
  factory $DesertCopyWith(Desert value, $Res Function(Desert) then) =
      _$DesertCopyWithImpl<$Res, Desert>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    double? area,
    DesertType? type,
    List<String> oases,
    List<String> dangers,
    List<String> resources,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$DesertCopyWithImpl<$Res, $Val extends Desert>
    implements $DesertCopyWith<$Res> {
  _$DesertCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Desert
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? area = freezed,
    Object? type = freezed,
    Object? oases = null,
    Object? dangers = null,
    Object? resources = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            area: freezed == area
                ? _value.area
                : area // ignore: cast_nullable_to_non_nullable
                      as double?,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as DesertType?,
            oases: null == oases
                ? _value.oases
                : oases // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            dangers: null == dangers
                ? _value.dangers
                : dangers // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            resources: null == resources
                ? _value.resources
                : resources // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DesertImplCopyWith<$Res> implements $DesertCopyWith<$Res> {
  factory _$$DesertImplCopyWith(
    _$DesertImpl value,
    $Res Function(_$DesertImpl) then,
  ) = __$$DesertImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    double? area,
    DesertType? type,
    List<String> oases,
    List<String> dangers,
    List<String> resources,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$DesertImplCopyWithImpl<$Res>
    extends _$DesertCopyWithImpl<$Res, _$DesertImpl>
    implements _$$DesertImplCopyWith<$Res> {
  __$$DesertImplCopyWithImpl(
    _$DesertImpl _value,
    $Res Function(_$DesertImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Desert
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? area = freezed,
    Object? type = freezed,
    Object? oases = null,
    Object? dangers = null,
    Object? resources = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$DesertImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        area: freezed == area
            ? _value.area
            : area // ignore: cast_nullable_to_non_nullable
                  as double?,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as DesertType?,
        oases: null == oases
            ? _value._oases
            : oases // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        dangers: null == dangers
            ? _value._dangers
            : dangers // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        resources: null == resources
            ? _value._resources
            : resources // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$DesertImpl implements _Desert {
  const _$DesertImpl({
    required this.id,
    required this.name,
    this.description,
    this.area,
    this.type,
    final List<String> oases = const [],
    final List<String> dangers = const [],
    final List<String> resources = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _oases = oases,
       _dangers = dangers,
       _resources = resources,
       _customAttributes = customAttributes;

  factory _$DesertImpl.fromJson(Map<String, dynamic> json) =>
      _$$DesertImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final double? area;
  @override
  final DesertType? type;
  final List<String> _oases;
  @override
  @JsonKey()
  List<String> get oases {
    if (_oases is EqualUnmodifiableListView) return _oases;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_oases);
  }

  final List<String> _dangers;
  @override
  @JsonKey()
  List<String> get dangers {
    if (_dangers is EqualUnmodifiableListView) return _dangers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dangers);
  }

  final List<String> _resources;
  @override
  @JsonKey()
  List<String> get resources {
    if (_resources is EqualUnmodifiableListView) return _resources;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_resources);
  }

  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'Desert(id: $id, name: $name, description: $description, area: $area, type: $type, oases: $oases, dangers: $dangers, resources: $resources, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DesertImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.area, area) || other.area == area) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other._oases, _oases) &&
            const DeepCollectionEquality().equals(other._dangers, _dangers) &&
            const DeepCollectionEquality().equals(
              other._resources,
              _resources,
            ) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    area,
    type,
    const DeepCollectionEquality().hash(_oases),
    const DeepCollectionEquality().hash(_dangers),
    const DeepCollectionEquality().hash(_resources),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of Desert
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DesertImplCopyWith<_$DesertImpl> get copyWith =>
      __$$DesertImplCopyWithImpl<_$DesertImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DesertImplToJson(this);
  }
}

abstract class _Desert implements Desert {
  const factory _Desert({
    required final String id,
    required final String name,
    final String? description,
    final double? area,
    final DesertType? type,
    final List<String> oases,
    final List<String> dangers,
    final List<String> resources,
    final Map<String, dynamic> customAttributes,
  }) = _$DesertImpl;

  factory _Desert.fromJson(Map<String, dynamic> json) = _$DesertImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  double? get area;
  @override
  DesertType? get type;
  @override
  List<String> get oases;
  @override
  List<String> get dangers;
  @override
  List<String> get resources;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of Desert
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DesertImplCopyWith<_$DesertImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Forest _$ForestFromJson(Map<String, dynamic> json) {
  return _Forest.fromJson(json);
}

/// @nodoc
mixin _$Forest {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  double? get area => throw _privateConstructorUsedError;
  ForestType? get type => throw _privateConstructorUsedError;
  List<String> get wildlife => throw _privateConstructorUsedError;
  List<String> get resources => throw _privateConstructorUsedError;
  List<String> get dangers => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this Forest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Forest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ForestCopyWith<Forest> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ForestCopyWith<$Res> {
  factory $ForestCopyWith(Forest value, $Res Function(Forest) then) =
      _$ForestCopyWithImpl<$Res, Forest>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    double? area,
    ForestType? type,
    List<String> wildlife,
    List<String> resources,
    List<String> dangers,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$ForestCopyWithImpl<$Res, $Val extends Forest>
    implements $ForestCopyWith<$Res> {
  _$ForestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Forest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? area = freezed,
    Object? type = freezed,
    Object? wildlife = null,
    Object? resources = null,
    Object? dangers = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            area: freezed == area
                ? _value.area
                : area // ignore: cast_nullable_to_non_nullable
                      as double?,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as ForestType?,
            wildlife: null == wildlife
                ? _value.wildlife
                : wildlife // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            resources: null == resources
                ? _value.resources
                : resources // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            dangers: null == dangers
                ? _value.dangers
                : dangers // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ForestImplCopyWith<$Res> implements $ForestCopyWith<$Res> {
  factory _$$ForestImplCopyWith(
    _$ForestImpl value,
    $Res Function(_$ForestImpl) then,
  ) = __$$ForestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    double? area,
    ForestType? type,
    List<String> wildlife,
    List<String> resources,
    List<String> dangers,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$ForestImplCopyWithImpl<$Res>
    extends _$ForestCopyWithImpl<$Res, _$ForestImpl>
    implements _$$ForestImplCopyWith<$Res> {
  __$$ForestImplCopyWithImpl(
    _$ForestImpl _value,
    $Res Function(_$ForestImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Forest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? area = freezed,
    Object? type = freezed,
    Object? wildlife = null,
    Object? resources = null,
    Object? dangers = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$ForestImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        area: freezed == area
            ? _value.area
            : area // ignore: cast_nullable_to_non_nullable
                  as double?,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as ForestType?,
        wildlife: null == wildlife
            ? _value._wildlife
            : wildlife // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        resources: null == resources
            ? _value._resources
            : resources // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        dangers: null == dangers
            ? _value._dangers
            : dangers // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ForestImpl implements _Forest {
  const _$ForestImpl({
    required this.id,
    required this.name,
    this.description,
    this.area,
    this.type,
    final List<String> wildlife = const [],
    final List<String> resources = const [],
    final List<String> dangers = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _wildlife = wildlife,
       _resources = resources,
       _dangers = dangers,
       _customAttributes = customAttributes;

  factory _$ForestImpl.fromJson(Map<String, dynamic> json) =>
      _$$ForestImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final double? area;
  @override
  final ForestType? type;
  final List<String> _wildlife;
  @override
  @JsonKey()
  List<String> get wildlife {
    if (_wildlife is EqualUnmodifiableListView) return _wildlife;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_wildlife);
  }

  final List<String> _resources;
  @override
  @JsonKey()
  List<String> get resources {
    if (_resources is EqualUnmodifiableListView) return _resources;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_resources);
  }

  final List<String> _dangers;
  @override
  @JsonKey()
  List<String> get dangers {
    if (_dangers is EqualUnmodifiableListView) return _dangers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dangers);
  }

  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'Forest(id: $id, name: $name, description: $description, area: $area, type: $type, wildlife: $wildlife, resources: $resources, dangers: $dangers, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ForestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.area, area) || other.area == area) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other._wildlife, _wildlife) &&
            const DeepCollectionEquality().equals(
              other._resources,
              _resources,
            ) &&
            const DeepCollectionEquality().equals(other._dangers, _dangers) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    area,
    type,
    const DeepCollectionEquality().hash(_wildlife),
    const DeepCollectionEquality().hash(_resources),
    const DeepCollectionEquality().hash(_dangers),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of Forest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ForestImplCopyWith<_$ForestImpl> get copyWith =>
      __$$ForestImplCopyWithImpl<_$ForestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ForestImplToJson(this);
  }
}

abstract class _Forest implements Forest {
  const factory _Forest({
    required final String id,
    required final String name,
    final String? description,
    final double? area,
    final ForestType? type,
    final List<String> wildlife,
    final List<String> resources,
    final List<String> dangers,
    final Map<String, dynamic> customAttributes,
  }) = _$ForestImpl;

  factory _Forest.fromJson(Map<String, dynamic> json) = _$ForestImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  double? get area;
  @override
  ForestType? get type;
  @override
  List<String> get wildlife;
  @override
  List<String> get resources;
  @override
  List<String> get dangers;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of Forest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ForestImplCopyWith<_$ForestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WorldSociety _$WorldSocietyFromJson(Map<String, dynamic> json) {
  return _WorldSociety.fromJson(json);
}

/// @nodoc
mixin _$WorldSociety {
  List<SocialClass> get socialClasses => throw _privateConstructorUsedError;
  List<Institution> get institutions => throw _privateConstructorUsedError;
  List<SocialNorm> get norms => throw _privateConstructorUsedError;
  List<String> get values => throw _privateConstructorUsedError;
  String? get familyStructure => throw _privateConstructorUsedError;
  String? get educationSystem => throw _privateConstructorUsedError;
  Map<String, dynamic> get customSociety => throw _privateConstructorUsedError;

  /// Serializes this WorldSociety to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WorldSociety
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WorldSocietyCopyWith<WorldSociety> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorldSocietyCopyWith<$Res> {
  factory $WorldSocietyCopyWith(
    WorldSociety value,
    $Res Function(WorldSociety) then,
  ) = _$WorldSocietyCopyWithImpl<$Res, WorldSociety>;
  @useResult
  $Res call({
    List<SocialClass> socialClasses,
    List<Institution> institutions,
    List<SocialNorm> norms,
    List<String> values,
    String? familyStructure,
    String? educationSystem,
    Map<String, dynamic> customSociety,
  });
}

/// @nodoc
class _$WorldSocietyCopyWithImpl<$Res, $Val extends WorldSociety>
    implements $WorldSocietyCopyWith<$Res> {
  _$WorldSocietyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WorldSociety
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? socialClasses = null,
    Object? institutions = null,
    Object? norms = null,
    Object? values = null,
    Object? familyStructure = freezed,
    Object? educationSystem = freezed,
    Object? customSociety = null,
  }) {
    return _then(
      _value.copyWith(
            socialClasses: null == socialClasses
                ? _value.socialClasses
                : socialClasses // ignore: cast_nullable_to_non_nullable
                      as List<SocialClass>,
            institutions: null == institutions
                ? _value.institutions
                : institutions // ignore: cast_nullable_to_non_nullable
                      as List<Institution>,
            norms: null == norms
                ? _value.norms
                : norms // ignore: cast_nullable_to_non_nullable
                      as List<SocialNorm>,
            values: null == values
                ? _value.values
                : values // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            familyStructure: freezed == familyStructure
                ? _value.familyStructure
                : familyStructure // ignore: cast_nullable_to_non_nullable
                      as String?,
            educationSystem: freezed == educationSystem
                ? _value.educationSystem
                : educationSystem // ignore: cast_nullable_to_non_nullable
                      as String?,
            customSociety: null == customSociety
                ? _value.customSociety
                : customSociety // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$WorldSocietyImplCopyWith<$Res>
    implements $WorldSocietyCopyWith<$Res> {
  factory _$$WorldSocietyImplCopyWith(
    _$WorldSocietyImpl value,
    $Res Function(_$WorldSocietyImpl) then,
  ) = __$$WorldSocietyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    List<SocialClass> socialClasses,
    List<Institution> institutions,
    List<SocialNorm> norms,
    List<String> values,
    String? familyStructure,
    String? educationSystem,
    Map<String, dynamic> customSociety,
  });
}

/// @nodoc
class __$$WorldSocietyImplCopyWithImpl<$Res>
    extends _$WorldSocietyCopyWithImpl<$Res, _$WorldSocietyImpl>
    implements _$$WorldSocietyImplCopyWith<$Res> {
  __$$WorldSocietyImplCopyWithImpl(
    _$WorldSocietyImpl _value,
    $Res Function(_$WorldSocietyImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of WorldSociety
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? socialClasses = null,
    Object? institutions = null,
    Object? norms = null,
    Object? values = null,
    Object? familyStructure = freezed,
    Object? educationSystem = freezed,
    Object? customSociety = null,
  }) {
    return _then(
      _$WorldSocietyImpl(
        socialClasses: null == socialClasses
            ? _value._socialClasses
            : socialClasses // ignore: cast_nullable_to_non_nullable
                  as List<SocialClass>,
        institutions: null == institutions
            ? _value._institutions
            : institutions // ignore: cast_nullable_to_non_nullable
                  as List<Institution>,
        norms: null == norms
            ? _value._norms
            : norms // ignore: cast_nullable_to_non_nullable
                  as List<SocialNorm>,
        values: null == values
            ? _value._values
            : values // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        familyStructure: freezed == familyStructure
            ? _value.familyStructure
            : familyStructure // ignore: cast_nullable_to_non_nullable
                  as String?,
        educationSystem: freezed == educationSystem
            ? _value.educationSystem
            : educationSystem // ignore: cast_nullable_to_non_nullable
                  as String?,
        customSociety: null == customSociety
            ? _value._customSociety
            : customSociety // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$WorldSocietyImpl implements _WorldSociety {
  const _$WorldSocietyImpl({
    final List<SocialClass> socialClasses = const [],
    final List<Institution> institutions = const [],
    final List<SocialNorm> norms = const [],
    final List<String> values = const [],
    this.familyStructure,
    this.educationSystem,
    final Map<String, dynamic> customSociety = const {},
  }) : _socialClasses = socialClasses,
       _institutions = institutions,
       _norms = norms,
       _values = values,
       _customSociety = customSociety;

  factory _$WorldSocietyImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorldSocietyImplFromJson(json);

  final List<SocialClass> _socialClasses;
  @override
  @JsonKey()
  List<SocialClass> get socialClasses {
    if (_socialClasses is EqualUnmodifiableListView) return _socialClasses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_socialClasses);
  }

  final List<Institution> _institutions;
  @override
  @JsonKey()
  List<Institution> get institutions {
    if (_institutions is EqualUnmodifiableListView) return _institutions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_institutions);
  }

  final List<SocialNorm> _norms;
  @override
  @JsonKey()
  List<SocialNorm> get norms {
    if (_norms is EqualUnmodifiableListView) return _norms;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_norms);
  }

  final List<String> _values;
  @override
  @JsonKey()
  List<String> get values {
    if (_values is EqualUnmodifiableListView) return _values;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_values);
  }

  @override
  final String? familyStructure;
  @override
  final String? educationSystem;
  final Map<String, dynamic> _customSociety;
  @override
  @JsonKey()
  Map<String, dynamic> get customSociety {
    if (_customSociety is EqualUnmodifiableMapView) return _customSociety;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customSociety);
  }

  @override
  String toString() {
    return 'WorldSociety(socialClasses: $socialClasses, institutions: $institutions, norms: $norms, values: $values, familyStructure: $familyStructure, educationSystem: $educationSystem, customSociety: $customSociety)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorldSocietyImpl &&
            const DeepCollectionEquality().equals(
              other._socialClasses,
              _socialClasses,
            ) &&
            const DeepCollectionEquality().equals(
              other._institutions,
              _institutions,
            ) &&
            const DeepCollectionEquality().equals(other._norms, _norms) &&
            const DeepCollectionEquality().equals(other._values, _values) &&
            (identical(other.familyStructure, familyStructure) ||
                other.familyStructure == familyStructure) &&
            (identical(other.educationSystem, educationSystem) ||
                other.educationSystem == educationSystem) &&
            const DeepCollectionEquality().equals(
              other._customSociety,
              _customSociety,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_socialClasses),
    const DeepCollectionEquality().hash(_institutions),
    const DeepCollectionEquality().hash(_norms),
    const DeepCollectionEquality().hash(_values),
    familyStructure,
    educationSystem,
    const DeepCollectionEquality().hash(_customSociety),
  );

  /// Create a copy of WorldSociety
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WorldSocietyImplCopyWith<_$WorldSocietyImpl> get copyWith =>
      __$$WorldSocietyImplCopyWithImpl<_$WorldSocietyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorldSocietyImplToJson(this);
  }
}

abstract class _WorldSociety implements WorldSociety {
  const factory _WorldSociety({
    final List<SocialClass> socialClasses,
    final List<Institution> institutions,
    final List<SocialNorm> norms,
    final List<String> values,
    final String? familyStructure,
    final String? educationSystem,
    final Map<String, dynamic> customSociety,
  }) = _$WorldSocietyImpl;

  factory _WorldSociety.fromJson(Map<String, dynamic> json) =
      _$WorldSocietyImpl.fromJson;

  @override
  List<SocialClass> get socialClasses;
  @override
  List<Institution> get institutions;
  @override
  List<SocialNorm> get norms;
  @override
  List<String> get values;
  @override
  String? get familyStructure;
  @override
  String? get educationSystem;
  @override
  Map<String, dynamic> get customSociety;

  /// Create a copy of WorldSociety
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WorldSocietyImplCopyWith<_$WorldSocietyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SocialClass _$SocialClassFromJson(Map<String, dynamic> json) {
  return _SocialClass.fromJson(json);
}

/// @nodoc
mixin _$SocialClass {
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  int? get rank => throw _privateConstructorUsedError; // 等级 1-10
  List<String> get privileges => throw _privateConstructorUsedError;
  List<String> get responsibilities => throw _privateConstructorUsedError;
  String? get mobility => throw _privateConstructorUsedError; // 流动性
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this SocialClass to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SocialClass
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SocialClassCopyWith<SocialClass> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SocialClassCopyWith<$Res> {
  factory $SocialClassCopyWith(
    SocialClass value,
    $Res Function(SocialClass) then,
  ) = _$SocialClassCopyWithImpl<$Res, SocialClass>;
  @useResult
  $Res call({
    String name,
    String? description,
    int? rank,
    List<String> privileges,
    List<String> responsibilities,
    String? mobility,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$SocialClassCopyWithImpl<$Res, $Val extends SocialClass>
    implements $SocialClassCopyWith<$Res> {
  _$SocialClassCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SocialClass
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? rank = freezed,
    Object? privileges = null,
    Object? responsibilities = null,
    Object? mobility = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            rank: freezed == rank
                ? _value.rank
                : rank // ignore: cast_nullable_to_non_nullable
                      as int?,
            privileges: null == privileges
                ? _value.privileges
                : privileges // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            responsibilities: null == responsibilities
                ? _value.responsibilities
                : responsibilities // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            mobility: freezed == mobility
                ? _value.mobility
                : mobility // ignore: cast_nullable_to_non_nullable
                      as String?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$SocialClassImplCopyWith<$Res>
    implements $SocialClassCopyWith<$Res> {
  factory _$$SocialClassImplCopyWith(
    _$SocialClassImpl value,
    $Res Function(_$SocialClassImpl) then,
  ) = __$$SocialClassImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    String? description,
    int? rank,
    List<String> privileges,
    List<String> responsibilities,
    String? mobility,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$SocialClassImplCopyWithImpl<$Res>
    extends _$SocialClassCopyWithImpl<$Res, _$SocialClassImpl>
    implements _$$SocialClassImplCopyWith<$Res> {
  __$$SocialClassImplCopyWithImpl(
    _$SocialClassImpl _value,
    $Res Function(_$SocialClassImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of SocialClass
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? rank = freezed,
    Object? privileges = null,
    Object? responsibilities = null,
    Object? mobility = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _$SocialClassImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        rank: freezed == rank
            ? _value.rank
            : rank // ignore: cast_nullable_to_non_nullable
                  as int?,
        privileges: null == privileges
            ? _value._privileges
            : privileges // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        responsibilities: null == responsibilities
            ? _value._responsibilities
            : responsibilities // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        mobility: freezed == mobility
            ? _value.mobility
            : mobility // ignore: cast_nullable_to_non_nullable
                  as String?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SocialClassImpl implements _SocialClass {
  const _$SocialClassImpl({
    required this.name,
    this.description,
    this.rank,
    final List<String> privileges = const [],
    final List<String> responsibilities = const [],
    this.mobility,
    final Map<String, dynamic> customAttributes = const {},
  }) : _privileges = privileges,
       _responsibilities = responsibilities,
       _customAttributes = customAttributes;

  factory _$SocialClassImpl.fromJson(Map<String, dynamic> json) =>
      _$$SocialClassImplFromJson(json);

  @override
  final String name;
  @override
  final String? description;
  @override
  final int? rank;
  // 等级 1-10
  final List<String> _privileges;
  // 等级 1-10
  @override
  @JsonKey()
  List<String> get privileges {
    if (_privileges is EqualUnmodifiableListView) return _privileges;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_privileges);
  }

  final List<String> _responsibilities;
  @override
  @JsonKey()
  List<String> get responsibilities {
    if (_responsibilities is EqualUnmodifiableListView)
      return _responsibilities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_responsibilities);
  }

  @override
  final String? mobility;
  // 流动性
  final Map<String, dynamic> _customAttributes;
  // 流动性
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'SocialClass(name: $name, description: $description, rank: $rank, privileges: $privileges, responsibilities: $responsibilities, mobility: $mobility, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SocialClassImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.rank, rank) || other.rank == rank) &&
            const DeepCollectionEquality().equals(
              other._privileges,
              _privileges,
            ) &&
            const DeepCollectionEquality().equals(
              other._responsibilities,
              _responsibilities,
            ) &&
            (identical(other.mobility, mobility) ||
                other.mobility == mobility) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    description,
    rank,
    const DeepCollectionEquality().hash(_privileges),
    const DeepCollectionEquality().hash(_responsibilities),
    mobility,
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of SocialClass
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SocialClassImplCopyWith<_$SocialClassImpl> get copyWith =>
      __$$SocialClassImplCopyWithImpl<_$SocialClassImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SocialClassImplToJson(this);
  }
}

abstract class _SocialClass implements SocialClass {
  const factory _SocialClass({
    required final String name,
    final String? description,
    final int? rank,
    final List<String> privileges,
    final List<String> responsibilities,
    final String? mobility,
    final Map<String, dynamic> customAttributes,
  }) = _$SocialClassImpl;

  factory _SocialClass.fromJson(Map<String, dynamic> json) =
      _$SocialClassImpl.fromJson;

  @override
  String get name;
  @override
  String? get description;
  @override
  int? get rank; // 等级 1-10
  @override
  List<String> get privileges;
  @override
  List<String> get responsibilities;
  @override
  String? get mobility; // 流动性
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of SocialClass
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SocialClassImplCopyWith<_$SocialClassImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Institution _$InstitutionFromJson(Map<String, dynamic> json) {
  return _Institution.fromJson(json);
}

/// @nodoc
mixin _$Institution {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  InstitutionType? get type => throw _privateConstructorUsedError;
  String? get purpose => throw _privateConstructorUsedError;
  List<String> get functions => throw _privateConstructorUsedError;
  String? get structure => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this Institution to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Institution
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InstitutionCopyWith<Institution> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InstitutionCopyWith<$Res> {
  factory $InstitutionCopyWith(
    Institution value,
    $Res Function(Institution) then,
  ) = _$InstitutionCopyWithImpl<$Res, Institution>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    InstitutionType? type,
    String? purpose,
    List<String> functions,
    String? structure,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$InstitutionCopyWithImpl<$Res, $Val extends Institution>
    implements $InstitutionCopyWith<$Res> {
  _$InstitutionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Institution
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = freezed,
    Object? purpose = freezed,
    Object? functions = null,
    Object? structure = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as InstitutionType?,
            purpose: freezed == purpose
                ? _value.purpose
                : purpose // ignore: cast_nullable_to_non_nullable
                      as String?,
            functions: null == functions
                ? _value.functions
                : functions // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            structure: freezed == structure
                ? _value.structure
                : structure // ignore: cast_nullable_to_non_nullable
                      as String?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$InstitutionImplCopyWith<$Res>
    implements $InstitutionCopyWith<$Res> {
  factory _$$InstitutionImplCopyWith(
    _$InstitutionImpl value,
    $Res Function(_$InstitutionImpl) then,
  ) = __$$InstitutionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    InstitutionType? type,
    String? purpose,
    List<String> functions,
    String? structure,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$InstitutionImplCopyWithImpl<$Res>
    extends _$InstitutionCopyWithImpl<$Res, _$InstitutionImpl>
    implements _$$InstitutionImplCopyWith<$Res> {
  __$$InstitutionImplCopyWithImpl(
    _$InstitutionImpl _value,
    $Res Function(_$InstitutionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Institution
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = freezed,
    Object? purpose = freezed,
    Object? functions = null,
    Object? structure = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _$InstitutionImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as InstitutionType?,
        purpose: freezed == purpose
            ? _value.purpose
            : purpose // ignore: cast_nullable_to_non_nullable
                  as String?,
        functions: null == functions
            ? _value._functions
            : functions // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        structure: freezed == structure
            ? _value.structure
            : structure // ignore: cast_nullable_to_non_nullable
                  as String?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$InstitutionImpl implements _Institution {
  const _$InstitutionImpl({
    required this.id,
    required this.name,
    this.description,
    this.type,
    this.purpose,
    final List<String> functions = const [],
    this.structure,
    final Map<String, dynamic> customAttributes = const {},
  }) : _functions = functions,
       _customAttributes = customAttributes;

  factory _$InstitutionImpl.fromJson(Map<String, dynamic> json) =>
      _$$InstitutionImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final InstitutionType? type;
  @override
  final String? purpose;
  final List<String> _functions;
  @override
  @JsonKey()
  List<String> get functions {
    if (_functions is EqualUnmodifiableListView) return _functions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_functions);
  }

  @override
  final String? structure;
  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'Institution(id: $id, name: $name, description: $description, type: $type, purpose: $purpose, functions: $functions, structure: $structure, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InstitutionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.purpose, purpose) || other.purpose == purpose) &&
            const DeepCollectionEquality().equals(
              other._functions,
              _functions,
            ) &&
            (identical(other.structure, structure) ||
                other.structure == structure) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    type,
    purpose,
    const DeepCollectionEquality().hash(_functions),
    structure,
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of Institution
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InstitutionImplCopyWith<_$InstitutionImpl> get copyWith =>
      __$$InstitutionImplCopyWithImpl<_$InstitutionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$InstitutionImplToJson(this);
  }
}

abstract class _Institution implements Institution {
  const factory _Institution({
    required final String id,
    required final String name,
    final String? description,
    final InstitutionType? type,
    final String? purpose,
    final List<String> functions,
    final String? structure,
    final Map<String, dynamic> customAttributes,
  }) = _$InstitutionImpl;

  factory _Institution.fromJson(Map<String, dynamic> json) =
      _$InstitutionImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  InstitutionType? get type;
  @override
  String? get purpose;
  @override
  List<String> get functions;
  @override
  String? get structure;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of Institution
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InstitutionImplCopyWith<_$InstitutionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SocialNorm _$SocialNormFromJson(Map<String, dynamic> json) {
  return _SocialNorm.fromJson(json);
}

/// @nodoc
mixin _$SocialNorm {
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  NormType? get type => throw _privateConstructorUsedError;
  NormStrength? get strength => throw _privateConstructorUsedError;
  List<String> get violations => throw _privateConstructorUsedError;
  List<String> get consequences => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this SocialNorm to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SocialNorm
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SocialNormCopyWith<SocialNorm> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SocialNormCopyWith<$Res> {
  factory $SocialNormCopyWith(
    SocialNorm value,
    $Res Function(SocialNorm) then,
  ) = _$SocialNormCopyWithImpl<$Res, SocialNorm>;
  @useResult
  $Res call({
    String name,
    String description,
    NormType? type,
    NormStrength? strength,
    List<String> violations,
    List<String> consequences,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$SocialNormCopyWithImpl<$Res, $Val extends SocialNorm>
    implements $SocialNormCopyWith<$Res> {
  _$SocialNormCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SocialNorm
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = null,
    Object? type = freezed,
    Object? strength = freezed,
    Object? violations = null,
    Object? consequences = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as NormType?,
            strength: freezed == strength
                ? _value.strength
                : strength // ignore: cast_nullable_to_non_nullable
                      as NormStrength?,
            violations: null == violations
                ? _value.violations
                : violations // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            consequences: null == consequences
                ? _value.consequences
                : consequences // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$SocialNormImplCopyWith<$Res>
    implements $SocialNormCopyWith<$Res> {
  factory _$$SocialNormImplCopyWith(
    _$SocialNormImpl value,
    $Res Function(_$SocialNormImpl) then,
  ) = __$$SocialNormImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    String description,
    NormType? type,
    NormStrength? strength,
    List<String> violations,
    List<String> consequences,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$SocialNormImplCopyWithImpl<$Res>
    extends _$SocialNormCopyWithImpl<$Res, _$SocialNormImpl>
    implements _$$SocialNormImplCopyWith<$Res> {
  __$$SocialNormImplCopyWithImpl(
    _$SocialNormImpl _value,
    $Res Function(_$SocialNormImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of SocialNorm
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = null,
    Object? type = freezed,
    Object? strength = freezed,
    Object? violations = null,
    Object? consequences = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$SocialNormImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as NormType?,
        strength: freezed == strength
            ? _value.strength
            : strength // ignore: cast_nullable_to_non_nullable
                  as NormStrength?,
        violations: null == violations
            ? _value._violations
            : violations // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        consequences: null == consequences
            ? _value._consequences
            : consequences // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SocialNormImpl implements _SocialNorm {
  const _$SocialNormImpl({
    required this.name,
    required this.description,
    this.type,
    this.strength,
    final List<String> violations = const [],
    final List<String> consequences = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _violations = violations,
       _consequences = consequences,
       _customAttributes = customAttributes;

  factory _$SocialNormImpl.fromJson(Map<String, dynamic> json) =>
      _$$SocialNormImplFromJson(json);

  @override
  final String name;
  @override
  final String description;
  @override
  final NormType? type;
  @override
  final NormStrength? strength;
  final List<String> _violations;
  @override
  @JsonKey()
  List<String> get violations {
    if (_violations is EqualUnmodifiableListView) return _violations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_violations);
  }

  final List<String> _consequences;
  @override
  @JsonKey()
  List<String> get consequences {
    if (_consequences is EqualUnmodifiableListView) return _consequences;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_consequences);
  }

  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'SocialNorm(name: $name, description: $description, type: $type, strength: $strength, violations: $violations, consequences: $consequences, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SocialNormImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.strength, strength) ||
                other.strength == strength) &&
            const DeepCollectionEquality().equals(
              other._violations,
              _violations,
            ) &&
            const DeepCollectionEquality().equals(
              other._consequences,
              _consequences,
            ) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    description,
    type,
    strength,
    const DeepCollectionEquality().hash(_violations),
    const DeepCollectionEquality().hash(_consequences),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of SocialNorm
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SocialNormImplCopyWith<_$SocialNormImpl> get copyWith =>
      __$$SocialNormImplCopyWithImpl<_$SocialNormImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SocialNormImplToJson(this);
  }
}

abstract class _SocialNorm implements SocialNorm {
  const factory _SocialNorm({
    required final String name,
    required final String description,
    final NormType? type,
    final NormStrength? strength,
    final List<String> violations,
    final List<String> consequences,
    final Map<String, dynamic> customAttributes,
  }) = _$SocialNormImpl;

  factory _SocialNorm.fromJson(Map<String, dynamic> json) =
      _$SocialNormImpl.fromJson;

  @override
  String get name;
  @override
  String get description;
  @override
  NormType? get type;
  @override
  NormStrength? get strength;
  @override
  List<String> get violations;
  @override
  List<String> get consequences;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of SocialNorm
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SocialNormImplCopyWith<_$SocialNormImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WorldEconomy _$WorldEconomyFromJson(Map<String, dynamic> json) {
  return _WorldEconomy.fromJson(json);
}

/// @nodoc
mixin _$WorldEconomy {
  List<EconomicSystem> get systems => throw _privateConstructorUsedError;
  List<TradeNetwork> get tradeNetworks => throw _privateConstructorUsedError;
  List<Resource> get resources => throw _privateConstructorUsedError;
  List<Industry> get industries => throw _privateConstructorUsedError;
  String? get primaryCurrency => throw _privateConstructorUsedError;
  Map<String, dynamic> get customEconomy => throw _privateConstructorUsedError;

  /// Serializes this WorldEconomy to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WorldEconomy
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WorldEconomyCopyWith<WorldEconomy> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorldEconomyCopyWith<$Res> {
  factory $WorldEconomyCopyWith(
    WorldEconomy value,
    $Res Function(WorldEconomy) then,
  ) = _$WorldEconomyCopyWithImpl<$Res, WorldEconomy>;
  @useResult
  $Res call({
    List<EconomicSystem> systems,
    List<TradeNetwork> tradeNetworks,
    List<Resource> resources,
    List<Industry> industries,
    String? primaryCurrency,
    Map<String, dynamic> customEconomy,
  });
}

/// @nodoc
class _$WorldEconomyCopyWithImpl<$Res, $Val extends WorldEconomy>
    implements $WorldEconomyCopyWith<$Res> {
  _$WorldEconomyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WorldEconomy
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? systems = null,
    Object? tradeNetworks = null,
    Object? resources = null,
    Object? industries = null,
    Object? primaryCurrency = freezed,
    Object? customEconomy = null,
  }) {
    return _then(
      _value.copyWith(
            systems: null == systems
                ? _value.systems
                : systems // ignore: cast_nullable_to_non_nullable
                      as List<EconomicSystem>,
            tradeNetworks: null == tradeNetworks
                ? _value.tradeNetworks
                : tradeNetworks // ignore: cast_nullable_to_non_nullable
                      as List<TradeNetwork>,
            resources: null == resources
                ? _value.resources
                : resources // ignore: cast_nullable_to_non_nullable
                      as List<Resource>,
            industries: null == industries
                ? _value.industries
                : industries // ignore: cast_nullable_to_non_nullable
                      as List<Industry>,
            primaryCurrency: freezed == primaryCurrency
                ? _value.primaryCurrency
                : primaryCurrency // ignore: cast_nullable_to_non_nullable
                      as String?,
            customEconomy: null == customEconomy
                ? _value.customEconomy
                : customEconomy // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$WorldEconomyImplCopyWith<$Res>
    implements $WorldEconomyCopyWith<$Res> {
  factory _$$WorldEconomyImplCopyWith(
    _$WorldEconomyImpl value,
    $Res Function(_$WorldEconomyImpl) then,
  ) = __$$WorldEconomyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    List<EconomicSystem> systems,
    List<TradeNetwork> tradeNetworks,
    List<Resource> resources,
    List<Industry> industries,
    String? primaryCurrency,
    Map<String, dynamic> customEconomy,
  });
}

/// @nodoc
class __$$WorldEconomyImplCopyWithImpl<$Res>
    extends _$WorldEconomyCopyWithImpl<$Res, _$WorldEconomyImpl>
    implements _$$WorldEconomyImplCopyWith<$Res> {
  __$$WorldEconomyImplCopyWithImpl(
    _$WorldEconomyImpl _value,
    $Res Function(_$WorldEconomyImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of WorldEconomy
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? systems = null,
    Object? tradeNetworks = null,
    Object? resources = null,
    Object? industries = null,
    Object? primaryCurrency = freezed,
    Object? customEconomy = null,
  }) {
    return _then(
      _$WorldEconomyImpl(
        systems: null == systems
            ? _value._systems
            : systems // ignore: cast_nullable_to_non_nullable
                  as List<EconomicSystem>,
        tradeNetworks: null == tradeNetworks
            ? _value._tradeNetworks
            : tradeNetworks // ignore: cast_nullable_to_non_nullable
                  as List<TradeNetwork>,
        resources: null == resources
            ? _value._resources
            : resources // ignore: cast_nullable_to_non_nullable
                  as List<Resource>,
        industries: null == industries
            ? _value._industries
            : industries // ignore: cast_nullable_to_non_nullable
                  as List<Industry>,
        primaryCurrency: freezed == primaryCurrency
            ? _value.primaryCurrency
            : primaryCurrency // ignore: cast_nullable_to_non_nullable
                  as String?,
        customEconomy: null == customEconomy
            ? _value._customEconomy
            : customEconomy // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$WorldEconomyImpl implements _WorldEconomy {
  const _$WorldEconomyImpl({
    final List<EconomicSystem> systems = const [],
    final List<TradeNetwork> tradeNetworks = const [],
    final List<Resource> resources = const [],
    final List<Industry> industries = const [],
    this.primaryCurrency,
    final Map<String, dynamic> customEconomy = const {},
  }) : _systems = systems,
       _tradeNetworks = tradeNetworks,
       _resources = resources,
       _industries = industries,
       _customEconomy = customEconomy;

  factory _$WorldEconomyImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorldEconomyImplFromJson(json);

  final List<EconomicSystem> _systems;
  @override
  @JsonKey()
  List<EconomicSystem> get systems {
    if (_systems is EqualUnmodifiableListView) return _systems;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_systems);
  }

  final List<TradeNetwork> _tradeNetworks;
  @override
  @JsonKey()
  List<TradeNetwork> get tradeNetworks {
    if (_tradeNetworks is EqualUnmodifiableListView) return _tradeNetworks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tradeNetworks);
  }

  final List<Resource> _resources;
  @override
  @JsonKey()
  List<Resource> get resources {
    if (_resources is EqualUnmodifiableListView) return _resources;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_resources);
  }

  final List<Industry> _industries;
  @override
  @JsonKey()
  List<Industry> get industries {
    if (_industries is EqualUnmodifiableListView) return _industries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_industries);
  }

  @override
  final String? primaryCurrency;
  final Map<String, dynamic> _customEconomy;
  @override
  @JsonKey()
  Map<String, dynamic> get customEconomy {
    if (_customEconomy is EqualUnmodifiableMapView) return _customEconomy;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customEconomy);
  }

  @override
  String toString() {
    return 'WorldEconomy(systems: $systems, tradeNetworks: $tradeNetworks, resources: $resources, industries: $industries, primaryCurrency: $primaryCurrency, customEconomy: $customEconomy)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorldEconomyImpl &&
            const DeepCollectionEquality().equals(other._systems, _systems) &&
            const DeepCollectionEquality().equals(
              other._tradeNetworks,
              _tradeNetworks,
            ) &&
            const DeepCollectionEquality().equals(
              other._resources,
              _resources,
            ) &&
            const DeepCollectionEquality().equals(
              other._industries,
              _industries,
            ) &&
            (identical(other.primaryCurrency, primaryCurrency) ||
                other.primaryCurrency == primaryCurrency) &&
            const DeepCollectionEquality().equals(
              other._customEconomy,
              _customEconomy,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_systems),
    const DeepCollectionEquality().hash(_tradeNetworks),
    const DeepCollectionEquality().hash(_resources),
    const DeepCollectionEquality().hash(_industries),
    primaryCurrency,
    const DeepCollectionEquality().hash(_customEconomy),
  );

  /// Create a copy of WorldEconomy
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WorldEconomyImplCopyWith<_$WorldEconomyImpl> get copyWith =>
      __$$WorldEconomyImplCopyWithImpl<_$WorldEconomyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorldEconomyImplToJson(this);
  }
}

abstract class _WorldEconomy implements WorldEconomy {
  const factory _WorldEconomy({
    final List<EconomicSystem> systems,
    final List<TradeNetwork> tradeNetworks,
    final List<Resource> resources,
    final List<Industry> industries,
    final String? primaryCurrency,
    final Map<String, dynamic> customEconomy,
  }) = _$WorldEconomyImpl;

  factory _WorldEconomy.fromJson(Map<String, dynamic> json) =
      _$WorldEconomyImpl.fromJson;

  @override
  List<EconomicSystem> get systems;
  @override
  List<TradeNetwork> get tradeNetworks;
  @override
  List<Resource> get resources;
  @override
  List<Industry> get industries;
  @override
  String? get primaryCurrency;
  @override
  Map<String, dynamic> get customEconomy;

  /// Create a copy of WorldEconomy
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WorldEconomyImplCopyWith<_$WorldEconomyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

EconomicSystem _$EconomicSystemFromJson(Map<String, dynamic> json) {
  return _EconomicSystem.fromJson(json);
}

/// @nodoc
mixin _$EconomicSystem {
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  EconomicSystemType? get type => throw _privateConstructorUsedError;
  List<String> get characteristics => throw _privateConstructorUsedError;
  List<String> get advantages => throw _privateConstructorUsedError;
  List<String> get disadvantages => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this EconomicSystem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EconomicSystem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EconomicSystemCopyWith<EconomicSystem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EconomicSystemCopyWith<$Res> {
  factory $EconomicSystemCopyWith(
    EconomicSystem value,
    $Res Function(EconomicSystem) then,
  ) = _$EconomicSystemCopyWithImpl<$Res, EconomicSystem>;
  @useResult
  $Res call({
    String name,
    String? description,
    EconomicSystemType? type,
    List<String> characteristics,
    List<String> advantages,
    List<String> disadvantages,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$EconomicSystemCopyWithImpl<$Res, $Val extends EconomicSystem>
    implements $EconomicSystemCopyWith<$Res> {
  _$EconomicSystemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EconomicSystem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? type = freezed,
    Object? characteristics = null,
    Object? advantages = null,
    Object? disadvantages = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as EconomicSystemType?,
            characteristics: null == characteristics
                ? _value.characteristics
                : characteristics // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            advantages: null == advantages
                ? _value.advantages
                : advantages // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            disadvantages: null == disadvantages
                ? _value.disadvantages
                : disadvantages // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$EconomicSystemImplCopyWith<$Res>
    implements $EconomicSystemCopyWith<$Res> {
  factory _$$EconomicSystemImplCopyWith(
    _$EconomicSystemImpl value,
    $Res Function(_$EconomicSystemImpl) then,
  ) = __$$EconomicSystemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    String? description,
    EconomicSystemType? type,
    List<String> characteristics,
    List<String> advantages,
    List<String> disadvantages,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$EconomicSystemImplCopyWithImpl<$Res>
    extends _$EconomicSystemCopyWithImpl<$Res, _$EconomicSystemImpl>
    implements _$$EconomicSystemImplCopyWith<$Res> {
  __$$EconomicSystemImplCopyWithImpl(
    _$EconomicSystemImpl _value,
    $Res Function(_$EconomicSystemImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EconomicSystem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? type = freezed,
    Object? characteristics = null,
    Object? advantages = null,
    Object? disadvantages = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$EconomicSystemImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as EconomicSystemType?,
        characteristics: null == characteristics
            ? _value._characteristics
            : characteristics // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        advantages: null == advantages
            ? _value._advantages
            : advantages // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        disadvantages: null == disadvantages
            ? _value._disadvantages
            : disadvantages // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$EconomicSystemImpl implements _EconomicSystem {
  const _$EconomicSystemImpl({
    required this.name,
    this.description,
    this.type,
    final List<String> characteristics = const [],
    final List<String> advantages = const [],
    final List<String> disadvantages = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _characteristics = characteristics,
       _advantages = advantages,
       _disadvantages = disadvantages,
       _customAttributes = customAttributes;

  factory _$EconomicSystemImpl.fromJson(Map<String, dynamic> json) =>
      _$$EconomicSystemImplFromJson(json);

  @override
  final String name;
  @override
  final String? description;
  @override
  final EconomicSystemType? type;
  final List<String> _characteristics;
  @override
  @JsonKey()
  List<String> get characteristics {
    if (_characteristics is EqualUnmodifiableListView) return _characteristics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_characteristics);
  }

  final List<String> _advantages;
  @override
  @JsonKey()
  List<String> get advantages {
    if (_advantages is EqualUnmodifiableListView) return _advantages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_advantages);
  }

  final List<String> _disadvantages;
  @override
  @JsonKey()
  List<String> get disadvantages {
    if (_disadvantages is EqualUnmodifiableListView) return _disadvantages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_disadvantages);
  }

  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'EconomicSystem(name: $name, description: $description, type: $type, characteristics: $characteristics, advantages: $advantages, disadvantages: $disadvantages, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EconomicSystemImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(
              other._characteristics,
              _characteristics,
            ) &&
            const DeepCollectionEquality().equals(
              other._advantages,
              _advantages,
            ) &&
            const DeepCollectionEquality().equals(
              other._disadvantages,
              _disadvantages,
            ) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    description,
    type,
    const DeepCollectionEquality().hash(_characteristics),
    const DeepCollectionEquality().hash(_advantages),
    const DeepCollectionEquality().hash(_disadvantages),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of EconomicSystem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EconomicSystemImplCopyWith<_$EconomicSystemImpl> get copyWith =>
      __$$EconomicSystemImplCopyWithImpl<_$EconomicSystemImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$EconomicSystemImplToJson(this);
  }
}

abstract class _EconomicSystem implements EconomicSystem {
  const factory _EconomicSystem({
    required final String name,
    final String? description,
    final EconomicSystemType? type,
    final List<String> characteristics,
    final List<String> advantages,
    final List<String> disadvantages,
    final Map<String, dynamic> customAttributes,
  }) = _$EconomicSystemImpl;

  factory _EconomicSystem.fromJson(Map<String, dynamic> json) =
      _$EconomicSystemImpl.fromJson;

  @override
  String get name;
  @override
  String? get description;
  @override
  EconomicSystemType? get type;
  @override
  List<String> get characteristics;
  @override
  List<String> get advantages;
  @override
  List<String> get disadvantages;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of EconomicSystem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EconomicSystemImplCopyWith<_$EconomicSystemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TradeNetwork _$TradeNetworkFromJson(Map<String, dynamic> json) {
  return _TradeNetwork.fromJson(json);
}

/// @nodoc
mixin _$TradeNetwork {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  List<String> get participants => throw _privateConstructorUsedError;
  List<String> get tradedGoods => throw _privateConstructorUsedError;
  List<String> get routes => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this TradeNetwork to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TradeNetwork
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TradeNetworkCopyWith<TradeNetwork> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TradeNetworkCopyWith<$Res> {
  factory $TradeNetworkCopyWith(
    TradeNetwork value,
    $Res Function(TradeNetwork) then,
  ) = _$TradeNetworkCopyWithImpl<$Res, TradeNetwork>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    List<String> participants,
    List<String> tradedGoods,
    List<String> routes,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$TradeNetworkCopyWithImpl<$Res, $Val extends TradeNetwork>
    implements $TradeNetworkCopyWith<$Res> {
  _$TradeNetworkCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TradeNetwork
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? participants = null,
    Object? tradedGoods = null,
    Object? routes = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            participants: null == participants
                ? _value.participants
                : participants // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            tradedGoods: null == tradedGoods
                ? _value.tradedGoods
                : tradedGoods // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            routes: null == routes
                ? _value.routes
                : routes // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$TradeNetworkImplCopyWith<$Res>
    implements $TradeNetworkCopyWith<$Res> {
  factory _$$TradeNetworkImplCopyWith(
    _$TradeNetworkImpl value,
    $Res Function(_$TradeNetworkImpl) then,
  ) = __$$TradeNetworkImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    List<String> participants,
    List<String> tradedGoods,
    List<String> routes,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$TradeNetworkImplCopyWithImpl<$Res>
    extends _$TradeNetworkCopyWithImpl<$Res, _$TradeNetworkImpl>
    implements _$$TradeNetworkImplCopyWith<$Res> {
  __$$TradeNetworkImplCopyWithImpl(
    _$TradeNetworkImpl _value,
    $Res Function(_$TradeNetworkImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TradeNetwork
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? participants = null,
    Object? tradedGoods = null,
    Object? routes = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$TradeNetworkImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        participants: null == participants
            ? _value._participants
            : participants // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        tradedGoods: null == tradedGoods
            ? _value._tradedGoods
            : tradedGoods // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        routes: null == routes
            ? _value._routes
            : routes // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$TradeNetworkImpl implements _TradeNetwork {
  const _$TradeNetworkImpl({
    required this.id,
    required this.name,
    this.description,
    final List<String> participants = const [],
    final List<String> tradedGoods = const [],
    final List<String> routes = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _participants = participants,
       _tradedGoods = tradedGoods,
       _routes = routes,
       _customAttributes = customAttributes;

  factory _$TradeNetworkImpl.fromJson(Map<String, dynamic> json) =>
      _$$TradeNetworkImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  final List<String> _participants;
  @override
  @JsonKey()
  List<String> get participants {
    if (_participants is EqualUnmodifiableListView) return _participants;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_participants);
  }

  final List<String> _tradedGoods;
  @override
  @JsonKey()
  List<String> get tradedGoods {
    if (_tradedGoods is EqualUnmodifiableListView) return _tradedGoods;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tradedGoods);
  }

  final List<String> _routes;
  @override
  @JsonKey()
  List<String> get routes {
    if (_routes is EqualUnmodifiableListView) return _routes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_routes);
  }

  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'TradeNetwork(id: $id, name: $name, description: $description, participants: $participants, tradedGoods: $tradedGoods, routes: $routes, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TradeNetworkImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(
              other._participants,
              _participants,
            ) &&
            const DeepCollectionEquality().equals(
              other._tradedGoods,
              _tradedGoods,
            ) &&
            const DeepCollectionEquality().equals(other._routes, _routes) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    const DeepCollectionEquality().hash(_participants),
    const DeepCollectionEquality().hash(_tradedGoods),
    const DeepCollectionEquality().hash(_routes),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of TradeNetwork
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TradeNetworkImplCopyWith<_$TradeNetworkImpl> get copyWith =>
      __$$TradeNetworkImplCopyWithImpl<_$TradeNetworkImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TradeNetworkImplToJson(this);
  }
}

abstract class _TradeNetwork implements TradeNetwork {
  const factory _TradeNetwork({
    required final String id,
    required final String name,
    final String? description,
    final List<String> participants,
    final List<String> tradedGoods,
    final List<String> routes,
    final Map<String, dynamic> customAttributes,
  }) = _$TradeNetworkImpl;

  factory _TradeNetwork.fromJson(Map<String, dynamic> json) =
      _$TradeNetworkImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  List<String> get participants;
  @override
  List<String> get tradedGoods;
  @override
  List<String> get routes;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of TradeNetwork
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TradeNetworkImplCopyWith<_$TradeNetworkImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Resource _$ResourceFromJson(Map<String, dynamic> json) {
  return _Resource.fromJson(json);
}

/// @nodoc
mixin _$Resource {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  ResourceType? get type => throw _privateConstructorUsedError;
  ResourceRarity? get rarity => throw _privateConstructorUsedError;
  List<String> get locations => throw _privateConstructorUsedError;
  List<String> get uses => throw _privateConstructorUsedError;
  String? get extractionMethod => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this Resource to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Resource
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ResourceCopyWith<Resource> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ResourceCopyWith<$Res> {
  factory $ResourceCopyWith(Resource value, $Res Function(Resource) then) =
      _$ResourceCopyWithImpl<$Res, Resource>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    ResourceType? type,
    ResourceRarity? rarity,
    List<String> locations,
    List<String> uses,
    String? extractionMethod,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$ResourceCopyWithImpl<$Res, $Val extends Resource>
    implements $ResourceCopyWith<$Res> {
  _$ResourceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Resource
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = freezed,
    Object? rarity = freezed,
    Object? locations = null,
    Object? uses = null,
    Object? extractionMethod = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as ResourceType?,
            rarity: freezed == rarity
                ? _value.rarity
                : rarity // ignore: cast_nullable_to_non_nullable
                      as ResourceRarity?,
            locations: null == locations
                ? _value.locations
                : locations // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            uses: null == uses
                ? _value.uses
                : uses // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            extractionMethod: freezed == extractionMethod
                ? _value.extractionMethod
                : extractionMethod // ignore: cast_nullable_to_non_nullable
                      as String?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ResourceImplCopyWith<$Res>
    implements $ResourceCopyWith<$Res> {
  factory _$$ResourceImplCopyWith(
    _$ResourceImpl value,
    $Res Function(_$ResourceImpl) then,
  ) = __$$ResourceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    ResourceType? type,
    ResourceRarity? rarity,
    List<String> locations,
    List<String> uses,
    String? extractionMethod,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$ResourceImplCopyWithImpl<$Res>
    extends _$ResourceCopyWithImpl<$Res, _$ResourceImpl>
    implements _$$ResourceImplCopyWith<$Res> {
  __$$ResourceImplCopyWithImpl(
    _$ResourceImpl _value,
    $Res Function(_$ResourceImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Resource
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = freezed,
    Object? rarity = freezed,
    Object? locations = null,
    Object? uses = null,
    Object? extractionMethod = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _$ResourceImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as ResourceType?,
        rarity: freezed == rarity
            ? _value.rarity
            : rarity // ignore: cast_nullable_to_non_nullable
                  as ResourceRarity?,
        locations: null == locations
            ? _value._locations
            : locations // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        uses: null == uses
            ? _value._uses
            : uses // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        extractionMethod: freezed == extractionMethod
            ? _value.extractionMethod
            : extractionMethod // ignore: cast_nullable_to_non_nullable
                  as String?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ResourceImpl implements _Resource {
  const _$ResourceImpl({
    required this.id,
    required this.name,
    this.description,
    this.type,
    this.rarity,
    final List<String> locations = const [],
    final List<String> uses = const [],
    this.extractionMethod,
    final Map<String, dynamic> customAttributes = const {},
  }) : _locations = locations,
       _uses = uses,
       _customAttributes = customAttributes;

  factory _$ResourceImpl.fromJson(Map<String, dynamic> json) =>
      _$$ResourceImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final ResourceType? type;
  @override
  final ResourceRarity? rarity;
  final List<String> _locations;
  @override
  @JsonKey()
  List<String> get locations {
    if (_locations is EqualUnmodifiableListView) return _locations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_locations);
  }

  final List<String> _uses;
  @override
  @JsonKey()
  List<String> get uses {
    if (_uses is EqualUnmodifiableListView) return _uses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_uses);
  }

  @override
  final String? extractionMethod;
  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'Resource(id: $id, name: $name, description: $description, type: $type, rarity: $rarity, locations: $locations, uses: $uses, extractionMethod: $extractionMethod, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ResourceImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.rarity, rarity) || other.rarity == rarity) &&
            const DeepCollectionEquality().equals(
              other._locations,
              _locations,
            ) &&
            const DeepCollectionEquality().equals(other._uses, _uses) &&
            (identical(other.extractionMethod, extractionMethod) ||
                other.extractionMethod == extractionMethod) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    type,
    rarity,
    const DeepCollectionEquality().hash(_locations),
    const DeepCollectionEquality().hash(_uses),
    extractionMethod,
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of Resource
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ResourceImplCopyWith<_$ResourceImpl> get copyWith =>
      __$$ResourceImplCopyWithImpl<_$ResourceImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ResourceImplToJson(this);
  }
}

abstract class _Resource implements Resource {
  const factory _Resource({
    required final String id,
    required final String name,
    final String? description,
    final ResourceType? type,
    final ResourceRarity? rarity,
    final List<String> locations,
    final List<String> uses,
    final String? extractionMethod,
    final Map<String, dynamic> customAttributes,
  }) = _$ResourceImpl;

  factory _Resource.fromJson(Map<String, dynamic> json) =
      _$ResourceImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  ResourceType? get type;
  @override
  ResourceRarity? get rarity;
  @override
  List<String> get locations;
  @override
  List<String> get uses;
  @override
  String? get extractionMethod;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of Resource
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ResourceImplCopyWith<_$ResourceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Industry _$IndustryFromJson(Map<String, dynamic> json) {
  return _Industry.fromJson(json);
}

/// @nodoc
mixin _$Industry {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  IndustryType? get type => throw _privateConstructorUsedError;
  List<String> get products => throw _privateConstructorUsedError;
  List<String> get requiredResources => throw _privateConstructorUsedError;
  List<String> get locations => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this Industry to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Industry
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $IndustryCopyWith<Industry> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IndustryCopyWith<$Res> {
  factory $IndustryCopyWith(Industry value, $Res Function(Industry) then) =
      _$IndustryCopyWithImpl<$Res, Industry>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    IndustryType? type,
    List<String> products,
    List<String> requiredResources,
    List<String> locations,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$IndustryCopyWithImpl<$Res, $Val extends Industry>
    implements $IndustryCopyWith<$Res> {
  _$IndustryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Industry
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = freezed,
    Object? products = null,
    Object? requiredResources = null,
    Object? locations = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as IndustryType?,
            products: null == products
                ? _value.products
                : products // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            requiredResources: null == requiredResources
                ? _value.requiredResources
                : requiredResources // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            locations: null == locations
                ? _value.locations
                : locations // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$IndustryImplCopyWith<$Res>
    implements $IndustryCopyWith<$Res> {
  factory _$$IndustryImplCopyWith(
    _$IndustryImpl value,
    $Res Function(_$IndustryImpl) then,
  ) = __$$IndustryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    IndustryType? type,
    List<String> products,
    List<String> requiredResources,
    List<String> locations,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$IndustryImplCopyWithImpl<$Res>
    extends _$IndustryCopyWithImpl<$Res, _$IndustryImpl>
    implements _$$IndustryImplCopyWith<$Res> {
  __$$IndustryImplCopyWithImpl(
    _$IndustryImpl _value,
    $Res Function(_$IndustryImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Industry
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = freezed,
    Object? products = null,
    Object? requiredResources = null,
    Object? locations = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$IndustryImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as IndustryType?,
        products: null == products
            ? _value._products
            : products // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        requiredResources: null == requiredResources
            ? _value._requiredResources
            : requiredResources // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        locations: null == locations
            ? _value._locations
            : locations // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$IndustryImpl implements _Industry {
  const _$IndustryImpl({
    required this.id,
    required this.name,
    this.description,
    this.type,
    final List<String> products = const [],
    final List<String> requiredResources = const [],
    final List<String> locations = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _products = products,
       _requiredResources = requiredResources,
       _locations = locations,
       _customAttributes = customAttributes;

  factory _$IndustryImpl.fromJson(Map<String, dynamic> json) =>
      _$$IndustryImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final IndustryType? type;
  final List<String> _products;
  @override
  @JsonKey()
  List<String> get products {
    if (_products is EqualUnmodifiableListView) return _products;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_products);
  }

  final List<String> _requiredResources;
  @override
  @JsonKey()
  List<String> get requiredResources {
    if (_requiredResources is EqualUnmodifiableListView)
      return _requiredResources;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_requiredResources);
  }

  final List<String> _locations;
  @override
  @JsonKey()
  List<String> get locations {
    if (_locations is EqualUnmodifiableListView) return _locations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_locations);
  }

  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'Industry(id: $id, name: $name, description: $description, type: $type, products: $products, requiredResources: $requiredResources, locations: $locations, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IndustryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other._products, _products) &&
            const DeepCollectionEquality().equals(
              other._requiredResources,
              _requiredResources,
            ) &&
            const DeepCollectionEquality().equals(
              other._locations,
              _locations,
            ) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    type,
    const DeepCollectionEquality().hash(_products),
    const DeepCollectionEquality().hash(_requiredResources),
    const DeepCollectionEquality().hash(_locations),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of Industry
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IndustryImplCopyWith<_$IndustryImpl> get copyWith =>
      __$$IndustryImplCopyWithImpl<_$IndustryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$IndustryImplToJson(this);
  }
}

abstract class _Industry implements Industry {
  const factory _Industry({
    required final String id,
    required final String name,
    final String? description,
    final IndustryType? type,
    final List<String> products,
    final List<String> requiredResources,
    final List<String> locations,
    final Map<String, dynamic> customAttributes,
  }) = _$IndustryImpl;

  factory _Industry.fromJson(Map<String, dynamic> json) =
      _$IndustryImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  IndustryType? get type;
  @override
  List<String> get products;
  @override
  List<String> get requiredResources;
  @override
  List<String> get locations;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of Industry
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IndustryImplCopyWith<_$IndustryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WorldReligion _$WorldReligionFromJson(Map<String, dynamic> json) {
  return _WorldReligion.fromJson(json);
}

/// @nodoc
mixin _$WorldReligion {
  List<Religion> get religions => throw _privateConstructorUsedError;
  List<Deity> get deities => throw _privateConstructorUsedError;
  List<ReligiousOrder> get orders => throw _privateConstructorUsedError;
  List<Prophecy> get prophecies => throw _privateConstructorUsedError;
  Map<String, dynamic> get customReligion => throw _privateConstructorUsedError;

  /// Serializes this WorldReligion to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WorldReligion
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WorldReligionCopyWith<WorldReligion> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorldReligionCopyWith<$Res> {
  factory $WorldReligionCopyWith(
    WorldReligion value,
    $Res Function(WorldReligion) then,
  ) = _$WorldReligionCopyWithImpl<$Res, WorldReligion>;
  @useResult
  $Res call({
    List<Religion> religions,
    List<Deity> deities,
    List<ReligiousOrder> orders,
    List<Prophecy> prophecies,
    Map<String, dynamic> customReligion,
  });
}

/// @nodoc
class _$WorldReligionCopyWithImpl<$Res, $Val extends WorldReligion>
    implements $WorldReligionCopyWith<$Res> {
  _$WorldReligionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WorldReligion
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? religions = null,
    Object? deities = null,
    Object? orders = null,
    Object? prophecies = null,
    Object? customReligion = null,
  }) {
    return _then(
      _value.copyWith(
            religions: null == religions
                ? _value.religions
                : religions // ignore: cast_nullable_to_non_nullable
                      as List<Religion>,
            deities: null == deities
                ? _value.deities
                : deities // ignore: cast_nullable_to_non_nullable
                      as List<Deity>,
            orders: null == orders
                ? _value.orders
                : orders // ignore: cast_nullable_to_non_nullable
                      as List<ReligiousOrder>,
            prophecies: null == prophecies
                ? _value.prophecies
                : prophecies // ignore: cast_nullable_to_non_nullable
                      as List<Prophecy>,
            customReligion: null == customReligion
                ? _value.customReligion
                : customReligion // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$WorldReligionImplCopyWith<$Res>
    implements $WorldReligionCopyWith<$Res> {
  factory _$$WorldReligionImplCopyWith(
    _$WorldReligionImpl value,
    $Res Function(_$WorldReligionImpl) then,
  ) = __$$WorldReligionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    List<Religion> religions,
    List<Deity> deities,
    List<ReligiousOrder> orders,
    List<Prophecy> prophecies,
    Map<String, dynamic> customReligion,
  });
}

/// @nodoc
class __$$WorldReligionImplCopyWithImpl<$Res>
    extends _$WorldReligionCopyWithImpl<$Res, _$WorldReligionImpl>
    implements _$$WorldReligionImplCopyWith<$Res> {
  __$$WorldReligionImplCopyWithImpl(
    _$WorldReligionImpl _value,
    $Res Function(_$WorldReligionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of WorldReligion
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? religions = null,
    Object? deities = null,
    Object? orders = null,
    Object? prophecies = null,
    Object? customReligion = null,
  }) {
    return _then(
      _$WorldReligionImpl(
        religions: null == religions
            ? _value._religions
            : religions // ignore: cast_nullable_to_non_nullable
                  as List<Religion>,
        deities: null == deities
            ? _value._deities
            : deities // ignore: cast_nullable_to_non_nullable
                  as List<Deity>,
        orders: null == orders
            ? _value._orders
            : orders // ignore: cast_nullable_to_non_nullable
                  as List<ReligiousOrder>,
        prophecies: null == prophecies
            ? _value._prophecies
            : prophecies // ignore: cast_nullable_to_non_nullable
                  as List<Prophecy>,
        customReligion: null == customReligion
            ? _value._customReligion
            : customReligion // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$WorldReligionImpl implements _WorldReligion {
  const _$WorldReligionImpl({
    final List<Religion> religions = const [],
    final List<Deity> deities = const [],
    final List<ReligiousOrder> orders = const [],
    final List<Prophecy> prophecies = const [],
    final Map<String, dynamic> customReligion = const {},
  }) : _religions = religions,
       _deities = deities,
       _orders = orders,
       _prophecies = prophecies,
       _customReligion = customReligion;

  factory _$WorldReligionImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorldReligionImplFromJson(json);

  final List<Religion> _religions;
  @override
  @JsonKey()
  List<Religion> get religions {
    if (_religions is EqualUnmodifiableListView) return _religions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_religions);
  }

  final List<Deity> _deities;
  @override
  @JsonKey()
  List<Deity> get deities {
    if (_deities is EqualUnmodifiableListView) return _deities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_deities);
  }

  final List<ReligiousOrder> _orders;
  @override
  @JsonKey()
  List<ReligiousOrder> get orders {
    if (_orders is EqualUnmodifiableListView) return _orders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_orders);
  }

  final List<Prophecy> _prophecies;
  @override
  @JsonKey()
  List<Prophecy> get prophecies {
    if (_prophecies is EqualUnmodifiableListView) return _prophecies;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_prophecies);
  }

  final Map<String, dynamic> _customReligion;
  @override
  @JsonKey()
  Map<String, dynamic> get customReligion {
    if (_customReligion is EqualUnmodifiableMapView) return _customReligion;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customReligion);
  }

  @override
  String toString() {
    return 'WorldReligion(religions: $religions, deities: $deities, orders: $orders, prophecies: $prophecies, customReligion: $customReligion)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorldReligionImpl &&
            const DeepCollectionEquality().equals(
              other._religions,
              _religions,
            ) &&
            const DeepCollectionEquality().equals(other._deities, _deities) &&
            const DeepCollectionEquality().equals(other._orders, _orders) &&
            const DeepCollectionEquality().equals(
              other._prophecies,
              _prophecies,
            ) &&
            const DeepCollectionEquality().equals(
              other._customReligion,
              _customReligion,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_religions),
    const DeepCollectionEquality().hash(_deities),
    const DeepCollectionEquality().hash(_orders),
    const DeepCollectionEquality().hash(_prophecies),
    const DeepCollectionEquality().hash(_customReligion),
  );

  /// Create a copy of WorldReligion
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WorldReligionImplCopyWith<_$WorldReligionImpl> get copyWith =>
      __$$WorldReligionImplCopyWithImpl<_$WorldReligionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorldReligionImplToJson(this);
  }
}

abstract class _WorldReligion implements WorldReligion {
  const factory _WorldReligion({
    final List<Religion> religions,
    final List<Deity> deities,
    final List<ReligiousOrder> orders,
    final List<Prophecy> prophecies,
    final Map<String, dynamic> customReligion,
  }) = _$WorldReligionImpl;

  factory _WorldReligion.fromJson(Map<String, dynamic> json) =
      _$WorldReligionImpl.fromJson;

  @override
  List<Religion> get religions;
  @override
  List<Deity> get deities;
  @override
  List<ReligiousOrder> get orders;
  @override
  List<Prophecy> get prophecies;
  @override
  Map<String, dynamic> get customReligion;

  /// Create a copy of WorldReligion
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WorldReligionImplCopyWith<_$WorldReligionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Religion _$ReligionFromJson(Map<String, dynamic> json) {
  return _Religion.fromJson(json);
}

/// @nodoc
mixin _$Religion {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  ReligionType? get type => throw _privateConstructorUsedError;
  List<String> get beliefs => throw _privateConstructorUsedError;
  List<String> get practices => throw _privateConstructorUsedError;
  List<String> get deityIds => throw _privateConstructorUsedError;
  List<String> get holyTexts => throw _privateConstructorUsedError;
  List<String> get holyPlaces => throw _privateConstructorUsedError;
  String? get afterlife => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this Religion to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Religion
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReligionCopyWith<Religion> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReligionCopyWith<$Res> {
  factory $ReligionCopyWith(Religion value, $Res Function(Religion) then) =
      _$ReligionCopyWithImpl<$Res, Religion>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    ReligionType? type,
    List<String> beliefs,
    List<String> practices,
    List<String> deityIds,
    List<String> holyTexts,
    List<String> holyPlaces,
    String? afterlife,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$ReligionCopyWithImpl<$Res, $Val extends Religion>
    implements $ReligionCopyWith<$Res> {
  _$ReligionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Religion
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = freezed,
    Object? beliefs = null,
    Object? practices = null,
    Object? deityIds = null,
    Object? holyTexts = null,
    Object? holyPlaces = null,
    Object? afterlife = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as ReligionType?,
            beliefs: null == beliefs
                ? _value.beliefs
                : beliefs // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            practices: null == practices
                ? _value.practices
                : practices // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            deityIds: null == deityIds
                ? _value.deityIds
                : deityIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            holyTexts: null == holyTexts
                ? _value.holyTexts
                : holyTexts // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            holyPlaces: null == holyPlaces
                ? _value.holyPlaces
                : holyPlaces // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            afterlife: freezed == afterlife
                ? _value.afterlife
                : afterlife // ignore: cast_nullable_to_non_nullable
                      as String?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ReligionImplCopyWith<$Res>
    implements $ReligionCopyWith<$Res> {
  factory _$$ReligionImplCopyWith(
    _$ReligionImpl value,
    $Res Function(_$ReligionImpl) then,
  ) = __$$ReligionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    ReligionType? type,
    List<String> beliefs,
    List<String> practices,
    List<String> deityIds,
    List<String> holyTexts,
    List<String> holyPlaces,
    String? afterlife,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$ReligionImplCopyWithImpl<$Res>
    extends _$ReligionCopyWithImpl<$Res, _$ReligionImpl>
    implements _$$ReligionImplCopyWith<$Res> {
  __$$ReligionImplCopyWithImpl(
    _$ReligionImpl _value,
    $Res Function(_$ReligionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Religion
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = freezed,
    Object? beliefs = null,
    Object? practices = null,
    Object? deityIds = null,
    Object? holyTexts = null,
    Object? holyPlaces = null,
    Object? afterlife = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _$ReligionImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as ReligionType?,
        beliefs: null == beliefs
            ? _value._beliefs
            : beliefs // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        practices: null == practices
            ? _value._practices
            : practices // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        deityIds: null == deityIds
            ? _value._deityIds
            : deityIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        holyTexts: null == holyTexts
            ? _value._holyTexts
            : holyTexts // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        holyPlaces: null == holyPlaces
            ? _value._holyPlaces
            : holyPlaces // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        afterlife: freezed == afterlife
            ? _value.afterlife
            : afterlife // ignore: cast_nullable_to_non_nullable
                  as String?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ReligionImpl implements _Religion {
  const _$ReligionImpl({
    required this.id,
    required this.name,
    this.description,
    this.type,
    final List<String> beliefs = const [],
    final List<String> practices = const [],
    final List<String> deityIds = const [],
    final List<String> holyTexts = const [],
    final List<String> holyPlaces = const [],
    this.afterlife,
    final Map<String, dynamic> customAttributes = const {},
  }) : _beliefs = beliefs,
       _practices = practices,
       _deityIds = deityIds,
       _holyTexts = holyTexts,
       _holyPlaces = holyPlaces,
       _customAttributes = customAttributes;

  factory _$ReligionImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReligionImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final ReligionType? type;
  final List<String> _beliefs;
  @override
  @JsonKey()
  List<String> get beliefs {
    if (_beliefs is EqualUnmodifiableListView) return _beliefs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_beliefs);
  }

  final List<String> _practices;
  @override
  @JsonKey()
  List<String> get practices {
    if (_practices is EqualUnmodifiableListView) return _practices;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_practices);
  }

  final List<String> _deityIds;
  @override
  @JsonKey()
  List<String> get deityIds {
    if (_deityIds is EqualUnmodifiableListView) return _deityIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_deityIds);
  }

  final List<String> _holyTexts;
  @override
  @JsonKey()
  List<String> get holyTexts {
    if (_holyTexts is EqualUnmodifiableListView) return _holyTexts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_holyTexts);
  }

  final List<String> _holyPlaces;
  @override
  @JsonKey()
  List<String> get holyPlaces {
    if (_holyPlaces is EqualUnmodifiableListView) return _holyPlaces;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_holyPlaces);
  }

  @override
  final String? afterlife;
  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'Religion(id: $id, name: $name, description: $description, type: $type, beliefs: $beliefs, practices: $practices, deityIds: $deityIds, holyTexts: $holyTexts, holyPlaces: $holyPlaces, afterlife: $afterlife, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReligionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other._beliefs, _beliefs) &&
            const DeepCollectionEquality().equals(
              other._practices,
              _practices,
            ) &&
            const DeepCollectionEquality().equals(other._deityIds, _deityIds) &&
            const DeepCollectionEquality().equals(
              other._holyTexts,
              _holyTexts,
            ) &&
            const DeepCollectionEquality().equals(
              other._holyPlaces,
              _holyPlaces,
            ) &&
            (identical(other.afterlife, afterlife) ||
                other.afterlife == afterlife) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    type,
    const DeepCollectionEquality().hash(_beliefs),
    const DeepCollectionEquality().hash(_practices),
    const DeepCollectionEquality().hash(_deityIds),
    const DeepCollectionEquality().hash(_holyTexts),
    const DeepCollectionEquality().hash(_holyPlaces),
    afterlife,
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of Religion
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReligionImplCopyWith<_$ReligionImpl> get copyWith =>
      __$$ReligionImplCopyWithImpl<_$ReligionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReligionImplToJson(this);
  }
}

abstract class _Religion implements Religion {
  const factory _Religion({
    required final String id,
    required final String name,
    final String? description,
    final ReligionType? type,
    final List<String> beliefs,
    final List<String> practices,
    final List<String> deityIds,
    final List<String> holyTexts,
    final List<String> holyPlaces,
    final String? afterlife,
    final Map<String, dynamic> customAttributes,
  }) = _$ReligionImpl;

  factory _Religion.fromJson(Map<String, dynamic> json) =
      _$ReligionImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  ReligionType? get type;
  @override
  List<String> get beliefs;
  @override
  List<String> get practices;
  @override
  List<String> get deityIds;
  @override
  List<String> get holyTexts;
  @override
  List<String> get holyPlaces;
  @override
  String? get afterlife;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of Religion
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReligionImplCopyWith<_$ReligionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Deity _$DeityFromJson(Map<String, dynamic> json) {
  return _Deity.fromJson(json);
}

/// @nodoc
mixin _$Deity {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  List<String> get domains => throw _privateConstructorUsedError; // 神域
  List<String> get symbols => throw _privateConstructorUsedError;
  String? get alignment => throw _privateConstructorUsedError;
  DeityPower? get power => throw _privateConstructorUsedError;
  List<String> get followers => throw _privateConstructorUsedError;
  List<String> get enemies => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this Deity to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Deity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeityCopyWith<Deity> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeityCopyWith<$Res> {
  factory $DeityCopyWith(Deity value, $Res Function(Deity) then) =
      _$DeityCopyWithImpl<$Res, Deity>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    List<String> domains,
    List<String> symbols,
    String? alignment,
    DeityPower? power,
    List<String> followers,
    List<String> enemies,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$DeityCopyWithImpl<$Res, $Val extends Deity>
    implements $DeityCopyWith<$Res> {
  _$DeityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Deity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? domains = null,
    Object? symbols = null,
    Object? alignment = freezed,
    Object? power = freezed,
    Object? followers = null,
    Object? enemies = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            domains: null == domains
                ? _value.domains
                : domains // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            symbols: null == symbols
                ? _value.symbols
                : symbols // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            alignment: freezed == alignment
                ? _value.alignment
                : alignment // ignore: cast_nullable_to_non_nullable
                      as String?,
            power: freezed == power
                ? _value.power
                : power // ignore: cast_nullable_to_non_nullable
                      as DeityPower?,
            followers: null == followers
                ? _value.followers
                : followers // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            enemies: null == enemies
                ? _value.enemies
                : enemies // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DeityImplCopyWith<$Res> implements $DeityCopyWith<$Res> {
  factory _$$DeityImplCopyWith(
    _$DeityImpl value,
    $Res Function(_$DeityImpl) then,
  ) = __$$DeityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    List<String> domains,
    List<String> symbols,
    String? alignment,
    DeityPower? power,
    List<String> followers,
    List<String> enemies,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$DeityImplCopyWithImpl<$Res>
    extends _$DeityCopyWithImpl<$Res, _$DeityImpl>
    implements _$$DeityImplCopyWith<$Res> {
  __$$DeityImplCopyWithImpl(
    _$DeityImpl _value,
    $Res Function(_$DeityImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Deity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? domains = null,
    Object? symbols = null,
    Object? alignment = freezed,
    Object? power = freezed,
    Object? followers = null,
    Object? enemies = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$DeityImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        domains: null == domains
            ? _value._domains
            : domains // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        symbols: null == symbols
            ? _value._symbols
            : symbols // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        alignment: freezed == alignment
            ? _value.alignment
            : alignment // ignore: cast_nullable_to_non_nullable
                  as String?,
        power: freezed == power
            ? _value.power
            : power // ignore: cast_nullable_to_non_nullable
                  as DeityPower?,
        followers: null == followers
            ? _value._followers
            : followers // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        enemies: null == enemies
            ? _value._enemies
            : enemies // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$DeityImpl implements _Deity {
  const _$DeityImpl({
    required this.id,
    required this.name,
    this.description,
    final List<String> domains = const [],
    final List<String> symbols = const [],
    this.alignment,
    this.power,
    final List<String> followers = const [],
    final List<String> enemies = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _domains = domains,
       _symbols = symbols,
       _followers = followers,
       _enemies = enemies,
       _customAttributes = customAttributes;

  factory _$DeityImpl.fromJson(Map<String, dynamic> json) =>
      _$$DeityImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  final List<String> _domains;
  @override
  @JsonKey()
  List<String> get domains {
    if (_domains is EqualUnmodifiableListView) return _domains;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_domains);
  }

  // 神域
  final List<String> _symbols;
  // 神域
  @override
  @JsonKey()
  List<String> get symbols {
    if (_symbols is EqualUnmodifiableListView) return _symbols;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_symbols);
  }

  @override
  final String? alignment;
  @override
  final DeityPower? power;
  final List<String> _followers;
  @override
  @JsonKey()
  List<String> get followers {
    if (_followers is EqualUnmodifiableListView) return _followers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_followers);
  }

  final List<String> _enemies;
  @override
  @JsonKey()
  List<String> get enemies {
    if (_enemies is EqualUnmodifiableListView) return _enemies;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_enemies);
  }

  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'Deity(id: $id, name: $name, description: $description, domains: $domains, symbols: $symbols, alignment: $alignment, power: $power, followers: $followers, enemies: $enemies, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeityImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(other._domains, _domains) &&
            const DeepCollectionEquality().equals(other._symbols, _symbols) &&
            (identical(other.alignment, alignment) ||
                other.alignment == alignment) &&
            (identical(other.power, power) || other.power == power) &&
            const DeepCollectionEquality().equals(
              other._followers,
              _followers,
            ) &&
            const DeepCollectionEquality().equals(other._enemies, _enemies) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    const DeepCollectionEquality().hash(_domains),
    const DeepCollectionEquality().hash(_symbols),
    alignment,
    power,
    const DeepCollectionEquality().hash(_followers),
    const DeepCollectionEquality().hash(_enemies),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of Deity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeityImplCopyWith<_$DeityImpl> get copyWith =>
      __$$DeityImplCopyWithImpl<_$DeityImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeityImplToJson(this);
  }
}

abstract class _Deity implements Deity {
  const factory _Deity({
    required final String id,
    required final String name,
    final String? description,
    final List<String> domains,
    final List<String> symbols,
    final String? alignment,
    final DeityPower? power,
    final List<String> followers,
    final List<String> enemies,
    final Map<String, dynamic> customAttributes,
  }) = _$DeityImpl;

  factory _Deity.fromJson(Map<String, dynamic> json) = _$DeityImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  List<String> get domains; // 神域
  @override
  List<String> get symbols;
  @override
  String? get alignment;
  @override
  DeityPower? get power;
  @override
  List<String> get followers;
  @override
  List<String> get enemies;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of Deity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeityImplCopyWith<_$DeityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ReligiousOrder _$ReligiousOrderFromJson(Map<String, dynamic> json) {
  return _ReligiousOrder.fromJson(json);
}

/// @nodoc
mixin _$ReligiousOrder {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get religionId => throw _privateConstructorUsedError;
  OrderType? get type => throw _privateConstructorUsedError;
  List<String> get goals => throw _privateConstructorUsedError;
  List<String> get practices => throw _privateConstructorUsedError;
  String? get hierarchy => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this ReligiousOrder to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReligiousOrder
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReligiousOrderCopyWith<ReligiousOrder> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReligiousOrderCopyWith<$Res> {
  factory $ReligiousOrderCopyWith(
    ReligiousOrder value,
    $Res Function(ReligiousOrder) then,
  ) = _$ReligiousOrderCopyWithImpl<$Res, ReligiousOrder>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    String? religionId,
    OrderType? type,
    List<String> goals,
    List<String> practices,
    String? hierarchy,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$ReligiousOrderCopyWithImpl<$Res, $Val extends ReligiousOrder>
    implements $ReligiousOrderCopyWith<$Res> {
  _$ReligiousOrderCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReligiousOrder
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? religionId = freezed,
    Object? type = freezed,
    Object? goals = null,
    Object? practices = null,
    Object? hierarchy = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            religionId: freezed == religionId
                ? _value.religionId
                : religionId // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as OrderType?,
            goals: null == goals
                ? _value.goals
                : goals // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            practices: null == practices
                ? _value.practices
                : practices // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            hierarchy: freezed == hierarchy
                ? _value.hierarchy
                : hierarchy // ignore: cast_nullable_to_non_nullable
                      as String?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ReligiousOrderImplCopyWith<$Res>
    implements $ReligiousOrderCopyWith<$Res> {
  factory _$$ReligiousOrderImplCopyWith(
    _$ReligiousOrderImpl value,
    $Res Function(_$ReligiousOrderImpl) then,
  ) = __$$ReligiousOrderImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    String? religionId,
    OrderType? type,
    List<String> goals,
    List<String> practices,
    String? hierarchy,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$ReligiousOrderImplCopyWithImpl<$Res>
    extends _$ReligiousOrderCopyWithImpl<$Res, _$ReligiousOrderImpl>
    implements _$$ReligiousOrderImplCopyWith<$Res> {
  __$$ReligiousOrderImplCopyWithImpl(
    _$ReligiousOrderImpl _value,
    $Res Function(_$ReligiousOrderImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ReligiousOrder
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? religionId = freezed,
    Object? type = freezed,
    Object? goals = null,
    Object? practices = null,
    Object? hierarchy = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _$ReligiousOrderImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        religionId: freezed == religionId
            ? _value.religionId
            : religionId // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as OrderType?,
        goals: null == goals
            ? _value._goals
            : goals // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        practices: null == practices
            ? _value._practices
            : practices // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        hierarchy: freezed == hierarchy
            ? _value.hierarchy
            : hierarchy // ignore: cast_nullable_to_non_nullable
                  as String?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ReligiousOrderImpl implements _ReligiousOrder {
  const _$ReligiousOrderImpl({
    required this.id,
    required this.name,
    this.description,
    this.religionId,
    this.type,
    final List<String> goals = const [],
    final List<String> practices = const [],
    this.hierarchy,
    final Map<String, dynamic> customAttributes = const {},
  }) : _goals = goals,
       _practices = practices,
       _customAttributes = customAttributes;

  factory _$ReligiousOrderImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReligiousOrderImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final String? religionId;
  @override
  final OrderType? type;
  final List<String> _goals;
  @override
  @JsonKey()
  List<String> get goals {
    if (_goals is EqualUnmodifiableListView) return _goals;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_goals);
  }

  final List<String> _practices;
  @override
  @JsonKey()
  List<String> get practices {
    if (_practices is EqualUnmodifiableListView) return _practices;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_practices);
  }

  @override
  final String? hierarchy;
  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'ReligiousOrder(id: $id, name: $name, description: $description, religionId: $religionId, type: $type, goals: $goals, practices: $practices, hierarchy: $hierarchy, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReligiousOrderImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.religionId, religionId) ||
                other.religionId == religionId) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other._goals, _goals) &&
            const DeepCollectionEquality().equals(
              other._practices,
              _practices,
            ) &&
            (identical(other.hierarchy, hierarchy) ||
                other.hierarchy == hierarchy) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    religionId,
    type,
    const DeepCollectionEquality().hash(_goals),
    const DeepCollectionEquality().hash(_practices),
    hierarchy,
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of ReligiousOrder
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReligiousOrderImplCopyWith<_$ReligiousOrderImpl> get copyWith =>
      __$$ReligiousOrderImplCopyWithImpl<_$ReligiousOrderImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ReligiousOrderImplToJson(this);
  }
}

abstract class _ReligiousOrder implements ReligiousOrder {
  const factory _ReligiousOrder({
    required final String id,
    required final String name,
    final String? description,
    final String? religionId,
    final OrderType? type,
    final List<String> goals,
    final List<String> practices,
    final String? hierarchy,
    final Map<String, dynamic> customAttributes,
  }) = _$ReligiousOrderImpl;

  factory _ReligiousOrder.fromJson(Map<String, dynamic> json) =
      _$ReligiousOrderImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  String? get religionId;
  @override
  OrderType? get type;
  @override
  List<String> get goals;
  @override
  List<String> get practices;
  @override
  String? get hierarchy;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of ReligiousOrder
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReligiousOrderImplCopyWith<_$ReligiousOrderImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Prophecy _$ProphecyFromJson(Map<String, dynamic> json) {
  return _Prophecy.fromJson(json);
}

/// @nodoc
mixin _$Prophecy {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get text => throw _privateConstructorUsedError;
  String? get interpretation => throw _privateConstructorUsedError;
  String? get source => throw _privateConstructorUsedError;
  ProphecyStatus? get status => throw _privateConstructorUsedError;
  List<String> get conditions => throw _privateConstructorUsedError;
  DateTime? get prophesiedDate => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this Prophecy to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Prophecy
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProphecyCopyWith<Prophecy> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProphecyCopyWith<$Res> {
  factory $ProphecyCopyWith(Prophecy value, $Res Function(Prophecy) then) =
      _$ProphecyCopyWithImpl<$Res, Prophecy>;
  @useResult
  $Res call({
    String id,
    String title,
    String text,
    String? interpretation,
    String? source,
    ProphecyStatus? status,
    List<String> conditions,
    DateTime? prophesiedDate,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$ProphecyCopyWithImpl<$Res, $Val extends Prophecy>
    implements $ProphecyCopyWith<$Res> {
  _$ProphecyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Prophecy
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? text = null,
    Object? interpretation = freezed,
    Object? source = freezed,
    Object? status = freezed,
    Object? conditions = null,
    Object? prophesiedDate = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            text: null == text
                ? _value.text
                : text // ignore: cast_nullable_to_non_nullable
                      as String,
            interpretation: freezed == interpretation
                ? _value.interpretation
                : interpretation // ignore: cast_nullable_to_non_nullable
                      as String?,
            source: freezed == source
                ? _value.source
                : source // ignore: cast_nullable_to_non_nullable
                      as String?,
            status: freezed == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as ProphecyStatus?,
            conditions: null == conditions
                ? _value.conditions
                : conditions // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            prophesiedDate: freezed == prophesiedDate
                ? _value.prophesiedDate
                : prophesiedDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ProphecyImplCopyWith<$Res>
    implements $ProphecyCopyWith<$Res> {
  factory _$$ProphecyImplCopyWith(
    _$ProphecyImpl value,
    $Res Function(_$ProphecyImpl) then,
  ) = __$$ProphecyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String text,
    String? interpretation,
    String? source,
    ProphecyStatus? status,
    List<String> conditions,
    DateTime? prophesiedDate,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$ProphecyImplCopyWithImpl<$Res>
    extends _$ProphecyCopyWithImpl<$Res, _$ProphecyImpl>
    implements _$$ProphecyImplCopyWith<$Res> {
  __$$ProphecyImplCopyWithImpl(
    _$ProphecyImpl _value,
    $Res Function(_$ProphecyImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Prophecy
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? text = null,
    Object? interpretation = freezed,
    Object? source = freezed,
    Object? status = freezed,
    Object? conditions = null,
    Object? prophesiedDate = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _$ProphecyImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        text: null == text
            ? _value.text
            : text // ignore: cast_nullable_to_non_nullable
                  as String,
        interpretation: freezed == interpretation
            ? _value.interpretation
            : interpretation // ignore: cast_nullable_to_non_nullable
                  as String?,
        source: freezed == source
            ? _value.source
            : source // ignore: cast_nullable_to_non_nullable
                  as String?,
        status: freezed == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as ProphecyStatus?,
        conditions: null == conditions
            ? _value._conditions
            : conditions // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        prophesiedDate: freezed == prophesiedDate
            ? _value.prophesiedDate
            : prophesiedDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProphecyImpl implements _Prophecy {
  const _$ProphecyImpl({
    required this.id,
    required this.title,
    required this.text,
    this.interpretation,
    this.source,
    this.status,
    final List<String> conditions = const [],
    this.prophesiedDate,
    final Map<String, dynamic> customAttributes = const {},
  }) : _conditions = conditions,
       _customAttributes = customAttributes;

  factory _$ProphecyImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProphecyImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String text;
  @override
  final String? interpretation;
  @override
  final String? source;
  @override
  final ProphecyStatus? status;
  final List<String> _conditions;
  @override
  @JsonKey()
  List<String> get conditions {
    if (_conditions is EqualUnmodifiableListView) return _conditions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_conditions);
  }

  @override
  final DateTime? prophesiedDate;
  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'Prophecy(id: $id, title: $title, text: $text, interpretation: $interpretation, source: $source, status: $status, conditions: $conditions, prophesiedDate: $prophesiedDate, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProphecyImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.interpretation, interpretation) ||
                other.interpretation == interpretation) &&
            (identical(other.source, source) || other.source == source) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(
              other._conditions,
              _conditions,
            ) &&
            (identical(other.prophesiedDate, prophesiedDate) ||
                other.prophesiedDate == prophesiedDate) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    title,
    text,
    interpretation,
    source,
    status,
    const DeepCollectionEquality().hash(_conditions),
    prophesiedDate,
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of Prophecy
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProphecyImplCopyWith<_$ProphecyImpl> get copyWith =>
      __$$ProphecyImplCopyWithImpl<_$ProphecyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProphecyImplToJson(this);
  }
}

abstract class _Prophecy implements Prophecy {
  const factory _Prophecy({
    required final String id,
    required final String title,
    required final String text,
    final String? interpretation,
    final String? source,
    final ProphecyStatus? status,
    final List<String> conditions,
    final DateTime? prophesiedDate,
    final Map<String, dynamic> customAttributes,
  }) = _$ProphecyImpl;

  factory _Prophecy.fromJson(Map<String, dynamic> json) =
      _$ProphecyImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String get text;
  @override
  String? get interpretation;
  @override
  String? get source;
  @override
  ProphecyStatus? get status;
  @override
  List<String> get conditions;
  @override
  DateTime? get prophesiedDate;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of Prophecy
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProphecyImplCopyWith<_$ProphecyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WorldRule _$WorldRuleFromJson(Map<String, dynamic> json) {
  return _WorldRule.fromJson(json);
}

/// @nodoc
mixin _$WorldRule {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  WorldRuleType get type => throw _privateConstructorUsedError;
  WorldRuleSeverity get severity => throw _privateConstructorUsedError;
  List<String> get effects => throw _privateConstructorUsedError;
  List<String> get exceptions => throw _privateConstructorUsedError;
  Map<String, dynamic> get parameters => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this WorldRule to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WorldRule
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WorldRuleCopyWith<WorldRule> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorldRuleCopyWith<$Res> {
  factory $WorldRuleCopyWith(WorldRule value, $Res Function(WorldRule) then) =
      _$WorldRuleCopyWithImpl<$Res, WorldRule>;
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    WorldRuleType type,
    WorldRuleSeverity severity,
    List<String> effects,
    List<String> exceptions,
    Map<String, dynamic> parameters,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$WorldRuleCopyWithImpl<$Res, $Val extends WorldRule>
    implements $WorldRuleCopyWith<$Res> {
  _$WorldRuleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WorldRule
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? type = null,
    Object? severity = null,
    Object? effects = null,
    Object? exceptions = null,
    Object? parameters = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as WorldRuleType,
            severity: null == severity
                ? _value.severity
                : severity // ignore: cast_nullable_to_non_nullable
                      as WorldRuleSeverity,
            effects: null == effects
                ? _value.effects
                : effects // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            exceptions: null == exceptions
                ? _value.exceptions
                : exceptions // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            parameters: null == parameters
                ? _value.parameters
                : parameters // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$WorldRuleImplCopyWith<$Res>
    implements $WorldRuleCopyWith<$Res> {
  factory _$$WorldRuleImplCopyWith(
    _$WorldRuleImpl value,
    $Res Function(_$WorldRuleImpl) then,
  ) = __$$WorldRuleImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    WorldRuleType type,
    WorldRuleSeverity severity,
    List<String> effects,
    List<String> exceptions,
    Map<String, dynamic> parameters,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$WorldRuleImplCopyWithImpl<$Res>
    extends _$WorldRuleCopyWithImpl<$Res, _$WorldRuleImpl>
    implements _$$WorldRuleImplCopyWith<$Res> {
  __$$WorldRuleImplCopyWithImpl(
    _$WorldRuleImpl _value,
    $Res Function(_$WorldRuleImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of WorldRule
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? type = null,
    Object? severity = null,
    Object? effects = null,
    Object? exceptions = null,
    Object? parameters = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$WorldRuleImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as WorldRuleType,
        severity: null == severity
            ? _value.severity
            : severity // ignore: cast_nullable_to_non_nullable
                  as WorldRuleSeverity,
        effects: null == effects
            ? _value._effects
            : effects // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        exceptions: null == exceptions
            ? _value._exceptions
            : exceptions // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        parameters: null == parameters
            ? _value._parameters
            : parameters // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$WorldRuleImpl implements _WorldRule {
  const _$WorldRuleImpl({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.severity,
    final List<String> effects = const [],
    final List<String> exceptions = const [],
    final Map<String, dynamic> parameters = const {},
    this.createdAt,
    this.updatedAt,
  }) : _effects = effects,
       _exceptions = exceptions,
       _parameters = parameters;

  factory _$WorldRuleImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorldRuleImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final WorldRuleType type;
  @override
  final WorldRuleSeverity severity;
  final List<String> _effects;
  @override
  @JsonKey()
  List<String> get effects {
    if (_effects is EqualUnmodifiableListView) return _effects;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_effects);
  }

  final List<String> _exceptions;
  @override
  @JsonKey()
  List<String> get exceptions {
    if (_exceptions is EqualUnmodifiableListView) return _exceptions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_exceptions);
  }

  final Map<String, dynamic> _parameters;
  @override
  @JsonKey()
  Map<String, dynamic> get parameters {
    if (_parameters is EqualUnmodifiableMapView) return _parameters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_parameters);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'WorldRule(id: $id, name: $name, description: $description, type: $type, severity: $severity, effects: $effects, exceptions: $exceptions, parameters: $parameters, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorldRuleImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.severity, severity) ||
                other.severity == severity) &&
            const DeepCollectionEquality().equals(other._effects, _effects) &&
            const DeepCollectionEquality().equals(
              other._exceptions,
              _exceptions,
            ) &&
            const DeepCollectionEquality().equals(
              other._parameters,
              _parameters,
            ) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    type,
    severity,
    const DeepCollectionEquality().hash(_effects),
    const DeepCollectionEquality().hash(_exceptions),
    const DeepCollectionEquality().hash(_parameters),
    createdAt,
    updatedAt,
  );

  /// Create a copy of WorldRule
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WorldRuleImplCopyWith<_$WorldRuleImpl> get copyWith =>
      __$$WorldRuleImplCopyWithImpl<_$WorldRuleImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorldRuleImplToJson(this);
  }
}

abstract class _WorldRule implements WorldRule {
  const factory _WorldRule({
    required final String id,
    required final String name,
    required final String description,
    required final WorldRuleType type,
    required final WorldRuleSeverity severity,
    final List<String> effects,
    final List<String> exceptions,
    final Map<String, dynamic> parameters,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$WorldRuleImpl;

  factory _WorldRule.fromJson(Map<String, dynamic> json) =
      _$WorldRuleImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  WorldRuleType get type;
  @override
  WorldRuleSeverity get severity;
  @override
  List<String> get effects;
  @override
  List<String> get exceptions;
  @override
  Map<String, dynamic> get parameters;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of WorldRule
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WorldRuleImplCopyWith<_$WorldRuleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WorldEvent _$WorldEventFromJson(Map<String, dynamic> json) {
  return _WorldEvent.fromJson(json);
}

/// @nodoc
mixin _$WorldEvent {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  DateTime get date => throw _privateConstructorUsedError;
  WorldEventType? get type => throw _privateConstructorUsedError;
  WorldEventScope? get scope => throw _privateConstructorUsedError;
  List<String> get consequences => throw _privateConstructorUsedError;
  List<String> get involvedEntities => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this WorldEvent to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WorldEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WorldEventCopyWith<WorldEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorldEventCopyWith<$Res> {
  factory $WorldEventCopyWith(
    WorldEvent value,
    $Res Function(WorldEvent) then,
  ) = _$WorldEventCopyWithImpl<$Res, WorldEvent>;
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    DateTime date,
    WorldEventType? type,
    WorldEventScope? scope,
    List<String> consequences,
    List<String> involvedEntities,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$WorldEventCopyWithImpl<$Res, $Val extends WorldEvent>
    implements $WorldEventCopyWith<$Res> {
  _$WorldEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WorldEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? date = null,
    Object? type = freezed,
    Object? scope = freezed,
    Object? consequences = null,
    Object? involvedEntities = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            date: null == date
                ? _value.date
                : date // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as WorldEventType?,
            scope: freezed == scope
                ? _value.scope
                : scope // ignore: cast_nullable_to_non_nullable
                      as WorldEventScope?,
            consequences: null == consequences
                ? _value.consequences
                : consequences // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            involvedEntities: null == involvedEntities
                ? _value.involvedEntities
                : involvedEntities // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$WorldEventImplCopyWith<$Res>
    implements $WorldEventCopyWith<$Res> {
  factory _$$WorldEventImplCopyWith(
    _$WorldEventImpl value,
    $Res Function(_$WorldEventImpl) then,
  ) = __$$WorldEventImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    DateTime date,
    WorldEventType? type,
    WorldEventScope? scope,
    List<String> consequences,
    List<String> involvedEntities,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$WorldEventImplCopyWithImpl<$Res>
    extends _$WorldEventCopyWithImpl<$Res, _$WorldEventImpl>
    implements _$$WorldEventImplCopyWith<$Res> {
  __$$WorldEventImplCopyWithImpl(
    _$WorldEventImpl _value,
    $Res Function(_$WorldEventImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of WorldEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? date = null,
    Object? type = freezed,
    Object? scope = freezed,
    Object? consequences = null,
    Object? involvedEntities = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$WorldEventImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        date: null == date
            ? _value.date
            : date // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as WorldEventType?,
        scope: freezed == scope
            ? _value.scope
            : scope // ignore: cast_nullable_to_non_nullable
                  as WorldEventScope?,
        consequences: null == consequences
            ? _value._consequences
            : consequences // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        involvedEntities: null == involvedEntities
            ? _value._involvedEntities
            : involvedEntities // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$WorldEventImpl implements _WorldEvent {
  const _$WorldEventImpl({
    required this.id,
    required this.name,
    required this.description,
    required this.date,
    this.type,
    this.scope,
    final List<String> consequences = const [],
    final List<String> involvedEntities = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _consequences = consequences,
       _involvedEntities = involvedEntities,
       _customAttributes = customAttributes;

  factory _$WorldEventImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorldEventImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final DateTime date;
  @override
  final WorldEventType? type;
  @override
  final WorldEventScope? scope;
  final List<String> _consequences;
  @override
  @JsonKey()
  List<String> get consequences {
    if (_consequences is EqualUnmodifiableListView) return _consequences;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_consequences);
  }

  final List<String> _involvedEntities;
  @override
  @JsonKey()
  List<String> get involvedEntities {
    if (_involvedEntities is EqualUnmodifiableListView)
      return _involvedEntities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_involvedEntities);
  }

  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'WorldEvent(id: $id, name: $name, description: $description, date: $date, type: $type, scope: $scope, consequences: $consequences, involvedEntities: $involvedEntities, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorldEventImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.scope, scope) || other.scope == scope) &&
            const DeepCollectionEquality().equals(
              other._consequences,
              _consequences,
            ) &&
            const DeepCollectionEquality().equals(
              other._involvedEntities,
              _involvedEntities,
            ) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    date,
    type,
    scope,
    const DeepCollectionEquality().hash(_consequences),
    const DeepCollectionEquality().hash(_involvedEntities),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of WorldEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WorldEventImplCopyWith<_$WorldEventImpl> get copyWith =>
      __$$WorldEventImplCopyWithImpl<_$WorldEventImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorldEventImplToJson(this);
  }
}

abstract class _WorldEvent implements WorldEvent {
  const factory _WorldEvent({
    required final String id,
    required final String name,
    required final String description,
    required final DateTime date,
    final WorldEventType? type,
    final WorldEventScope? scope,
    final List<String> consequences,
    final List<String> involvedEntities,
    final Map<String, dynamic> customAttributes,
  }) = _$WorldEventImpl;

  factory _WorldEvent.fromJson(Map<String, dynamic> json) =
      _$WorldEventImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  DateTime get date;
  @override
  WorldEventType? get type;
  @override
  WorldEventScope? get scope;
  @override
  List<String> get consequences;
  @override
  List<String> get involvedEntities;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of WorldEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WorldEventImplCopyWith<_$WorldEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Species _$SpeciesFromJson(Map<String, dynamic> json) {
  return _Species.fromJson(json);
}

/// @nodoc
mixin _$Species {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  SpeciesType? get type => throw _privateConstructorUsedError;
  List<String> get characteristics => throw _privateConstructorUsedError;
  List<String> get abilities => throw _privateConstructorUsedError;
  List<String> get habitats => throw _privateConstructorUsedError;
  String? get lifespan => throw _privateConstructorUsedError;
  String? get intelligence => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this Species to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Species
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SpeciesCopyWith<Species> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpeciesCopyWith<$Res> {
  factory $SpeciesCopyWith(Species value, $Res Function(Species) then) =
      _$SpeciesCopyWithImpl<$Res, Species>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    SpeciesType? type,
    List<String> characteristics,
    List<String> abilities,
    List<String> habitats,
    String? lifespan,
    String? intelligence,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$SpeciesCopyWithImpl<$Res, $Val extends Species>
    implements $SpeciesCopyWith<$Res> {
  _$SpeciesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Species
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = freezed,
    Object? characteristics = null,
    Object? abilities = null,
    Object? habitats = null,
    Object? lifespan = freezed,
    Object? intelligence = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as SpeciesType?,
            characteristics: null == characteristics
                ? _value.characteristics
                : characteristics // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            abilities: null == abilities
                ? _value.abilities
                : abilities // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            habitats: null == habitats
                ? _value.habitats
                : habitats // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            lifespan: freezed == lifespan
                ? _value.lifespan
                : lifespan // ignore: cast_nullable_to_non_nullable
                      as String?,
            intelligence: freezed == intelligence
                ? _value.intelligence
                : intelligence // ignore: cast_nullable_to_non_nullable
                      as String?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$SpeciesImplCopyWith<$Res> implements $SpeciesCopyWith<$Res> {
  factory _$$SpeciesImplCopyWith(
    _$SpeciesImpl value,
    $Res Function(_$SpeciesImpl) then,
  ) = __$$SpeciesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    SpeciesType? type,
    List<String> characteristics,
    List<String> abilities,
    List<String> habitats,
    String? lifespan,
    String? intelligence,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$SpeciesImplCopyWithImpl<$Res>
    extends _$SpeciesCopyWithImpl<$Res, _$SpeciesImpl>
    implements _$$SpeciesImplCopyWith<$Res> {
  __$$SpeciesImplCopyWithImpl(
    _$SpeciesImpl _value,
    $Res Function(_$SpeciesImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Species
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = freezed,
    Object? characteristics = null,
    Object? abilities = null,
    Object? habitats = null,
    Object? lifespan = freezed,
    Object? intelligence = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _$SpeciesImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as SpeciesType?,
        characteristics: null == characteristics
            ? _value._characteristics
            : characteristics // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        abilities: null == abilities
            ? _value._abilities
            : abilities // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        habitats: null == habitats
            ? _value._habitats
            : habitats // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        lifespan: freezed == lifespan
            ? _value.lifespan
            : lifespan // ignore: cast_nullable_to_non_nullable
                  as String?,
        intelligence: freezed == intelligence
            ? _value.intelligence
            : intelligence // ignore: cast_nullable_to_non_nullable
                  as String?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SpeciesImpl implements _Species {
  const _$SpeciesImpl({
    required this.id,
    required this.name,
    this.description,
    this.type,
    final List<String> characteristics = const [],
    final List<String> abilities = const [],
    final List<String> habitats = const [],
    this.lifespan,
    this.intelligence,
    final Map<String, dynamic> customAttributes = const {},
  }) : _characteristics = characteristics,
       _abilities = abilities,
       _habitats = habitats,
       _customAttributes = customAttributes;

  factory _$SpeciesImpl.fromJson(Map<String, dynamic> json) =>
      _$$SpeciesImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final SpeciesType? type;
  final List<String> _characteristics;
  @override
  @JsonKey()
  List<String> get characteristics {
    if (_characteristics is EqualUnmodifiableListView) return _characteristics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_characteristics);
  }

  final List<String> _abilities;
  @override
  @JsonKey()
  List<String> get abilities {
    if (_abilities is EqualUnmodifiableListView) return _abilities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_abilities);
  }

  final List<String> _habitats;
  @override
  @JsonKey()
  List<String> get habitats {
    if (_habitats is EqualUnmodifiableListView) return _habitats;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_habitats);
  }

  @override
  final String? lifespan;
  @override
  final String? intelligence;
  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'Species(id: $id, name: $name, description: $description, type: $type, characteristics: $characteristics, abilities: $abilities, habitats: $habitats, lifespan: $lifespan, intelligence: $intelligence, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SpeciesImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(
              other._characteristics,
              _characteristics,
            ) &&
            const DeepCollectionEquality().equals(
              other._abilities,
              _abilities,
            ) &&
            const DeepCollectionEquality().equals(other._habitats, _habitats) &&
            (identical(other.lifespan, lifespan) ||
                other.lifespan == lifespan) &&
            (identical(other.intelligence, intelligence) ||
                other.intelligence == intelligence) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    type,
    const DeepCollectionEquality().hash(_characteristics),
    const DeepCollectionEquality().hash(_abilities),
    const DeepCollectionEquality().hash(_habitats),
    lifespan,
    intelligence,
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of Species
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SpeciesImplCopyWith<_$SpeciesImpl> get copyWith =>
      __$$SpeciesImplCopyWithImpl<_$SpeciesImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SpeciesImplToJson(this);
  }
}

abstract class _Species implements Species {
  const factory _Species({
    required final String id,
    required final String name,
    final String? description,
    final SpeciesType? type,
    final List<String> characteristics,
    final List<String> abilities,
    final List<String> habitats,
    final String? lifespan,
    final String? intelligence,
    final Map<String, dynamic> customAttributes,
  }) = _$SpeciesImpl;

  factory _Species.fromJson(Map<String, dynamic> json) = _$SpeciesImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  SpeciesType? get type;
  @override
  List<String> get characteristics;
  @override
  List<String> get abilities;
  @override
  List<String> get habitats;
  @override
  String? get lifespan;
  @override
  String? get intelligence;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of Species
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SpeciesImplCopyWith<_$SpeciesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Language _$LanguageFromJson(Map<String, dynamic> json) {
  return _Language.fromJson(json);
}

/// @nodoc
mixin _$Language {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  LanguageType? get type => throw _privateConstructorUsedError;
  List<String> get speakers => throw _privateConstructorUsedError;
  String? get writingSystem => throw _privateConstructorUsedError;
  List<String> get dialects => throw _privateConstructorUsedError;
  String? get origin => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this Language to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Language
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LanguageCopyWith<Language> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LanguageCopyWith<$Res> {
  factory $LanguageCopyWith(Language value, $Res Function(Language) then) =
      _$LanguageCopyWithImpl<$Res, Language>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    LanguageType? type,
    List<String> speakers,
    String? writingSystem,
    List<String> dialects,
    String? origin,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$LanguageCopyWithImpl<$Res, $Val extends Language>
    implements $LanguageCopyWith<$Res> {
  _$LanguageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Language
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = freezed,
    Object? speakers = null,
    Object? writingSystem = freezed,
    Object? dialects = null,
    Object? origin = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as LanguageType?,
            speakers: null == speakers
                ? _value.speakers
                : speakers // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            writingSystem: freezed == writingSystem
                ? _value.writingSystem
                : writingSystem // ignore: cast_nullable_to_non_nullable
                      as String?,
            dialects: null == dialects
                ? _value.dialects
                : dialects // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            origin: freezed == origin
                ? _value.origin
                : origin // ignore: cast_nullable_to_non_nullable
                      as String?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$LanguageImplCopyWith<$Res>
    implements $LanguageCopyWith<$Res> {
  factory _$$LanguageImplCopyWith(
    _$LanguageImpl value,
    $Res Function(_$LanguageImpl) then,
  ) = __$$LanguageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    LanguageType? type,
    List<String> speakers,
    String? writingSystem,
    List<String> dialects,
    String? origin,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$LanguageImplCopyWithImpl<$Res>
    extends _$LanguageCopyWithImpl<$Res, _$LanguageImpl>
    implements _$$LanguageImplCopyWith<$Res> {
  __$$LanguageImplCopyWithImpl(
    _$LanguageImpl _value,
    $Res Function(_$LanguageImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Language
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = freezed,
    Object? speakers = null,
    Object? writingSystem = freezed,
    Object? dialects = null,
    Object? origin = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _$LanguageImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as LanguageType?,
        speakers: null == speakers
            ? _value._speakers
            : speakers // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        writingSystem: freezed == writingSystem
            ? _value.writingSystem
            : writingSystem // ignore: cast_nullable_to_non_nullable
                  as String?,
        dialects: null == dialects
            ? _value._dialects
            : dialects // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        origin: freezed == origin
            ? _value.origin
            : origin // ignore: cast_nullable_to_non_nullable
                  as String?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$LanguageImpl implements _Language {
  const _$LanguageImpl({
    required this.id,
    required this.name,
    this.description,
    this.type,
    final List<String> speakers = const [],
    this.writingSystem,
    final List<String> dialects = const [],
    this.origin,
    final Map<String, dynamic> customAttributes = const {},
  }) : _speakers = speakers,
       _dialects = dialects,
       _customAttributes = customAttributes;

  factory _$LanguageImpl.fromJson(Map<String, dynamic> json) =>
      _$$LanguageImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final LanguageType? type;
  final List<String> _speakers;
  @override
  @JsonKey()
  List<String> get speakers {
    if (_speakers is EqualUnmodifiableListView) return _speakers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_speakers);
  }

  @override
  final String? writingSystem;
  final List<String> _dialects;
  @override
  @JsonKey()
  List<String> get dialects {
    if (_dialects is EqualUnmodifiableListView) return _dialects;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dialects);
  }

  @override
  final String? origin;
  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'Language(id: $id, name: $name, description: $description, type: $type, speakers: $speakers, writingSystem: $writingSystem, dialects: $dialects, origin: $origin, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LanguageImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other._speakers, _speakers) &&
            (identical(other.writingSystem, writingSystem) ||
                other.writingSystem == writingSystem) &&
            const DeepCollectionEquality().equals(other._dialects, _dialects) &&
            (identical(other.origin, origin) || other.origin == origin) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    type,
    const DeepCollectionEquality().hash(_speakers),
    writingSystem,
    const DeepCollectionEquality().hash(_dialects),
    origin,
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of Language
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LanguageImplCopyWith<_$LanguageImpl> get copyWith =>
      __$$LanguageImplCopyWithImpl<_$LanguageImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LanguageImplToJson(this);
  }
}

abstract class _Language implements Language {
  const factory _Language({
    required final String id,
    required final String name,
    final String? description,
    final LanguageType? type,
    final List<String> speakers,
    final String? writingSystem,
    final List<String> dialects,
    final String? origin,
    final Map<String, dynamic> customAttributes,
  }) = _$LanguageImpl;

  factory _Language.fromJson(Map<String, dynamic> json) =
      _$LanguageImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  LanguageType? get type;
  @override
  List<String> get speakers;
  @override
  String? get writingSystem;
  @override
  List<String> get dialects;
  @override
  String? get origin;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of Language
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LanguageImplCopyWith<_$LanguageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Calendar _$CalendarFromJson(Map<String, dynamic> json) {
  return _Calendar.fromJson(json);
}

/// @nodoc
mixin _$Calendar {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  int? get daysPerYear => throw _privateConstructorUsedError;
  int? get monthsPerYear => throw _privateConstructorUsedError;
  int? get daysPerMonth => throw _privateConstructorUsedError;
  List<String> get monthNames => throw _privateConstructorUsedError;
  List<String> get dayNames => throw _privateConstructorUsedError;
  List<Holiday> get holidays => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this Calendar to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Calendar
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CalendarCopyWith<Calendar> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CalendarCopyWith<$Res> {
  factory $CalendarCopyWith(Calendar value, $Res Function(Calendar) then) =
      _$CalendarCopyWithImpl<$Res, Calendar>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    int? daysPerYear,
    int? monthsPerYear,
    int? daysPerMonth,
    List<String> monthNames,
    List<String> dayNames,
    List<Holiday> holidays,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$CalendarCopyWithImpl<$Res, $Val extends Calendar>
    implements $CalendarCopyWith<$Res> {
  _$CalendarCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Calendar
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? daysPerYear = freezed,
    Object? monthsPerYear = freezed,
    Object? daysPerMonth = freezed,
    Object? monthNames = null,
    Object? dayNames = null,
    Object? holidays = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            daysPerYear: freezed == daysPerYear
                ? _value.daysPerYear
                : daysPerYear // ignore: cast_nullable_to_non_nullable
                      as int?,
            monthsPerYear: freezed == monthsPerYear
                ? _value.monthsPerYear
                : monthsPerYear // ignore: cast_nullable_to_non_nullable
                      as int?,
            daysPerMonth: freezed == daysPerMonth
                ? _value.daysPerMonth
                : daysPerMonth // ignore: cast_nullable_to_non_nullable
                      as int?,
            monthNames: null == monthNames
                ? _value.monthNames
                : monthNames // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            dayNames: null == dayNames
                ? _value.dayNames
                : dayNames // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            holidays: null == holidays
                ? _value.holidays
                : holidays // ignore: cast_nullable_to_non_nullable
                      as List<Holiday>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CalendarImplCopyWith<$Res>
    implements $CalendarCopyWith<$Res> {
  factory _$$CalendarImplCopyWith(
    _$CalendarImpl value,
    $Res Function(_$CalendarImpl) then,
  ) = __$$CalendarImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    int? daysPerYear,
    int? monthsPerYear,
    int? daysPerMonth,
    List<String> monthNames,
    List<String> dayNames,
    List<Holiday> holidays,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$CalendarImplCopyWithImpl<$Res>
    extends _$CalendarCopyWithImpl<$Res, _$CalendarImpl>
    implements _$$CalendarImplCopyWith<$Res> {
  __$$CalendarImplCopyWithImpl(
    _$CalendarImpl _value,
    $Res Function(_$CalendarImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Calendar
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? daysPerYear = freezed,
    Object? monthsPerYear = freezed,
    Object? daysPerMonth = freezed,
    Object? monthNames = null,
    Object? dayNames = null,
    Object? holidays = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$CalendarImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        daysPerYear: freezed == daysPerYear
            ? _value.daysPerYear
            : daysPerYear // ignore: cast_nullable_to_non_nullable
                  as int?,
        monthsPerYear: freezed == monthsPerYear
            ? _value.monthsPerYear
            : monthsPerYear // ignore: cast_nullable_to_non_nullable
                  as int?,
        daysPerMonth: freezed == daysPerMonth
            ? _value.daysPerMonth
            : daysPerMonth // ignore: cast_nullable_to_non_nullable
                  as int?,
        monthNames: null == monthNames
            ? _value._monthNames
            : monthNames // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        dayNames: null == dayNames
            ? _value._dayNames
            : dayNames // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        holidays: null == holidays
            ? _value._holidays
            : holidays // ignore: cast_nullable_to_non_nullable
                  as List<Holiday>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CalendarImpl implements _Calendar {
  const _$CalendarImpl({
    required this.id,
    required this.name,
    this.description,
    this.daysPerYear,
    this.monthsPerYear,
    this.daysPerMonth,
    final List<String> monthNames = const [],
    final List<String> dayNames = const [],
    final List<Holiday> holidays = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _monthNames = monthNames,
       _dayNames = dayNames,
       _holidays = holidays,
       _customAttributes = customAttributes;

  factory _$CalendarImpl.fromJson(Map<String, dynamic> json) =>
      _$$CalendarImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final int? daysPerYear;
  @override
  final int? monthsPerYear;
  @override
  final int? daysPerMonth;
  final List<String> _monthNames;
  @override
  @JsonKey()
  List<String> get monthNames {
    if (_monthNames is EqualUnmodifiableListView) return _monthNames;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_monthNames);
  }

  final List<String> _dayNames;
  @override
  @JsonKey()
  List<String> get dayNames {
    if (_dayNames is EqualUnmodifiableListView) return _dayNames;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dayNames);
  }

  final List<Holiday> _holidays;
  @override
  @JsonKey()
  List<Holiday> get holidays {
    if (_holidays is EqualUnmodifiableListView) return _holidays;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_holidays);
  }

  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'Calendar(id: $id, name: $name, description: $description, daysPerYear: $daysPerYear, monthsPerYear: $monthsPerYear, daysPerMonth: $daysPerMonth, monthNames: $monthNames, dayNames: $dayNames, holidays: $holidays, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CalendarImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.daysPerYear, daysPerYear) ||
                other.daysPerYear == daysPerYear) &&
            (identical(other.monthsPerYear, monthsPerYear) ||
                other.monthsPerYear == monthsPerYear) &&
            (identical(other.daysPerMonth, daysPerMonth) ||
                other.daysPerMonth == daysPerMonth) &&
            const DeepCollectionEquality().equals(
              other._monthNames,
              _monthNames,
            ) &&
            const DeepCollectionEquality().equals(other._dayNames, _dayNames) &&
            const DeepCollectionEquality().equals(other._holidays, _holidays) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    daysPerYear,
    monthsPerYear,
    daysPerMonth,
    const DeepCollectionEquality().hash(_monthNames),
    const DeepCollectionEquality().hash(_dayNames),
    const DeepCollectionEquality().hash(_holidays),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of Calendar
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CalendarImplCopyWith<_$CalendarImpl> get copyWith =>
      __$$CalendarImplCopyWithImpl<_$CalendarImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CalendarImplToJson(this);
  }
}

abstract class _Calendar implements Calendar {
  const factory _Calendar({
    required final String id,
    required final String name,
    final String? description,
    final int? daysPerYear,
    final int? monthsPerYear,
    final int? daysPerMonth,
    final List<String> monthNames,
    final List<String> dayNames,
    final List<Holiday> holidays,
    final Map<String, dynamic> customAttributes,
  }) = _$CalendarImpl;

  factory _Calendar.fromJson(Map<String, dynamic> json) =
      _$CalendarImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  int? get daysPerYear;
  @override
  int? get monthsPerYear;
  @override
  int? get daysPerMonth;
  @override
  List<String> get monthNames;
  @override
  List<String> get dayNames;
  @override
  List<Holiday> get holidays;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of Calendar
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CalendarImplCopyWith<_$CalendarImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Holiday _$HolidayFromJson(Map<String, dynamic> json) {
  return _Holiday.fromJson(json);
}

/// @nodoc
mixin _$Holiday {
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get date => throw _privateConstructorUsedError;
  HolidayType? get type => throw _privateConstructorUsedError;
  List<String> get traditions => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this Holiday to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Holiday
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HolidayCopyWith<Holiday> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HolidayCopyWith<$Res> {
  factory $HolidayCopyWith(Holiday value, $Res Function(Holiday) then) =
      _$HolidayCopyWithImpl<$Res, Holiday>;
  @useResult
  $Res call({
    String name,
    String? description,
    String? date,
    HolidayType? type,
    List<String> traditions,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$HolidayCopyWithImpl<$Res, $Val extends Holiday>
    implements $HolidayCopyWith<$Res> {
  _$HolidayCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Holiday
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? date = freezed,
    Object? type = freezed,
    Object? traditions = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            date: freezed == date
                ? _value.date
                : date // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as HolidayType?,
            traditions: null == traditions
                ? _value.traditions
                : traditions // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$HolidayImplCopyWith<$Res> implements $HolidayCopyWith<$Res> {
  factory _$$HolidayImplCopyWith(
    _$HolidayImpl value,
    $Res Function(_$HolidayImpl) then,
  ) = __$$HolidayImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    String? description,
    String? date,
    HolidayType? type,
    List<String> traditions,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$HolidayImplCopyWithImpl<$Res>
    extends _$HolidayCopyWithImpl<$Res, _$HolidayImpl>
    implements _$$HolidayImplCopyWith<$Res> {
  __$$HolidayImplCopyWithImpl(
    _$HolidayImpl _value,
    $Res Function(_$HolidayImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Holiday
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? date = freezed,
    Object? type = freezed,
    Object? traditions = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$HolidayImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        date: freezed == date
            ? _value.date
            : date // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as HolidayType?,
        traditions: null == traditions
            ? _value._traditions
            : traditions // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$HolidayImpl implements _Holiday {
  const _$HolidayImpl({
    required this.name,
    this.description,
    this.date,
    this.type,
    final List<String> traditions = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _traditions = traditions,
       _customAttributes = customAttributes;

  factory _$HolidayImpl.fromJson(Map<String, dynamic> json) =>
      _$$HolidayImplFromJson(json);

  @override
  final String name;
  @override
  final String? description;
  @override
  final String? date;
  @override
  final HolidayType? type;
  final List<String> _traditions;
  @override
  @JsonKey()
  List<String> get traditions {
    if (_traditions is EqualUnmodifiableListView) return _traditions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_traditions);
  }

  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'Holiday(name: $name, description: $description, date: $date, type: $type, traditions: $traditions, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HolidayImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(
              other._traditions,
              _traditions,
            ) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    description,
    date,
    type,
    const DeepCollectionEquality().hash(_traditions),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of Holiday
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HolidayImplCopyWith<_$HolidayImpl> get copyWith =>
      __$$HolidayImplCopyWithImpl<_$HolidayImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HolidayImplToJson(this);
  }
}

abstract class _Holiday implements Holiday {
  const factory _Holiday({
    required final String name,
    final String? description,
    final String? date,
    final HolidayType? type,
    final List<String> traditions,
    final Map<String, dynamic> customAttributes,
  }) = _$HolidayImpl;

  factory _Holiday.fromJson(Map<String, dynamic> json) = _$HolidayImpl.fromJson;

  @override
  String get name;
  @override
  String? get description;
  @override
  String? get date;
  @override
  HolidayType? get type;
  @override
  List<String> get traditions;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of Holiday
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HolidayImplCopyWith<_$HolidayImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Currency _$CurrencyFromJson(Map<String, dynamic> json) {
  return _Currency.fromJson(json);
}

/// @nodoc
mixin _$Currency {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get symbol => throw _privateConstructorUsedError;
  List<Denomination> get denominations => throw _privateConstructorUsedError;
  List<String> get acceptedRegions => throw _privateConstructorUsedError;
  String? get backingSystem => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this Currency to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Currency
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CurrencyCopyWith<Currency> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CurrencyCopyWith<$Res> {
  factory $CurrencyCopyWith(Currency value, $Res Function(Currency) then) =
      _$CurrencyCopyWithImpl<$Res, Currency>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    String? symbol,
    List<Denomination> denominations,
    List<String> acceptedRegions,
    String? backingSystem,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$CurrencyCopyWithImpl<$Res, $Val extends Currency>
    implements $CurrencyCopyWith<$Res> {
  _$CurrencyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Currency
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? symbol = freezed,
    Object? denominations = null,
    Object? acceptedRegions = null,
    Object? backingSystem = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            symbol: freezed == symbol
                ? _value.symbol
                : symbol // ignore: cast_nullable_to_non_nullable
                      as String?,
            denominations: null == denominations
                ? _value.denominations
                : denominations // ignore: cast_nullable_to_non_nullable
                      as List<Denomination>,
            acceptedRegions: null == acceptedRegions
                ? _value.acceptedRegions
                : acceptedRegions // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            backingSystem: freezed == backingSystem
                ? _value.backingSystem
                : backingSystem // ignore: cast_nullable_to_non_nullable
                      as String?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CurrencyImplCopyWith<$Res>
    implements $CurrencyCopyWith<$Res> {
  factory _$$CurrencyImplCopyWith(
    _$CurrencyImpl value,
    $Res Function(_$CurrencyImpl) then,
  ) = __$$CurrencyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    String? symbol,
    List<Denomination> denominations,
    List<String> acceptedRegions,
    String? backingSystem,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$CurrencyImplCopyWithImpl<$Res>
    extends _$CurrencyCopyWithImpl<$Res, _$CurrencyImpl>
    implements _$$CurrencyImplCopyWith<$Res> {
  __$$CurrencyImplCopyWithImpl(
    _$CurrencyImpl _value,
    $Res Function(_$CurrencyImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Currency
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? symbol = freezed,
    Object? denominations = null,
    Object? acceptedRegions = null,
    Object? backingSystem = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _$CurrencyImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        symbol: freezed == symbol
            ? _value.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String?,
        denominations: null == denominations
            ? _value._denominations
            : denominations // ignore: cast_nullable_to_non_nullable
                  as List<Denomination>,
        acceptedRegions: null == acceptedRegions
            ? _value._acceptedRegions
            : acceptedRegions // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        backingSystem: freezed == backingSystem
            ? _value.backingSystem
            : backingSystem // ignore: cast_nullable_to_non_nullable
                  as String?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CurrencyImpl implements _Currency {
  const _$CurrencyImpl({
    required this.id,
    required this.name,
    this.description,
    this.symbol,
    final List<Denomination> denominations = const [],
    final List<String> acceptedRegions = const [],
    this.backingSystem,
    final Map<String, dynamic> customAttributes = const {},
  }) : _denominations = denominations,
       _acceptedRegions = acceptedRegions,
       _customAttributes = customAttributes;

  factory _$CurrencyImpl.fromJson(Map<String, dynamic> json) =>
      _$$CurrencyImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final String? symbol;
  final List<Denomination> _denominations;
  @override
  @JsonKey()
  List<Denomination> get denominations {
    if (_denominations is EqualUnmodifiableListView) return _denominations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_denominations);
  }

  final List<String> _acceptedRegions;
  @override
  @JsonKey()
  List<String> get acceptedRegions {
    if (_acceptedRegions is EqualUnmodifiableListView) return _acceptedRegions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_acceptedRegions);
  }

  @override
  final String? backingSystem;
  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'Currency(id: $id, name: $name, description: $description, symbol: $symbol, denominations: $denominations, acceptedRegions: $acceptedRegions, backingSystem: $backingSystem, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CurrencyImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            const DeepCollectionEquality().equals(
              other._denominations,
              _denominations,
            ) &&
            const DeepCollectionEquality().equals(
              other._acceptedRegions,
              _acceptedRegions,
            ) &&
            (identical(other.backingSystem, backingSystem) ||
                other.backingSystem == backingSystem) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    symbol,
    const DeepCollectionEquality().hash(_denominations),
    const DeepCollectionEquality().hash(_acceptedRegions),
    backingSystem,
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of Currency
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CurrencyImplCopyWith<_$CurrencyImpl> get copyWith =>
      __$$CurrencyImplCopyWithImpl<_$CurrencyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CurrencyImplToJson(this);
  }
}

abstract class _Currency implements Currency {
  const factory _Currency({
    required final String id,
    required final String name,
    final String? description,
    final String? symbol,
    final List<Denomination> denominations,
    final List<String> acceptedRegions,
    final String? backingSystem,
    final Map<String, dynamic> customAttributes,
  }) = _$CurrencyImpl;

  factory _Currency.fromJson(Map<String, dynamic> json) =
      _$CurrencyImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  String? get symbol;
  @override
  List<Denomination> get denominations;
  @override
  List<String> get acceptedRegions;
  @override
  String? get backingSystem;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of Currency
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CurrencyImplCopyWith<_$CurrencyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Denomination _$DenominationFromJson(Map<String, dynamic> json) {
  return _Denomination.fromJson(json);
}

/// @nodoc
mixin _$Denomination {
  String get name => throw _privateConstructorUsedError;
  double get value => throw _privateConstructorUsedError;
  String? get material => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this Denomination to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Denomination
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DenominationCopyWith<Denomination> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DenominationCopyWith<$Res> {
  factory $DenominationCopyWith(
    Denomination value,
    $Res Function(Denomination) then,
  ) = _$DenominationCopyWithImpl<$Res, Denomination>;
  @useResult
  $Res call({
    String name,
    double value,
    String? material,
    String? description,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$DenominationCopyWithImpl<$Res, $Val extends Denomination>
    implements $DenominationCopyWith<$Res> {
  _$DenominationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Denomination
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? value = null,
    Object? material = freezed,
    Object? description = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            value: null == value
                ? _value.value
                : value // ignore: cast_nullable_to_non_nullable
                      as double,
            material: freezed == material
                ? _value.material
                : material // ignore: cast_nullable_to_non_nullable
                      as String?,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DenominationImplCopyWith<$Res>
    implements $DenominationCopyWith<$Res> {
  factory _$$DenominationImplCopyWith(
    _$DenominationImpl value,
    $Res Function(_$DenominationImpl) then,
  ) = __$$DenominationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    double value,
    String? material,
    String? description,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$DenominationImplCopyWithImpl<$Res>
    extends _$DenominationCopyWithImpl<$Res, _$DenominationImpl>
    implements _$$DenominationImplCopyWith<$Res> {
  __$$DenominationImplCopyWithImpl(
    _$DenominationImpl _value,
    $Res Function(_$DenominationImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Denomination
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? value = null,
    Object? material = freezed,
    Object? description = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _$DenominationImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        value: null == value
            ? _value.value
            : value // ignore: cast_nullable_to_non_nullable
                  as double,
        material: freezed == material
            ? _value.material
            : material // ignore: cast_nullable_to_non_nullable
                  as String?,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$DenominationImpl implements _Denomination {
  const _$DenominationImpl({
    required this.name,
    required this.value,
    this.material,
    this.description,
    final Map<String, dynamic> customAttributes = const {},
  }) : _customAttributes = customAttributes;

  factory _$DenominationImpl.fromJson(Map<String, dynamic> json) =>
      _$$DenominationImplFromJson(json);

  @override
  final String name;
  @override
  final double value;
  @override
  final String? material;
  @override
  final String? description;
  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'Denomination(name: $name, value: $value, material: $material, description: $description, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DenominationImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.material, material) ||
                other.material == material) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    value,
    material,
    description,
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of Denomination
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DenominationImplCopyWith<_$DenominationImpl> get copyWith =>
      __$$DenominationImplCopyWithImpl<_$DenominationImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DenominationImplToJson(this);
  }
}

abstract class _Denomination implements Denomination {
  const factory _Denomination({
    required final String name,
    required final double value,
    final String? material,
    final String? description,
    final Map<String, dynamic> customAttributes,
  }) = _$DenominationImpl;

  factory _Denomination.fromJson(Map<String, dynamic> json) =
      _$DenominationImpl.fromJson;

  @override
  String get name;
  @override
  double get value;
  @override
  String? get material;
  @override
  String? get description;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of Denomination
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DenominationImplCopyWith<_$DenominationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
