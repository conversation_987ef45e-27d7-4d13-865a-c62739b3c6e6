import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

import '../../domain/usecases/track_progress.dart';

/// 进度仪表板组件
/// 显示项目的进度统计和可视化图表
class ProgressDashboard extends ConsumerStatefulWidget {
  final String projectId;

  const ProgressDashboard({
    super.key,
    required this.projectId,
  });

  @override
  ConsumerState<ProgressDashboard> createState() => _ProgressDashboardState();
}

class _ProgressDashboardState extends ConsumerState<ProgressDashboard> {
  Duration _selectedPeriod = const Duration(days: 30);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 头部控制栏
        _buildHeader(),
        
        const SizedBox(height: 16),
        
        // 主要进度指标
        _buildMainMetrics(),
        
        const SizedBox(height: 24),
        
        // 进度图表和详细统计
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 左侧：进度图表
              Expanded(
                flex: 2,
                child: _buildProgressCharts(),
              ),
              
              const SizedBox(width: 16),
              
              // 右侧：统计信息和里程碑
              Expanded(
                flex: 1,
                child: _buildSidePanel(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Text(
          '进度跟踪',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        const Spacer(),
        fluent.ComboBox<Duration>(
          value: _selectedPeriod,
          items: const [
            fluent.ComboBoxItem(
              value: Duration(days: 7),
              child: Text('最近7天'),
            ),
            fluent.ComboBoxItem(
              value: Duration(days: 30),
              child: Text('最近30天'),
            ),
            fluent.ComboBoxItem(
              value: Duration(days: 90),
              child: Text('最近90天'),
            ),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() => _selectedPeriod = value);
            }
          },
        ),
      ],
    );
  }

  Widget _buildMainMetrics() {
    return FutureBuilder<ProjectProgressOverview>(
      future: ref.read(trackProgressUseCaseProvider).getProjectProgressOverview(widget.projectId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: fluent.ProgressRing());
        }

        if (snapshot.hasError) {
          return fluent.InfoBar(
            title: const Text('加载失败'),
            content: Text('无法加载进度数据: ${snapshot.error}'),
            severity: fluent.InfoBarSeverity.error,
          );
        }

        final overview = snapshot.data!;
        return Row(
          children: [
            Expanded(
              child: _buildMetricCard(
                '字数进度',
                '${overview.project.statistics.totalWordCount}',
                '/ ${overview.project.config.targetWordCount}',
                overview.wordCountProgress,
                fluent.FluentIcons.edit,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildMetricCard(
                '每日目标',
                '${overview.project.statistics.averageDailyWords}',
                '/ ${overview.project.config.dailyWritingGoal}',
                overview.dailyProgress,
                fluent.FluentIcons.calendar_day,
                Colors.green,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildMetricCard(
                '写作天数',
                '${overview.project.statistics.writingDays}',
                '天',
                null,
                fluent.FluentIcons.date_time,
                Colors.orange,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildMetricCard(
                '连续天数',
                '${overview.project.statistics.consecutiveWritingDays}',
                '天',
                null,
                fluent.FluentIcons.streaming,
                Colors.purple,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    String suffix,
    double? progress,
    IconData icon,
    Color color,
  ) {
    return fluent.Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                fluent.Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  suffix,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            if (progress != null) ...[
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: progress / 100,
                backgroundColor: color.withValues(alpha: 0.2),
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
              const SizedBox(height: 4),
              Text(
                '${progress.toStringAsFixed(1)}%',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProgressCharts() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 进度趋势图
        Expanded(
          flex: 2,
          child: fluent.Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '写作进度趋势',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: _buildProgressChart(),
                  ),
                ],
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // 每日写作统计
        Expanded(
          flex: 1,
          child: fluent.Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '每日写作统计',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: _buildDailyStatsChart(),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSidePanel() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 写作统计
        Expanded(
          flex: 1,
          child: _buildWritingStats(),
        ),
        
        const SizedBox(height: 16),
        
        // 里程碑
        Expanded(
          flex: 1,
          child: _buildMilestones(),
        ),
        
        const SizedBox(height: 16),
        
        // 目标达成情况
        Expanded(
          flex: 1,
          child: _buildGoalAchievement(),
        ),
      ],
    );
  }

  Widget _buildProgressChart() {
    return FutureBuilder<List<ProgressDataPoint>>(
      future: ref.read(trackProgressUseCaseProvider).getProgressHistory(
        widget.projectId,
        period: _selectedPeriod,
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: fluent.ProgressRing());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text('加载图表失败: ${snapshot.error}'),
          );
        }

        final dataPoints = snapshot.data ?? [];
        
        // 简化的图表实现（实际项目中应使用专业图表库如fl_chart）
        return Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: CustomPaint(
            painter: ProgressChartPainter(dataPoints),
            child: const SizedBox.expand(),
          ),
        );
      },
    );
  }

  Widget _buildDailyStatsChart() {
    return FutureBuilder<List<ProgressDataPoint>>(
      future: ref.read(trackProgressUseCaseProvider).getProgressHistory(
        widget.projectId,
        period: _selectedPeriod,
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: fluent.ProgressRing());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text('加载数据失败: ${snapshot.error}'),
          );
        }

        final dataPoints = snapshot.data ?? [];
        
        return Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: CustomPaint(
            painter: DailyStatsChartPainter(dataPoints),
            child: const SizedBox.expand(),
          ),
        );
      },
    );
  }

  Widget _buildWritingStats() {
    return fluent.Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '写作统计',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: FutureBuilder<WritingStatistics>(
                future: ref.read(trackProgressUseCaseProvider).getWritingStatistics(widget.projectId),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: fluent.ProgressRing());
                  }

                  if (snapshot.hasError) {
                    return Text('加载失败: ${snapshot.error}');
                  }

                  final stats = snapshot.data!;
                  return Column(
                    children: [
                      _buildStatRow('总字符数', _formatNumber(stats.totalCharacters)),
                      _buildStatRow('写作效率', '${stats.wordsPerMinute.toStringAsFixed(1)} 字/分钟'),
                      _buildStatRow('生产力指数', '${stats.productivity.toStringAsFixed(1)}%'),
                      _buildStatRow('一致性指数', '${stats.consistency.toStringAsFixed(1)}%'),
                      _buildStatRow('写作时长', '${stats.writingTimeHours.toStringAsFixed(1)} 小时'),
                      _buildStatRow('最高日字数', _formatNumber(stats.maxDailyWords)),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMilestones() {
    return fluent.Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '里程碑',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: FutureBuilder<ProjectProgressOverview>(
                future: ref.read(trackProgressUseCaseProvider).getProjectProgressOverview(widget.projectId),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: fluent.ProgressRing());
                  }

                  if (snapshot.hasError) {
                    return Text('加载失败: ${snapshot.error}');
                  }

                  final milestones = snapshot.data!.milestones;
                  return ListView.builder(
                    itemCount: milestones.length,
                    itemBuilder: (context, index) {
                      final milestone = milestones[index];
                      return _buildMilestoneItem(milestone);
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalAchievement() {
    return fluent.Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '目标达成',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: FutureBuilder<GoalAchievement>(
                future: ref.read(trackProgressUseCaseProvider).getGoalAchievement(widget.projectId),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: fluent.ProgressRing());
                  }

                  if (snapshot.hasError) {
                    return Text('加载失败: ${snapshot.error}');
                  }

                  final achievement = snapshot.data!;
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildGoalProgress('字数目标', achievement.wordGoalAchievement),
                      _buildGoalProgress('每日目标', achievement.dailyGoalAchievement),
                      if (achievement.timeGoalAchievement != null)
                        _buildGoalProgress('时间目标', achievement.timeGoalAchievement!),
                      
                      const SizedBox(height: 16),
                      
                      Row(
                        children: [
                          fluent.Icon(
                            achievement.isOnTrack 
                                ? fluent.FluentIcons.completed_solid 
                                : fluent.FluentIcons.warning,
                            color: achievement.isOnTrack ? Colors.green : Colors.orange,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            achievement.isOnTrack ? '进度正常' : '需要关注',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: achievement.isOnTrack ? Colors.green : Colors.orange,
                            ),
                          ),
                        ],
                      ),
                      
                      if (achievement.recommendations.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        const Text(
                          '改进建议:',
                          style: TextStyle(fontWeight: FontWeight.w500),
                        ),
                        const SizedBox(height: 8),
                        ...achievement.recommendations.map((rec) => Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Text(
                            '• $rec',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        )),
                      ],
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMilestoneItem(Milestone milestone) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          fluent.Icon(
            milestone.isCompleted 
                ? fluent.FluentIcons.completed_solid 
                : fluent.FluentIcons.circle_ring,
            color: milestone.isCompleted ? Colors.green : Colors.grey,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  milestone.title,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    decoration: milestone.isCompleted ? TextDecoration.lineThrough : null,
                  ),
                ),
                const SizedBox(height: 2),
                LinearProgressIndicator(
                  value: milestone.completionPercentage / 100,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    milestone.isCompleted ? Colors.green : Colors.blue,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGoalProgress(String label, double progress) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '${progress.toStringAsFixed(1)}%',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: (progress / 100).clamp(0.0, 1.0),
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              progress >= 100 ? Colors.green : 
              progress >= 75 ? Colors.blue :
              progress >= 50 ? Colors.orange : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  String _formatNumber(int number) {
    if (number >= 10000) {
      return '${(number / 10000).toStringAsFixed(1)}万';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}k';
    } else {
      return number.toString();
    }
  }
}

/// 进度图表绘制器
class ProgressChartPainter extends CustomPainter {
  final List<ProgressDataPoint> dataPoints;

  ProgressChartPainter(this.dataPoints);

  @override
  void paint(Canvas canvas, Size size) {
    if (dataPoints.isEmpty) return;

    final paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final path = Path();
    final maxWords = dataPoints.map((p) => p.wordCount).reduce((a, b) => a > b ? a : b);
    
    for (int i = 0; i < dataPoints.length; i++) {
      final x = (i / (dataPoints.length - 1)) * size.width;
      final y = size.height - (dataPoints[i].wordCount / maxWords) * size.height;
      
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// 每日统计图表绘制器
class DailyStatsChartPainter extends CustomPainter {
  final List<ProgressDataPoint> dataPoints;

  DailyStatsChartPainter(this.dataPoints);

  @override
  void paint(Canvas canvas, Size size) {
    if (dataPoints.isEmpty) return;

    final paint = Paint()
      ..color = Colors.green
      ..style = PaintingStyle.fill;

    final maxDailyWords = dataPoints.map((p) => p.dailyWords).reduce((a, b) => a > b ? a : b);
    if (maxDailyWords == 0) return;

    final barWidth = size.width / dataPoints.length;
    
    for (int i = 0; i < dataPoints.length; i++) {
      final barHeight = (dataPoints[i].dailyWords / maxDailyWords) * size.height;
      final rect = Rect.fromLTWH(
        i * barWidth,
        size.height - barHeight,
        barWidth * 0.8,
        barHeight,
      );
      canvas.drawRect(rect, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}