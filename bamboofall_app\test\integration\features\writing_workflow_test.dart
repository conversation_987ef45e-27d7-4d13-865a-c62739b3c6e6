import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

// 导入应用相关的类
import 'package:bamboofall_app/features/project/domain/entities/project.dart';
import 'package:bamboofall_app/features/writing/domain/entities/chapter.dart';
import 'package:bamboofall_app/features/project/domain/repositories/project_repository.dart';
import 'package:bamboofall_app/features/writing/domain/repositories/chapter_repository.dart';

// Mock类生成
@GenerateMocks([
  ProjectRepository,
  ChapterRepository,
])

/// 写作工作流程集成测试
/// 测试项目创建、章节管理、内容编辑等核心写作功能
void main() {
  group('Writing Workflow Integration Tests', () {
    late MockProjectRepository mockProjectRepository;
    late MockChapterRepository mockChapterRepository;

    setUp(() {
      mockProjectRepository = MockProjectRepository();
      mockChapterRepository = MockChapterRepository();
    });

    group('Project Management Workflow', () {
      test('should create new project successfully', () async {
        // Arrange
        final newProject = Project(
          id: 'project-1',
          name: '我的第一部小说',
          description: '这是一个关于冒险的故事',
          type: ProjectType.novel,
          status: ProjectStatus.active,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        when(mockProjectRepository.createProject(any))
            .thenAnswer((_) async => newProject);

        // Act
        final result = await mockProjectRepository.createProject(newProject);

        // Assert
        expect(result.id, equals('project-1'));
        expect(result.name, equals('我的第一部小说'));
        expect(result.type, equals(ProjectType.novel));
        expect(result.status, equals(ProjectStatus.active));
        verify(mockProjectRepository.createProject(any)).called(1);
      });

      test('should get all projects', () async {
        // Arrange
        final projects = [
          Project(
            id: 'project-1',
            name: '项目1',
            description: '描述1',
            type: ProjectType.novel,
            status: ProjectStatus.active,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Project(
            id: 'project-2',
            name: '项目2',
            description: '描述2',
            type: ProjectType.essay,
            status: ProjectStatus.draft,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        when(mockProjectRepository.getProjects())
            .thenAnswer((_) async => projects);

        // Act
        final result = await mockProjectRepository.getProjects();

        // Assert
        expect(result.length, equals(2));
        expect(result.first.name, equals('项目1'));
        expect(result.last.name, equals('项目2'));
        verify(mockProjectRepository.getProjects()).called(1);
      });

      test('should update project status', () async {
        // Arrange
        final project = Project(
          id: 'project-1',
          name: '测试项目',
          description: '测试描述',
          type: ProjectType.novel,
          status: ProjectStatus.draft,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final updatedProject = project.copyWith(
          status: ProjectStatus.active,
          updatedAt: DateTime.now(),
        );

        when(mockProjectRepository.updateProject(any))
            .thenAnswer((_) async => updatedProject);

        // Act
        final result = await mockProjectRepository.updateProject(updatedProject);

        // Assert
        expect(result.status, equals(ProjectStatus.active));
        verify(mockProjectRepository.updateProject(any)).called(1);
      });
    });

    group('Chapter Management Workflow', () {
      test('should create new chapter', () async {
        // Arrange
        final newChapter = Chapter(
          id: 'chapter-1',
          projectId: 'project-1',
          title: '第一章：开始',
          content: '故事从这里开始...',
          order: 1,
          wordCount: 100,
          status: ChapterStatus.draft,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        when(mockChapterRepository.createChapter(any))
            .thenAnswer((_) async => newChapter);

        // Act
        final result = await mockChapterRepository.createChapter(newChapter);

        // Assert
        expect(result.id, equals('chapter-1'));
        expect(result.title, equals('第一章：开始'));
        expect(result.order, equals(1));
        expect(result.status, equals(ChapterStatus.draft));
        verify(mockChapterRepository.createChapter(any)).called(1);
      });

      test('should get chapters by project', () async {
        // Arrange
        const projectId = 'project-1';
        final chapters = [
          Chapter(
            id: 'chapter-1',
            projectId: projectId,
            title: '第一章',
            content: '内容1',
            order: 1,
            wordCount: 100,
            status: ChapterStatus.published,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Chapter(
            id: 'chapter-2',
            projectId: projectId,
            title: '第二章',
            content: '内容2',
            order: 2,
            wordCount: 150,
            status: ChapterStatus.draft,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        when(mockChapterRepository.getChaptersByProject(projectId))
            .thenAnswer((_) async => chapters);

        // Act
        final result = await mockChapterRepository.getChaptersByProject(projectId);

        // Assert
        expect(result.length, equals(2));
        expect(result.first.title, equals('第一章'));
        expect(result.last.title, equals('第二章'));
        expect(result.first.order, lessThan(result.last.order));
        verify(mockChapterRepository.getChaptersByProject(projectId)).called(1);
      });

      test('should reorder chapters', () async {
        // Arrange
        const projectId = 'project-1';
        final chapterIds = ['chapter-2', 'chapter-1', 'chapter-3'];

        when(mockChapterRepository.reorderChapters(projectId, chapterIds))
            .thenAnswer((_) async => {});

        // Act
        await mockChapterRepository.reorderChapters(projectId, chapterIds);

        // Assert
        verify(mockChapterRepository.reorderChapters(projectId, chapterIds)).called(1);
      });

      test('should update chapter content', () async {
        // Arrange
        final originalChapter = Chapter(
          id: 'chapter-1',
          projectId: 'project-1',
          title: '第一章',
          content: '原始内容',
          order: 1,
          wordCount: 50,
          status: ChapterStatus.draft,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final updatedChapter = originalChapter.copyWith(
          content: '更新后的内容，包含更多细节和描述。',
          wordCount: 150,
          updatedAt: DateTime.now(),
        );

        when(mockChapterRepository.updateChapter(any))
            .thenAnswer((_) async => updatedChapter);

        // Act
        final result = await mockChapterRepository.updateChapter(updatedChapter);

        // Assert
        expect(result.content, equals('更新后的内容，包含更多细节和描述。'));
        expect(result.wordCount, equals(150));
        expect(result.updatedAt.isAfter(originalChapter.updatedAt), isTrue);
        verify(mockChapterRepository.updateChapter(any)).called(1);
      });

      test('should delete chapter', () async {
        // Arrange
        const chapterId = 'chapter-1';

        when(mockChapterRepository.deleteChapter(chapterId))
            .thenAnswer((_) async => {});

        // Act
        await mockChapterRepository.deleteChapter(chapterId);

        // Assert
        verify(mockChapterRepository.deleteChapter(chapterId)).called(1);
      });
    });

    group('Writing Statistics and Analytics', () {
      test('should calculate project statistics', () async {
        // Arrange
        const projectId = 'project-1';
        final chapters = [
          Chapter(
            id: 'chapter-1',
            projectId: projectId,
            title: '第一章',
            content: '这是第一章的内容，包含一些文字。',
            order: 1,
            wordCount: 100,
            status: ChapterStatus.published,
            createdAt: DateTime.now().subtract(const Duration(days: 5)),
            updatedAt: DateTime.now().subtract(const Duration(days: 2)),
          ),
          Chapter(
            id: 'chapter-2',
            projectId: projectId,
            title: '第二章',
            content: '这是第二章的内容，比第一章更长一些，包含更多的描述和对话。',
            order: 2,
            wordCount: 200,
            status: ChapterStatus.draft,
            createdAt: DateTime.now().subtract(const Duration(days: 3)),
            updatedAt: DateTime.now().subtract(const Duration(days: 1)),
          ),
        ];

        when(mockChapterRepository.getChaptersByProject(projectId))
            .thenAnswer((_) async => chapters);

        // Act
        final result = await mockChapterRepository.getChaptersByProject(projectId);

        // Assert - 计算统计信息
        final totalWordCount = result.fold<int>(0, (sum, chapter) => sum + chapter.wordCount);
        final publishedChapters = result.where((c) => c.status == ChapterStatus.published).length;
        final draftChapters = result.where((c) => c.status == ChapterStatus.draft).length;

        expect(totalWordCount, equals(300));
        expect(publishedChapters, equals(1));
        expect(draftChapters, equals(1));
        expect(result.length, equals(2));
      });

      test('should track writing progress over time', () async {
        // Arrange
        const projectId = 'project-1';
        final chapters = List.generate(5, (index) => Chapter(
          id: 'chapter-${index + 1}',
          projectId: projectId,
          title: '第${index + 1}章',
          content: '章节内容 ${index + 1}',
          order: index + 1,
          wordCount: (index + 1) * 100,
          status: index < 3 ? ChapterStatus.published : ChapterStatus.draft,
          createdAt: DateTime.now().subtract(Duration(days: 10 - index * 2)),
          updatedAt: DateTime.now().subtract(Duration(days: 5 - index)),
        ));

        when(mockChapterRepository.getChaptersByProject(projectId))
            .thenAnswer((_) async => chapters);

        // Act
        final result = await mockChapterRepository.getChaptersByProject(projectId);

        // Assert - 分析写作进度
        final sortedByCreation = List<Chapter>.from(result)
          ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
        
        expect(sortedByCreation.length, equals(5));
        expect(sortedByCreation.first.createdAt.isBefore(sortedByCreation.last.createdAt), isTrue);
        
        // 验证字数递增趋势
        for (int i = 0; i < sortedByCreation.length - 1; i++) {
          expect(sortedByCreation[i].wordCount, lessThan(sortedByCreation[i + 1].wordCount));
        }
      });
    });

    group('Auto-save and Data Persistence', () {
      test('should auto-save chapter content', () async {
        // Arrange
        final chapter = Chapter(
          id: 'chapter-1',
          projectId: 'project-1',
          title: '自动保存测试章节',
          content: '初始内容',
          order: 1,
          wordCount: 50,
          status: ChapterStatus.draft,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // 模拟多次内容更新（自动保存）
        final updates = [
          '初始内容，添加了一些文字',
          '初始内容，添加了一些文字，继续编写故事',
          '初始内容，添加了一些文字，继续编写故事，完善情节发展',
        ];

        for (int i = 0; i < updates.length; i++) {
          final updatedChapter = chapter.copyWith(
            content: updates[i],
            wordCount: updates[i].length,
            updatedAt: DateTime.now().add(Duration(seconds: i + 1)),
          );

          when(mockChapterRepository.updateChapter(updatedChapter))
              .thenAnswer((_) async => updatedChapter);

          // Act
          final result = await mockChapterRepository.updateChapter(updatedChapter);

          // Assert
          expect(result.content, equals(updates[i]));
          expect(result.wordCount, equals(updates[i].length));
        }

        // 验证所有更新都被调用
        verify(mockChapterRepository.updateChapter(any)).called(updates.length);
      });

      test('should handle concurrent editing conflicts', () async {
        // Arrange
        final baseChapter = Chapter(
          id: 'chapter-1',
          projectId: 'project-1',
          title: '并发编辑测试',
          content: '基础内容',
          order: 1,
          wordCount: 50,
          status: ChapterStatus.draft,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // 模拟两个并发编辑
        final edit1 = baseChapter.copyWith(
          content: '基础内容 - 编辑1的修改',
          updatedAt: DateTime.now().add(const Duration(seconds: 1)),
        );

        final edit2 = baseChapter.copyWith(
          content: '基础内容 - 编辑2的修改',
          updatedAt: DateTime.now().add(const Duration(seconds: 2)),
        );

        when(mockChapterRepository.updateChapter(edit1))
            .thenAnswer((_) async => edit1);
        when(mockChapterRepository.updateChapter(edit2))
            .thenAnswer((_) async => edit2);

        // Act
        final result1 = await mockChapterRepository.updateChapter(edit1);
        final result2 = await mockChapterRepository.updateChapter(edit2);

        // Assert
        expect(result1.content, contains('编辑1的修改'));
        expect(result2.content, contains('编辑2的修改'));
        expect(result2.updatedAt.isAfter(result1.updatedAt), isTrue);
        
        verify(mockChapterRepository.updateChapter(edit1)).called(1);
        verify(mockChapterRepository.updateChapter(edit2)).called(1);
      });
    });

    group('Search and Navigation', () {
      test('should search chapters by content', () async {
        // Arrange
        const projectId = 'project-1';
        final chapters = [
          Chapter(
            id: 'chapter-1',
            projectId: projectId,
            title: '第一章：英雄的诞生',
            content: '在一个遥远的王国里，住着一位勇敢的骑士。',
            order: 1,
            wordCount: 100,
            status: ChapterStatus.published,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Chapter(
            id: 'chapter-2',
            projectId: projectId,
            title: '第二章：冒险开始',
            content: '骑士踏上了寻找宝藏的旅程，遇到了许多挑战。',
            order: 2,
            wordCount: 150,
            status: ChapterStatus.draft,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Chapter(
            id: 'chapter-3',
            projectId: projectId,
            title: '第三章：神秘森林',
            content: '在森林深处，隐藏着古老的秘密和魔法。',
            order: 3,
            wordCount: 120,
            status: ChapterStatus.draft,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        when(mockChapterRepository.getChaptersByProject(projectId))
            .thenAnswer((_) async => chapters);

        // Act
        final allChapters = await mockChapterRepository.getChaptersByProject(projectId);
        
        // 模拟搜索功能
        const searchTerm = '骑士';
        final searchResults = allChapters.where((chapter) =>
          chapter.title.contains(searchTerm) || 
          chapter.content.contains(searchTerm)
        ).toList();

        // Assert
        expect(searchResults.length, equals(2));
        expect(searchResults.first.title, contains('英雄的诞生'));
        expect(searchResults.last.title, contains('冒险开始'));
      });
    });
  });
}

// 辅助枚举和类定义
enum ProjectType { novel, essay, script, other }
enum ProjectStatus { draft, active, completed, archived }
enum ChapterStatus { draft, published, archived }

// Mock类
class MockProjectRepository extends Mock implements ProjectRepository {}
class MockChapterRepository extends Mock implements ChapterRepository {}