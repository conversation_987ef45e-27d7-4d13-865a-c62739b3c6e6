// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'project.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

Project _$ProjectFromJson(Map<String, dynamic> json) {
  return _Project.fromJson(json);
}

/// @nodoc
mixin _$Project {
  /// 项目唯一标识符
  String get id => throw _privateConstructorUsedError;

  /// 项目名称
  String get name => throw _privateConstructorUsedError;

  /// 项目描述
  String get description => throw _privateConstructorUsedError;

  /// 项目类型
  ProjectType get type => throw _privateConstructorUsedError;

  /// 项目状态
  ProjectStatus get status => throw _privateConstructorUsedError;

  /// 项目创建时间
  DateTime get createdAt => throw _privateConstructorUsedError;

  /// 项目最后修改时间
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// 项目创建者
  String get createdBy => throw _privateConstructorUsedError;

  /// 项目封面图片路径
  String? get coverImagePath => throw _privateConstructorUsedError;

  /// 项目标签
  List<String> get tags => throw _privateConstructorUsedError;

  /// 项目配置
  ProjectConfig get config => throw _privateConstructorUsedError;

  /// 项目统计信息
  ProjectStatistics get statistics => throw _privateConstructorUsedError;

  /// 项目模板ID（如果基于模板创建）
  String? get templateId => throw _privateConstructorUsedError;

  /// 项目归档时间（如果已归档）
  DateTime? get archivedAt => throw _privateConstructorUsedError;

  /// 项目归档原因
  String? get archiveReason => throw _privateConstructorUsedError;

  /// 项目是否为收藏
  bool get isFavorite => throw _privateConstructorUsedError;

  /// 项目颜色主题
  String? get colorTheme => throw _privateConstructorUsedError;

  /// 项目自定义字段
  Map<String, dynamic> get customFields => throw _privateConstructorUsedError;

  /// Serializes this Project to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Project
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProjectCopyWith<Project> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProjectCopyWith<$Res> {
  factory $ProjectCopyWith(Project value, $Res Function(Project) then) =
      _$ProjectCopyWithImpl<$Res, Project>;
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    ProjectType type,
    ProjectStatus status,
    DateTime createdAt,
    DateTime updatedAt,
    String createdBy,
    String? coverImagePath,
    List<String> tags,
    ProjectConfig config,
    ProjectStatistics statistics,
    String? templateId,
    DateTime? archivedAt,
    String? archiveReason,
    bool isFavorite,
    String? colorTheme,
    Map<String, dynamic> customFields,
  });

  $ProjectConfigCopyWith<$Res> get config;
  $ProjectStatisticsCopyWith<$Res> get statistics;
}

/// @nodoc
class _$ProjectCopyWithImpl<$Res, $Val extends Project>
    implements $ProjectCopyWith<$Res> {
  _$ProjectCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Project
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? type = null,
    Object? status = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? createdBy = null,
    Object? coverImagePath = freezed,
    Object? tags = null,
    Object? config = null,
    Object? statistics = null,
    Object? templateId = freezed,
    Object? archivedAt = freezed,
    Object? archiveReason = freezed,
    Object? isFavorite = null,
    Object? colorTheme = freezed,
    Object? customFields = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as ProjectType,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as ProjectStatus,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            createdBy: null == createdBy
                ? _value.createdBy
                : createdBy // ignore: cast_nullable_to_non_nullable
                      as String,
            coverImagePath: freezed == coverImagePath
                ? _value.coverImagePath
                : coverImagePath // ignore: cast_nullable_to_non_nullable
                      as String?,
            tags: null == tags
                ? _value.tags
                : tags // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            config: null == config
                ? _value.config
                : config // ignore: cast_nullable_to_non_nullable
                      as ProjectConfig,
            statistics: null == statistics
                ? _value.statistics
                : statistics // ignore: cast_nullable_to_non_nullable
                      as ProjectStatistics,
            templateId: freezed == templateId
                ? _value.templateId
                : templateId // ignore: cast_nullable_to_non_nullable
                      as String?,
            archivedAt: freezed == archivedAt
                ? _value.archivedAt
                : archivedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            archiveReason: freezed == archiveReason
                ? _value.archiveReason
                : archiveReason // ignore: cast_nullable_to_non_nullable
                      as String?,
            isFavorite: null == isFavorite
                ? _value.isFavorite
                : isFavorite // ignore: cast_nullable_to_non_nullable
                      as bool,
            colorTheme: freezed == colorTheme
                ? _value.colorTheme
                : colorTheme // ignore: cast_nullable_to_non_nullable
                      as String?,
            customFields: null == customFields
                ? _value.customFields
                : customFields // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }

  /// Create a copy of Project
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProjectConfigCopyWith<$Res> get config {
    return $ProjectConfigCopyWith<$Res>(_value.config, (value) {
      return _then(_value.copyWith(config: value) as $Val);
    });
  }

  /// Create a copy of Project
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProjectStatisticsCopyWith<$Res> get statistics {
    return $ProjectStatisticsCopyWith<$Res>(_value.statistics, (value) {
      return _then(_value.copyWith(statistics: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProjectImplCopyWith<$Res> implements $ProjectCopyWith<$Res> {
  factory _$$ProjectImplCopyWith(
    _$ProjectImpl value,
    $Res Function(_$ProjectImpl) then,
  ) = __$$ProjectImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    ProjectType type,
    ProjectStatus status,
    DateTime createdAt,
    DateTime updatedAt,
    String createdBy,
    String? coverImagePath,
    List<String> tags,
    ProjectConfig config,
    ProjectStatistics statistics,
    String? templateId,
    DateTime? archivedAt,
    String? archiveReason,
    bool isFavorite,
    String? colorTheme,
    Map<String, dynamic> customFields,
  });

  @override
  $ProjectConfigCopyWith<$Res> get config;
  @override
  $ProjectStatisticsCopyWith<$Res> get statistics;
}

/// @nodoc
class __$$ProjectImplCopyWithImpl<$Res>
    extends _$ProjectCopyWithImpl<$Res, _$ProjectImpl>
    implements _$$ProjectImplCopyWith<$Res> {
  __$$ProjectImplCopyWithImpl(
    _$ProjectImpl _value,
    $Res Function(_$ProjectImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Project
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? type = null,
    Object? status = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? createdBy = null,
    Object? coverImagePath = freezed,
    Object? tags = null,
    Object? config = null,
    Object? statistics = null,
    Object? templateId = freezed,
    Object? archivedAt = freezed,
    Object? archiveReason = freezed,
    Object? isFavorite = null,
    Object? colorTheme = freezed,
    Object? customFields = null,
  }) {
    return _then(
      _$ProjectImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as ProjectType,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as ProjectStatus,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        createdBy: null == createdBy
            ? _value.createdBy
            : createdBy // ignore: cast_nullable_to_non_nullable
                  as String,
        coverImagePath: freezed == coverImagePath
            ? _value.coverImagePath
            : coverImagePath // ignore: cast_nullable_to_non_nullable
                  as String?,
        tags: null == tags
            ? _value._tags
            : tags // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        config: null == config
            ? _value.config
            : config // ignore: cast_nullable_to_non_nullable
                  as ProjectConfig,
        statistics: null == statistics
            ? _value.statistics
            : statistics // ignore: cast_nullable_to_non_nullable
                  as ProjectStatistics,
        templateId: freezed == templateId
            ? _value.templateId
            : templateId // ignore: cast_nullable_to_non_nullable
                  as String?,
        archivedAt: freezed == archivedAt
            ? _value.archivedAt
            : archivedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        archiveReason: freezed == archiveReason
            ? _value.archiveReason
            : archiveReason // ignore: cast_nullable_to_non_nullable
                  as String?,
        isFavorite: null == isFavorite
            ? _value.isFavorite
            : isFavorite // ignore: cast_nullable_to_non_nullable
                  as bool,
        colorTheme: freezed == colorTheme
            ? _value.colorTheme
            : colorTheme // ignore: cast_nullable_to_non_nullable
                  as String?,
        customFields: null == customFields
            ? _value._customFields
            : customFields // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProjectImpl implements _Project {
  const _$ProjectImpl({
    required this.id,
    required this.name,
    this.description = '',
    this.type = ProjectType.novel,
    this.status = ProjectStatus.active,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    this.coverImagePath,
    final List<String> tags = const [],
    this.config = const ProjectConfig(),
    this.statistics = const ProjectStatistics(),
    this.templateId,
    this.archivedAt,
    this.archiveReason,
    this.isFavorite = false,
    this.colorTheme,
    final Map<String, dynamic> customFields = const {},
  }) : _tags = tags,
       _customFields = customFields;

  factory _$ProjectImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProjectImplFromJson(json);

  /// 项目唯一标识符
  @override
  final String id;

  /// 项目名称
  @override
  final String name;

  /// 项目描述
  @override
  @JsonKey()
  final String description;

  /// 项目类型
  @override
  @JsonKey()
  final ProjectType type;

  /// 项目状态
  @override
  @JsonKey()
  final ProjectStatus status;

  /// 项目创建时间
  @override
  final DateTime createdAt;

  /// 项目最后修改时间
  @override
  final DateTime updatedAt;

  /// 项目创建者
  @override
  final String createdBy;

  /// 项目封面图片路径
  @override
  final String? coverImagePath;

  /// 项目标签
  final List<String> _tags;

  /// 项目标签
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  /// 项目配置
  @override
  @JsonKey()
  final ProjectConfig config;

  /// 项目统计信息
  @override
  @JsonKey()
  final ProjectStatistics statistics;

  /// 项目模板ID（如果基于模板创建）
  @override
  final String? templateId;

  /// 项目归档时间（如果已归档）
  @override
  final DateTime? archivedAt;

  /// 项目归档原因
  @override
  final String? archiveReason;

  /// 项目是否为收藏
  @override
  @JsonKey()
  final bool isFavorite;

  /// 项目颜色主题
  @override
  final String? colorTheme;

  /// 项目自定义字段
  final Map<String, dynamic> _customFields;

  /// 项目自定义字段
  @override
  @JsonKey()
  Map<String, dynamic> get customFields {
    if (_customFields is EqualUnmodifiableMapView) return _customFields;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customFields);
  }

  @override
  String toString() {
    return 'Project(id: $id, name: $name, description: $description, type: $type, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, createdBy: $createdBy, coverImagePath: $coverImagePath, tags: $tags, config: $config, statistics: $statistics, templateId: $templateId, archivedAt: $archivedAt, archiveReason: $archiveReason, isFavorite: $isFavorite, colorTheme: $colorTheme, customFields: $customFields)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProjectImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.coverImagePath, coverImagePath) ||
                other.coverImagePath == coverImagePath) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.config, config) || other.config == config) &&
            (identical(other.statistics, statistics) ||
                other.statistics == statistics) &&
            (identical(other.templateId, templateId) ||
                other.templateId == templateId) &&
            (identical(other.archivedAt, archivedAt) ||
                other.archivedAt == archivedAt) &&
            (identical(other.archiveReason, archiveReason) ||
                other.archiveReason == archiveReason) &&
            (identical(other.isFavorite, isFavorite) ||
                other.isFavorite == isFavorite) &&
            (identical(other.colorTheme, colorTheme) ||
                other.colorTheme == colorTheme) &&
            const DeepCollectionEquality().equals(
              other._customFields,
              _customFields,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    type,
    status,
    createdAt,
    updatedAt,
    createdBy,
    coverImagePath,
    const DeepCollectionEquality().hash(_tags),
    config,
    statistics,
    templateId,
    archivedAt,
    archiveReason,
    isFavorite,
    colorTheme,
    const DeepCollectionEquality().hash(_customFields),
  );

  /// Create a copy of Project
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProjectImplCopyWith<_$ProjectImpl> get copyWith =>
      __$$ProjectImplCopyWithImpl<_$ProjectImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProjectImplToJson(this);
  }
}

abstract class _Project implements Project {
  const factory _Project({
    required final String id,
    required final String name,
    final String description,
    final ProjectType type,
    final ProjectStatus status,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    required final String createdBy,
    final String? coverImagePath,
    final List<String> tags,
    final ProjectConfig config,
    final ProjectStatistics statistics,
    final String? templateId,
    final DateTime? archivedAt,
    final String? archiveReason,
    final bool isFavorite,
    final String? colorTheme,
    final Map<String, dynamic> customFields,
  }) = _$ProjectImpl;

  factory _Project.fromJson(Map<String, dynamic> json) = _$ProjectImpl.fromJson;

  /// 项目唯一标识符
  @override
  String get id;

  /// 项目名称
  @override
  String get name;

  /// 项目描述
  @override
  String get description;

  /// 项目类型
  @override
  ProjectType get type;

  /// 项目状态
  @override
  ProjectStatus get status;

  /// 项目创建时间
  @override
  DateTime get createdAt;

  /// 项目最后修改时间
  @override
  DateTime get updatedAt;

  /// 项目创建者
  @override
  String get createdBy;

  /// 项目封面图片路径
  @override
  String? get coverImagePath;

  /// 项目标签
  @override
  List<String> get tags;

  /// 项目配置
  @override
  ProjectConfig get config;

  /// 项目统计信息
  @override
  ProjectStatistics get statistics;

  /// 项目模板ID（如果基于模板创建）
  @override
  String? get templateId;

  /// 项目归档时间（如果已归档）
  @override
  DateTime? get archivedAt;

  /// 项目归档原因
  @override
  String? get archiveReason;

  /// 项目是否为收藏
  @override
  bool get isFavorite;

  /// 项目颜色主题
  @override
  String? get colorTheme;

  /// 项目自定义字段
  @override
  Map<String, dynamic> get customFields;

  /// Create a copy of Project
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProjectImplCopyWith<_$ProjectImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProjectConfig _$ProjectConfigFromJson(Map<String, dynamic> json) {
  return _ProjectConfig.fromJson(json);
}

/// @nodoc
mixin _$ProjectConfig {
  /// 目标字数
  int get targetWordCount => throw _privateConstructorUsedError;

  /// 每日写作目标
  int get dailyWritingGoal => throw _privateConstructorUsedError;

  /// 截止日期
  DateTime? get deadline => throw _privateConstructorUsedError;

  /// 自动保存间隔（分钟）
  int get autoSaveInterval => throw _privateConstructorUsedError;

  /// 启用版本控制
  bool get enableVersionControl => throw _privateConstructorUsedError;

  /// 启用AI助手
  bool get enableAIAssistant => throw _privateConstructorUsedError;

  /// 默认AI模型
  String get defaultAIModel => throw _privateConstructorUsedError;

  /// 写作提醒设置
  WritingReminder get writingReminder => throw _privateConstructorUsedError;

  /// 导出设置
  ExportSettings get exportSettings => throw _privateConstructorUsedError;

  /// 备份设置
  BackupSettings get backupSettings => throw _privateConstructorUsedError;

  /// 协作设置
  CollaborationSettings get collaborationSettings =>
      throw _privateConstructorUsedError;

  /// Serializes this ProjectConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProjectConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProjectConfigCopyWith<ProjectConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProjectConfigCopyWith<$Res> {
  factory $ProjectConfigCopyWith(
    ProjectConfig value,
    $Res Function(ProjectConfig) then,
  ) = _$ProjectConfigCopyWithImpl<$Res, ProjectConfig>;
  @useResult
  $Res call({
    int targetWordCount,
    int dailyWritingGoal,
    DateTime? deadline,
    int autoSaveInterval,
    bool enableVersionControl,
    bool enableAIAssistant,
    String defaultAIModel,
    WritingReminder writingReminder,
    ExportSettings exportSettings,
    BackupSettings backupSettings,
    CollaborationSettings collaborationSettings,
  });

  $WritingReminderCopyWith<$Res> get writingReminder;
  $ExportSettingsCopyWith<$Res> get exportSettings;
  $BackupSettingsCopyWith<$Res> get backupSettings;
  $CollaborationSettingsCopyWith<$Res> get collaborationSettings;
}

/// @nodoc
class _$ProjectConfigCopyWithImpl<$Res, $Val extends ProjectConfig>
    implements $ProjectConfigCopyWith<$Res> {
  _$ProjectConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProjectConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? targetWordCount = null,
    Object? dailyWritingGoal = null,
    Object? deadline = freezed,
    Object? autoSaveInterval = null,
    Object? enableVersionControl = null,
    Object? enableAIAssistant = null,
    Object? defaultAIModel = null,
    Object? writingReminder = null,
    Object? exportSettings = null,
    Object? backupSettings = null,
    Object? collaborationSettings = null,
  }) {
    return _then(
      _value.copyWith(
            targetWordCount: null == targetWordCount
                ? _value.targetWordCount
                : targetWordCount // ignore: cast_nullable_to_non_nullable
                      as int,
            dailyWritingGoal: null == dailyWritingGoal
                ? _value.dailyWritingGoal
                : dailyWritingGoal // ignore: cast_nullable_to_non_nullable
                      as int,
            deadline: freezed == deadline
                ? _value.deadline
                : deadline // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            autoSaveInterval: null == autoSaveInterval
                ? _value.autoSaveInterval
                : autoSaveInterval // ignore: cast_nullable_to_non_nullable
                      as int,
            enableVersionControl: null == enableVersionControl
                ? _value.enableVersionControl
                : enableVersionControl // ignore: cast_nullable_to_non_nullable
                      as bool,
            enableAIAssistant: null == enableAIAssistant
                ? _value.enableAIAssistant
                : enableAIAssistant // ignore: cast_nullable_to_non_nullable
                      as bool,
            defaultAIModel: null == defaultAIModel
                ? _value.defaultAIModel
                : defaultAIModel // ignore: cast_nullable_to_non_nullable
                      as String,
            writingReminder: null == writingReminder
                ? _value.writingReminder
                : writingReminder // ignore: cast_nullable_to_non_nullable
                      as WritingReminder,
            exportSettings: null == exportSettings
                ? _value.exportSettings
                : exportSettings // ignore: cast_nullable_to_non_nullable
                      as ExportSettings,
            backupSettings: null == backupSettings
                ? _value.backupSettings
                : backupSettings // ignore: cast_nullable_to_non_nullable
                      as BackupSettings,
            collaborationSettings: null == collaborationSettings
                ? _value.collaborationSettings
                : collaborationSettings // ignore: cast_nullable_to_non_nullable
                      as CollaborationSettings,
          )
          as $Val,
    );
  }

  /// Create a copy of ProjectConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WritingReminderCopyWith<$Res> get writingReminder {
    return $WritingReminderCopyWith<$Res>(_value.writingReminder, (value) {
      return _then(_value.copyWith(writingReminder: value) as $Val);
    });
  }

  /// Create a copy of ProjectConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ExportSettingsCopyWith<$Res> get exportSettings {
    return $ExportSettingsCopyWith<$Res>(_value.exportSettings, (value) {
      return _then(_value.copyWith(exportSettings: value) as $Val);
    });
  }

  /// Create a copy of ProjectConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BackupSettingsCopyWith<$Res> get backupSettings {
    return $BackupSettingsCopyWith<$Res>(_value.backupSettings, (value) {
      return _then(_value.copyWith(backupSettings: value) as $Val);
    });
  }

  /// Create a copy of ProjectConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CollaborationSettingsCopyWith<$Res> get collaborationSettings {
    return $CollaborationSettingsCopyWith<$Res>(_value.collaborationSettings, (
      value,
    ) {
      return _then(_value.copyWith(collaborationSettings: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProjectConfigImplCopyWith<$Res>
    implements $ProjectConfigCopyWith<$Res> {
  factory _$$ProjectConfigImplCopyWith(
    _$ProjectConfigImpl value,
    $Res Function(_$ProjectConfigImpl) then,
  ) = __$$ProjectConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int targetWordCount,
    int dailyWritingGoal,
    DateTime? deadline,
    int autoSaveInterval,
    bool enableVersionControl,
    bool enableAIAssistant,
    String defaultAIModel,
    WritingReminder writingReminder,
    ExportSettings exportSettings,
    BackupSettings backupSettings,
    CollaborationSettings collaborationSettings,
  });

  @override
  $WritingReminderCopyWith<$Res> get writingReminder;
  @override
  $ExportSettingsCopyWith<$Res> get exportSettings;
  @override
  $BackupSettingsCopyWith<$Res> get backupSettings;
  @override
  $CollaborationSettingsCopyWith<$Res> get collaborationSettings;
}

/// @nodoc
class __$$ProjectConfigImplCopyWithImpl<$Res>
    extends _$ProjectConfigCopyWithImpl<$Res, _$ProjectConfigImpl>
    implements _$$ProjectConfigImplCopyWith<$Res> {
  __$$ProjectConfigImplCopyWithImpl(
    _$ProjectConfigImpl _value,
    $Res Function(_$ProjectConfigImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ProjectConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? targetWordCount = null,
    Object? dailyWritingGoal = null,
    Object? deadline = freezed,
    Object? autoSaveInterval = null,
    Object? enableVersionControl = null,
    Object? enableAIAssistant = null,
    Object? defaultAIModel = null,
    Object? writingReminder = null,
    Object? exportSettings = null,
    Object? backupSettings = null,
    Object? collaborationSettings = null,
  }) {
    return _then(
      _$ProjectConfigImpl(
        targetWordCount: null == targetWordCount
            ? _value.targetWordCount
            : targetWordCount // ignore: cast_nullable_to_non_nullable
                  as int,
        dailyWritingGoal: null == dailyWritingGoal
            ? _value.dailyWritingGoal
            : dailyWritingGoal // ignore: cast_nullable_to_non_nullable
                  as int,
        deadline: freezed == deadline
            ? _value.deadline
            : deadline // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        autoSaveInterval: null == autoSaveInterval
            ? _value.autoSaveInterval
            : autoSaveInterval // ignore: cast_nullable_to_non_nullable
                  as int,
        enableVersionControl: null == enableVersionControl
            ? _value.enableVersionControl
            : enableVersionControl // ignore: cast_nullable_to_non_nullable
                  as bool,
        enableAIAssistant: null == enableAIAssistant
            ? _value.enableAIAssistant
            : enableAIAssistant // ignore: cast_nullable_to_non_nullable
                  as bool,
        defaultAIModel: null == defaultAIModel
            ? _value.defaultAIModel
            : defaultAIModel // ignore: cast_nullable_to_non_nullable
                  as String,
        writingReminder: null == writingReminder
            ? _value.writingReminder
            : writingReminder // ignore: cast_nullable_to_non_nullable
                  as WritingReminder,
        exportSettings: null == exportSettings
            ? _value.exportSettings
            : exportSettings // ignore: cast_nullable_to_non_nullable
                  as ExportSettings,
        backupSettings: null == backupSettings
            ? _value.backupSettings
            : backupSettings // ignore: cast_nullable_to_non_nullable
                  as BackupSettings,
        collaborationSettings: null == collaborationSettings
            ? _value.collaborationSettings
            : collaborationSettings // ignore: cast_nullable_to_non_nullable
                  as CollaborationSettings,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProjectConfigImpl implements _ProjectConfig {
  const _$ProjectConfigImpl({
    this.targetWordCount = 0,
    this.dailyWritingGoal = 0,
    this.deadline,
    this.autoSaveInterval = 5,
    this.enableVersionControl = true,
    this.enableAIAssistant = true,
    this.defaultAIModel = 'gpt-3.5-turbo',
    this.writingReminder = const WritingReminder(),
    this.exportSettings = const ExportSettings(),
    this.backupSettings = const BackupSettings(),
    this.collaborationSettings = const CollaborationSettings(),
  });

  factory _$ProjectConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProjectConfigImplFromJson(json);

  /// 目标字数
  @override
  @JsonKey()
  final int targetWordCount;

  /// 每日写作目标
  @override
  @JsonKey()
  final int dailyWritingGoal;

  /// 截止日期
  @override
  final DateTime? deadline;

  /// 自动保存间隔（分钟）
  @override
  @JsonKey()
  final int autoSaveInterval;

  /// 启用版本控制
  @override
  @JsonKey()
  final bool enableVersionControl;

  /// 启用AI助手
  @override
  @JsonKey()
  final bool enableAIAssistant;

  /// 默认AI模型
  @override
  @JsonKey()
  final String defaultAIModel;

  /// 写作提醒设置
  @override
  @JsonKey()
  final WritingReminder writingReminder;

  /// 导出设置
  @override
  @JsonKey()
  final ExportSettings exportSettings;

  /// 备份设置
  @override
  @JsonKey()
  final BackupSettings backupSettings;

  /// 协作设置
  @override
  @JsonKey()
  final CollaborationSettings collaborationSettings;

  @override
  String toString() {
    return 'ProjectConfig(targetWordCount: $targetWordCount, dailyWritingGoal: $dailyWritingGoal, deadline: $deadline, autoSaveInterval: $autoSaveInterval, enableVersionControl: $enableVersionControl, enableAIAssistant: $enableAIAssistant, defaultAIModel: $defaultAIModel, writingReminder: $writingReminder, exportSettings: $exportSettings, backupSettings: $backupSettings, collaborationSettings: $collaborationSettings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProjectConfigImpl &&
            (identical(other.targetWordCount, targetWordCount) ||
                other.targetWordCount == targetWordCount) &&
            (identical(other.dailyWritingGoal, dailyWritingGoal) ||
                other.dailyWritingGoal == dailyWritingGoal) &&
            (identical(other.deadline, deadline) ||
                other.deadline == deadline) &&
            (identical(other.autoSaveInterval, autoSaveInterval) ||
                other.autoSaveInterval == autoSaveInterval) &&
            (identical(other.enableVersionControl, enableVersionControl) ||
                other.enableVersionControl == enableVersionControl) &&
            (identical(other.enableAIAssistant, enableAIAssistant) ||
                other.enableAIAssistant == enableAIAssistant) &&
            (identical(other.defaultAIModel, defaultAIModel) ||
                other.defaultAIModel == defaultAIModel) &&
            (identical(other.writingReminder, writingReminder) ||
                other.writingReminder == writingReminder) &&
            (identical(other.exportSettings, exportSettings) ||
                other.exportSettings == exportSettings) &&
            (identical(other.backupSettings, backupSettings) ||
                other.backupSettings == backupSettings) &&
            (identical(other.collaborationSettings, collaborationSettings) ||
                other.collaborationSettings == collaborationSettings));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    targetWordCount,
    dailyWritingGoal,
    deadline,
    autoSaveInterval,
    enableVersionControl,
    enableAIAssistant,
    defaultAIModel,
    writingReminder,
    exportSettings,
    backupSettings,
    collaborationSettings,
  );

  /// Create a copy of ProjectConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProjectConfigImplCopyWith<_$ProjectConfigImpl> get copyWith =>
      __$$ProjectConfigImplCopyWithImpl<_$ProjectConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProjectConfigImplToJson(this);
  }
}

abstract class _ProjectConfig implements ProjectConfig {
  const factory _ProjectConfig({
    final int targetWordCount,
    final int dailyWritingGoal,
    final DateTime? deadline,
    final int autoSaveInterval,
    final bool enableVersionControl,
    final bool enableAIAssistant,
    final String defaultAIModel,
    final WritingReminder writingReminder,
    final ExportSettings exportSettings,
    final BackupSettings backupSettings,
    final CollaborationSettings collaborationSettings,
  }) = _$ProjectConfigImpl;

  factory _ProjectConfig.fromJson(Map<String, dynamic> json) =
      _$ProjectConfigImpl.fromJson;

  /// 目标字数
  @override
  int get targetWordCount;

  /// 每日写作目标
  @override
  int get dailyWritingGoal;

  /// 截止日期
  @override
  DateTime? get deadline;

  /// 自动保存间隔（分钟）
  @override
  int get autoSaveInterval;

  /// 启用版本控制
  @override
  bool get enableVersionControl;

  /// 启用AI助手
  @override
  bool get enableAIAssistant;

  /// 默认AI模型
  @override
  String get defaultAIModel;

  /// 写作提醒设置
  @override
  WritingReminder get writingReminder;

  /// 导出设置
  @override
  ExportSettings get exportSettings;

  /// 备份设置
  @override
  BackupSettings get backupSettings;

  /// 协作设置
  @override
  CollaborationSettings get collaborationSettings;

  /// Create a copy of ProjectConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProjectConfigImplCopyWith<_$ProjectConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WritingReminder _$WritingReminderFromJson(Map<String, dynamic> json) {
  return _WritingReminder.fromJson(json);
}

/// @nodoc
mixin _$WritingReminder {
  /// 是否启用提醒
  bool get enabled => throw _privateConstructorUsedError;

  /// 提醒时间
  List<String> get reminderTimes => throw _privateConstructorUsedError;

  /// 提醒频率
  ReminderFrequency get frequency => throw _privateConstructorUsedError;

  /// 提醒消息
  String get message => throw _privateConstructorUsedError;

  /// Serializes this WritingReminder to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WritingReminder
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WritingReminderCopyWith<WritingReminder> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WritingReminderCopyWith<$Res> {
  factory $WritingReminderCopyWith(
    WritingReminder value,
    $Res Function(WritingReminder) then,
  ) = _$WritingReminderCopyWithImpl<$Res, WritingReminder>;
  @useResult
  $Res call({
    bool enabled,
    List<String> reminderTimes,
    ReminderFrequency frequency,
    String message,
  });
}

/// @nodoc
class _$WritingReminderCopyWithImpl<$Res, $Val extends WritingReminder>
    implements $WritingReminderCopyWith<$Res> {
  _$WritingReminderCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WritingReminder
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enabled = null,
    Object? reminderTimes = null,
    Object? frequency = null,
    Object? message = null,
  }) {
    return _then(
      _value.copyWith(
            enabled: null == enabled
                ? _value.enabled
                : enabled // ignore: cast_nullable_to_non_nullable
                      as bool,
            reminderTimes: null == reminderTimes
                ? _value.reminderTimes
                : reminderTimes // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            frequency: null == frequency
                ? _value.frequency
                : frequency // ignore: cast_nullable_to_non_nullable
                      as ReminderFrequency,
            message: null == message
                ? _value.message
                : message // ignore: cast_nullable_to_non_nullable
                      as String,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$WritingReminderImplCopyWith<$Res>
    implements $WritingReminderCopyWith<$Res> {
  factory _$$WritingReminderImplCopyWith(
    _$WritingReminderImpl value,
    $Res Function(_$WritingReminderImpl) then,
  ) = __$$WritingReminderImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    bool enabled,
    List<String> reminderTimes,
    ReminderFrequency frequency,
    String message,
  });
}

/// @nodoc
class __$$WritingReminderImplCopyWithImpl<$Res>
    extends _$WritingReminderCopyWithImpl<$Res, _$WritingReminderImpl>
    implements _$$WritingReminderImplCopyWith<$Res> {
  __$$WritingReminderImplCopyWithImpl(
    _$WritingReminderImpl _value,
    $Res Function(_$WritingReminderImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of WritingReminder
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enabled = null,
    Object? reminderTimes = null,
    Object? frequency = null,
    Object? message = null,
  }) {
    return _then(
      _$WritingReminderImpl(
        enabled: null == enabled
            ? _value.enabled
            : enabled // ignore: cast_nullable_to_non_nullable
                  as bool,
        reminderTimes: null == reminderTimes
            ? _value._reminderTimes
            : reminderTimes // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        frequency: null == frequency
            ? _value.frequency
            : frequency // ignore: cast_nullable_to_non_nullable
                  as ReminderFrequency,
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$WritingReminderImpl implements _WritingReminder {
  const _$WritingReminderImpl({
    this.enabled = false,
    final List<String> reminderTimes = const [],
    this.frequency = ReminderFrequency.daily,
    this.message = '该写作了！',
  }) : _reminderTimes = reminderTimes;

  factory _$WritingReminderImpl.fromJson(Map<String, dynamic> json) =>
      _$$WritingReminderImplFromJson(json);

  /// 是否启用提醒
  @override
  @JsonKey()
  final bool enabled;

  /// 提醒时间
  final List<String> _reminderTimes;

  /// 提醒时间
  @override
  @JsonKey()
  List<String> get reminderTimes {
    if (_reminderTimes is EqualUnmodifiableListView) return _reminderTimes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reminderTimes);
  }

  /// 提醒频率
  @override
  @JsonKey()
  final ReminderFrequency frequency;

  /// 提醒消息
  @override
  @JsonKey()
  final String message;

  @override
  String toString() {
    return 'WritingReminder(enabled: $enabled, reminderTimes: $reminderTimes, frequency: $frequency, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WritingReminderImpl &&
            (identical(other.enabled, enabled) || other.enabled == enabled) &&
            const DeepCollectionEquality().equals(
              other._reminderTimes,
              _reminderTimes,
            ) &&
            (identical(other.frequency, frequency) ||
                other.frequency == frequency) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    enabled,
    const DeepCollectionEquality().hash(_reminderTimes),
    frequency,
    message,
  );

  /// Create a copy of WritingReminder
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WritingReminderImplCopyWith<_$WritingReminderImpl> get copyWith =>
      __$$WritingReminderImplCopyWithImpl<_$WritingReminderImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$WritingReminderImplToJson(this);
  }
}

abstract class _WritingReminder implements WritingReminder {
  const factory _WritingReminder({
    final bool enabled,
    final List<String> reminderTimes,
    final ReminderFrequency frequency,
    final String message,
  }) = _$WritingReminderImpl;

  factory _WritingReminder.fromJson(Map<String, dynamic> json) =
      _$WritingReminderImpl.fromJson;

  /// 是否启用提醒
  @override
  bool get enabled;

  /// 提醒时间
  @override
  List<String> get reminderTimes;

  /// 提醒频率
  @override
  ReminderFrequency get frequency;

  /// 提醒消息
  @override
  String get message;

  /// Create a copy of WritingReminder
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WritingReminderImplCopyWith<_$WritingReminderImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ExportSettings _$ExportSettingsFromJson(Map<String, dynamic> json) {
  return _ExportSettings.fromJson(json);
}

/// @nodoc
mixin _$ExportSettings {
  /// 默认导出格式
  ExportFormat get defaultFormat => throw _privateConstructorUsedError;

  /// 包含元数据
  bool get includeMetadata => throw _privateConstructorUsedError;

  /// 包含目录
  bool get includeTableOfContents => throw _privateConstructorUsedError;

  /// 页面设置
  PageSettings get pageSettings => throw _privateConstructorUsedError;

  /// 字体设置
  FontSettings get fontSettings => throw _privateConstructorUsedError;

  /// Serializes this ExportSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ExportSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ExportSettingsCopyWith<ExportSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExportSettingsCopyWith<$Res> {
  factory $ExportSettingsCopyWith(
    ExportSettings value,
    $Res Function(ExportSettings) then,
  ) = _$ExportSettingsCopyWithImpl<$Res, ExportSettings>;
  @useResult
  $Res call({
    ExportFormat defaultFormat,
    bool includeMetadata,
    bool includeTableOfContents,
    PageSettings pageSettings,
    FontSettings fontSettings,
  });

  $PageSettingsCopyWith<$Res> get pageSettings;
  $FontSettingsCopyWith<$Res> get fontSettings;
}

/// @nodoc
class _$ExportSettingsCopyWithImpl<$Res, $Val extends ExportSettings>
    implements $ExportSettingsCopyWith<$Res> {
  _$ExportSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ExportSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? defaultFormat = null,
    Object? includeMetadata = null,
    Object? includeTableOfContents = null,
    Object? pageSettings = null,
    Object? fontSettings = null,
  }) {
    return _then(
      _value.copyWith(
            defaultFormat: null == defaultFormat
                ? _value.defaultFormat
                : defaultFormat // ignore: cast_nullable_to_non_nullable
                      as ExportFormat,
            includeMetadata: null == includeMetadata
                ? _value.includeMetadata
                : includeMetadata // ignore: cast_nullable_to_non_nullable
                      as bool,
            includeTableOfContents: null == includeTableOfContents
                ? _value.includeTableOfContents
                : includeTableOfContents // ignore: cast_nullable_to_non_nullable
                      as bool,
            pageSettings: null == pageSettings
                ? _value.pageSettings
                : pageSettings // ignore: cast_nullable_to_non_nullable
                      as PageSettings,
            fontSettings: null == fontSettings
                ? _value.fontSettings
                : fontSettings // ignore: cast_nullable_to_non_nullable
                      as FontSettings,
          )
          as $Val,
    );
  }

  /// Create a copy of ExportSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PageSettingsCopyWith<$Res> get pageSettings {
    return $PageSettingsCopyWith<$Res>(_value.pageSettings, (value) {
      return _then(_value.copyWith(pageSettings: value) as $Val);
    });
  }

  /// Create a copy of ExportSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FontSettingsCopyWith<$Res> get fontSettings {
    return $FontSettingsCopyWith<$Res>(_value.fontSettings, (value) {
      return _then(_value.copyWith(fontSettings: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ExportSettingsImplCopyWith<$Res>
    implements $ExportSettingsCopyWith<$Res> {
  factory _$$ExportSettingsImplCopyWith(
    _$ExportSettingsImpl value,
    $Res Function(_$ExportSettingsImpl) then,
  ) = __$$ExportSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    ExportFormat defaultFormat,
    bool includeMetadata,
    bool includeTableOfContents,
    PageSettings pageSettings,
    FontSettings fontSettings,
  });

  @override
  $PageSettingsCopyWith<$Res> get pageSettings;
  @override
  $FontSettingsCopyWith<$Res> get fontSettings;
}

/// @nodoc
class __$$ExportSettingsImplCopyWithImpl<$Res>
    extends _$ExportSettingsCopyWithImpl<$Res, _$ExportSettingsImpl>
    implements _$$ExportSettingsImplCopyWith<$Res> {
  __$$ExportSettingsImplCopyWithImpl(
    _$ExportSettingsImpl _value,
    $Res Function(_$ExportSettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ExportSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? defaultFormat = null,
    Object? includeMetadata = null,
    Object? includeTableOfContents = null,
    Object? pageSettings = null,
    Object? fontSettings = null,
  }) {
    return _then(
      _$ExportSettingsImpl(
        defaultFormat: null == defaultFormat
            ? _value.defaultFormat
            : defaultFormat // ignore: cast_nullable_to_non_nullable
                  as ExportFormat,
        includeMetadata: null == includeMetadata
            ? _value.includeMetadata
            : includeMetadata // ignore: cast_nullable_to_non_nullable
                  as bool,
        includeTableOfContents: null == includeTableOfContents
            ? _value.includeTableOfContents
            : includeTableOfContents // ignore: cast_nullable_to_non_nullable
                  as bool,
        pageSettings: null == pageSettings
            ? _value.pageSettings
            : pageSettings // ignore: cast_nullable_to_non_nullable
                  as PageSettings,
        fontSettings: null == fontSettings
            ? _value.fontSettings
            : fontSettings // ignore: cast_nullable_to_non_nullable
                  as FontSettings,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ExportSettingsImpl implements _ExportSettings {
  const _$ExportSettingsImpl({
    this.defaultFormat = ExportFormat.docx,
    this.includeMetadata = true,
    this.includeTableOfContents = true,
    this.pageSettings = const PageSettings(),
    this.fontSettings = const FontSettings(),
  });

  factory _$ExportSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$ExportSettingsImplFromJson(json);

  /// 默认导出格式
  @override
  @JsonKey()
  final ExportFormat defaultFormat;

  /// 包含元数据
  @override
  @JsonKey()
  final bool includeMetadata;

  /// 包含目录
  @override
  @JsonKey()
  final bool includeTableOfContents;

  /// 页面设置
  @override
  @JsonKey()
  final PageSettings pageSettings;

  /// 字体设置
  @override
  @JsonKey()
  final FontSettings fontSettings;

  @override
  String toString() {
    return 'ExportSettings(defaultFormat: $defaultFormat, includeMetadata: $includeMetadata, includeTableOfContents: $includeTableOfContents, pageSettings: $pageSettings, fontSettings: $fontSettings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExportSettingsImpl &&
            (identical(other.defaultFormat, defaultFormat) ||
                other.defaultFormat == defaultFormat) &&
            (identical(other.includeMetadata, includeMetadata) ||
                other.includeMetadata == includeMetadata) &&
            (identical(other.includeTableOfContents, includeTableOfContents) ||
                other.includeTableOfContents == includeTableOfContents) &&
            (identical(other.pageSettings, pageSettings) ||
                other.pageSettings == pageSettings) &&
            (identical(other.fontSettings, fontSettings) ||
                other.fontSettings == fontSettings));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    defaultFormat,
    includeMetadata,
    includeTableOfContents,
    pageSettings,
    fontSettings,
  );

  /// Create a copy of ExportSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ExportSettingsImplCopyWith<_$ExportSettingsImpl> get copyWith =>
      __$$ExportSettingsImplCopyWithImpl<_$ExportSettingsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ExportSettingsImplToJson(this);
  }
}

abstract class _ExportSettings implements ExportSettings {
  const factory _ExportSettings({
    final ExportFormat defaultFormat,
    final bool includeMetadata,
    final bool includeTableOfContents,
    final PageSettings pageSettings,
    final FontSettings fontSettings,
  }) = _$ExportSettingsImpl;

  factory _ExportSettings.fromJson(Map<String, dynamic> json) =
      _$ExportSettingsImpl.fromJson;

  /// 默认导出格式
  @override
  ExportFormat get defaultFormat;

  /// 包含元数据
  @override
  bool get includeMetadata;

  /// 包含目录
  @override
  bool get includeTableOfContents;

  /// 页面设置
  @override
  PageSettings get pageSettings;

  /// 字体设置
  @override
  FontSettings get fontSettings;

  /// Create a copy of ExportSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ExportSettingsImplCopyWith<_$ExportSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PageSettings _$PageSettingsFromJson(Map<String, dynamic> json) {
  return _PageSettings.fromJson(json);
}

/// @nodoc
mixin _$PageSettings {
  /// 页面大小
  String get pageSize => throw _privateConstructorUsedError;

  /// 页边距
  PageMargins get margins => throw _privateConstructorUsedError;

  /// 行间距
  double get lineSpacing => throw _privateConstructorUsedError;

  /// Serializes this PageSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PageSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PageSettingsCopyWith<PageSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PageSettingsCopyWith<$Res> {
  factory $PageSettingsCopyWith(
    PageSettings value,
    $Res Function(PageSettings) then,
  ) = _$PageSettingsCopyWithImpl<$Res, PageSettings>;
  @useResult
  $Res call({String pageSize, PageMargins margins, double lineSpacing});

  $PageMarginsCopyWith<$Res> get margins;
}

/// @nodoc
class _$PageSettingsCopyWithImpl<$Res, $Val extends PageSettings>
    implements $PageSettingsCopyWith<$Res> {
  _$PageSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PageSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageSize = null,
    Object? margins = null,
    Object? lineSpacing = null,
  }) {
    return _then(
      _value.copyWith(
            pageSize: null == pageSize
                ? _value.pageSize
                : pageSize // ignore: cast_nullable_to_non_nullable
                      as String,
            margins: null == margins
                ? _value.margins
                : margins // ignore: cast_nullable_to_non_nullable
                      as PageMargins,
            lineSpacing: null == lineSpacing
                ? _value.lineSpacing
                : lineSpacing // ignore: cast_nullable_to_non_nullable
                      as double,
          )
          as $Val,
    );
  }

  /// Create a copy of PageSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PageMarginsCopyWith<$Res> get margins {
    return $PageMarginsCopyWith<$Res>(_value.margins, (value) {
      return _then(_value.copyWith(margins: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PageSettingsImplCopyWith<$Res>
    implements $PageSettingsCopyWith<$Res> {
  factory _$$PageSettingsImplCopyWith(
    _$PageSettingsImpl value,
    $Res Function(_$PageSettingsImpl) then,
  ) = __$$PageSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String pageSize, PageMargins margins, double lineSpacing});

  @override
  $PageMarginsCopyWith<$Res> get margins;
}

/// @nodoc
class __$$PageSettingsImplCopyWithImpl<$Res>
    extends _$PageSettingsCopyWithImpl<$Res, _$PageSettingsImpl>
    implements _$$PageSettingsImplCopyWith<$Res> {
  __$$PageSettingsImplCopyWithImpl(
    _$PageSettingsImpl _value,
    $Res Function(_$PageSettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PageSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageSize = null,
    Object? margins = null,
    Object? lineSpacing = null,
  }) {
    return _then(
      _$PageSettingsImpl(
        pageSize: null == pageSize
            ? _value.pageSize
            : pageSize // ignore: cast_nullable_to_non_nullable
                  as String,
        margins: null == margins
            ? _value.margins
            : margins // ignore: cast_nullable_to_non_nullable
                  as PageMargins,
        lineSpacing: null == lineSpacing
            ? _value.lineSpacing
            : lineSpacing // ignore: cast_nullable_to_non_nullable
                  as double,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PageSettingsImpl implements _PageSettings {
  const _$PageSettingsImpl({
    this.pageSize = 'A4',
    this.margins = const PageMargins(),
    this.lineSpacing = 1.5,
  });

  factory _$PageSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$PageSettingsImplFromJson(json);

  /// 页面大小
  @override
  @JsonKey()
  final String pageSize;

  /// 页边距
  @override
  @JsonKey()
  final PageMargins margins;

  /// 行间距
  @override
  @JsonKey()
  final double lineSpacing;

  @override
  String toString() {
    return 'PageSettings(pageSize: $pageSize, margins: $margins, lineSpacing: $lineSpacing)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PageSettingsImpl &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.margins, margins) || other.margins == margins) &&
            (identical(other.lineSpacing, lineSpacing) ||
                other.lineSpacing == lineSpacing));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, pageSize, margins, lineSpacing);

  /// Create a copy of PageSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PageSettingsImplCopyWith<_$PageSettingsImpl> get copyWith =>
      __$$PageSettingsImplCopyWithImpl<_$PageSettingsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PageSettingsImplToJson(this);
  }
}

abstract class _PageSettings implements PageSettings {
  const factory _PageSettings({
    final String pageSize,
    final PageMargins margins,
    final double lineSpacing,
  }) = _$PageSettingsImpl;

  factory _PageSettings.fromJson(Map<String, dynamic> json) =
      _$PageSettingsImpl.fromJson;

  /// 页面大小
  @override
  String get pageSize;

  /// 页边距
  @override
  PageMargins get margins;

  /// 行间距
  @override
  double get lineSpacing;

  /// Create a copy of PageSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PageSettingsImplCopyWith<_$PageSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PageMargins _$PageMarginsFromJson(Map<String, dynamic> json) {
  return _PageMargins.fromJson(json);
}

/// @nodoc
mixin _$PageMargins {
  double get top => throw _privateConstructorUsedError;
  double get bottom => throw _privateConstructorUsedError;
  double get left => throw _privateConstructorUsedError;
  double get right => throw _privateConstructorUsedError;

  /// Serializes this PageMargins to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PageMargins
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PageMarginsCopyWith<PageMargins> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PageMarginsCopyWith<$Res> {
  factory $PageMarginsCopyWith(
    PageMargins value,
    $Res Function(PageMargins) then,
  ) = _$PageMarginsCopyWithImpl<$Res, PageMargins>;
  @useResult
  $Res call({double top, double bottom, double left, double right});
}

/// @nodoc
class _$PageMarginsCopyWithImpl<$Res, $Val extends PageMargins>
    implements $PageMarginsCopyWith<$Res> {
  _$PageMarginsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PageMargins
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? top = null,
    Object? bottom = null,
    Object? left = null,
    Object? right = null,
  }) {
    return _then(
      _value.copyWith(
            top: null == top
                ? _value.top
                : top // ignore: cast_nullable_to_non_nullable
                      as double,
            bottom: null == bottom
                ? _value.bottom
                : bottom // ignore: cast_nullable_to_non_nullable
                      as double,
            left: null == left
                ? _value.left
                : left // ignore: cast_nullable_to_non_nullable
                      as double,
            right: null == right
                ? _value.right
                : right // ignore: cast_nullable_to_non_nullable
                      as double,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PageMarginsImplCopyWith<$Res>
    implements $PageMarginsCopyWith<$Res> {
  factory _$$PageMarginsImplCopyWith(
    _$PageMarginsImpl value,
    $Res Function(_$PageMarginsImpl) then,
  ) = __$$PageMarginsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double top, double bottom, double left, double right});
}

/// @nodoc
class __$$PageMarginsImplCopyWithImpl<$Res>
    extends _$PageMarginsCopyWithImpl<$Res, _$PageMarginsImpl>
    implements _$$PageMarginsImplCopyWith<$Res> {
  __$$PageMarginsImplCopyWithImpl(
    _$PageMarginsImpl _value,
    $Res Function(_$PageMarginsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PageMargins
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? top = null,
    Object? bottom = null,
    Object? left = null,
    Object? right = null,
  }) {
    return _then(
      _$PageMarginsImpl(
        top: null == top
            ? _value.top
            : top // ignore: cast_nullable_to_non_nullable
                  as double,
        bottom: null == bottom
            ? _value.bottom
            : bottom // ignore: cast_nullable_to_non_nullable
                  as double,
        left: null == left
            ? _value.left
            : left // ignore: cast_nullable_to_non_nullable
                  as double,
        right: null == right
            ? _value.right
            : right // ignore: cast_nullable_to_non_nullable
                  as double,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PageMarginsImpl implements _PageMargins {
  const _$PageMarginsImpl({
    this.top = 2.5,
    this.bottom = 2.5,
    this.left = 2.0,
    this.right = 2.0,
  });

  factory _$PageMarginsImpl.fromJson(Map<String, dynamic> json) =>
      _$$PageMarginsImplFromJson(json);

  @override
  @JsonKey()
  final double top;
  @override
  @JsonKey()
  final double bottom;
  @override
  @JsonKey()
  final double left;
  @override
  @JsonKey()
  final double right;

  @override
  String toString() {
    return 'PageMargins(top: $top, bottom: $bottom, left: $left, right: $right)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PageMarginsImpl &&
            (identical(other.top, top) || other.top == top) &&
            (identical(other.bottom, bottom) || other.bottom == bottom) &&
            (identical(other.left, left) || other.left == left) &&
            (identical(other.right, right) || other.right == right));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, top, bottom, left, right);

  /// Create a copy of PageMargins
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PageMarginsImplCopyWith<_$PageMarginsImpl> get copyWith =>
      __$$PageMarginsImplCopyWithImpl<_$PageMarginsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PageMarginsImplToJson(this);
  }
}

abstract class _PageMargins implements PageMargins {
  const factory _PageMargins({
    final double top,
    final double bottom,
    final double left,
    final double right,
  }) = _$PageMarginsImpl;

  factory _PageMargins.fromJson(Map<String, dynamic> json) =
      _$PageMarginsImpl.fromJson;

  @override
  double get top;
  @override
  double get bottom;
  @override
  double get left;
  @override
  double get right;

  /// Create a copy of PageMargins
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PageMarginsImplCopyWith<_$PageMarginsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FontSettings _$FontSettingsFromJson(Map<String, dynamic> json) {
  return _FontSettings.fromJson(json);
}

/// @nodoc
mixin _$FontSettings {
  /// 字体族
  String get fontFamily => throw _privateConstructorUsedError;

  /// 字体大小
  int get fontSize => throw _privateConstructorUsedError;

  /// 是否粗体
  bool get bold => throw _privateConstructorUsedError;

  /// 是否斜体
  bool get italic => throw _privateConstructorUsedError;

  /// Serializes this FontSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FontSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FontSettingsCopyWith<FontSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FontSettingsCopyWith<$Res> {
  factory $FontSettingsCopyWith(
    FontSettings value,
    $Res Function(FontSettings) then,
  ) = _$FontSettingsCopyWithImpl<$Res, FontSettings>;
  @useResult
  $Res call({String fontFamily, int fontSize, bool bold, bool italic});
}

/// @nodoc
class _$FontSettingsCopyWithImpl<$Res, $Val extends FontSettings>
    implements $FontSettingsCopyWith<$Res> {
  _$FontSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FontSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fontFamily = null,
    Object? fontSize = null,
    Object? bold = null,
    Object? italic = null,
  }) {
    return _then(
      _value.copyWith(
            fontFamily: null == fontFamily
                ? _value.fontFamily
                : fontFamily // ignore: cast_nullable_to_non_nullable
                      as String,
            fontSize: null == fontSize
                ? _value.fontSize
                : fontSize // ignore: cast_nullable_to_non_nullable
                      as int,
            bold: null == bold
                ? _value.bold
                : bold // ignore: cast_nullable_to_non_nullable
                      as bool,
            italic: null == italic
                ? _value.italic
                : italic // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$FontSettingsImplCopyWith<$Res>
    implements $FontSettingsCopyWith<$Res> {
  factory _$$FontSettingsImplCopyWith(
    _$FontSettingsImpl value,
    $Res Function(_$FontSettingsImpl) then,
  ) = __$$FontSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String fontFamily, int fontSize, bool bold, bool italic});
}

/// @nodoc
class __$$FontSettingsImplCopyWithImpl<$Res>
    extends _$FontSettingsCopyWithImpl<$Res, _$FontSettingsImpl>
    implements _$$FontSettingsImplCopyWith<$Res> {
  __$$FontSettingsImplCopyWithImpl(
    _$FontSettingsImpl _value,
    $Res Function(_$FontSettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FontSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fontFamily = null,
    Object? fontSize = null,
    Object? bold = null,
    Object? italic = null,
  }) {
    return _then(
      _$FontSettingsImpl(
        fontFamily: null == fontFamily
            ? _value.fontFamily
            : fontFamily // ignore: cast_nullable_to_non_nullable
                  as String,
        fontSize: null == fontSize
            ? _value.fontSize
            : fontSize // ignore: cast_nullable_to_non_nullable
                  as int,
        bold: null == bold
            ? _value.bold
            : bold // ignore: cast_nullable_to_non_nullable
                  as bool,
        italic: null == italic
            ? _value.italic
            : italic // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$FontSettingsImpl implements _FontSettings {
  const _$FontSettingsImpl({
    this.fontFamily = 'Times New Roman',
    this.fontSize = 12,
    this.bold = false,
    this.italic = false,
  });

  factory _$FontSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$FontSettingsImplFromJson(json);

  /// 字体族
  @override
  @JsonKey()
  final String fontFamily;

  /// 字体大小
  @override
  @JsonKey()
  final int fontSize;

  /// 是否粗体
  @override
  @JsonKey()
  final bool bold;

  /// 是否斜体
  @override
  @JsonKey()
  final bool italic;

  @override
  String toString() {
    return 'FontSettings(fontFamily: $fontFamily, fontSize: $fontSize, bold: $bold, italic: $italic)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FontSettingsImpl &&
            (identical(other.fontFamily, fontFamily) ||
                other.fontFamily == fontFamily) &&
            (identical(other.fontSize, fontSize) ||
                other.fontSize == fontSize) &&
            (identical(other.bold, bold) || other.bold == bold) &&
            (identical(other.italic, italic) || other.italic == italic));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, fontFamily, fontSize, bold, italic);

  /// Create a copy of FontSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FontSettingsImplCopyWith<_$FontSettingsImpl> get copyWith =>
      __$$FontSettingsImplCopyWithImpl<_$FontSettingsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FontSettingsImplToJson(this);
  }
}

abstract class _FontSettings implements FontSettings {
  const factory _FontSettings({
    final String fontFamily,
    final int fontSize,
    final bool bold,
    final bool italic,
  }) = _$FontSettingsImpl;

  factory _FontSettings.fromJson(Map<String, dynamic> json) =
      _$FontSettingsImpl.fromJson;

  /// 字体族
  @override
  String get fontFamily;

  /// 字体大小
  @override
  int get fontSize;

  /// 是否粗体
  @override
  bool get bold;

  /// 是否斜体
  @override
  bool get italic;

  /// Create a copy of FontSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FontSettingsImplCopyWith<_$FontSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BackupSettings _$BackupSettingsFromJson(Map<String, dynamic> json) {
  return _BackupSettings.fromJson(json);
}

/// @nodoc
mixin _$BackupSettings {
  /// 是否启用自动备份
  bool get autoBackup => throw _privateConstructorUsedError;

  /// 备份频率（小时）
  int get backupFrequency => throw _privateConstructorUsedError;

  /// 保留备份数量
  int get keepBackupCount => throw _privateConstructorUsedError;

  /// 备份位置
  String get backupLocation => throw _privateConstructorUsedError;

  /// 云备份设置
  CloudBackupSettings get cloudBackup => throw _privateConstructorUsedError;

  /// Serializes this BackupSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BackupSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BackupSettingsCopyWith<BackupSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BackupSettingsCopyWith<$Res> {
  factory $BackupSettingsCopyWith(
    BackupSettings value,
    $Res Function(BackupSettings) then,
  ) = _$BackupSettingsCopyWithImpl<$Res, BackupSettings>;
  @useResult
  $Res call({
    bool autoBackup,
    int backupFrequency,
    int keepBackupCount,
    String backupLocation,
    CloudBackupSettings cloudBackup,
  });

  $CloudBackupSettingsCopyWith<$Res> get cloudBackup;
}

/// @nodoc
class _$BackupSettingsCopyWithImpl<$Res, $Val extends BackupSettings>
    implements $BackupSettingsCopyWith<$Res> {
  _$BackupSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BackupSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? autoBackup = null,
    Object? backupFrequency = null,
    Object? keepBackupCount = null,
    Object? backupLocation = null,
    Object? cloudBackup = null,
  }) {
    return _then(
      _value.copyWith(
            autoBackup: null == autoBackup
                ? _value.autoBackup
                : autoBackup // ignore: cast_nullable_to_non_nullable
                      as bool,
            backupFrequency: null == backupFrequency
                ? _value.backupFrequency
                : backupFrequency // ignore: cast_nullable_to_non_nullable
                      as int,
            keepBackupCount: null == keepBackupCount
                ? _value.keepBackupCount
                : keepBackupCount // ignore: cast_nullable_to_non_nullable
                      as int,
            backupLocation: null == backupLocation
                ? _value.backupLocation
                : backupLocation // ignore: cast_nullable_to_non_nullable
                      as String,
            cloudBackup: null == cloudBackup
                ? _value.cloudBackup
                : cloudBackup // ignore: cast_nullable_to_non_nullable
                      as CloudBackupSettings,
          )
          as $Val,
    );
  }

  /// Create a copy of BackupSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CloudBackupSettingsCopyWith<$Res> get cloudBackup {
    return $CloudBackupSettingsCopyWith<$Res>(_value.cloudBackup, (value) {
      return _then(_value.copyWith(cloudBackup: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BackupSettingsImplCopyWith<$Res>
    implements $BackupSettingsCopyWith<$Res> {
  factory _$$BackupSettingsImplCopyWith(
    _$BackupSettingsImpl value,
    $Res Function(_$BackupSettingsImpl) then,
  ) = __$$BackupSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    bool autoBackup,
    int backupFrequency,
    int keepBackupCount,
    String backupLocation,
    CloudBackupSettings cloudBackup,
  });

  @override
  $CloudBackupSettingsCopyWith<$Res> get cloudBackup;
}

/// @nodoc
class __$$BackupSettingsImplCopyWithImpl<$Res>
    extends _$BackupSettingsCopyWithImpl<$Res, _$BackupSettingsImpl>
    implements _$$BackupSettingsImplCopyWith<$Res> {
  __$$BackupSettingsImplCopyWithImpl(
    _$BackupSettingsImpl _value,
    $Res Function(_$BackupSettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of BackupSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? autoBackup = null,
    Object? backupFrequency = null,
    Object? keepBackupCount = null,
    Object? backupLocation = null,
    Object? cloudBackup = null,
  }) {
    return _then(
      _$BackupSettingsImpl(
        autoBackup: null == autoBackup
            ? _value.autoBackup
            : autoBackup // ignore: cast_nullable_to_non_nullable
                  as bool,
        backupFrequency: null == backupFrequency
            ? _value.backupFrequency
            : backupFrequency // ignore: cast_nullable_to_non_nullable
                  as int,
        keepBackupCount: null == keepBackupCount
            ? _value.keepBackupCount
            : keepBackupCount // ignore: cast_nullable_to_non_nullable
                  as int,
        backupLocation: null == backupLocation
            ? _value.backupLocation
            : backupLocation // ignore: cast_nullable_to_non_nullable
                  as String,
        cloudBackup: null == cloudBackup
            ? _value.cloudBackup
            : cloudBackup // ignore: cast_nullable_to_non_nullable
                  as CloudBackupSettings,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$BackupSettingsImpl implements _BackupSettings {
  const _$BackupSettingsImpl({
    this.autoBackup = true,
    this.backupFrequency = 24,
    this.keepBackupCount = 10,
    this.backupLocation = '',
    this.cloudBackup = const CloudBackupSettings(),
  });

  factory _$BackupSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$BackupSettingsImplFromJson(json);

  /// 是否启用自动备份
  @override
  @JsonKey()
  final bool autoBackup;

  /// 备份频率（小时）
  @override
  @JsonKey()
  final int backupFrequency;

  /// 保留备份数量
  @override
  @JsonKey()
  final int keepBackupCount;

  /// 备份位置
  @override
  @JsonKey()
  final String backupLocation;

  /// 云备份设置
  @override
  @JsonKey()
  final CloudBackupSettings cloudBackup;

  @override
  String toString() {
    return 'BackupSettings(autoBackup: $autoBackup, backupFrequency: $backupFrequency, keepBackupCount: $keepBackupCount, backupLocation: $backupLocation, cloudBackup: $cloudBackup)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BackupSettingsImpl &&
            (identical(other.autoBackup, autoBackup) ||
                other.autoBackup == autoBackup) &&
            (identical(other.backupFrequency, backupFrequency) ||
                other.backupFrequency == backupFrequency) &&
            (identical(other.keepBackupCount, keepBackupCount) ||
                other.keepBackupCount == keepBackupCount) &&
            (identical(other.backupLocation, backupLocation) ||
                other.backupLocation == backupLocation) &&
            (identical(other.cloudBackup, cloudBackup) ||
                other.cloudBackup == cloudBackup));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    autoBackup,
    backupFrequency,
    keepBackupCount,
    backupLocation,
    cloudBackup,
  );

  /// Create a copy of BackupSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BackupSettingsImplCopyWith<_$BackupSettingsImpl> get copyWith =>
      __$$BackupSettingsImplCopyWithImpl<_$BackupSettingsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$BackupSettingsImplToJson(this);
  }
}

abstract class _BackupSettings implements BackupSettings {
  const factory _BackupSettings({
    final bool autoBackup,
    final int backupFrequency,
    final int keepBackupCount,
    final String backupLocation,
    final CloudBackupSettings cloudBackup,
  }) = _$BackupSettingsImpl;

  factory _BackupSettings.fromJson(Map<String, dynamic> json) =
      _$BackupSettingsImpl.fromJson;

  /// 是否启用自动备份
  @override
  bool get autoBackup;

  /// 备份频率（小时）
  @override
  int get backupFrequency;

  /// 保留备份数量
  @override
  int get keepBackupCount;

  /// 备份位置
  @override
  String get backupLocation;

  /// 云备份设置
  @override
  CloudBackupSettings get cloudBackup;

  /// Create a copy of BackupSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BackupSettingsImplCopyWith<_$BackupSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CloudBackupSettings _$CloudBackupSettingsFromJson(Map<String, dynamic> json) {
  return _CloudBackupSettings.fromJson(json);
}

/// @nodoc
mixin _$CloudBackupSettings {
  /// 是否启用云备份
  bool get enabled => throw _privateConstructorUsedError;

  /// 云服务提供商
  CloudProvider get provider => throw _privateConstructorUsedError;

  /// 访问令牌
  String? get accessToken => throw _privateConstructorUsedError;

  /// 备份路径
  String get backupPath => throw _privateConstructorUsedError;

  /// Serializes this CloudBackupSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CloudBackupSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CloudBackupSettingsCopyWith<CloudBackupSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CloudBackupSettingsCopyWith<$Res> {
  factory $CloudBackupSettingsCopyWith(
    CloudBackupSettings value,
    $Res Function(CloudBackupSettings) then,
  ) = _$CloudBackupSettingsCopyWithImpl<$Res, CloudBackupSettings>;
  @useResult
  $Res call({
    bool enabled,
    CloudProvider provider,
    String? accessToken,
    String backupPath,
  });
}

/// @nodoc
class _$CloudBackupSettingsCopyWithImpl<$Res, $Val extends CloudBackupSettings>
    implements $CloudBackupSettingsCopyWith<$Res> {
  _$CloudBackupSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CloudBackupSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enabled = null,
    Object? provider = null,
    Object? accessToken = freezed,
    Object? backupPath = null,
  }) {
    return _then(
      _value.copyWith(
            enabled: null == enabled
                ? _value.enabled
                : enabled // ignore: cast_nullable_to_non_nullable
                      as bool,
            provider: null == provider
                ? _value.provider
                : provider // ignore: cast_nullable_to_non_nullable
                      as CloudProvider,
            accessToken: freezed == accessToken
                ? _value.accessToken
                : accessToken // ignore: cast_nullable_to_non_nullable
                      as String?,
            backupPath: null == backupPath
                ? _value.backupPath
                : backupPath // ignore: cast_nullable_to_non_nullable
                      as String,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CloudBackupSettingsImplCopyWith<$Res>
    implements $CloudBackupSettingsCopyWith<$Res> {
  factory _$$CloudBackupSettingsImplCopyWith(
    _$CloudBackupSettingsImpl value,
    $Res Function(_$CloudBackupSettingsImpl) then,
  ) = __$$CloudBackupSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    bool enabled,
    CloudProvider provider,
    String? accessToken,
    String backupPath,
  });
}

/// @nodoc
class __$$CloudBackupSettingsImplCopyWithImpl<$Res>
    extends _$CloudBackupSettingsCopyWithImpl<$Res, _$CloudBackupSettingsImpl>
    implements _$$CloudBackupSettingsImplCopyWith<$Res> {
  __$$CloudBackupSettingsImplCopyWithImpl(
    _$CloudBackupSettingsImpl _value,
    $Res Function(_$CloudBackupSettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CloudBackupSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enabled = null,
    Object? provider = null,
    Object? accessToken = freezed,
    Object? backupPath = null,
  }) {
    return _then(
      _$CloudBackupSettingsImpl(
        enabled: null == enabled
            ? _value.enabled
            : enabled // ignore: cast_nullable_to_non_nullable
                  as bool,
        provider: null == provider
            ? _value.provider
            : provider // ignore: cast_nullable_to_non_nullable
                  as CloudProvider,
        accessToken: freezed == accessToken
            ? _value.accessToken
            : accessToken // ignore: cast_nullable_to_non_nullable
                  as String?,
        backupPath: null == backupPath
            ? _value.backupPath
            : backupPath // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CloudBackupSettingsImpl implements _CloudBackupSettings {
  const _$CloudBackupSettingsImpl({
    this.enabled = false,
    this.provider = CloudProvider.none,
    this.accessToken,
    this.backupPath = '',
  });

  factory _$CloudBackupSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$CloudBackupSettingsImplFromJson(json);

  /// 是否启用云备份
  @override
  @JsonKey()
  final bool enabled;

  /// 云服务提供商
  @override
  @JsonKey()
  final CloudProvider provider;

  /// 访问令牌
  @override
  final String? accessToken;

  /// 备份路径
  @override
  @JsonKey()
  final String backupPath;

  @override
  String toString() {
    return 'CloudBackupSettings(enabled: $enabled, provider: $provider, accessToken: $accessToken, backupPath: $backupPath)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CloudBackupSettingsImpl &&
            (identical(other.enabled, enabled) || other.enabled == enabled) &&
            (identical(other.provider, provider) ||
                other.provider == provider) &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.backupPath, backupPath) ||
                other.backupPath == backupPath));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, enabled, provider, accessToken, backupPath);

  /// Create a copy of CloudBackupSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CloudBackupSettingsImplCopyWith<_$CloudBackupSettingsImpl> get copyWith =>
      __$$CloudBackupSettingsImplCopyWithImpl<_$CloudBackupSettingsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CloudBackupSettingsImplToJson(this);
  }
}

abstract class _CloudBackupSettings implements CloudBackupSettings {
  const factory _CloudBackupSettings({
    final bool enabled,
    final CloudProvider provider,
    final String? accessToken,
    final String backupPath,
  }) = _$CloudBackupSettingsImpl;

  factory _CloudBackupSettings.fromJson(Map<String, dynamic> json) =
      _$CloudBackupSettingsImpl.fromJson;

  /// 是否启用云备份
  @override
  bool get enabled;

  /// 云服务提供商
  @override
  CloudProvider get provider;

  /// 访问令牌
  @override
  String? get accessToken;

  /// 备份路径
  @override
  String get backupPath;

  /// Create a copy of CloudBackupSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CloudBackupSettingsImplCopyWith<_$CloudBackupSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CollaborationSettings _$CollaborationSettingsFromJson(
  Map<String, dynamic> json,
) {
  return _CollaborationSettings.fromJson(json);
}

/// @nodoc
mixin _$CollaborationSettings {
  /// 是否启用协作
  bool get enabled => throw _privateConstructorUsedError;

  /// 协作者列表
  List<Collaborator> get collaborators => throw _privateConstructorUsedError;

  /// 权限设置
  PermissionSettings get permissions => throw _privateConstructorUsedError;

  /// Serializes this CollaborationSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CollaborationSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CollaborationSettingsCopyWith<CollaborationSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CollaborationSettingsCopyWith<$Res> {
  factory $CollaborationSettingsCopyWith(
    CollaborationSettings value,
    $Res Function(CollaborationSettings) then,
  ) = _$CollaborationSettingsCopyWithImpl<$Res, CollaborationSettings>;
  @useResult
  $Res call({
    bool enabled,
    List<Collaborator> collaborators,
    PermissionSettings permissions,
  });

  $PermissionSettingsCopyWith<$Res> get permissions;
}

/// @nodoc
class _$CollaborationSettingsCopyWithImpl<
  $Res,
  $Val extends CollaborationSettings
>
    implements $CollaborationSettingsCopyWith<$Res> {
  _$CollaborationSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CollaborationSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enabled = null,
    Object? collaborators = null,
    Object? permissions = null,
  }) {
    return _then(
      _value.copyWith(
            enabled: null == enabled
                ? _value.enabled
                : enabled // ignore: cast_nullable_to_non_nullable
                      as bool,
            collaborators: null == collaborators
                ? _value.collaborators
                : collaborators // ignore: cast_nullable_to_non_nullable
                      as List<Collaborator>,
            permissions: null == permissions
                ? _value.permissions
                : permissions // ignore: cast_nullable_to_non_nullable
                      as PermissionSettings,
          )
          as $Val,
    );
  }

  /// Create a copy of CollaborationSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PermissionSettingsCopyWith<$Res> get permissions {
    return $PermissionSettingsCopyWith<$Res>(_value.permissions, (value) {
      return _then(_value.copyWith(permissions: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CollaborationSettingsImplCopyWith<$Res>
    implements $CollaborationSettingsCopyWith<$Res> {
  factory _$$CollaborationSettingsImplCopyWith(
    _$CollaborationSettingsImpl value,
    $Res Function(_$CollaborationSettingsImpl) then,
  ) = __$$CollaborationSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    bool enabled,
    List<Collaborator> collaborators,
    PermissionSettings permissions,
  });

  @override
  $PermissionSettingsCopyWith<$Res> get permissions;
}

/// @nodoc
class __$$CollaborationSettingsImplCopyWithImpl<$Res>
    extends
        _$CollaborationSettingsCopyWithImpl<$Res, _$CollaborationSettingsImpl>
    implements _$$CollaborationSettingsImplCopyWith<$Res> {
  __$$CollaborationSettingsImplCopyWithImpl(
    _$CollaborationSettingsImpl _value,
    $Res Function(_$CollaborationSettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CollaborationSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enabled = null,
    Object? collaborators = null,
    Object? permissions = null,
  }) {
    return _then(
      _$CollaborationSettingsImpl(
        enabled: null == enabled
            ? _value.enabled
            : enabled // ignore: cast_nullable_to_non_nullable
                  as bool,
        collaborators: null == collaborators
            ? _value._collaborators
            : collaborators // ignore: cast_nullable_to_non_nullable
                  as List<Collaborator>,
        permissions: null == permissions
            ? _value.permissions
            : permissions // ignore: cast_nullable_to_non_nullable
                  as PermissionSettings,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CollaborationSettingsImpl implements _CollaborationSettings {
  const _$CollaborationSettingsImpl({
    this.enabled = false,
    final List<Collaborator> collaborators = const [],
    this.permissions = const PermissionSettings(),
  }) : _collaborators = collaborators;

  factory _$CollaborationSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$CollaborationSettingsImplFromJson(json);

  /// 是否启用协作
  @override
  @JsonKey()
  final bool enabled;

  /// 协作者列表
  final List<Collaborator> _collaborators;

  /// 协作者列表
  @override
  @JsonKey()
  List<Collaborator> get collaborators {
    if (_collaborators is EqualUnmodifiableListView) return _collaborators;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_collaborators);
  }

  /// 权限设置
  @override
  @JsonKey()
  final PermissionSettings permissions;

  @override
  String toString() {
    return 'CollaborationSettings(enabled: $enabled, collaborators: $collaborators, permissions: $permissions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CollaborationSettingsImpl &&
            (identical(other.enabled, enabled) || other.enabled == enabled) &&
            const DeepCollectionEquality().equals(
              other._collaborators,
              _collaborators,
            ) &&
            (identical(other.permissions, permissions) ||
                other.permissions == permissions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    enabled,
    const DeepCollectionEquality().hash(_collaborators),
    permissions,
  );

  /// Create a copy of CollaborationSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CollaborationSettingsImplCopyWith<_$CollaborationSettingsImpl>
  get copyWith =>
      __$$CollaborationSettingsImplCopyWithImpl<_$CollaborationSettingsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CollaborationSettingsImplToJson(this);
  }
}

abstract class _CollaborationSettings implements CollaborationSettings {
  const factory _CollaborationSettings({
    final bool enabled,
    final List<Collaborator> collaborators,
    final PermissionSettings permissions,
  }) = _$CollaborationSettingsImpl;

  factory _CollaborationSettings.fromJson(Map<String, dynamic> json) =
      _$CollaborationSettingsImpl.fromJson;

  /// 是否启用协作
  @override
  bool get enabled;

  /// 协作者列表
  @override
  List<Collaborator> get collaborators;

  /// 权限设置
  @override
  PermissionSettings get permissions;

  /// Create a copy of CollaborationSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CollaborationSettingsImplCopyWith<_$CollaborationSettingsImpl>
  get copyWith => throw _privateConstructorUsedError;
}

Collaborator _$CollaboratorFromJson(Map<String, dynamic> json) {
  return _Collaborator.fromJson(json);
}

/// @nodoc
mixin _$Collaborator {
  /// 协作者ID
  String get id => throw _privateConstructorUsedError;

  /// 协作者姓名
  String get name => throw _privateConstructorUsedError;

  /// 协作者邮箱
  String get email => throw _privateConstructorUsedError;

  /// 协作者角色
  CollaboratorRole get role => throw _privateConstructorUsedError;

  /// 邀请时间
  DateTime get invitedAt => throw _privateConstructorUsedError;

  /// 接受邀请时间
  DateTime? get acceptedAt => throw _privateConstructorUsedError;

  /// 协作者状态
  CollaboratorStatus get status => throw _privateConstructorUsedError;

  /// Serializes this Collaborator to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Collaborator
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CollaboratorCopyWith<Collaborator> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CollaboratorCopyWith<$Res> {
  factory $CollaboratorCopyWith(
    Collaborator value,
    $Res Function(Collaborator) then,
  ) = _$CollaboratorCopyWithImpl<$Res, Collaborator>;
  @useResult
  $Res call({
    String id,
    String name,
    String email,
    CollaboratorRole role,
    DateTime invitedAt,
    DateTime? acceptedAt,
    CollaboratorStatus status,
  });
}

/// @nodoc
class _$CollaboratorCopyWithImpl<$Res, $Val extends Collaborator>
    implements $CollaboratorCopyWith<$Res> {
  _$CollaboratorCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Collaborator
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? email = null,
    Object? role = null,
    Object? invitedAt = null,
    Object? acceptedAt = freezed,
    Object? status = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            email: null == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String,
            role: null == role
                ? _value.role
                : role // ignore: cast_nullable_to_non_nullable
                      as CollaboratorRole,
            invitedAt: null == invitedAt
                ? _value.invitedAt
                : invitedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            acceptedAt: freezed == acceptedAt
                ? _value.acceptedAt
                : acceptedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as CollaboratorStatus,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CollaboratorImplCopyWith<$Res>
    implements $CollaboratorCopyWith<$Res> {
  factory _$$CollaboratorImplCopyWith(
    _$CollaboratorImpl value,
    $Res Function(_$CollaboratorImpl) then,
  ) = __$$CollaboratorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String email,
    CollaboratorRole role,
    DateTime invitedAt,
    DateTime? acceptedAt,
    CollaboratorStatus status,
  });
}

/// @nodoc
class __$$CollaboratorImplCopyWithImpl<$Res>
    extends _$CollaboratorCopyWithImpl<$Res, _$CollaboratorImpl>
    implements _$$CollaboratorImplCopyWith<$Res> {
  __$$CollaboratorImplCopyWithImpl(
    _$CollaboratorImpl _value,
    $Res Function(_$CollaboratorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Collaborator
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? email = null,
    Object? role = null,
    Object? invitedAt = null,
    Object? acceptedAt = freezed,
    Object? status = null,
  }) {
    return _then(
      _$CollaboratorImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        email: null == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String,
        role: null == role
            ? _value.role
            : role // ignore: cast_nullable_to_non_nullable
                  as CollaboratorRole,
        invitedAt: null == invitedAt
            ? _value.invitedAt
            : invitedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        acceptedAt: freezed == acceptedAt
            ? _value.acceptedAt
            : acceptedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as CollaboratorStatus,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CollaboratorImpl implements _Collaborator {
  const _$CollaboratorImpl({
    required this.id,
    required this.name,
    required this.email,
    this.role = CollaboratorRole.viewer,
    required this.invitedAt,
    this.acceptedAt,
    this.status = CollaboratorStatus.pending,
  });

  factory _$CollaboratorImpl.fromJson(Map<String, dynamic> json) =>
      _$$CollaboratorImplFromJson(json);

  /// 协作者ID
  @override
  final String id;

  /// 协作者姓名
  @override
  final String name;

  /// 协作者邮箱
  @override
  final String email;

  /// 协作者角色
  @override
  @JsonKey()
  final CollaboratorRole role;

  /// 邀请时间
  @override
  final DateTime invitedAt;

  /// 接受邀请时间
  @override
  final DateTime? acceptedAt;

  /// 协作者状态
  @override
  @JsonKey()
  final CollaboratorStatus status;

  @override
  String toString() {
    return 'Collaborator(id: $id, name: $name, email: $email, role: $role, invitedAt: $invitedAt, acceptedAt: $acceptedAt, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CollaboratorImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.invitedAt, invitedAt) ||
                other.invitedAt == invitedAt) &&
            (identical(other.acceptedAt, acceptedAt) ||
                other.acceptedAt == acceptedAt) &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    email,
    role,
    invitedAt,
    acceptedAt,
    status,
  );

  /// Create a copy of Collaborator
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CollaboratorImplCopyWith<_$CollaboratorImpl> get copyWith =>
      __$$CollaboratorImplCopyWithImpl<_$CollaboratorImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CollaboratorImplToJson(this);
  }
}

abstract class _Collaborator implements Collaborator {
  const factory _Collaborator({
    required final String id,
    required final String name,
    required final String email,
    final CollaboratorRole role,
    required final DateTime invitedAt,
    final DateTime? acceptedAt,
    final CollaboratorStatus status,
  }) = _$CollaboratorImpl;

  factory _Collaborator.fromJson(Map<String, dynamic> json) =
      _$CollaboratorImpl.fromJson;

  /// 协作者ID
  @override
  String get id;

  /// 协作者姓名
  @override
  String get name;

  /// 协作者邮箱
  @override
  String get email;

  /// 协作者角色
  @override
  CollaboratorRole get role;

  /// 邀请时间
  @override
  DateTime get invitedAt;

  /// 接受邀请时间
  @override
  DateTime? get acceptedAt;

  /// 协作者状态
  @override
  CollaboratorStatus get status;

  /// Create a copy of Collaborator
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CollaboratorImplCopyWith<_$CollaboratorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PermissionSettings _$PermissionSettingsFromJson(Map<String, dynamic> json) {
  return _PermissionSettings.fromJson(json);
}

/// @nodoc
mixin _$PermissionSettings {
  /// 允许编辑
  bool get allowEdit => throw _privateConstructorUsedError;

  /// 允许评论
  bool get allowComment => throw _privateConstructorUsedError;

  /// 允许导出
  bool get allowExport => throw _privateConstructorUsedError;

  /// 允许分享
  bool get allowShare => throw _privateConstructorUsedError;

  /// 允许删除
  bool get allowDelete => throw _privateConstructorUsedError;

  /// Serializes this PermissionSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PermissionSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PermissionSettingsCopyWith<PermissionSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PermissionSettingsCopyWith<$Res> {
  factory $PermissionSettingsCopyWith(
    PermissionSettings value,
    $Res Function(PermissionSettings) then,
  ) = _$PermissionSettingsCopyWithImpl<$Res, PermissionSettings>;
  @useResult
  $Res call({
    bool allowEdit,
    bool allowComment,
    bool allowExport,
    bool allowShare,
    bool allowDelete,
  });
}

/// @nodoc
class _$PermissionSettingsCopyWithImpl<$Res, $Val extends PermissionSettings>
    implements $PermissionSettingsCopyWith<$Res> {
  _$PermissionSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PermissionSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? allowEdit = null,
    Object? allowComment = null,
    Object? allowExport = null,
    Object? allowShare = null,
    Object? allowDelete = null,
  }) {
    return _then(
      _value.copyWith(
            allowEdit: null == allowEdit
                ? _value.allowEdit
                : allowEdit // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowComment: null == allowComment
                ? _value.allowComment
                : allowComment // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowExport: null == allowExport
                ? _value.allowExport
                : allowExport // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowShare: null == allowShare
                ? _value.allowShare
                : allowShare // ignore: cast_nullable_to_non_nullable
                      as bool,
            allowDelete: null == allowDelete
                ? _value.allowDelete
                : allowDelete // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PermissionSettingsImplCopyWith<$Res>
    implements $PermissionSettingsCopyWith<$Res> {
  factory _$$PermissionSettingsImplCopyWith(
    _$PermissionSettingsImpl value,
    $Res Function(_$PermissionSettingsImpl) then,
  ) = __$$PermissionSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    bool allowEdit,
    bool allowComment,
    bool allowExport,
    bool allowShare,
    bool allowDelete,
  });
}

/// @nodoc
class __$$PermissionSettingsImplCopyWithImpl<$Res>
    extends _$PermissionSettingsCopyWithImpl<$Res, _$PermissionSettingsImpl>
    implements _$$PermissionSettingsImplCopyWith<$Res> {
  __$$PermissionSettingsImplCopyWithImpl(
    _$PermissionSettingsImpl _value,
    $Res Function(_$PermissionSettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PermissionSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? allowEdit = null,
    Object? allowComment = null,
    Object? allowExport = null,
    Object? allowShare = null,
    Object? allowDelete = null,
  }) {
    return _then(
      _$PermissionSettingsImpl(
        allowEdit: null == allowEdit
            ? _value.allowEdit
            : allowEdit // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowComment: null == allowComment
            ? _value.allowComment
            : allowComment // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowExport: null == allowExport
            ? _value.allowExport
            : allowExport // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowShare: null == allowShare
            ? _value.allowShare
            : allowShare // ignore: cast_nullable_to_non_nullable
                  as bool,
        allowDelete: null == allowDelete
            ? _value.allowDelete
            : allowDelete // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PermissionSettingsImpl implements _PermissionSettings {
  const _$PermissionSettingsImpl({
    this.allowEdit = true,
    this.allowComment = true,
    this.allowExport = false,
    this.allowShare = false,
    this.allowDelete = false,
  });

  factory _$PermissionSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$PermissionSettingsImplFromJson(json);

  /// 允许编辑
  @override
  @JsonKey()
  final bool allowEdit;

  /// 允许评论
  @override
  @JsonKey()
  final bool allowComment;

  /// 允许导出
  @override
  @JsonKey()
  final bool allowExport;

  /// 允许分享
  @override
  @JsonKey()
  final bool allowShare;

  /// 允许删除
  @override
  @JsonKey()
  final bool allowDelete;

  @override
  String toString() {
    return 'PermissionSettings(allowEdit: $allowEdit, allowComment: $allowComment, allowExport: $allowExport, allowShare: $allowShare, allowDelete: $allowDelete)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PermissionSettingsImpl &&
            (identical(other.allowEdit, allowEdit) ||
                other.allowEdit == allowEdit) &&
            (identical(other.allowComment, allowComment) ||
                other.allowComment == allowComment) &&
            (identical(other.allowExport, allowExport) ||
                other.allowExport == allowExport) &&
            (identical(other.allowShare, allowShare) ||
                other.allowShare == allowShare) &&
            (identical(other.allowDelete, allowDelete) ||
                other.allowDelete == allowDelete));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    allowEdit,
    allowComment,
    allowExport,
    allowShare,
    allowDelete,
  );

  /// Create a copy of PermissionSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PermissionSettingsImplCopyWith<_$PermissionSettingsImpl> get copyWith =>
      __$$PermissionSettingsImplCopyWithImpl<_$PermissionSettingsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$PermissionSettingsImplToJson(this);
  }
}

abstract class _PermissionSettings implements PermissionSettings {
  const factory _PermissionSettings({
    final bool allowEdit,
    final bool allowComment,
    final bool allowExport,
    final bool allowShare,
    final bool allowDelete,
  }) = _$PermissionSettingsImpl;

  factory _PermissionSettings.fromJson(Map<String, dynamic> json) =
      _$PermissionSettingsImpl.fromJson;

  /// 允许编辑
  @override
  bool get allowEdit;

  /// 允许评论
  @override
  bool get allowComment;

  /// 允许导出
  @override
  bool get allowExport;

  /// 允许分享
  @override
  bool get allowShare;

  /// 允许删除
  @override
  bool get allowDelete;

  /// Create a copy of PermissionSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PermissionSettingsImplCopyWith<_$PermissionSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProjectStatistics _$ProjectStatisticsFromJson(Map<String, dynamic> json) {
  return _ProjectStatistics.fromJson(json);
}

/// @nodoc
mixin _$ProjectStatistics {
  /// 总字数
  int get totalWordCount => throw _privateConstructorUsedError;

  /// 总字符数
  int get totalCharacterCount => throw _privateConstructorUsedError;

  /// 章节数
  int get chapterCount => throw _privateConstructorUsedError;

  /// 版本数
  int get versionCount => throw _privateConstructorUsedError;

  /// 写作天数
  int get writingDays => throw _privateConstructorUsedError;

  /// 平均每日字数
  int get averageDailyWords => throw _privateConstructorUsedError;

  /// 最后写作时间
  DateTime? get lastWritingTime => throw _privateConstructorUsedError;

  /// 完成进度（0-100）
  double get completionProgress => throw _privateConstructorUsedError;

  /// 写作时长（分钟）
  int get writingTimeMinutes => throw _privateConstructorUsedError;

  /// 最高日写作字数
  int get maxDailyWords => throw _privateConstructorUsedError;

  /// 连续写作天数
  int get consecutiveWritingDays => throw _privateConstructorUsedError;

  /// Serializes this ProjectStatistics to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProjectStatistics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProjectStatisticsCopyWith<ProjectStatistics> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProjectStatisticsCopyWith<$Res> {
  factory $ProjectStatisticsCopyWith(
    ProjectStatistics value,
    $Res Function(ProjectStatistics) then,
  ) = _$ProjectStatisticsCopyWithImpl<$Res, ProjectStatistics>;
  @useResult
  $Res call({
    int totalWordCount,
    int totalCharacterCount,
    int chapterCount,
    int versionCount,
    int writingDays,
    int averageDailyWords,
    DateTime? lastWritingTime,
    double completionProgress,
    int writingTimeMinutes,
    int maxDailyWords,
    int consecutiveWritingDays,
  });
}

/// @nodoc
class _$ProjectStatisticsCopyWithImpl<$Res, $Val extends ProjectStatistics>
    implements $ProjectStatisticsCopyWith<$Res> {
  _$ProjectStatisticsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProjectStatistics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalWordCount = null,
    Object? totalCharacterCount = null,
    Object? chapterCount = null,
    Object? versionCount = null,
    Object? writingDays = null,
    Object? averageDailyWords = null,
    Object? lastWritingTime = freezed,
    Object? completionProgress = null,
    Object? writingTimeMinutes = null,
    Object? maxDailyWords = null,
    Object? consecutiveWritingDays = null,
  }) {
    return _then(
      _value.copyWith(
            totalWordCount: null == totalWordCount
                ? _value.totalWordCount
                : totalWordCount // ignore: cast_nullable_to_non_nullable
                      as int,
            totalCharacterCount: null == totalCharacterCount
                ? _value.totalCharacterCount
                : totalCharacterCount // ignore: cast_nullable_to_non_nullable
                      as int,
            chapterCount: null == chapterCount
                ? _value.chapterCount
                : chapterCount // ignore: cast_nullable_to_non_nullable
                      as int,
            versionCount: null == versionCount
                ? _value.versionCount
                : versionCount // ignore: cast_nullable_to_non_nullable
                      as int,
            writingDays: null == writingDays
                ? _value.writingDays
                : writingDays // ignore: cast_nullable_to_non_nullable
                      as int,
            averageDailyWords: null == averageDailyWords
                ? _value.averageDailyWords
                : averageDailyWords // ignore: cast_nullable_to_non_nullable
                      as int,
            lastWritingTime: freezed == lastWritingTime
                ? _value.lastWritingTime
                : lastWritingTime // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            completionProgress: null == completionProgress
                ? _value.completionProgress
                : completionProgress // ignore: cast_nullable_to_non_nullable
                      as double,
            writingTimeMinutes: null == writingTimeMinutes
                ? _value.writingTimeMinutes
                : writingTimeMinutes // ignore: cast_nullable_to_non_nullable
                      as int,
            maxDailyWords: null == maxDailyWords
                ? _value.maxDailyWords
                : maxDailyWords // ignore: cast_nullable_to_non_nullable
                      as int,
            consecutiveWritingDays: null == consecutiveWritingDays
                ? _value.consecutiveWritingDays
                : consecutiveWritingDays // ignore: cast_nullable_to_non_nullable
                      as int,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ProjectStatisticsImplCopyWith<$Res>
    implements $ProjectStatisticsCopyWith<$Res> {
  factory _$$ProjectStatisticsImplCopyWith(
    _$ProjectStatisticsImpl value,
    $Res Function(_$ProjectStatisticsImpl) then,
  ) = __$$ProjectStatisticsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int totalWordCount,
    int totalCharacterCount,
    int chapterCount,
    int versionCount,
    int writingDays,
    int averageDailyWords,
    DateTime? lastWritingTime,
    double completionProgress,
    int writingTimeMinutes,
    int maxDailyWords,
    int consecutiveWritingDays,
  });
}

/// @nodoc
class __$$ProjectStatisticsImplCopyWithImpl<$Res>
    extends _$ProjectStatisticsCopyWithImpl<$Res, _$ProjectStatisticsImpl>
    implements _$$ProjectStatisticsImplCopyWith<$Res> {
  __$$ProjectStatisticsImplCopyWithImpl(
    _$ProjectStatisticsImpl _value,
    $Res Function(_$ProjectStatisticsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ProjectStatistics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalWordCount = null,
    Object? totalCharacterCount = null,
    Object? chapterCount = null,
    Object? versionCount = null,
    Object? writingDays = null,
    Object? averageDailyWords = null,
    Object? lastWritingTime = freezed,
    Object? completionProgress = null,
    Object? writingTimeMinutes = null,
    Object? maxDailyWords = null,
    Object? consecutiveWritingDays = null,
  }) {
    return _then(
      _$ProjectStatisticsImpl(
        totalWordCount: null == totalWordCount
            ? _value.totalWordCount
            : totalWordCount // ignore: cast_nullable_to_non_nullable
                  as int,
        totalCharacterCount: null == totalCharacterCount
            ? _value.totalCharacterCount
            : totalCharacterCount // ignore: cast_nullable_to_non_nullable
                  as int,
        chapterCount: null == chapterCount
            ? _value.chapterCount
            : chapterCount // ignore: cast_nullable_to_non_nullable
                  as int,
        versionCount: null == versionCount
            ? _value.versionCount
            : versionCount // ignore: cast_nullable_to_non_nullable
                  as int,
        writingDays: null == writingDays
            ? _value.writingDays
            : writingDays // ignore: cast_nullable_to_non_nullable
                  as int,
        averageDailyWords: null == averageDailyWords
            ? _value.averageDailyWords
            : averageDailyWords // ignore: cast_nullable_to_non_nullable
                  as int,
        lastWritingTime: freezed == lastWritingTime
            ? _value.lastWritingTime
            : lastWritingTime // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        completionProgress: null == completionProgress
            ? _value.completionProgress
            : completionProgress // ignore: cast_nullable_to_non_nullable
                  as double,
        writingTimeMinutes: null == writingTimeMinutes
            ? _value.writingTimeMinutes
            : writingTimeMinutes // ignore: cast_nullable_to_non_nullable
                  as int,
        maxDailyWords: null == maxDailyWords
            ? _value.maxDailyWords
            : maxDailyWords // ignore: cast_nullable_to_non_nullable
                  as int,
        consecutiveWritingDays: null == consecutiveWritingDays
            ? _value.consecutiveWritingDays
            : consecutiveWritingDays // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProjectStatisticsImpl implements _ProjectStatistics {
  const _$ProjectStatisticsImpl({
    this.totalWordCount = 0,
    this.totalCharacterCount = 0,
    this.chapterCount = 0,
    this.versionCount = 0,
    this.writingDays = 0,
    this.averageDailyWords = 0,
    this.lastWritingTime,
    this.completionProgress = 0.0,
    this.writingTimeMinutes = 0,
    this.maxDailyWords = 0,
    this.consecutiveWritingDays = 0,
  });

  factory _$ProjectStatisticsImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProjectStatisticsImplFromJson(json);

  /// 总字数
  @override
  @JsonKey()
  final int totalWordCount;

  /// 总字符数
  @override
  @JsonKey()
  final int totalCharacterCount;

  /// 章节数
  @override
  @JsonKey()
  final int chapterCount;

  /// 版本数
  @override
  @JsonKey()
  final int versionCount;

  /// 写作天数
  @override
  @JsonKey()
  final int writingDays;

  /// 平均每日字数
  @override
  @JsonKey()
  final int averageDailyWords;

  /// 最后写作时间
  @override
  final DateTime? lastWritingTime;

  /// 完成进度（0-100）
  @override
  @JsonKey()
  final double completionProgress;

  /// 写作时长（分钟）
  @override
  @JsonKey()
  final int writingTimeMinutes;

  /// 最高日写作字数
  @override
  @JsonKey()
  final int maxDailyWords;

  /// 连续写作天数
  @override
  @JsonKey()
  final int consecutiveWritingDays;

  @override
  String toString() {
    return 'ProjectStatistics(totalWordCount: $totalWordCount, totalCharacterCount: $totalCharacterCount, chapterCount: $chapterCount, versionCount: $versionCount, writingDays: $writingDays, averageDailyWords: $averageDailyWords, lastWritingTime: $lastWritingTime, completionProgress: $completionProgress, writingTimeMinutes: $writingTimeMinutes, maxDailyWords: $maxDailyWords, consecutiveWritingDays: $consecutiveWritingDays)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProjectStatisticsImpl &&
            (identical(other.totalWordCount, totalWordCount) ||
                other.totalWordCount == totalWordCount) &&
            (identical(other.totalCharacterCount, totalCharacterCount) ||
                other.totalCharacterCount == totalCharacterCount) &&
            (identical(other.chapterCount, chapterCount) ||
                other.chapterCount == chapterCount) &&
            (identical(other.versionCount, versionCount) ||
                other.versionCount == versionCount) &&
            (identical(other.writingDays, writingDays) ||
                other.writingDays == writingDays) &&
            (identical(other.averageDailyWords, averageDailyWords) ||
                other.averageDailyWords == averageDailyWords) &&
            (identical(other.lastWritingTime, lastWritingTime) ||
                other.lastWritingTime == lastWritingTime) &&
            (identical(other.completionProgress, completionProgress) ||
                other.completionProgress == completionProgress) &&
            (identical(other.writingTimeMinutes, writingTimeMinutes) ||
                other.writingTimeMinutes == writingTimeMinutes) &&
            (identical(other.maxDailyWords, maxDailyWords) ||
                other.maxDailyWords == maxDailyWords) &&
            (identical(other.consecutiveWritingDays, consecutiveWritingDays) ||
                other.consecutiveWritingDays == consecutiveWritingDays));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    totalWordCount,
    totalCharacterCount,
    chapterCount,
    versionCount,
    writingDays,
    averageDailyWords,
    lastWritingTime,
    completionProgress,
    writingTimeMinutes,
    maxDailyWords,
    consecutiveWritingDays,
  );

  /// Create a copy of ProjectStatistics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProjectStatisticsImplCopyWith<_$ProjectStatisticsImpl> get copyWith =>
      __$$ProjectStatisticsImplCopyWithImpl<_$ProjectStatisticsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ProjectStatisticsImplToJson(this);
  }
}

abstract class _ProjectStatistics implements ProjectStatistics {
  const factory _ProjectStatistics({
    final int totalWordCount,
    final int totalCharacterCount,
    final int chapterCount,
    final int versionCount,
    final int writingDays,
    final int averageDailyWords,
    final DateTime? lastWritingTime,
    final double completionProgress,
    final int writingTimeMinutes,
    final int maxDailyWords,
    final int consecutiveWritingDays,
  }) = _$ProjectStatisticsImpl;

  factory _ProjectStatistics.fromJson(Map<String, dynamic> json) =
      _$ProjectStatisticsImpl.fromJson;

  /// 总字数
  @override
  int get totalWordCount;

  /// 总字符数
  @override
  int get totalCharacterCount;

  /// 章节数
  @override
  int get chapterCount;

  /// 版本数
  @override
  int get versionCount;

  /// 写作天数
  @override
  int get writingDays;

  /// 平均每日字数
  @override
  int get averageDailyWords;

  /// 最后写作时间
  @override
  DateTime? get lastWritingTime;

  /// 完成进度（0-100）
  @override
  double get completionProgress;

  /// 写作时长（分钟）
  @override
  int get writingTimeMinutes;

  /// 最高日写作字数
  @override
  int get maxDailyWords;

  /// 连续写作天数
  @override
  int get consecutiveWritingDays;

  /// Create a copy of ProjectStatistics
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProjectStatisticsImplCopyWith<_$ProjectStatisticsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
