import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:logger/logger.dart';

import 'package:bamboofall_app/features/ai_integration/data/repositories/llm_repository_impl.dart';
import 'package:bamboofall_app/features/ai_integration/data/datasources/openai_datasource.dart';
import 'package:bamboofall_app/features/ai_integration/data/datasources/claude_datasource.dart';
import 'package:bamboofall_app/features/ai_integration/data/datasources/gemini_datasource.dart';
import 'package:bamboofall_app/features/ai_integration/data/datasources/deepseek_datasource.dart';
import 'package:bamboofall_app/features/ai_integration/domain/entities/ai_model.dart';
import 'package:bamboofall_app/core/storage/database.dart';
import 'package:bamboofall_app/core/storage/secure_storage.dart';

import 'llm_repository_impl_test.mocks.dart';

@GenerateMocks([
  OpenAIDataSource,
  ClaudeDataSource,
  GeminiDataSource,
  DeepSeekDataSource,
  LocalDatabase,
  SecureStorageManager,
  Logger,
])
void main() {
  late LLMRepositoryImpl repository;
  late MockOpenAIDataSource mockOpenAIDataSource;
  late MockClaudeDataSource mockClaudeDataSource;
  late MockGeminiDataSource mockGeminiDataSource;
  late MockDeepSeekDataSource mockDeepSeekDataSource;
  late MockLocalDatabase mockDatabase;
  late MockSecureStorageManager mockSecureStorage;
  late MockLogger mockLogger;

  setUp(() {
    mockOpenAIDataSource = MockOpenAIDataSource();
    mockClaudeDataSource = MockClaudeDataSource();
    mockGeminiDataSource = MockGeminiDataSource();
    mockDeepSeekDataSource = MockDeepSeekDataSource();
    mockDatabase = MockLocalDatabase();
    mockSecureStorage = MockSecureStorageManager();
    mockLogger = MockLogger();

    repository = LLMRepositoryImpl(
      openAIDataSource: mockOpenAIDataSource,
      claudeDataSource: mockClaudeDataSource,
      geminiDataSource: mockGeminiDataSource,
      deepSeekDataSource: mockDeepSeekDataSource,
      database: mockDatabase,
      secureStorage: mockSecureStorage,
      logger: mockLogger,
    );
  });

  group('LLMRepositoryImpl - Model Management', () {
    test('should get available models from all providers', () async {
      // Arrange
      final openAIModels = [
        const AIModel(
          id: 'gpt-4',
          name: 'GPT-4',
          provider: 'OpenAI',
          maxTokens: 8192,
          supportsFunctions: true,
          supportsStreaming: true,
        ),
      ];

      final claudeModels = [
        const AIModel(
          id: 'claude-3-opus',
          name: 'Claude 3 Opus',
          provider: 'Anthropic',
          maxTokens: 200000,
          supportsFunctions: false,
          supportsStreaming: true,
        ),
      ];

      when(mockOpenAIDataSource.getSupportedModels()).thenReturn(openAIModels);
      when(mockClaudeDataSource.getSupportedModels()).thenReturn(claudeModels);
      when(mockGeminiDataSource.getSupportedModels()).thenReturn([]);
      when(mockDeepSeekDataSource.getSupportedModels()).thenReturn([]);

      // Act
      final models = await repository.getAvailableModels();

      // Assert
      expect(models.length, 2);
      expect(models.any((m) => m.id == 'gpt-4'), true);
      expect(models.any((m) => m.id == 'claude-3-opus'), true);
    });

    test('should get model by id from correct provider', () async {
      // Arrange
      const testModel = AIModel(
        id: 'gpt-4',
        name: 'GPT-4',
        provider: 'OpenAI',
        maxTokens: 8192,
        supportsFunctions: true,
        supportsStreaming: true,
      );

      when(mockOpenAIDataSource.getSupportedModels()).thenReturn([testModel]);
      when(mockClaudeDataSource.getSupportedModels()).thenReturn([]);
      when(mockGeminiDataSource.getSupportedModels()).thenReturn([]);
      when(mockDeepSeekDataSource.getSupportedModels()).thenReturn([]);

      // Act
      final model = await repository.getModelById('gpt-4');

      // Assert
      expect(model, isNotNull);
      expect(model!.id, 'gpt-4');
      expect(model.provider, 'OpenAI');
    });

    test('should return null for non-existent model', () async {
      // Arrange
      when(mockOpenAIDataSource.getSupportedModels()).thenReturn([]);
      when(mockClaudeDataSource.getSupportedModels()).thenReturn([]);
      when(mockGeminiDataSource.getSupportedModels()).thenReturn([]);
      when(mockDeepSeekDataSource.getSupportedModels()).thenReturn([]);

      // Act
      final model = await repository.getModelById('non-existent');

      // Assert
      expect(model, isNull);
    });
  });

  group('LLMRepositoryImpl - Completion', () {
    final testRequest = AIRequest(
      modelId: 'gpt-4',
      messages: [
        const AIMessage(
          role: AIMessageRole.user,
          content: 'Hello, world!',
        ),
      ],
      maxTokens: 1000,
      temperature: 0.7,
    );

    final testResponse = AIResponse(
      id: 'test-response',
      content: 'Hello! How can I help you today?',
      model: 'gpt-4',
      usage: const AIUsage(
        promptTokens: 10,
        completionTokens: 20,
        totalTokens: 30,
      ),
      finishReason: 'stop',
      createdAt: DateTime.now(),
    );

    test('should route completion to correct provider', () async {
      // Arrange
      when(mockOpenAIDataSource.completion(any)).thenAnswer((_) async => testResponse);

      // Act
      final response = await repository.completion(testRequest);

      // Assert
      expect(response.content, testResponse.content);
      verify(mockOpenAIDataSource.completion(any)).called(1);
    });

    test('should handle completion errors gracefully', () async {
      // Arrange
      when(mockOpenAIDataSource.completion(any)).thenThrow(Exception('API Error'));

      // Act & Assert
      expect(
        () => repository.completion(testRequest),
        throwsA(isA<Exception>()),
      );
    });

    test('should cache responses when enabled', () async {
      // Arrange
      when(mockOpenAIDataSource.completion(any)).thenAnswer((_) async => testResponse);

      // Act
      final response1 = await repository.completion(testRequest);
      final response2 = await repository.completion(testRequest);

      // Assert
      expect(response1.content, testResponse.content);
      expect(response2.content, testResponse.content);
      // Should only call the datasource once due to caching
      verify(mockOpenAIDataSource.completion(any)).called(1);
    });
  });

  group('LLMRepositoryImpl - Streaming', () {
    final testRequest = AIRequest(
      modelId: 'gpt-4',
      messages: [
        const AIMessage(
          role: AIMessageRole.user,
          content: 'Tell me a story',
        ),
      ],
      maxTokens: 1000,
      temperature: 0.7,
    );

    test('should handle streaming completion', () async {
      // Arrange
      final streamResponses = [
        AIResponse(
          id: 'stream-1',
          content: 'Once',
          model: 'gpt-4',
          usage: const AIUsage(promptTokens: 5, completionTokens: 1, totalTokens: 6),
          finishReason: null,
          createdAt: DateTime.now(),
        ),
        AIResponse(
          id: 'stream-2',
          content: ' upon',
          model: 'gpt-4',
          usage: const AIUsage(promptTokens: 5, completionTokens: 2, totalTokens: 7),
          finishReason: null,
          createdAt: DateTime.now(),
        ),
        AIResponse(
          id: 'stream-3',
          content: ' a time',
          model: 'gpt-4',
          usage: const AIUsage(promptTokens: 5, completionTokens: 3, totalTokens: 8),
          finishReason: 'stop',
          createdAt: DateTime.now(),
        ),
      ];

      when(mockOpenAIDataSource.completionStream(any))
          .thenAnswer((_) => Stream.fromIterable(streamResponses));

      // Act
      final responseStream = repository.completionStream(testRequest);

      // Assert
      final responses = await responseStream.toList();
      expect(responses.length, 3);
      expect(responses[0].content, 'Once');
      expect(responses[1].content, ' upon');
      expect(responses[2].content, ' a time');
    });

    test('should handle streaming errors', () async {
      // Arrange
      when(mockOpenAIDataSource.completionStream(any))
          .thenAnswer((_) => Stream.error(Exception('Stream error')));

      // Act
      final responseStream = repository.completionStream(testRequest);

      // Assert
      expect(
        responseStream.toList(),
        throwsA(isA<Exception>()),
      );
    });
  });

  group('LLMRepositoryImpl - Session Management', () {
    const sessionId = 'test-session';

    test('should create new session', () async {
      // Act
      final session = await repository.createSession(sessionId);

      // Assert
      expect(session.id, sessionId);
      expect(session.messages, isEmpty);
      expect(session.createdAt, isNotNull);
    });

    test('should add message to session', () async {
      // Arrange
      await repository.createSession(sessionId);
      const message = AIMessage(
        role: AIMessageRole.user,
        content: 'Hello',
      );

      // Act
      await repository.addMessageToSession(sessionId, message);
      final session = await repository.getSession(sessionId);

      // Assert
      expect(session?.messages.length, 1);
      expect(session?.messages.first.content, 'Hello');
    });

    test('should clear session messages', () async {
      // Arrange
      await repository.createSession(sessionId);
      const message = AIMessage(
        role: AIMessageRole.user,
        content: 'Hello',
      );
      await repository.addMessageToSession(sessionId, message);

      // Act
      await repository.clearSession(sessionId);
      final session = await repository.getSession(sessionId);

      // Assert
      expect(session?.messages, isEmpty);
    });

    test('should delete session', () async {
      // Arrange
      await repository.createSession(sessionId);

      // Act
      await repository.deleteSession(sessionId);
      final session = await repository.getSession(sessionId);

      // Assert
      expect(session, isNull);
    });
  });

  group('LLMRepositoryImpl - Usage Statistics', () {
    test('should track usage statistics', () async {
      // Arrange
      const modelId = 'gpt-4';
      final testRequest = AIRequest(
        modelId: modelId,
        messages: [
          const AIMessage(
            role: AIMessageRole.user,
            content: 'Test message',
          ),
        ],
      );

      final testResponse = AIResponse(
        id: 'test-response',
        content: 'Test response',
        model: modelId,
        usage: const AIUsage(
          promptTokens: 10,
          completionTokens: 20,
          totalTokens: 30,
        ),
        finishReason: 'stop',
        createdAt: DateTime.now(),
      );

      when(mockOpenAIDataSource.completion(any)).thenAnswer((_) async => testResponse);

      // Act
      await repository.completion(testRequest);
      final usage = await repository.getUsageStatistics(modelId);

      // Assert
      expect(usage.totalTokens, 30);
      expect(usage.totalRequests, 1);
    });

    test('should get usage statistics for all models', () async {
      // Arrange
      const modelId1 = 'gpt-4';
      const modelId2 = 'claude-3-opus';

      // Simulate some usage
      final request1 = AIRequest(
        modelId: modelId1,
        messages: [const AIMessage(role: AIMessageRole.user, content: 'Test 1')],
      );

      final request2 = AIRequest(
        modelId: modelId2,
        messages: [const AIMessage(role: AIMessageRole.user, content: 'Test 2')],
      );

      final response1 = AIResponse(
        id: 'response-1',
        content: 'Response 1',
        model: modelId1,
        usage: const AIUsage(promptTokens: 5, completionTokens: 10, totalTokens: 15),
        finishReason: 'stop',
        createdAt: DateTime.now(),
      );

      final response2 = AIResponse(
        id: 'response-2',
        content: 'Response 2',
        model: modelId2,
        usage: const AIUsage(promptTokens: 8, completionTokens: 12, totalTokens: 20),
        finishReason: 'stop',
        createdAt: DateTime.now(),
      );

      when(mockOpenAIDataSource.completion(any)).thenAnswer((_) async => response1);
      when(mockClaudeDataSource.completion(any)).thenAnswer((_) async => response2);

      // Act
      await repository.completion(request1);
      await repository.completion(request2);
      final allUsage = await repository.getAllUsageStatistics();

      // Assert
      expect(allUsage.length, 2);
      expect(allUsage.containsKey(modelId1), true);
      expect(allUsage.containsKey(modelId2), true);
    });
  });

  group('LLMRepositoryImpl - Error Handling', () {
    test('should handle provider routing errors', () async {
      // Arrange
      final invalidRequest = AIRequest(
        modelId: 'invalid-model',
        messages: [
          const AIMessage(
            role: AIMessageRole.user,
            content: 'Test',
          ),
        ],
      );

      // Act & Assert
      expect(
        () => repository.completion(invalidRequest),
        throwsA(isA<Exception>()),
      );
    });

    test('should handle network errors with retry', () async {
      // Arrange
      final testRequest = AIRequest(
        modelId: 'gpt-4',
        messages: [
          const AIMessage(
            role: AIMessageRole.user,
            content: 'Test',
          ),
        ],
      );

      when(mockOpenAIDataSource.completion(any))
          .thenThrow(Exception('Network error'));

      // Act & Assert
      expect(
        () => repository.completion(testRequest),
        throwsA(isA<Exception>()),
      );
    });
  });

  group('LLMRepositoryImpl - Configuration', () {
    test('should update model configuration', () async {
      // Arrange
      const modelId = 'gpt-4';
      final config = AIModelConfig(
        temperature: 0.8,
        maxTokens: 2000,
        topP: 0.9,
        frequencyPenalty: 0.1,
        presencePenalty: 0.1,
      );

      // Act
      await repository.updateModelConfig(modelId, config);
      final retrievedConfig = await repository.getModelConfig(modelId);

      // Assert
      expect(retrievedConfig?.temperature, 0.8);
      expect(retrievedConfig?.maxTokens, 2000);
    });

    test('should validate model configuration', () async {
      // Arrange
      const modelId = 'gpt-4';
      final invalidConfig = AIModelConfig(
        temperature: 2.0, // Invalid: should be between 0 and 1
        maxTokens: -100, // Invalid: should be positive
      );

      // Act & Assert
      expect(
        () => repository.updateModelConfig(modelId, invalidConfig),
        throwsA(isA<ArgumentError>()),
      );
    });
  });
}