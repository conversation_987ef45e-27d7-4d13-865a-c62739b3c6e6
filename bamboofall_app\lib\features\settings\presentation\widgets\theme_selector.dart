import 'package:flutter/material.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;
import '../../../../app/theme.dart';

/// 主题选择器
class ThemeSelector extends StatelessWidget {
  final AppThemeMode currentTheme;
  final Function(AppThemeMode) onThemeChanged;

  const ThemeSelector({
    super.key,
    required this.currentTheme,
    required this.onThemeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 主题选项
        ...AppThemeMode.values.map((theme) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: fluent.RadioButton(
              checked: currentTheme == theme,
              onChanged: (checked) {
                if (checked) {
                  onThemeChanged(theme);
                }
              },
              content: Row(
                children: [
                  Icon(theme.icon, size: 16),
                  const SizedBox(width: 8),
                  Text(theme.displayName),
                ],
              ),
            ),
          );
        }),
        const SizedBox(height: 16),
        
        // 主题预览
        _buildThemePreview(),
      ],
    );
  }

  /// 构建主题预览
  Widget _buildThemePreview() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          // 亮色主题预览
          Expanded(
            child: _buildThemePreviewCard(
              '亮色主题',
              AppTheme.lightTheme,
              currentTheme == AppThemeMode.light,
            ),
          ),
          Container(
            width: 1,
            color: Colors.grey.shade300,
          ),
          // 暗色主题预览
          Expanded(
            child: _buildThemePreviewCard(
              '暗色主题',
              AppTheme.darkTheme,
              currentTheme == AppThemeMode.dark,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建主题预览卡片
  Widget _buildThemePreviewCard(
    String title,
    fluent.FluentThemeData theme,
    bool isSelected,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        border: isSelected
            ? Border.all(color: theme.accentColor, width: 2)
            : null,
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Text(
              title,
              style: theme.typography.caption?.copyWith(
                color: theme.brightness == Brightness.light
                    ? Colors.black87
                    : Colors.white70,
              ),
            ),
            const SizedBox(height: 8),
            
            // 模拟界面元素
            Expanded(
              child: Column(
                children: [
                  // 模拟按钮
                  Container(
                    width: double.infinity,
                    height: 24,
                    decoration: BoxDecoration(
                      color: theme.accentColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Center(
                      child: Text(
                        '按钮',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 6),
                  
                  // 模拟卡片
                  Container(
                    width: double.infinity,
                    height: 32,
                    decoration: BoxDecoration(
                      color: theme.cardColor,
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color: theme.brightness == Brightness.light
                            ? Colors.grey.shade300
                            : Colors.grey.shade700,
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(4),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            width: 40,
                            height: 8,
                            decoration: BoxDecoration(
                              color: theme.brightness == Brightness.light
                                  ? Colors.black54
                                  : Colors.white54,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          const SizedBox(height: 2),
                          Container(
                            width: 60,
                            height: 6,
                            decoration: BoxDecoration(
                              color: theme.brightness == Brightness.light
                                  ? Colors.black38
                                  : Colors.white38,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 高级主题选择器
class AdvancedThemeSelector extends StatefulWidget {
  final AppThemeMode currentTheme;
  final Function(AppThemeMode) onThemeChanged;

  const AdvancedThemeSelector({
    super.key,
    required this.currentTheme,
    required this.onThemeChanged,
  });

  @override
  State<AdvancedThemeSelector> createState() => _AdvancedThemeSelectorState();
}

class _AdvancedThemeSelectorState extends State<AdvancedThemeSelector> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 主题网格
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            childAspectRatio: 1.2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          ),
          itemCount: AppThemeMode.values.length,
          itemBuilder: (context, index) {
            final theme = AppThemeMode.values[index];
            return _buildThemeCard(theme);
          },
        ),
        const SizedBox(height: 16),
        
        // 自定义选项
        if (widget.currentTheme == AppThemeMode.system)
          _buildSystemThemeInfo(),
      ],
    );
  }

  /// 构建主题卡片
  Widget _buildThemeCard(AppThemeMode theme) {
    final isSelected = widget.currentTheme == theme;
    final themeData = theme == AppThemeMode.dark
        ? AppTheme.darkTheme
        : AppTheme.lightTheme;

    return GestureDetector(
      onTap: () => widget.onThemeChanged(theme),
      child: Container(
        decoration: BoxDecoration(
          color: themeData.scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? themeData.accentColor
                : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: themeData.accentColor.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            children: [
              // 图标
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: theme == AppThemeMode.system
                      ? Colors.blue
                      : themeData.accentColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  theme.icon,
                  color: Colors.white,
                  size: 16,
                ),
              ),
              const SizedBox(height: 8),
              
              // 标题
              Text(
                theme.displayName,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color: themeData.brightness == Brightness.light
                      ? Colors.black87
                      : Colors.white70,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              
              // 预览条
              if (theme != AppThemeMode.system)
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 12,
                      height: 4,
                      decoration: BoxDecoration(
                        color: themeData.accentColor,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 2),
                    Container(
                      width: 8,
                      height: 4,
                      decoration: BoxDecoration(
                        color: themeData.brightness == Brightness.light
                            ? Colors.grey.shade400
                            : Colors.grey.shade600,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建系统主题信息
  Widget _buildSystemThemeInfo() {
    final brightness = MediaQuery.of(context).platformBrightness;
    final currentSystemTheme = brightness == Brightness.dark ? '暗色' : '亮色';

    return fluent.InfoBar(
      title: const Text('跟随系统主题'),
      content: Text('当前系统主题: $currentSystemTheme'),
      severity: fluent.InfoBarSeverity.info,
    );
  }
}

/// 主题颜色选择器
class ThemeColorSelector extends StatelessWidget {
  final Color currentColor;
  final Function(Color) onColorChanged;

  const ThemeColorSelector({
    super.key,
    required this.currentColor,
    required this.onColorChanged,
  });

  static const List<Color> _predefinedColors = [
    Color(0xFF2E7D32), // 竹绿色
    Color(0xFF1976D2), // 蓝色
    Color(0xFF7B1FA2), // 紫色
    Color(0xFFD32F2F), // 红色
    Color(0xFFF57C00), // 橙色
    Color(0xFF5D4037), // 棕色
    Color(0xFF455A64), // 蓝灰色
    Color(0xFF424242), // 灰色
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '主题色',
          style: fluent.FluentTheme.of(context).typography.subtitle,
        ),
        const SizedBox(height: 12),
        
        // 预定义颜色
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _predefinedColors.map((color) {
            final isSelected = currentColor.toARGB32() == color.toARGB32();
            
            return GestureDetector(
              onTap: () => onColorChanged(color),
              child: Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(8),
                  border: isSelected
                      ? Border.all(color: Colors.white, width: 2)
                      : null,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 16,
                      )
                    : null,
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 16),
        
        // 自定义颜色按钮
        fluent.Button(
          onPressed: () => _showColorPicker(context),
          child: const Text('自定义颜色'),
        ),
      ],
    );
  }

  /// 显示颜色选择器
  void _showColorPicker(BuildContext context) {
    // 这里应该显示颜色选择器对话框
    // 暂时显示提示
    fluent.displayInfoBar(
      context,
      builder: (context, close) => fluent.InfoBar(
        title: const Text('自定义颜色'),
        content: const Text('颜色选择器功能开发中...'),
        severity: fluent.InfoBarSeverity.info,
        action: fluent.IconButton(
          icon: const Icon(fluent.FluentIcons.clear),
          onPressed: close,
        ),
      ),
    );
  }
}