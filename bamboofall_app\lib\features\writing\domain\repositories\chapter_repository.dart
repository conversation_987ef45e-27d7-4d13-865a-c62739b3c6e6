import '../entities/chapter.dart';

/// 章节仓库接口
/// 定义章节数据访问的抽象方法
abstract class ChapterRepository {
  /// 获取所有章节
  Future<List<Chapter>> getAllChapters();

  /// 根据ID获取章节
  Future<Chapter?> getChapterById(String id);

  /// 获取子章节列表
  Future<List<Chapter>> getChildChapters(String parentId);

  /// 创建章节
  Future<Chapter> createChapter(Chapter chapter);

  /// 更新章节
  Future<Chapter> updateChapter(Chapter chapter);

  /// 删除章节
  Future<void> deleteChapter(String id);

  /// 批量更新章节
  Future<void> updateChapters(List<Chapter> chapters);

  /// 根据状态获取章节
  Future<List<Chapter>> getChaptersByStatus(ChapterStatus status);

  /// 根据标签获取章节
  Future<List<Chapter>> getChaptersByTag(String tag);

  /// 搜索章节
  Future<List<Chapter>> searchChapters(String query);

  /// 获取章节数量
  Future<int> getChapterCount();

  /// 清空所有章节
  Future<void> clearAllChapters();
}