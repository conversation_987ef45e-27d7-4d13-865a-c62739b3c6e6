// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'version_branch.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

VersionBranch _$VersionBranchFromJson(Map<String, dynamic> json) {
  return _VersionBranch.fromJson(json);
}

/// @nodoc
mixin _$VersionBranch {
  /// 当前版本
  ChapterVersion get version => throw _privateConstructorUsedError;

  /// 子分支列表
  List<VersionBranch> get children => throw _privateConstructorUsedError;

  /// 分支深度
  int get depth => throw _privateConstructorUsedError;

  /// 是否展开
  bool get isExpanded => throw _privateConstructorUsedError;

  /// 分支类型
  BranchType get branchType => throw _privateConstructorUsedError;

  /// 分支颜色（用于UI显示）
  String? get branchColor => throw _privateConstructorUsedError;

  /// Serializes this VersionBranch to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VersionBranch
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VersionBranchCopyWith<VersionBranch> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VersionBranchCopyWith<$Res> {
  factory $VersionBranchCopyWith(
    VersionBranch value,
    $Res Function(VersionBranch) then,
  ) = _$VersionBranchCopyWithImpl<$Res, VersionBranch>;
  @useResult
  $Res call({
    ChapterVersion version,
    List<VersionBranch> children,
    int depth,
    bool isExpanded,
    BranchType branchType,
    String? branchColor,
  });

  $ChapterVersionCopyWith<$Res> get version;
}

/// @nodoc
class _$VersionBranchCopyWithImpl<$Res, $Val extends VersionBranch>
    implements $VersionBranchCopyWith<$Res> {
  _$VersionBranchCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VersionBranch
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? version = null,
    Object? children = null,
    Object? depth = null,
    Object? isExpanded = null,
    Object? branchType = null,
    Object? branchColor = freezed,
  }) {
    return _then(
      _value.copyWith(
            version: null == version
                ? _value.version
                : version // ignore: cast_nullable_to_non_nullable
                      as ChapterVersion,
            children: null == children
                ? _value.children
                : children // ignore: cast_nullable_to_non_nullable
                      as List<VersionBranch>,
            depth: null == depth
                ? _value.depth
                : depth // ignore: cast_nullable_to_non_nullable
                      as int,
            isExpanded: null == isExpanded
                ? _value.isExpanded
                : isExpanded // ignore: cast_nullable_to_non_nullable
                      as bool,
            branchType: null == branchType
                ? _value.branchType
                : branchType // ignore: cast_nullable_to_non_nullable
                      as BranchType,
            branchColor: freezed == branchColor
                ? _value.branchColor
                : branchColor // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }

  /// Create a copy of VersionBranch
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ChapterVersionCopyWith<$Res> get version {
    return $ChapterVersionCopyWith<$Res>(_value.version, (value) {
      return _then(_value.copyWith(version: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$VersionBranchImplCopyWith<$Res>
    implements $VersionBranchCopyWith<$Res> {
  factory _$$VersionBranchImplCopyWith(
    _$VersionBranchImpl value,
    $Res Function(_$VersionBranchImpl) then,
  ) = __$$VersionBranchImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    ChapterVersion version,
    List<VersionBranch> children,
    int depth,
    bool isExpanded,
    BranchType branchType,
    String? branchColor,
  });

  @override
  $ChapterVersionCopyWith<$Res> get version;
}

/// @nodoc
class __$$VersionBranchImplCopyWithImpl<$Res>
    extends _$VersionBranchCopyWithImpl<$Res, _$VersionBranchImpl>
    implements _$$VersionBranchImplCopyWith<$Res> {
  __$$VersionBranchImplCopyWithImpl(
    _$VersionBranchImpl _value,
    $Res Function(_$VersionBranchImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VersionBranch
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? version = null,
    Object? children = null,
    Object? depth = null,
    Object? isExpanded = null,
    Object? branchType = null,
    Object? branchColor = freezed,
  }) {
    return _then(
      _$VersionBranchImpl(
        version: null == version
            ? _value.version
            : version // ignore: cast_nullable_to_non_nullable
                  as ChapterVersion,
        children: null == children
            ? _value._children
            : children // ignore: cast_nullable_to_non_nullable
                  as List<VersionBranch>,
        depth: null == depth
            ? _value.depth
            : depth // ignore: cast_nullable_to_non_nullable
                  as int,
        isExpanded: null == isExpanded
            ? _value.isExpanded
            : isExpanded // ignore: cast_nullable_to_non_nullable
                  as bool,
        branchType: null == branchType
            ? _value.branchType
            : branchType // ignore: cast_nullable_to_non_nullable
                  as BranchType,
        branchColor: freezed == branchColor
            ? _value.branchColor
            : branchColor // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$VersionBranchImpl implements _VersionBranch {
  const _$VersionBranchImpl({
    required this.version,
    final List<VersionBranch> children = const [],
    this.depth = 0,
    this.isExpanded = true,
    this.branchType = BranchType.main,
    this.branchColor,
  }) : _children = children;

  factory _$VersionBranchImpl.fromJson(Map<String, dynamic> json) =>
      _$$VersionBranchImplFromJson(json);

  /// 当前版本
  @override
  final ChapterVersion version;

  /// 子分支列表
  final List<VersionBranch> _children;

  /// 子分支列表
  @override
  @JsonKey()
  List<VersionBranch> get children {
    if (_children is EqualUnmodifiableListView) return _children;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_children);
  }

  /// 分支深度
  @override
  @JsonKey()
  final int depth;

  /// 是否展开
  @override
  @JsonKey()
  final bool isExpanded;

  /// 分支类型
  @override
  @JsonKey()
  final BranchType branchType;

  /// 分支颜色（用于UI显示）
  @override
  final String? branchColor;

  @override
  String toString() {
    return 'VersionBranch(version: $version, children: $children, depth: $depth, isExpanded: $isExpanded, branchType: $branchType, branchColor: $branchColor)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VersionBranchImpl &&
            (identical(other.version, version) || other.version == version) &&
            const DeepCollectionEquality().equals(other._children, _children) &&
            (identical(other.depth, depth) || other.depth == depth) &&
            (identical(other.isExpanded, isExpanded) ||
                other.isExpanded == isExpanded) &&
            (identical(other.branchType, branchType) ||
                other.branchType == branchType) &&
            (identical(other.branchColor, branchColor) ||
                other.branchColor == branchColor));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    version,
    const DeepCollectionEquality().hash(_children),
    depth,
    isExpanded,
    branchType,
    branchColor,
  );

  /// Create a copy of VersionBranch
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VersionBranchImplCopyWith<_$VersionBranchImpl> get copyWith =>
      __$$VersionBranchImplCopyWithImpl<_$VersionBranchImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VersionBranchImplToJson(this);
  }
}

abstract class _VersionBranch implements VersionBranch {
  const factory _VersionBranch({
    required final ChapterVersion version,
    final List<VersionBranch> children,
    final int depth,
    final bool isExpanded,
    final BranchType branchType,
    final String? branchColor,
  }) = _$VersionBranchImpl;

  factory _VersionBranch.fromJson(Map<String, dynamic> json) =
      _$VersionBranchImpl.fromJson;

  /// 当前版本
  @override
  ChapterVersion get version;

  /// 子分支列表
  @override
  List<VersionBranch> get children;

  /// 分支深度
  @override
  int get depth;

  /// 是否展开
  @override
  bool get isExpanded;

  /// 分支类型
  @override
  BranchType get branchType;

  /// 分支颜色（用于UI显示）
  @override
  String? get branchColor;

  /// Create a copy of VersionBranch
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VersionBranchImplCopyWith<_$VersionBranchImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
