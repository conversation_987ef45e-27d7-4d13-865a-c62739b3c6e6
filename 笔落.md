我想要开发一套AI大模型辅助创作小说的app，这个app应当具有以下几个能力：
1.接入主流AI大模型厂商的API，如OpenAI、Anthropic、Google、DeepSeek、kimi、ChatGLM、文心一言等（还要可以直接自己输入URL和key来配置）。
2.使用一个名为圣经系统的机制来制约大模型生成的内容的随机性，圣经系统包含小说创作的框架和大纲内容的创建和管理，包含小说必须的一些要素，如主线脉络、支线剧情、人物、世界观、场景、道具/技能等等。 
3.app打开以后，进入主界面，该界面可以进行创建项目或者打开项目，或者对app进行设置。
4.在新项目创建之前，应当必须要录入圣经系统的主要内容，之后才可以基于圣经系统进行小说内容的生成。
5.项目创建以后，跳入到创作界面，在该界面可以于大模型进行交互以生成灵感和内容等，另外该界面需要提供圣经元素的编辑改动，另外还要可以看到当前创作的进度。
6.每一章内容生成以后需要作者进行审核才能写入到本地文件中，作者也可以使用大模型对小说内容进行评价和反馈，以完善小说的质量。
7.作者可以同时创建多个项目，并在app首页打开不同项目，然后进入创作界面进行编辑。
8.设置界面提供常规软件设置，主要包含一些主题等，大模型设置主要包含接入的AI大模型厂商的API的URL和key，圣经系统设置主要包含常用的一些圣经模板。
9.你需要帮我设计一套数据模型，用来保存小说相关内容、圣经系统相关内容和大模型配置相关内容。
10.你需要帮我设计一套UI界面交互，主要包括主界面、创作界面、设置界面等。
11.这个app是一个存本地使用的app，所有数据都在本地存储，不会上传到服务器，最好使用配置文件。
12.圣经系统需要大量的提示词工程，同样需要使用配置文件进行管理。
13.需要创建专门的目录用来管理这些不同用途的配置文件和文档内容。