// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'story_bible.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

StoryBible _$StoryBibleFromJson(Map<String, dynamic> json) {
  return _StoryBible.fromJson(json);
}

/// @nodoc
mixin _$StoryBible {
  String get id => throw _privateConstructorUsedError;
  String get projectId => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  WorldSettings get worldSettings => throw _privateConstructorUsedError;
  List<Character> get characters => throw _privateConstructorUsedError;
  List<Location> get locations => throw _privateConstructorUsedError;
  List<PlotLine> get plotLines => throw _privateConstructorUsedError;
  List<Item> get items => throw _privateConstructorUsedError;
  List<Skill> get skills => throw _privateConstructorUsedError;
  List<Constraint> get constraints => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  int get version => throw _privateConstructorUsedError;

  /// Serializes this StoryBible to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StoryBible
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StoryBibleCopyWith<StoryBible> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StoryBibleCopyWith<$Res> {
  factory $StoryBibleCopyWith(
    StoryBible value,
    $Res Function(StoryBible) then,
  ) = _$StoryBibleCopyWithImpl<$Res, StoryBible>;
  @useResult
  $Res call({
    String id,
    String projectId,
    String title,
    String? description,
    WorldSettings worldSettings,
    List<Character> characters,
    List<Location> locations,
    List<PlotLine> plotLines,
    List<Item> items,
    List<Skill> skills,
    List<Constraint> constraints,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
    int version,
  });

  $WorldSettingsCopyWith<$Res> get worldSettings;
}

/// @nodoc
class _$StoryBibleCopyWithImpl<$Res, $Val extends StoryBible>
    implements $StoryBibleCopyWith<$Res> {
  _$StoryBibleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StoryBible
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? projectId = null,
    Object? title = null,
    Object? description = freezed,
    Object? worldSettings = null,
    Object? characters = null,
    Object? locations = null,
    Object? plotLines = null,
    Object? items = null,
    Object? skills = null,
    Object? constraints = null,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? version = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            projectId: null == projectId
                ? _value.projectId
                : projectId // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            worldSettings: null == worldSettings
                ? _value.worldSettings
                : worldSettings // ignore: cast_nullable_to_non_nullable
                      as WorldSettings,
            characters: null == characters
                ? _value.characters
                : characters // ignore: cast_nullable_to_non_nullable
                      as List<Character>,
            locations: null == locations
                ? _value.locations
                : locations // ignore: cast_nullable_to_non_nullable
                      as List<Location>,
            plotLines: null == plotLines
                ? _value.plotLines
                : plotLines // ignore: cast_nullable_to_non_nullable
                      as List<PlotLine>,
            items: null == items
                ? _value.items
                : items // ignore: cast_nullable_to_non_nullable
                      as List<Item>,
            skills: null == skills
                ? _value.skills
                : skills // ignore: cast_nullable_to_non_nullable
                      as List<Skill>,
            constraints: null == constraints
                ? _value.constraints
                : constraints // ignore: cast_nullable_to_non_nullable
                      as List<Constraint>,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            version: null == version
                ? _value.version
                : version // ignore: cast_nullable_to_non_nullable
                      as int,
          )
          as $Val,
    );
  }

  /// Create a copy of StoryBible
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WorldSettingsCopyWith<$Res> get worldSettings {
    return $WorldSettingsCopyWith<$Res>(_value.worldSettings, (value) {
      return _then(_value.copyWith(worldSettings: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$StoryBibleImplCopyWith<$Res>
    implements $StoryBibleCopyWith<$Res> {
  factory _$$StoryBibleImplCopyWith(
    _$StoryBibleImpl value,
    $Res Function(_$StoryBibleImpl) then,
  ) = __$$StoryBibleImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String projectId,
    String title,
    String? description,
    WorldSettings worldSettings,
    List<Character> characters,
    List<Location> locations,
    List<PlotLine> plotLines,
    List<Item> items,
    List<Skill> skills,
    List<Constraint> constraints,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
    int version,
  });

  @override
  $WorldSettingsCopyWith<$Res> get worldSettings;
}

/// @nodoc
class __$$StoryBibleImplCopyWithImpl<$Res>
    extends _$StoryBibleCopyWithImpl<$Res, _$StoryBibleImpl>
    implements _$$StoryBibleImplCopyWith<$Res> {
  __$$StoryBibleImplCopyWithImpl(
    _$StoryBibleImpl _value,
    $Res Function(_$StoryBibleImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of StoryBible
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? projectId = null,
    Object? title = null,
    Object? description = freezed,
    Object? worldSettings = null,
    Object? characters = null,
    Object? locations = null,
    Object? plotLines = null,
    Object? items = null,
    Object? skills = null,
    Object? constraints = null,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? version = null,
  }) {
    return _then(
      _$StoryBibleImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        projectId: null == projectId
            ? _value.projectId
            : projectId // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        worldSettings: null == worldSettings
            ? _value.worldSettings
            : worldSettings // ignore: cast_nullable_to_non_nullable
                  as WorldSettings,
        characters: null == characters
            ? _value._characters
            : characters // ignore: cast_nullable_to_non_nullable
                  as List<Character>,
        locations: null == locations
            ? _value._locations
            : locations // ignore: cast_nullable_to_non_nullable
                  as List<Location>,
        plotLines: null == plotLines
            ? _value._plotLines
            : plotLines // ignore: cast_nullable_to_non_nullable
                  as List<PlotLine>,
        items: null == items
            ? _value._items
            : items // ignore: cast_nullable_to_non_nullable
                  as List<Item>,
        skills: null == skills
            ? _value._skills
            : skills // ignore: cast_nullable_to_non_nullable
                  as List<Skill>,
        constraints: null == constraints
            ? _value._constraints
            : constraints // ignore: cast_nullable_to_non_nullable
                  as List<Constraint>,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        version: null == version
            ? _value.version
            : version // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$StoryBibleImpl implements _StoryBible {
  const _$StoryBibleImpl({
    required this.id,
    required this.projectId,
    required this.title,
    this.description,
    required this.worldSettings,
    final List<Character> characters = const [],
    final List<Location> locations = const [],
    final List<PlotLine> plotLines = const [],
    final List<Item> items = const [],
    final List<Skill> skills = const [],
    final List<Constraint> constraints = const [],
    final Map<String, dynamic> metadata = const {},
    this.createdAt,
    this.updatedAt,
    this.version = 1,
  }) : _characters = characters,
       _locations = locations,
       _plotLines = plotLines,
       _items = items,
       _skills = skills,
       _constraints = constraints,
       _metadata = metadata;

  factory _$StoryBibleImpl.fromJson(Map<String, dynamic> json) =>
      _$$StoryBibleImplFromJson(json);

  @override
  final String id;
  @override
  final String projectId;
  @override
  final String title;
  @override
  final String? description;
  @override
  final WorldSettings worldSettings;
  final List<Character> _characters;
  @override
  @JsonKey()
  List<Character> get characters {
    if (_characters is EqualUnmodifiableListView) return _characters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_characters);
  }

  final List<Location> _locations;
  @override
  @JsonKey()
  List<Location> get locations {
    if (_locations is EqualUnmodifiableListView) return _locations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_locations);
  }

  final List<PlotLine> _plotLines;
  @override
  @JsonKey()
  List<PlotLine> get plotLines {
    if (_plotLines is EqualUnmodifiableListView) return _plotLines;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_plotLines);
  }

  final List<Item> _items;
  @override
  @JsonKey()
  List<Item> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  final List<Skill> _skills;
  @override
  @JsonKey()
  List<Skill> get skills {
    if (_skills is EqualUnmodifiableListView) return _skills;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_skills);
  }

  final List<Constraint> _constraints;
  @override
  @JsonKey()
  List<Constraint> get constraints {
    if (_constraints is EqualUnmodifiableListView) return _constraints;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_constraints);
  }

  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;
  @override
  @JsonKey()
  final int version;

  @override
  String toString() {
    return 'StoryBible(id: $id, projectId: $projectId, title: $title, description: $description, worldSettings: $worldSettings, characters: $characters, locations: $locations, plotLines: $plotLines, items: $items, skills: $skills, constraints: $constraints, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt, version: $version)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoryBibleImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.worldSettings, worldSettings) ||
                other.worldSettings == worldSettings) &&
            const DeepCollectionEquality().equals(
              other._characters,
              _characters,
            ) &&
            const DeepCollectionEquality().equals(
              other._locations,
              _locations,
            ) &&
            const DeepCollectionEquality().equals(
              other._plotLines,
              _plotLines,
            ) &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            const DeepCollectionEquality().equals(other._skills, _skills) &&
            const DeepCollectionEquality().equals(
              other._constraints,
              _constraints,
            ) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.version, version) || other.version == version));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    projectId,
    title,
    description,
    worldSettings,
    const DeepCollectionEquality().hash(_characters),
    const DeepCollectionEquality().hash(_locations),
    const DeepCollectionEquality().hash(_plotLines),
    const DeepCollectionEquality().hash(_items),
    const DeepCollectionEquality().hash(_skills),
    const DeepCollectionEquality().hash(_constraints),
    const DeepCollectionEquality().hash(_metadata),
    createdAt,
    updatedAt,
    version,
  );

  /// Create a copy of StoryBible
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StoryBibleImplCopyWith<_$StoryBibleImpl> get copyWith =>
      __$$StoryBibleImplCopyWithImpl<_$StoryBibleImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StoryBibleImplToJson(this);
  }
}

abstract class _StoryBible implements StoryBible {
  const factory _StoryBible({
    required final String id,
    required final String projectId,
    required final String title,
    final String? description,
    required final WorldSettings worldSettings,
    final List<Character> characters,
    final List<Location> locations,
    final List<PlotLine> plotLines,
    final List<Item> items,
    final List<Skill> skills,
    final List<Constraint> constraints,
    final Map<String, dynamic> metadata,
    final DateTime? createdAt,
    final DateTime? updatedAt,
    final int version,
  }) = _$StoryBibleImpl;

  factory _StoryBible.fromJson(Map<String, dynamic> json) =
      _$StoryBibleImpl.fromJson;

  @override
  String get id;
  @override
  String get projectId;
  @override
  String get title;
  @override
  String? get description;
  @override
  WorldSettings get worldSettings;
  @override
  List<Character> get characters;
  @override
  List<Location> get locations;
  @override
  List<PlotLine> get plotLines;
  @override
  List<Item> get items;
  @override
  List<Skill> get skills;
  @override
  List<Constraint> get constraints;
  @override
  Map<String, dynamic> get metadata;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  int get version;

  /// Create a copy of StoryBible
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StoryBibleImplCopyWith<_$StoryBibleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PlotLine _$PlotLineFromJson(Map<String, dynamic> json) {
  return _PlotLine.fromJson(json);
}

/// @nodoc
mixin _$PlotLine {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  PlotLineType get type => throw _privateConstructorUsedError;
  PlotLineStatus get status => throw _privateConstructorUsedError;
  List<PlotPoint> get plotPoints => throw _privateConstructorUsedError;
  List<String> get involvedCharacterIds => throw _privateConstructorUsedError;
  List<String> get involvedLocationIds => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this PlotLine to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PlotLine
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PlotLineCopyWith<PlotLine> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlotLineCopyWith<$Res> {
  factory $PlotLineCopyWith(PlotLine value, $Res Function(PlotLine) then) =
      _$PlotLineCopyWithImpl<$Res, PlotLine>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    PlotLineType type,
    PlotLineStatus status,
    List<PlotPoint> plotPoints,
    List<String> involvedCharacterIds,
    List<String> involvedLocationIds,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$PlotLineCopyWithImpl<$Res, $Val extends PlotLine>
    implements $PlotLineCopyWith<$Res> {
  _$PlotLineCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PlotLine
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = null,
    Object? status = null,
    Object? plotPoints = null,
    Object? involvedCharacterIds = null,
    Object? involvedLocationIds = null,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as PlotLineType,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as PlotLineStatus,
            plotPoints: null == plotPoints
                ? _value.plotPoints
                : plotPoints // ignore: cast_nullable_to_non_nullable
                      as List<PlotPoint>,
            involvedCharacterIds: null == involvedCharacterIds
                ? _value.involvedCharacterIds
                : involvedCharacterIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            involvedLocationIds: null == involvedLocationIds
                ? _value.involvedLocationIds
                : involvedLocationIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PlotLineImplCopyWith<$Res>
    implements $PlotLineCopyWith<$Res> {
  factory _$$PlotLineImplCopyWith(
    _$PlotLineImpl value,
    $Res Function(_$PlotLineImpl) then,
  ) = __$$PlotLineImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    PlotLineType type,
    PlotLineStatus status,
    List<PlotPoint> plotPoints,
    List<String> involvedCharacterIds,
    List<String> involvedLocationIds,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$PlotLineImplCopyWithImpl<$Res>
    extends _$PlotLineCopyWithImpl<$Res, _$PlotLineImpl>
    implements _$$PlotLineImplCopyWith<$Res> {
  __$$PlotLineImplCopyWithImpl(
    _$PlotLineImpl _value,
    $Res Function(_$PlotLineImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PlotLine
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = null,
    Object? status = null,
    Object? plotPoints = null,
    Object? involvedCharacterIds = null,
    Object? involvedLocationIds = null,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$PlotLineImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as PlotLineType,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as PlotLineStatus,
        plotPoints: null == plotPoints
            ? _value._plotPoints
            : plotPoints // ignore: cast_nullable_to_non_nullable
                  as List<PlotPoint>,
        involvedCharacterIds: null == involvedCharacterIds
            ? _value._involvedCharacterIds
            : involvedCharacterIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        involvedLocationIds: null == involvedLocationIds
            ? _value._involvedLocationIds
            : involvedLocationIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PlotLineImpl implements _PlotLine {
  const _$PlotLineImpl({
    required this.id,
    required this.name,
    this.description,
    required this.type,
    required this.status,
    final List<PlotPoint> plotPoints = const [],
    final List<String> involvedCharacterIds = const [],
    final List<String> involvedLocationIds = const [],
    final Map<String, dynamic> metadata = const {},
    this.createdAt,
    this.updatedAt,
  }) : _plotPoints = plotPoints,
       _involvedCharacterIds = involvedCharacterIds,
       _involvedLocationIds = involvedLocationIds,
       _metadata = metadata;

  factory _$PlotLineImpl.fromJson(Map<String, dynamic> json) =>
      _$$PlotLineImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final PlotLineType type;
  @override
  final PlotLineStatus status;
  final List<PlotPoint> _plotPoints;
  @override
  @JsonKey()
  List<PlotPoint> get plotPoints {
    if (_plotPoints is EqualUnmodifiableListView) return _plotPoints;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_plotPoints);
  }

  final List<String> _involvedCharacterIds;
  @override
  @JsonKey()
  List<String> get involvedCharacterIds {
    if (_involvedCharacterIds is EqualUnmodifiableListView)
      return _involvedCharacterIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_involvedCharacterIds);
  }

  final List<String> _involvedLocationIds;
  @override
  @JsonKey()
  List<String> get involvedLocationIds {
    if (_involvedLocationIds is EqualUnmodifiableListView)
      return _involvedLocationIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_involvedLocationIds);
  }

  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'PlotLine(id: $id, name: $name, description: $description, type: $type, status: $status, plotPoints: $plotPoints, involvedCharacterIds: $involvedCharacterIds, involvedLocationIds: $involvedLocationIds, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlotLineImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(
              other._plotPoints,
              _plotPoints,
            ) &&
            const DeepCollectionEquality().equals(
              other._involvedCharacterIds,
              _involvedCharacterIds,
            ) &&
            const DeepCollectionEquality().equals(
              other._involvedLocationIds,
              _involvedLocationIds,
            ) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    type,
    status,
    const DeepCollectionEquality().hash(_plotPoints),
    const DeepCollectionEquality().hash(_involvedCharacterIds),
    const DeepCollectionEquality().hash(_involvedLocationIds),
    const DeepCollectionEquality().hash(_metadata),
    createdAt,
    updatedAt,
  );

  /// Create a copy of PlotLine
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PlotLineImplCopyWith<_$PlotLineImpl> get copyWith =>
      __$$PlotLineImplCopyWithImpl<_$PlotLineImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PlotLineImplToJson(this);
  }
}

abstract class _PlotLine implements PlotLine {
  const factory _PlotLine({
    required final String id,
    required final String name,
    final String? description,
    required final PlotLineType type,
    required final PlotLineStatus status,
    final List<PlotPoint> plotPoints,
    final List<String> involvedCharacterIds,
    final List<String> involvedLocationIds,
    final Map<String, dynamic> metadata,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$PlotLineImpl;

  factory _PlotLine.fromJson(Map<String, dynamic> json) =
      _$PlotLineImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  PlotLineType get type;
  @override
  PlotLineStatus get status;
  @override
  List<PlotPoint> get plotPoints;
  @override
  List<String> get involvedCharacterIds;
  @override
  List<String> get involvedLocationIds;
  @override
  Map<String, dynamic> get metadata;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of PlotLine
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PlotLineImplCopyWith<_$PlotLineImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PlotPoint _$PlotPointFromJson(Map<String, dynamic> json) {
  return _PlotPoint.fromJson(json);
}

/// @nodoc
mixin _$PlotPoint {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  int get order => throw _privateConstructorUsedError;
  PlotPointStatus? get status => throw _privateConstructorUsedError;
  List<String> get characterIds => throw _privateConstructorUsedError;
  List<String> get locationIds => throw _privateConstructorUsedError;
  List<String> get itemIds => throw _privateConstructorUsedError;
  List<String> get skillIds => throw _privateConstructorUsedError;
  String? get chapterId => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this PlotPoint to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PlotPoint
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PlotPointCopyWith<PlotPoint> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlotPointCopyWith<$Res> {
  factory $PlotPointCopyWith(PlotPoint value, $Res Function(PlotPoint) then) =
      _$PlotPointCopyWithImpl<$Res, PlotPoint>;
  @useResult
  $Res call({
    String id,
    String title,
    String? description,
    int order,
    PlotPointStatus? status,
    List<String> characterIds,
    List<String> locationIds,
    List<String> itemIds,
    List<String> skillIds,
    String? chapterId,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$PlotPointCopyWithImpl<$Res, $Val extends PlotPoint>
    implements $PlotPointCopyWith<$Res> {
  _$PlotPointCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PlotPoint
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = freezed,
    Object? order = null,
    Object? status = freezed,
    Object? characterIds = null,
    Object? locationIds = null,
    Object? itemIds = null,
    Object? skillIds = null,
    Object? chapterId = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            order: null == order
                ? _value.order
                : order // ignore: cast_nullable_to_non_nullable
                      as int,
            status: freezed == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as PlotPointStatus?,
            characterIds: null == characterIds
                ? _value.characterIds
                : characterIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            locationIds: null == locationIds
                ? _value.locationIds
                : locationIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            itemIds: null == itemIds
                ? _value.itemIds
                : itemIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            skillIds: null == skillIds
                ? _value.skillIds
                : skillIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            chapterId: freezed == chapterId
                ? _value.chapterId
                : chapterId // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PlotPointImplCopyWith<$Res>
    implements $PlotPointCopyWith<$Res> {
  factory _$$PlotPointImplCopyWith(
    _$PlotPointImpl value,
    $Res Function(_$PlotPointImpl) then,
  ) = __$$PlotPointImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String? description,
    int order,
    PlotPointStatus? status,
    List<String> characterIds,
    List<String> locationIds,
    List<String> itemIds,
    List<String> skillIds,
    String? chapterId,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$PlotPointImplCopyWithImpl<$Res>
    extends _$PlotPointCopyWithImpl<$Res, _$PlotPointImpl>
    implements _$$PlotPointImplCopyWith<$Res> {
  __$$PlotPointImplCopyWithImpl(
    _$PlotPointImpl _value,
    $Res Function(_$PlotPointImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PlotPoint
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = freezed,
    Object? order = null,
    Object? status = freezed,
    Object? characterIds = null,
    Object? locationIds = null,
    Object? itemIds = null,
    Object? skillIds = null,
    Object? chapterId = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$PlotPointImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        order: null == order
            ? _value.order
            : order // ignore: cast_nullable_to_non_nullable
                  as int,
        status: freezed == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as PlotPointStatus?,
        characterIds: null == characterIds
            ? _value._characterIds
            : characterIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        locationIds: null == locationIds
            ? _value._locationIds
            : locationIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        itemIds: null == itemIds
            ? _value._itemIds
            : itemIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        skillIds: null == skillIds
            ? _value._skillIds
            : skillIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        chapterId: freezed == chapterId
            ? _value.chapterId
            : chapterId // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PlotPointImpl implements _PlotPoint {
  const _$PlotPointImpl({
    required this.id,
    required this.title,
    this.description,
    required this.order,
    this.status,
    final List<String> characterIds = const [],
    final List<String> locationIds = const [],
    final List<String> itemIds = const [],
    final List<String> skillIds = const [],
    this.chapterId,
    final Map<String, dynamic> metadata = const {},
    this.createdAt,
    this.updatedAt,
  }) : _characterIds = characterIds,
       _locationIds = locationIds,
       _itemIds = itemIds,
       _skillIds = skillIds,
       _metadata = metadata;

  factory _$PlotPointImpl.fromJson(Map<String, dynamic> json) =>
      _$$PlotPointImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String? description;
  @override
  final int order;
  @override
  final PlotPointStatus? status;
  final List<String> _characterIds;
  @override
  @JsonKey()
  List<String> get characterIds {
    if (_characterIds is EqualUnmodifiableListView) return _characterIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_characterIds);
  }

  final List<String> _locationIds;
  @override
  @JsonKey()
  List<String> get locationIds {
    if (_locationIds is EqualUnmodifiableListView) return _locationIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_locationIds);
  }

  final List<String> _itemIds;
  @override
  @JsonKey()
  List<String> get itemIds {
    if (_itemIds is EqualUnmodifiableListView) return _itemIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_itemIds);
  }

  final List<String> _skillIds;
  @override
  @JsonKey()
  List<String> get skillIds {
    if (_skillIds is EqualUnmodifiableListView) return _skillIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_skillIds);
  }

  @override
  final String? chapterId;
  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'PlotPoint(id: $id, title: $title, description: $description, order: $order, status: $status, characterIds: $characterIds, locationIds: $locationIds, itemIds: $itemIds, skillIds: $skillIds, chapterId: $chapterId, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlotPointImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(
              other._characterIds,
              _characterIds,
            ) &&
            const DeepCollectionEquality().equals(
              other._locationIds,
              _locationIds,
            ) &&
            const DeepCollectionEquality().equals(other._itemIds, _itemIds) &&
            const DeepCollectionEquality().equals(other._skillIds, _skillIds) &&
            (identical(other.chapterId, chapterId) ||
                other.chapterId == chapterId) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    title,
    description,
    order,
    status,
    const DeepCollectionEquality().hash(_characterIds),
    const DeepCollectionEquality().hash(_locationIds),
    const DeepCollectionEquality().hash(_itemIds),
    const DeepCollectionEquality().hash(_skillIds),
    chapterId,
    const DeepCollectionEquality().hash(_metadata),
    createdAt,
    updatedAt,
  );

  /// Create a copy of PlotPoint
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PlotPointImplCopyWith<_$PlotPointImpl> get copyWith =>
      __$$PlotPointImplCopyWithImpl<_$PlotPointImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PlotPointImplToJson(this);
  }
}

abstract class _PlotPoint implements PlotPoint {
  const factory _PlotPoint({
    required final String id,
    required final String title,
    final String? description,
    required final int order,
    final PlotPointStatus? status,
    final List<String> characterIds,
    final List<String> locationIds,
    final List<String> itemIds,
    final List<String> skillIds,
    final String? chapterId,
    final Map<String, dynamic> metadata,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$PlotPointImpl;

  factory _PlotPoint.fromJson(Map<String, dynamic> json) =
      _$PlotPointImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String? get description;
  @override
  int get order;
  @override
  PlotPointStatus? get status;
  @override
  List<String> get characterIds;
  @override
  List<String> get locationIds;
  @override
  List<String> get itemIds;
  @override
  List<String> get skillIds;
  @override
  String? get chapterId;
  @override
  Map<String, dynamic> get metadata;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of PlotPoint
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PlotPointImplCopyWith<_$PlotPointImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Item _$ItemFromJson(Map<String, dynamic> json) {
  return _Item.fromJson(json);
}

/// @nodoc
mixin _$Item {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  ItemType get type => throw _privateConstructorUsedError;
  ItemRarity get rarity => throw _privateConstructorUsedError;
  String? get origin => throw _privateConstructorUsedError;
  String? get currentOwnerId =>
      throw _privateConstructorUsedError; // 当前拥有者的角色ID
  List<String> get previousOwnerIds => throw _privateConstructorUsedError;
  List<ItemProperty> get properties => throw _privateConstructorUsedError;
  List<String> get relatedSkillIds => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this Item to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Item
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ItemCopyWith<Item> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ItemCopyWith<$Res> {
  factory $ItemCopyWith(Item value, $Res Function(Item) then) =
      _$ItemCopyWithImpl<$Res, Item>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    ItemType type,
    ItemRarity rarity,
    String? origin,
    String? currentOwnerId,
    List<String> previousOwnerIds,
    List<ItemProperty> properties,
    List<String> relatedSkillIds,
    String? imageUrl,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$ItemCopyWithImpl<$Res, $Val extends Item>
    implements $ItemCopyWith<$Res> {
  _$ItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Item
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = null,
    Object? rarity = null,
    Object? origin = freezed,
    Object? currentOwnerId = freezed,
    Object? previousOwnerIds = null,
    Object? properties = null,
    Object? relatedSkillIds = null,
    Object? imageUrl = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as ItemType,
            rarity: null == rarity
                ? _value.rarity
                : rarity // ignore: cast_nullable_to_non_nullable
                      as ItemRarity,
            origin: freezed == origin
                ? _value.origin
                : origin // ignore: cast_nullable_to_non_nullable
                      as String?,
            currentOwnerId: freezed == currentOwnerId
                ? _value.currentOwnerId
                : currentOwnerId // ignore: cast_nullable_to_non_nullable
                      as String?,
            previousOwnerIds: null == previousOwnerIds
                ? _value.previousOwnerIds
                : previousOwnerIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            properties: null == properties
                ? _value.properties
                : properties // ignore: cast_nullable_to_non_nullable
                      as List<ItemProperty>,
            relatedSkillIds: null == relatedSkillIds
                ? _value.relatedSkillIds
                : relatedSkillIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            imageUrl: freezed == imageUrl
                ? _value.imageUrl
                : imageUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ItemImplCopyWith<$Res> implements $ItemCopyWith<$Res> {
  factory _$$ItemImplCopyWith(
    _$ItemImpl value,
    $Res Function(_$ItemImpl) then,
  ) = __$$ItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    ItemType type,
    ItemRarity rarity,
    String? origin,
    String? currentOwnerId,
    List<String> previousOwnerIds,
    List<ItemProperty> properties,
    List<String> relatedSkillIds,
    String? imageUrl,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$ItemImplCopyWithImpl<$Res>
    extends _$ItemCopyWithImpl<$Res, _$ItemImpl>
    implements _$$ItemImplCopyWith<$Res> {
  __$$ItemImplCopyWithImpl(_$ItemImpl _value, $Res Function(_$ItemImpl) _then)
    : super(_value, _then);

  /// Create a copy of Item
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = null,
    Object? rarity = null,
    Object? origin = freezed,
    Object? currentOwnerId = freezed,
    Object? previousOwnerIds = null,
    Object? properties = null,
    Object? relatedSkillIds = null,
    Object? imageUrl = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$ItemImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as ItemType,
        rarity: null == rarity
            ? _value.rarity
            : rarity // ignore: cast_nullable_to_non_nullable
                  as ItemRarity,
        origin: freezed == origin
            ? _value.origin
            : origin // ignore: cast_nullable_to_non_nullable
                  as String?,
        currentOwnerId: freezed == currentOwnerId
            ? _value.currentOwnerId
            : currentOwnerId // ignore: cast_nullable_to_non_nullable
                  as String?,
        previousOwnerIds: null == previousOwnerIds
            ? _value._previousOwnerIds
            : previousOwnerIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        properties: null == properties
            ? _value._properties
            : properties // ignore: cast_nullable_to_non_nullable
                  as List<ItemProperty>,
        relatedSkillIds: null == relatedSkillIds
            ? _value._relatedSkillIds
            : relatedSkillIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        imageUrl: freezed == imageUrl
            ? _value.imageUrl
            : imageUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ItemImpl implements _Item {
  const _$ItemImpl({
    required this.id,
    required this.name,
    this.description,
    required this.type,
    required this.rarity,
    this.origin,
    this.currentOwnerId,
    final List<String> previousOwnerIds = const [],
    final List<ItemProperty> properties = const [],
    final List<String> relatedSkillIds = const [],
    this.imageUrl,
    final Map<String, dynamic> metadata = const {},
    this.createdAt,
    this.updatedAt,
  }) : _previousOwnerIds = previousOwnerIds,
       _properties = properties,
       _relatedSkillIds = relatedSkillIds,
       _metadata = metadata;

  factory _$ItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$ItemImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final ItemType type;
  @override
  final ItemRarity rarity;
  @override
  final String? origin;
  @override
  final String? currentOwnerId;
  // 当前拥有者的角色ID
  final List<String> _previousOwnerIds;
  // 当前拥有者的角色ID
  @override
  @JsonKey()
  List<String> get previousOwnerIds {
    if (_previousOwnerIds is EqualUnmodifiableListView)
      return _previousOwnerIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_previousOwnerIds);
  }

  final List<ItemProperty> _properties;
  @override
  @JsonKey()
  List<ItemProperty> get properties {
    if (_properties is EqualUnmodifiableListView) return _properties;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_properties);
  }

  final List<String> _relatedSkillIds;
  @override
  @JsonKey()
  List<String> get relatedSkillIds {
    if (_relatedSkillIds is EqualUnmodifiableListView) return _relatedSkillIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_relatedSkillIds);
  }

  @override
  final String? imageUrl;
  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'Item(id: $id, name: $name, description: $description, type: $type, rarity: $rarity, origin: $origin, currentOwnerId: $currentOwnerId, previousOwnerIds: $previousOwnerIds, properties: $properties, relatedSkillIds: $relatedSkillIds, imageUrl: $imageUrl, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.rarity, rarity) || other.rarity == rarity) &&
            (identical(other.origin, origin) || other.origin == origin) &&
            (identical(other.currentOwnerId, currentOwnerId) ||
                other.currentOwnerId == currentOwnerId) &&
            const DeepCollectionEquality().equals(
              other._previousOwnerIds,
              _previousOwnerIds,
            ) &&
            const DeepCollectionEquality().equals(
              other._properties,
              _properties,
            ) &&
            const DeepCollectionEquality().equals(
              other._relatedSkillIds,
              _relatedSkillIds,
            ) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    type,
    rarity,
    origin,
    currentOwnerId,
    const DeepCollectionEquality().hash(_previousOwnerIds),
    const DeepCollectionEquality().hash(_properties),
    const DeepCollectionEquality().hash(_relatedSkillIds),
    imageUrl,
    const DeepCollectionEquality().hash(_metadata),
    createdAt,
    updatedAt,
  );

  /// Create a copy of Item
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ItemImplCopyWith<_$ItemImpl> get copyWith =>
      __$$ItemImplCopyWithImpl<_$ItemImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ItemImplToJson(this);
  }
}

abstract class _Item implements Item {
  const factory _Item({
    required final String id,
    required final String name,
    final String? description,
    required final ItemType type,
    required final ItemRarity rarity,
    final String? origin,
    final String? currentOwnerId,
    final List<String> previousOwnerIds,
    final List<ItemProperty> properties,
    final List<String> relatedSkillIds,
    final String? imageUrl,
    final Map<String, dynamic> metadata,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$ItemImpl;

  factory _Item.fromJson(Map<String, dynamic> json) = _$ItemImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  ItemType get type;
  @override
  ItemRarity get rarity;
  @override
  String? get origin;
  @override
  String? get currentOwnerId; // 当前拥有者的角色ID
  @override
  List<String> get previousOwnerIds;
  @override
  List<ItemProperty> get properties;
  @override
  List<String> get relatedSkillIds;
  @override
  String? get imageUrl;
  @override
  Map<String, dynamic> get metadata;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of Item
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ItemImplCopyWith<_$ItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ItemProperty _$ItemPropertyFromJson(Map<String, dynamic> json) {
  return _ItemProperty.fromJson(json);
}

/// @nodoc
mixin _$ItemProperty {
  String get name => throw _privateConstructorUsedError;
  String get value => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  PropertyType? get type => throw _privateConstructorUsedError;

  /// Serializes this ItemProperty to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ItemProperty
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ItemPropertyCopyWith<ItemProperty> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ItemPropertyCopyWith<$Res> {
  factory $ItemPropertyCopyWith(
    ItemProperty value,
    $Res Function(ItemProperty) then,
  ) = _$ItemPropertyCopyWithImpl<$Res, ItemProperty>;
  @useResult
  $Res call({
    String name,
    String value,
    String? description,
    PropertyType? type,
  });
}

/// @nodoc
class _$ItemPropertyCopyWithImpl<$Res, $Val extends ItemProperty>
    implements $ItemPropertyCopyWith<$Res> {
  _$ItemPropertyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ItemProperty
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? value = null,
    Object? description = freezed,
    Object? type = freezed,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            value: null == value
                ? _value.value
                : value // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as PropertyType?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ItemPropertyImplCopyWith<$Res>
    implements $ItemPropertyCopyWith<$Res> {
  factory _$$ItemPropertyImplCopyWith(
    _$ItemPropertyImpl value,
    $Res Function(_$ItemPropertyImpl) then,
  ) = __$$ItemPropertyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    String value,
    String? description,
    PropertyType? type,
  });
}

/// @nodoc
class __$$ItemPropertyImplCopyWithImpl<$Res>
    extends _$ItemPropertyCopyWithImpl<$Res, _$ItemPropertyImpl>
    implements _$$ItemPropertyImplCopyWith<$Res> {
  __$$ItemPropertyImplCopyWithImpl(
    _$ItemPropertyImpl _value,
    $Res Function(_$ItemPropertyImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ItemProperty
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? value = null,
    Object? description = freezed,
    Object? type = freezed,
  }) {
    return _then(
      _$ItemPropertyImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        value: null == value
            ? _value.value
            : value // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as PropertyType?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ItemPropertyImpl implements _ItemProperty {
  const _$ItemPropertyImpl({
    required this.name,
    required this.value,
    this.description,
    this.type,
  });

  factory _$ItemPropertyImpl.fromJson(Map<String, dynamic> json) =>
      _$$ItemPropertyImplFromJson(json);

  @override
  final String name;
  @override
  final String value;
  @override
  final String? description;
  @override
  final PropertyType? type;

  @override
  String toString() {
    return 'ItemProperty(name: $name, value: $value, description: $description, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ItemPropertyImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, value, description, type);

  /// Create a copy of ItemProperty
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ItemPropertyImplCopyWith<_$ItemPropertyImpl> get copyWith =>
      __$$ItemPropertyImplCopyWithImpl<_$ItemPropertyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ItemPropertyImplToJson(this);
  }
}

abstract class _ItemProperty implements ItemProperty {
  const factory _ItemProperty({
    required final String name,
    required final String value,
    final String? description,
    final PropertyType? type,
  }) = _$ItemPropertyImpl;

  factory _ItemProperty.fromJson(Map<String, dynamic> json) =
      _$ItemPropertyImpl.fromJson;

  @override
  String get name;
  @override
  String get value;
  @override
  String? get description;
  @override
  PropertyType? get type;

  /// Create a copy of ItemProperty
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ItemPropertyImplCopyWith<_$ItemPropertyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Skill _$SkillFromJson(Map<String, dynamic> json) {
  return _Skill.fromJson(json);
}

/// @nodoc
mixin _$Skill {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  SkillType get type => throw _privateConstructorUsedError;
  SkillLevel get level => throw _privateConstructorUsedError;
  List<String> get prerequisites =>
      throw _privateConstructorUsedError; // 前置技能ID
  List<SkillEffect> get effects => throw _privateConstructorUsedError;
  String? get learnMethod => throw _privateConstructorUsedError;
  int? get cost => throw _privateConstructorUsedError;
  int? get cooldown => throw _privateConstructorUsedError;
  List<String> get relatedItemIds => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this Skill to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Skill
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SkillCopyWith<Skill> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SkillCopyWith<$Res> {
  factory $SkillCopyWith(Skill value, $Res Function(Skill) then) =
      _$SkillCopyWithImpl<$Res, Skill>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    SkillType type,
    SkillLevel level,
    List<String> prerequisites,
    List<SkillEffect> effects,
    String? learnMethod,
    int? cost,
    int? cooldown,
    List<String> relatedItemIds,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$SkillCopyWithImpl<$Res, $Val extends Skill>
    implements $SkillCopyWith<$Res> {
  _$SkillCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Skill
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = null,
    Object? level = null,
    Object? prerequisites = null,
    Object? effects = null,
    Object? learnMethod = freezed,
    Object? cost = freezed,
    Object? cooldown = freezed,
    Object? relatedItemIds = null,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as SkillType,
            level: null == level
                ? _value.level
                : level // ignore: cast_nullable_to_non_nullable
                      as SkillLevel,
            prerequisites: null == prerequisites
                ? _value.prerequisites
                : prerequisites // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            effects: null == effects
                ? _value.effects
                : effects // ignore: cast_nullable_to_non_nullable
                      as List<SkillEffect>,
            learnMethod: freezed == learnMethod
                ? _value.learnMethod
                : learnMethod // ignore: cast_nullable_to_non_nullable
                      as String?,
            cost: freezed == cost
                ? _value.cost
                : cost // ignore: cast_nullable_to_non_nullable
                      as int?,
            cooldown: freezed == cooldown
                ? _value.cooldown
                : cooldown // ignore: cast_nullable_to_non_nullable
                      as int?,
            relatedItemIds: null == relatedItemIds
                ? _value.relatedItemIds
                : relatedItemIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$SkillImplCopyWith<$Res> implements $SkillCopyWith<$Res> {
  factory _$$SkillImplCopyWith(
    _$SkillImpl value,
    $Res Function(_$SkillImpl) then,
  ) = __$$SkillImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    SkillType type,
    SkillLevel level,
    List<String> prerequisites,
    List<SkillEffect> effects,
    String? learnMethod,
    int? cost,
    int? cooldown,
    List<String> relatedItemIds,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$SkillImplCopyWithImpl<$Res>
    extends _$SkillCopyWithImpl<$Res, _$SkillImpl>
    implements _$$SkillImplCopyWith<$Res> {
  __$$SkillImplCopyWithImpl(
    _$SkillImpl _value,
    $Res Function(_$SkillImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Skill
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? type = null,
    Object? level = null,
    Object? prerequisites = null,
    Object? effects = null,
    Object? learnMethod = freezed,
    Object? cost = freezed,
    Object? cooldown = freezed,
    Object? relatedItemIds = null,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$SkillImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as SkillType,
        level: null == level
            ? _value.level
            : level // ignore: cast_nullable_to_non_nullable
                  as SkillLevel,
        prerequisites: null == prerequisites
            ? _value._prerequisites
            : prerequisites // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        effects: null == effects
            ? _value._effects
            : effects // ignore: cast_nullable_to_non_nullable
                  as List<SkillEffect>,
        learnMethod: freezed == learnMethod
            ? _value.learnMethod
            : learnMethod // ignore: cast_nullable_to_non_nullable
                  as String?,
        cost: freezed == cost
            ? _value.cost
            : cost // ignore: cast_nullable_to_non_nullable
                  as int?,
        cooldown: freezed == cooldown
            ? _value.cooldown
            : cooldown // ignore: cast_nullable_to_non_nullable
                  as int?,
        relatedItemIds: null == relatedItemIds
            ? _value._relatedItemIds
            : relatedItemIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SkillImpl implements _Skill {
  const _$SkillImpl({
    required this.id,
    required this.name,
    this.description,
    required this.type,
    required this.level,
    final List<String> prerequisites = const [],
    final List<SkillEffect> effects = const [],
    this.learnMethod,
    this.cost,
    this.cooldown,
    final List<String> relatedItemIds = const [],
    final Map<String, dynamic> metadata = const {},
    this.createdAt,
    this.updatedAt,
  }) : _prerequisites = prerequisites,
       _effects = effects,
       _relatedItemIds = relatedItemIds,
       _metadata = metadata;

  factory _$SkillImpl.fromJson(Map<String, dynamic> json) =>
      _$$SkillImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  final SkillType type;
  @override
  final SkillLevel level;
  final List<String> _prerequisites;
  @override
  @JsonKey()
  List<String> get prerequisites {
    if (_prerequisites is EqualUnmodifiableListView) return _prerequisites;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_prerequisites);
  }

  // 前置技能ID
  final List<SkillEffect> _effects;
  // 前置技能ID
  @override
  @JsonKey()
  List<SkillEffect> get effects {
    if (_effects is EqualUnmodifiableListView) return _effects;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_effects);
  }

  @override
  final String? learnMethod;
  @override
  final int? cost;
  @override
  final int? cooldown;
  final List<String> _relatedItemIds;
  @override
  @JsonKey()
  List<String> get relatedItemIds {
    if (_relatedItemIds is EqualUnmodifiableListView) return _relatedItemIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_relatedItemIds);
  }

  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'Skill(id: $id, name: $name, description: $description, type: $type, level: $level, prerequisites: $prerequisites, effects: $effects, learnMethod: $learnMethod, cost: $cost, cooldown: $cooldown, relatedItemIds: $relatedItemIds, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SkillImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.level, level) || other.level == level) &&
            const DeepCollectionEquality().equals(
              other._prerequisites,
              _prerequisites,
            ) &&
            const DeepCollectionEquality().equals(other._effects, _effects) &&
            (identical(other.learnMethod, learnMethod) ||
                other.learnMethod == learnMethod) &&
            (identical(other.cost, cost) || other.cost == cost) &&
            (identical(other.cooldown, cooldown) ||
                other.cooldown == cooldown) &&
            const DeepCollectionEquality().equals(
              other._relatedItemIds,
              _relatedItemIds,
            ) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    type,
    level,
    const DeepCollectionEquality().hash(_prerequisites),
    const DeepCollectionEquality().hash(_effects),
    learnMethod,
    cost,
    cooldown,
    const DeepCollectionEquality().hash(_relatedItemIds),
    const DeepCollectionEquality().hash(_metadata),
    createdAt,
    updatedAt,
  );

  /// Create a copy of Skill
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SkillImplCopyWith<_$SkillImpl> get copyWith =>
      __$$SkillImplCopyWithImpl<_$SkillImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SkillImplToJson(this);
  }
}

abstract class _Skill implements Skill {
  const factory _Skill({
    required final String id,
    required final String name,
    final String? description,
    required final SkillType type,
    required final SkillLevel level,
    final List<String> prerequisites,
    final List<SkillEffect> effects,
    final String? learnMethod,
    final int? cost,
    final int? cooldown,
    final List<String> relatedItemIds,
    final Map<String, dynamic> metadata,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$SkillImpl;

  factory _Skill.fromJson(Map<String, dynamic> json) = _$SkillImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  SkillType get type;
  @override
  SkillLevel get level;
  @override
  List<String> get prerequisites; // 前置技能ID
  @override
  List<SkillEffect> get effects;
  @override
  String? get learnMethod;
  @override
  int? get cost;
  @override
  int? get cooldown;
  @override
  List<String> get relatedItemIds;
  @override
  Map<String, dynamic> get metadata;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of Skill
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SkillImplCopyWith<_$SkillImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SkillEffect _$SkillEffectFromJson(Map<String, dynamic> json) {
  return _SkillEffect.fromJson(json);
}

/// @nodoc
mixin _$SkillEffect {
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  EffectType? get type => throw _privateConstructorUsedError;
  String? get target => throw _privateConstructorUsedError;
  String? get duration => throw _privateConstructorUsedError;
  Map<String, dynamic> get parameters => throw _privateConstructorUsedError;

  /// Serializes this SkillEffect to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SkillEffect
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SkillEffectCopyWith<SkillEffect> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SkillEffectCopyWith<$Res> {
  factory $SkillEffectCopyWith(
    SkillEffect value,
    $Res Function(SkillEffect) then,
  ) = _$SkillEffectCopyWithImpl<$Res, SkillEffect>;
  @useResult
  $Res call({
    String name,
    String description,
    EffectType? type,
    String? target,
    String? duration,
    Map<String, dynamic> parameters,
  });
}

/// @nodoc
class _$SkillEffectCopyWithImpl<$Res, $Val extends SkillEffect>
    implements $SkillEffectCopyWith<$Res> {
  _$SkillEffectCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SkillEffect
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = null,
    Object? type = freezed,
    Object? target = freezed,
    Object? duration = freezed,
    Object? parameters = null,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as EffectType?,
            target: freezed == target
                ? _value.target
                : target // ignore: cast_nullable_to_non_nullable
                      as String?,
            duration: freezed == duration
                ? _value.duration
                : duration // ignore: cast_nullable_to_non_nullable
                      as String?,
            parameters: null == parameters
                ? _value.parameters
                : parameters // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$SkillEffectImplCopyWith<$Res>
    implements $SkillEffectCopyWith<$Res> {
  factory _$$SkillEffectImplCopyWith(
    _$SkillEffectImpl value,
    $Res Function(_$SkillEffectImpl) then,
  ) = __$$SkillEffectImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    String description,
    EffectType? type,
    String? target,
    String? duration,
    Map<String, dynamic> parameters,
  });
}

/// @nodoc
class __$$SkillEffectImplCopyWithImpl<$Res>
    extends _$SkillEffectCopyWithImpl<$Res, _$SkillEffectImpl>
    implements _$$SkillEffectImplCopyWith<$Res> {
  __$$SkillEffectImplCopyWithImpl(
    _$SkillEffectImpl _value,
    $Res Function(_$SkillEffectImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of SkillEffect
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = null,
    Object? type = freezed,
    Object? target = freezed,
    Object? duration = freezed,
    Object? parameters = null,
  }) {
    return _then(
      _$SkillEffectImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as EffectType?,
        target: freezed == target
            ? _value.target
            : target // ignore: cast_nullable_to_non_nullable
                  as String?,
        duration: freezed == duration
            ? _value.duration
            : duration // ignore: cast_nullable_to_non_nullable
                  as String?,
        parameters: null == parameters
            ? _value._parameters
            : parameters // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SkillEffectImpl implements _SkillEffect {
  const _$SkillEffectImpl({
    required this.name,
    required this.description,
    this.type,
    this.target,
    this.duration,
    final Map<String, dynamic> parameters = const {},
  }) : _parameters = parameters;

  factory _$SkillEffectImpl.fromJson(Map<String, dynamic> json) =>
      _$$SkillEffectImplFromJson(json);

  @override
  final String name;
  @override
  final String description;
  @override
  final EffectType? type;
  @override
  final String? target;
  @override
  final String? duration;
  final Map<String, dynamic> _parameters;
  @override
  @JsonKey()
  Map<String, dynamic> get parameters {
    if (_parameters is EqualUnmodifiableMapView) return _parameters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_parameters);
  }

  @override
  String toString() {
    return 'SkillEffect(name: $name, description: $description, type: $type, target: $target, duration: $duration, parameters: $parameters)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SkillEffectImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.target, target) || other.target == target) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            const DeepCollectionEquality().equals(
              other._parameters,
              _parameters,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    description,
    type,
    target,
    duration,
    const DeepCollectionEquality().hash(_parameters),
  );

  /// Create a copy of SkillEffect
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SkillEffectImplCopyWith<_$SkillEffectImpl> get copyWith =>
      __$$SkillEffectImplCopyWithImpl<_$SkillEffectImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SkillEffectImplToJson(this);
  }
}

abstract class _SkillEffect implements SkillEffect {
  const factory _SkillEffect({
    required final String name,
    required final String description,
    final EffectType? type,
    final String? target,
    final String? duration,
    final Map<String, dynamic> parameters,
  }) = _$SkillEffectImpl;

  factory _SkillEffect.fromJson(Map<String, dynamic> json) =
      _$SkillEffectImpl.fromJson;

  @override
  String get name;
  @override
  String get description;
  @override
  EffectType? get type;
  @override
  String? get target;
  @override
  String? get duration;
  @override
  Map<String, dynamic> get parameters;

  /// Create a copy of SkillEffect
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SkillEffectImplCopyWith<_$SkillEffectImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Constraint _$ConstraintFromJson(Map<String, dynamic> json) {
  return _Constraint.fromJson(json);
}

/// @nodoc
mixin _$Constraint {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  ConstraintType get type => throw _privateConstructorUsedError;
  ConstraintSeverity get severity => throw _privateConstructorUsedError;
  List<String> get affectedEntityIds => throw _privateConstructorUsedError;
  List<ConstraintRule> get rules => throw _privateConstructorUsedError;
  bool? get isActive => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this Constraint to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Constraint
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ConstraintCopyWith<Constraint> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConstraintCopyWith<$Res> {
  factory $ConstraintCopyWith(
    Constraint value,
    $Res Function(Constraint) then,
  ) = _$ConstraintCopyWithImpl<$Res, Constraint>;
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    ConstraintType type,
    ConstraintSeverity severity,
    List<String> affectedEntityIds,
    List<ConstraintRule> rules,
    bool? isActive,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$ConstraintCopyWithImpl<$Res, $Val extends Constraint>
    implements $ConstraintCopyWith<$Res> {
  _$ConstraintCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Constraint
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? type = null,
    Object? severity = null,
    Object? affectedEntityIds = null,
    Object? rules = null,
    Object? isActive = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as ConstraintType,
            severity: null == severity
                ? _value.severity
                : severity // ignore: cast_nullable_to_non_nullable
                      as ConstraintSeverity,
            affectedEntityIds: null == affectedEntityIds
                ? _value.affectedEntityIds
                : affectedEntityIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            rules: null == rules
                ? _value.rules
                : rules // ignore: cast_nullable_to_non_nullable
                      as List<ConstraintRule>,
            isActive: freezed == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                      as bool?,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ConstraintImplCopyWith<$Res>
    implements $ConstraintCopyWith<$Res> {
  factory _$$ConstraintImplCopyWith(
    _$ConstraintImpl value,
    $Res Function(_$ConstraintImpl) then,
  ) = __$$ConstraintImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    ConstraintType type,
    ConstraintSeverity severity,
    List<String> affectedEntityIds,
    List<ConstraintRule> rules,
    bool? isActive,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$ConstraintImplCopyWithImpl<$Res>
    extends _$ConstraintCopyWithImpl<$Res, _$ConstraintImpl>
    implements _$$ConstraintImplCopyWith<$Res> {
  __$$ConstraintImplCopyWithImpl(
    _$ConstraintImpl _value,
    $Res Function(_$ConstraintImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Constraint
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? type = null,
    Object? severity = null,
    Object? affectedEntityIds = null,
    Object? rules = null,
    Object? isActive = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$ConstraintImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as ConstraintType,
        severity: null == severity
            ? _value.severity
            : severity // ignore: cast_nullable_to_non_nullable
                  as ConstraintSeverity,
        affectedEntityIds: null == affectedEntityIds
            ? _value._affectedEntityIds
            : affectedEntityIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        rules: null == rules
            ? _value._rules
            : rules // ignore: cast_nullable_to_non_nullable
                  as List<ConstraintRule>,
        isActive: freezed == isActive
            ? _value.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool?,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ConstraintImpl implements _Constraint {
  const _$ConstraintImpl({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.severity,
    final List<String> affectedEntityIds = const [],
    final List<ConstraintRule> rules = const [],
    this.isActive,
    final Map<String, dynamic> metadata = const {},
    this.createdAt,
    this.updatedAt,
  }) : _affectedEntityIds = affectedEntityIds,
       _rules = rules,
       _metadata = metadata;

  factory _$ConstraintImpl.fromJson(Map<String, dynamic> json) =>
      _$$ConstraintImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final ConstraintType type;
  @override
  final ConstraintSeverity severity;
  final List<String> _affectedEntityIds;
  @override
  @JsonKey()
  List<String> get affectedEntityIds {
    if (_affectedEntityIds is EqualUnmodifiableListView)
      return _affectedEntityIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_affectedEntityIds);
  }

  final List<ConstraintRule> _rules;
  @override
  @JsonKey()
  List<ConstraintRule> get rules {
    if (_rules is EqualUnmodifiableListView) return _rules;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_rules);
  }

  @override
  final bool? isActive;
  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'Constraint(id: $id, name: $name, description: $description, type: $type, severity: $severity, affectedEntityIds: $affectedEntityIds, rules: $rules, isActive: $isActive, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConstraintImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.severity, severity) ||
                other.severity == severity) &&
            const DeepCollectionEquality().equals(
              other._affectedEntityIds,
              _affectedEntityIds,
            ) &&
            const DeepCollectionEquality().equals(other._rules, _rules) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    type,
    severity,
    const DeepCollectionEquality().hash(_affectedEntityIds),
    const DeepCollectionEquality().hash(_rules),
    isActive,
    const DeepCollectionEquality().hash(_metadata),
    createdAt,
    updatedAt,
  );

  /// Create a copy of Constraint
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConstraintImplCopyWith<_$ConstraintImpl> get copyWith =>
      __$$ConstraintImplCopyWithImpl<_$ConstraintImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ConstraintImplToJson(this);
  }
}

abstract class _Constraint implements Constraint {
  const factory _Constraint({
    required final String id,
    required final String name,
    required final String description,
    required final ConstraintType type,
    required final ConstraintSeverity severity,
    final List<String> affectedEntityIds,
    final List<ConstraintRule> rules,
    final bool? isActive,
    final Map<String, dynamic> metadata,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$ConstraintImpl;

  factory _Constraint.fromJson(Map<String, dynamic> json) =
      _$ConstraintImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  ConstraintType get type;
  @override
  ConstraintSeverity get severity;
  @override
  List<String> get affectedEntityIds;
  @override
  List<ConstraintRule> get rules;
  @override
  bool? get isActive;
  @override
  Map<String, dynamic> get metadata;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of Constraint
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConstraintImplCopyWith<_$ConstraintImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ConstraintRule _$ConstraintRuleFromJson(Map<String, dynamic> json) {
  return _ConstraintRule.fromJson(json);
}

/// @nodoc
mixin _$ConstraintRule {
  String get condition => throw _privateConstructorUsedError;
  String get action => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  Map<String, dynamic> get parameters => throw _privateConstructorUsedError;

  /// Serializes this ConstraintRule to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ConstraintRule
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ConstraintRuleCopyWith<ConstraintRule> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConstraintRuleCopyWith<$Res> {
  factory $ConstraintRuleCopyWith(
    ConstraintRule value,
    $Res Function(ConstraintRule) then,
  ) = _$ConstraintRuleCopyWithImpl<$Res, ConstraintRule>;
  @useResult
  $Res call({
    String condition,
    String action,
    String? message,
    Map<String, dynamic> parameters,
  });
}

/// @nodoc
class _$ConstraintRuleCopyWithImpl<$Res, $Val extends ConstraintRule>
    implements $ConstraintRuleCopyWith<$Res> {
  _$ConstraintRuleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ConstraintRule
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? condition = null,
    Object? action = null,
    Object? message = freezed,
    Object? parameters = null,
  }) {
    return _then(
      _value.copyWith(
            condition: null == condition
                ? _value.condition
                : condition // ignore: cast_nullable_to_non_nullable
                      as String,
            action: null == action
                ? _value.action
                : action // ignore: cast_nullable_to_non_nullable
                      as String,
            message: freezed == message
                ? _value.message
                : message // ignore: cast_nullable_to_non_nullable
                      as String?,
            parameters: null == parameters
                ? _value.parameters
                : parameters // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ConstraintRuleImplCopyWith<$Res>
    implements $ConstraintRuleCopyWith<$Res> {
  factory _$$ConstraintRuleImplCopyWith(
    _$ConstraintRuleImpl value,
    $Res Function(_$ConstraintRuleImpl) then,
  ) = __$$ConstraintRuleImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String condition,
    String action,
    String? message,
    Map<String, dynamic> parameters,
  });
}

/// @nodoc
class __$$ConstraintRuleImplCopyWithImpl<$Res>
    extends _$ConstraintRuleCopyWithImpl<$Res, _$ConstraintRuleImpl>
    implements _$$ConstraintRuleImplCopyWith<$Res> {
  __$$ConstraintRuleImplCopyWithImpl(
    _$ConstraintRuleImpl _value,
    $Res Function(_$ConstraintRuleImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ConstraintRule
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? condition = null,
    Object? action = null,
    Object? message = freezed,
    Object? parameters = null,
  }) {
    return _then(
      _$ConstraintRuleImpl(
        condition: null == condition
            ? _value.condition
            : condition // ignore: cast_nullable_to_non_nullable
                  as String,
        action: null == action
            ? _value.action
            : action // ignore: cast_nullable_to_non_nullable
                  as String,
        message: freezed == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String?,
        parameters: null == parameters
            ? _value._parameters
            : parameters // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ConstraintRuleImpl implements _ConstraintRule {
  const _$ConstraintRuleImpl({
    required this.condition,
    required this.action,
    this.message,
    final Map<String, dynamic> parameters = const {},
  }) : _parameters = parameters;

  factory _$ConstraintRuleImpl.fromJson(Map<String, dynamic> json) =>
      _$$ConstraintRuleImplFromJson(json);

  @override
  final String condition;
  @override
  final String action;
  @override
  final String? message;
  final Map<String, dynamic> _parameters;
  @override
  @JsonKey()
  Map<String, dynamic> get parameters {
    if (_parameters is EqualUnmodifiableMapView) return _parameters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_parameters);
  }

  @override
  String toString() {
    return 'ConstraintRule(condition: $condition, action: $action, message: $message, parameters: $parameters)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConstraintRuleImpl &&
            (identical(other.condition, condition) ||
                other.condition == condition) &&
            (identical(other.action, action) || other.action == action) &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(
              other._parameters,
              _parameters,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    condition,
    action,
    message,
    const DeepCollectionEquality().hash(_parameters),
  );

  /// Create a copy of ConstraintRule
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConstraintRuleImplCopyWith<_$ConstraintRuleImpl> get copyWith =>
      __$$ConstraintRuleImplCopyWithImpl<_$ConstraintRuleImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ConstraintRuleImplToJson(this);
  }
}

abstract class _ConstraintRule implements ConstraintRule {
  const factory _ConstraintRule({
    required final String condition,
    required final String action,
    final String? message,
    final Map<String, dynamic> parameters,
  }) = _$ConstraintRuleImpl;

  factory _ConstraintRule.fromJson(Map<String, dynamic> json) =
      _$ConstraintRuleImpl.fromJson;

  @override
  String get condition;
  @override
  String get action;
  @override
  String? get message;
  @override
  Map<String, dynamic> get parameters;

  /// Create a copy of ConstraintRule
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConstraintRuleImplCopyWith<_$ConstraintRuleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Relationship _$RelationshipFromJson(Map<String, dynamic> json) {
  return _Relationship.fromJson(json);
}

/// @nodoc
mixin _$Relationship {
  String get id => throw _privateConstructorUsedError;
  String get fromEntityId => throw _privateConstructorUsedError;
  String get toEntityId => throw _privateConstructorUsedError;
  RelationshipType get type => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  int? get strength => throw _privateConstructorUsedError; // 关系强度 -100 到 100
  List<RelationshipEvent> get history => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this Relationship to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Relationship
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RelationshipCopyWith<Relationship> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RelationshipCopyWith<$Res> {
  factory $RelationshipCopyWith(
    Relationship value,
    $Res Function(Relationship) then,
  ) = _$RelationshipCopyWithImpl<$Res, Relationship>;
  @useResult
  $Res call({
    String id,
    String fromEntityId,
    String toEntityId,
    RelationshipType type,
    String? description,
    int? strength,
    List<RelationshipEvent> history,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$RelationshipCopyWithImpl<$Res, $Val extends Relationship>
    implements $RelationshipCopyWith<$Res> {
  _$RelationshipCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Relationship
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? fromEntityId = null,
    Object? toEntityId = null,
    Object? type = null,
    Object? description = freezed,
    Object? strength = freezed,
    Object? history = null,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            fromEntityId: null == fromEntityId
                ? _value.fromEntityId
                : fromEntityId // ignore: cast_nullable_to_non_nullable
                      as String,
            toEntityId: null == toEntityId
                ? _value.toEntityId
                : toEntityId // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as RelationshipType,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            strength: freezed == strength
                ? _value.strength
                : strength // ignore: cast_nullable_to_non_nullable
                      as int?,
            history: null == history
                ? _value.history
                : history // ignore: cast_nullable_to_non_nullable
                      as List<RelationshipEvent>,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$RelationshipImplCopyWith<$Res>
    implements $RelationshipCopyWith<$Res> {
  factory _$$RelationshipImplCopyWith(
    _$RelationshipImpl value,
    $Res Function(_$RelationshipImpl) then,
  ) = __$$RelationshipImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String fromEntityId,
    String toEntityId,
    RelationshipType type,
    String? description,
    int? strength,
    List<RelationshipEvent> history,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$RelationshipImplCopyWithImpl<$Res>
    extends _$RelationshipCopyWithImpl<$Res, _$RelationshipImpl>
    implements _$$RelationshipImplCopyWith<$Res> {
  __$$RelationshipImplCopyWithImpl(
    _$RelationshipImpl _value,
    $Res Function(_$RelationshipImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Relationship
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? fromEntityId = null,
    Object? toEntityId = null,
    Object? type = null,
    Object? description = freezed,
    Object? strength = freezed,
    Object? history = null,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$RelationshipImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        fromEntityId: null == fromEntityId
            ? _value.fromEntityId
            : fromEntityId // ignore: cast_nullable_to_non_nullable
                  as String,
        toEntityId: null == toEntityId
            ? _value.toEntityId
            : toEntityId // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as RelationshipType,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        strength: freezed == strength
            ? _value.strength
            : strength // ignore: cast_nullable_to_non_nullable
                  as int?,
        history: null == history
            ? _value._history
            : history // ignore: cast_nullable_to_non_nullable
                  as List<RelationshipEvent>,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$RelationshipImpl implements _Relationship {
  const _$RelationshipImpl({
    required this.id,
    required this.fromEntityId,
    required this.toEntityId,
    required this.type,
    this.description,
    this.strength,
    final List<RelationshipEvent> history = const [],
    final Map<String, dynamic> metadata = const {},
    this.createdAt,
    this.updatedAt,
  }) : _history = history,
       _metadata = metadata;

  factory _$RelationshipImpl.fromJson(Map<String, dynamic> json) =>
      _$$RelationshipImplFromJson(json);

  @override
  final String id;
  @override
  final String fromEntityId;
  @override
  final String toEntityId;
  @override
  final RelationshipType type;
  @override
  final String? description;
  @override
  final int? strength;
  // 关系强度 -100 到 100
  final List<RelationshipEvent> _history;
  // 关系强度 -100 到 100
  @override
  @JsonKey()
  List<RelationshipEvent> get history {
    if (_history is EqualUnmodifiableListView) return _history;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_history);
  }

  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'Relationship(id: $id, fromEntityId: $fromEntityId, toEntityId: $toEntityId, type: $type, description: $description, strength: $strength, history: $history, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RelationshipImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.fromEntityId, fromEntityId) ||
                other.fromEntityId == fromEntityId) &&
            (identical(other.toEntityId, toEntityId) ||
                other.toEntityId == toEntityId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.strength, strength) ||
                other.strength == strength) &&
            const DeepCollectionEquality().equals(other._history, _history) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    fromEntityId,
    toEntityId,
    type,
    description,
    strength,
    const DeepCollectionEquality().hash(_history),
    const DeepCollectionEquality().hash(_metadata),
    createdAt,
    updatedAt,
  );

  /// Create a copy of Relationship
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RelationshipImplCopyWith<_$RelationshipImpl> get copyWith =>
      __$$RelationshipImplCopyWithImpl<_$RelationshipImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RelationshipImplToJson(this);
  }
}

abstract class _Relationship implements Relationship {
  const factory _Relationship({
    required final String id,
    required final String fromEntityId,
    required final String toEntityId,
    required final RelationshipType type,
    final String? description,
    final int? strength,
    final List<RelationshipEvent> history,
    final Map<String, dynamic> metadata,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$RelationshipImpl;

  factory _Relationship.fromJson(Map<String, dynamic> json) =
      _$RelationshipImpl.fromJson;

  @override
  String get id;
  @override
  String get fromEntityId;
  @override
  String get toEntityId;
  @override
  RelationshipType get type;
  @override
  String? get description;
  @override
  int? get strength; // 关系强度 -100 到 100
  @override
  List<RelationshipEvent> get history;
  @override
  Map<String, dynamic> get metadata;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of Relationship
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RelationshipImplCopyWith<_$RelationshipImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RelationshipEvent _$RelationshipEventFromJson(Map<String, dynamic> json) {
  return _RelationshipEvent.fromJson(json);
}

/// @nodoc
mixin _$RelationshipEvent {
  String get id => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  int? get impactOnStrength => throw _privateConstructorUsedError;
  String? get chapterId => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;

  /// Serializes this RelationshipEvent to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RelationshipEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RelationshipEventCopyWith<RelationshipEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RelationshipEventCopyWith<$Res> {
  factory $RelationshipEventCopyWith(
    RelationshipEvent value,
    $Res Function(RelationshipEvent) then,
  ) = _$RelationshipEventCopyWithImpl<$Res, RelationshipEvent>;
  @useResult
  $Res call({
    String id,
    String description,
    DateTime timestamp,
    int? impactOnStrength,
    String? chapterId,
    Map<String, dynamic> metadata,
  });
}

/// @nodoc
class _$RelationshipEventCopyWithImpl<$Res, $Val extends RelationshipEvent>
    implements $RelationshipEventCopyWith<$Res> {
  _$RelationshipEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RelationshipEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? description = null,
    Object? timestamp = null,
    Object? impactOnStrength = freezed,
    Object? chapterId = freezed,
    Object? metadata = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            impactOnStrength: freezed == impactOnStrength
                ? _value.impactOnStrength
                : impactOnStrength // ignore: cast_nullable_to_non_nullable
                      as int?,
            chapterId: freezed == chapterId
                ? _value.chapterId
                : chapterId // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$RelationshipEventImplCopyWith<$Res>
    implements $RelationshipEventCopyWith<$Res> {
  factory _$$RelationshipEventImplCopyWith(
    _$RelationshipEventImpl value,
    $Res Function(_$RelationshipEventImpl) then,
  ) = __$$RelationshipEventImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String description,
    DateTime timestamp,
    int? impactOnStrength,
    String? chapterId,
    Map<String, dynamic> metadata,
  });
}

/// @nodoc
class __$$RelationshipEventImplCopyWithImpl<$Res>
    extends _$RelationshipEventCopyWithImpl<$Res, _$RelationshipEventImpl>
    implements _$$RelationshipEventImplCopyWith<$Res> {
  __$$RelationshipEventImplCopyWithImpl(
    _$RelationshipEventImpl _value,
    $Res Function(_$RelationshipEventImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of RelationshipEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? description = null,
    Object? timestamp = null,
    Object? impactOnStrength = freezed,
    Object? chapterId = freezed,
    Object? metadata = null,
  }) {
    return _then(
      _$RelationshipEventImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        impactOnStrength: freezed == impactOnStrength
            ? _value.impactOnStrength
            : impactOnStrength // ignore: cast_nullable_to_non_nullable
                  as int?,
        chapterId: freezed == chapterId
            ? _value.chapterId
            : chapterId // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$RelationshipEventImpl implements _RelationshipEvent {
  const _$RelationshipEventImpl({
    required this.id,
    required this.description,
    required this.timestamp,
    this.impactOnStrength,
    this.chapterId,
    final Map<String, dynamic> metadata = const {},
  }) : _metadata = metadata;

  factory _$RelationshipEventImpl.fromJson(Map<String, dynamic> json) =>
      _$$RelationshipEventImplFromJson(json);

  @override
  final String id;
  @override
  final String description;
  @override
  final DateTime timestamp;
  @override
  final int? impactOnStrength;
  @override
  final String? chapterId;
  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  String toString() {
    return 'RelationshipEvent(id: $id, description: $description, timestamp: $timestamp, impactOnStrength: $impactOnStrength, chapterId: $chapterId, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RelationshipEventImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.impactOnStrength, impactOnStrength) ||
                other.impactOnStrength == impactOnStrength) &&
            (identical(other.chapterId, chapterId) ||
                other.chapterId == chapterId) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    description,
    timestamp,
    impactOnStrength,
    chapterId,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of RelationshipEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RelationshipEventImplCopyWith<_$RelationshipEventImpl> get copyWith =>
      __$$RelationshipEventImplCopyWithImpl<_$RelationshipEventImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$RelationshipEventImplToJson(this);
  }
}

abstract class _RelationshipEvent implements RelationshipEvent {
  const factory _RelationshipEvent({
    required final String id,
    required final String description,
    required final DateTime timestamp,
    final int? impactOnStrength,
    final String? chapterId,
    final Map<String, dynamic> metadata,
  }) = _$RelationshipEventImpl;

  factory _RelationshipEvent.fromJson(Map<String, dynamic> json) =
      _$RelationshipEventImpl.fromJson;

  @override
  String get id;
  @override
  String get description;
  @override
  DateTime get timestamp;
  @override
  int? get impactOnStrength;
  @override
  String? get chapterId;
  @override
  Map<String, dynamic> get metadata;

  /// Create a copy of RelationshipEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RelationshipEventImplCopyWith<_$RelationshipEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Timeline _$TimelineFromJson(Map<String, dynamic> json) {
  return _Timeline.fromJson(json);
}

/// @nodoc
mixin _$Timeline {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  List<TimelineEvent> get events => throw _privateConstructorUsedError;
  DateTime? get startDate => throw _privateConstructorUsedError;
  DateTime? get endDate => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this Timeline to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Timeline
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TimelineCopyWith<Timeline> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TimelineCopyWith<$Res> {
  factory $TimelineCopyWith(Timeline value, $Res Function(Timeline) then) =
      _$TimelineCopyWithImpl<$Res, Timeline>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    List<TimelineEvent> events,
    DateTime? startDate,
    DateTime? endDate,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$TimelineCopyWithImpl<$Res, $Val extends Timeline>
    implements $TimelineCopyWith<$Res> {
  _$TimelineCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Timeline
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? events = null,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            events: null == events
                ? _value.events
                : events // ignore: cast_nullable_to_non_nullable
                      as List<TimelineEvent>,
            startDate: freezed == startDate
                ? _value.startDate
                : startDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            endDate: freezed == endDate
                ? _value.endDate
                : endDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$TimelineImplCopyWith<$Res>
    implements $TimelineCopyWith<$Res> {
  factory _$$TimelineImplCopyWith(
    _$TimelineImpl value,
    $Res Function(_$TimelineImpl) then,
  ) = __$$TimelineImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    List<TimelineEvent> events,
    DateTime? startDate,
    DateTime? endDate,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$TimelineImplCopyWithImpl<$Res>
    extends _$TimelineCopyWithImpl<$Res, _$TimelineImpl>
    implements _$$TimelineImplCopyWith<$Res> {
  __$$TimelineImplCopyWithImpl(
    _$TimelineImpl _value,
    $Res Function(_$TimelineImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Timeline
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? events = null,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$TimelineImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        events: null == events
            ? _value._events
            : events // ignore: cast_nullable_to_non_nullable
                  as List<TimelineEvent>,
        startDate: freezed == startDate
            ? _value.startDate
            : startDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        endDate: freezed == endDate
            ? _value.endDate
            : endDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$TimelineImpl implements _Timeline {
  const _$TimelineImpl({
    required this.id,
    required this.name,
    this.description,
    final List<TimelineEvent> events = const [],
    this.startDate,
    this.endDate,
    final Map<String, dynamic> metadata = const {},
    this.createdAt,
    this.updatedAt,
  }) : _events = events,
       _metadata = metadata;

  factory _$TimelineImpl.fromJson(Map<String, dynamic> json) =>
      _$$TimelineImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  final List<TimelineEvent> _events;
  @override
  @JsonKey()
  List<TimelineEvent> get events {
    if (_events is EqualUnmodifiableListView) return _events;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_events);
  }

  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;
  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'Timeline(id: $id, name: $name, description: $description, events: $events, startDate: $startDate, endDate: $endDate, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TimelineImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(other._events, _events) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    const DeepCollectionEquality().hash(_events),
    startDate,
    endDate,
    const DeepCollectionEquality().hash(_metadata),
    createdAt,
    updatedAt,
  );

  /// Create a copy of Timeline
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TimelineImplCopyWith<_$TimelineImpl> get copyWith =>
      __$$TimelineImplCopyWithImpl<_$TimelineImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TimelineImplToJson(this);
  }
}

abstract class _Timeline implements Timeline {
  const factory _Timeline({
    required final String id,
    required final String name,
    final String? description,
    final List<TimelineEvent> events,
    final DateTime? startDate,
    final DateTime? endDate,
    final Map<String, dynamic> metadata,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$TimelineImpl;

  factory _Timeline.fromJson(Map<String, dynamic> json) =
      _$TimelineImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  List<TimelineEvent> get events;
  @override
  DateTime? get startDate;
  @override
  DateTime? get endDate;
  @override
  Map<String, dynamic> get metadata;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of Timeline
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TimelineImplCopyWith<_$TimelineImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TimelineEvent _$TimelineEventFromJson(Map<String, dynamic> json) {
  return _TimelineEvent.fromJson(json);
}

/// @nodoc
mixin _$TimelineEvent {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  DateTime get eventDate => throw _privateConstructorUsedError;
  List<String> get involvedCharacterIds => throw _privateConstructorUsedError;
  List<String> get involvedLocationIds => throw _privateConstructorUsedError;
  String? get chapterId => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this TimelineEvent to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TimelineEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TimelineEventCopyWith<TimelineEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TimelineEventCopyWith<$Res> {
  factory $TimelineEventCopyWith(
    TimelineEvent value,
    $Res Function(TimelineEvent) then,
  ) = _$TimelineEventCopyWithImpl<$Res, TimelineEvent>;
  @useResult
  $Res call({
    String id,
    String title,
    String? description,
    DateTime eventDate,
    List<String> involvedCharacterIds,
    List<String> involvedLocationIds,
    String? chapterId,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$TimelineEventCopyWithImpl<$Res, $Val extends TimelineEvent>
    implements $TimelineEventCopyWith<$Res> {
  _$TimelineEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TimelineEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = freezed,
    Object? eventDate = null,
    Object? involvedCharacterIds = null,
    Object? involvedLocationIds = null,
    Object? chapterId = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            eventDate: null == eventDate
                ? _value.eventDate
                : eventDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            involvedCharacterIds: null == involvedCharacterIds
                ? _value.involvedCharacterIds
                : involvedCharacterIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            involvedLocationIds: null == involvedLocationIds
                ? _value.involvedLocationIds
                : involvedLocationIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            chapterId: freezed == chapterId
                ? _value.chapterId
                : chapterId // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$TimelineEventImplCopyWith<$Res>
    implements $TimelineEventCopyWith<$Res> {
  factory _$$TimelineEventImplCopyWith(
    _$TimelineEventImpl value,
    $Res Function(_$TimelineEventImpl) then,
  ) = __$$TimelineEventImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String? description,
    DateTime eventDate,
    List<String> involvedCharacterIds,
    List<String> involvedLocationIds,
    String? chapterId,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$TimelineEventImplCopyWithImpl<$Res>
    extends _$TimelineEventCopyWithImpl<$Res, _$TimelineEventImpl>
    implements _$$TimelineEventImplCopyWith<$Res> {
  __$$TimelineEventImplCopyWithImpl(
    _$TimelineEventImpl _value,
    $Res Function(_$TimelineEventImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TimelineEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = freezed,
    Object? eventDate = null,
    Object? involvedCharacterIds = null,
    Object? involvedLocationIds = null,
    Object? chapterId = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$TimelineEventImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        eventDate: null == eventDate
            ? _value.eventDate
            : eventDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        involvedCharacterIds: null == involvedCharacterIds
            ? _value._involvedCharacterIds
            : involvedCharacterIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        involvedLocationIds: null == involvedLocationIds
            ? _value._involvedLocationIds
            : involvedLocationIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        chapterId: freezed == chapterId
            ? _value.chapterId
            : chapterId // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$TimelineEventImpl implements _TimelineEvent {
  const _$TimelineEventImpl({
    required this.id,
    required this.title,
    this.description,
    required this.eventDate,
    final List<String> involvedCharacterIds = const [],
    final List<String> involvedLocationIds = const [],
    this.chapterId,
    final Map<String, dynamic> metadata = const {},
    this.createdAt,
    this.updatedAt,
  }) : _involvedCharacterIds = involvedCharacterIds,
       _involvedLocationIds = involvedLocationIds,
       _metadata = metadata;

  factory _$TimelineEventImpl.fromJson(Map<String, dynamic> json) =>
      _$$TimelineEventImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String? description;
  @override
  final DateTime eventDate;
  final List<String> _involvedCharacterIds;
  @override
  @JsonKey()
  List<String> get involvedCharacterIds {
    if (_involvedCharacterIds is EqualUnmodifiableListView)
      return _involvedCharacterIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_involvedCharacterIds);
  }

  final List<String> _involvedLocationIds;
  @override
  @JsonKey()
  List<String> get involvedLocationIds {
    if (_involvedLocationIds is EqualUnmodifiableListView)
      return _involvedLocationIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_involvedLocationIds);
  }

  @override
  final String? chapterId;
  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'TimelineEvent(id: $id, title: $title, description: $description, eventDate: $eventDate, involvedCharacterIds: $involvedCharacterIds, involvedLocationIds: $involvedLocationIds, chapterId: $chapterId, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TimelineEventImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.eventDate, eventDate) ||
                other.eventDate == eventDate) &&
            const DeepCollectionEquality().equals(
              other._involvedCharacterIds,
              _involvedCharacterIds,
            ) &&
            const DeepCollectionEquality().equals(
              other._involvedLocationIds,
              _involvedLocationIds,
            ) &&
            (identical(other.chapterId, chapterId) ||
                other.chapterId == chapterId) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    title,
    description,
    eventDate,
    const DeepCollectionEquality().hash(_involvedCharacterIds),
    const DeepCollectionEquality().hash(_involvedLocationIds),
    chapterId,
    const DeepCollectionEquality().hash(_metadata),
    createdAt,
    updatedAt,
  );

  /// Create a copy of TimelineEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TimelineEventImplCopyWith<_$TimelineEventImpl> get copyWith =>
      __$$TimelineEventImplCopyWithImpl<_$TimelineEventImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TimelineEventImplToJson(this);
  }
}

abstract class _TimelineEvent implements TimelineEvent {
  const factory _TimelineEvent({
    required final String id,
    required final String title,
    final String? description,
    required final DateTime eventDate,
    final List<String> involvedCharacterIds,
    final List<String> involvedLocationIds,
    final String? chapterId,
    final Map<String, dynamic> metadata,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$TimelineEventImpl;

  factory _TimelineEvent.fromJson(Map<String, dynamic> json) =
      _$TimelineEventImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String? get description;
  @override
  DateTime get eventDate;
  @override
  List<String> get involvedCharacterIds;
  @override
  List<String> get involvedLocationIds;
  @override
  String? get chapterId;
  @override
  Map<String, dynamic> get metadata;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of TimelineEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TimelineEventImplCopyWith<_$TimelineEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ChapterReference _$ChapterReferenceFromJson(Map<String, dynamic> json) {
  return _ChapterReference.fromJson(json);
}

/// @nodoc
mixin _$ChapterReference {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  int? get chapterNumber => throw _privateConstructorUsedError;
  String? get summary => throw _privateConstructorUsedError;
  List<String> get characterIds => throw _privateConstructorUsedError;
  List<String> get locationIds => throw _privateConstructorUsedError;
  List<String> get plotPointIds => throw _privateConstructorUsedError;
  DateTime? get publishDate => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;

  /// Serializes this ChapterReference to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChapterReference
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChapterReferenceCopyWith<ChapterReference> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChapterReferenceCopyWith<$Res> {
  factory $ChapterReferenceCopyWith(
    ChapterReference value,
    $Res Function(ChapterReference) then,
  ) = _$ChapterReferenceCopyWithImpl<$Res, ChapterReference>;
  @useResult
  $Res call({
    String id,
    String title,
    int? chapterNumber,
    String? summary,
    List<String> characterIds,
    List<String> locationIds,
    List<String> plotPointIds,
    DateTime? publishDate,
    Map<String, dynamic> metadata,
  });
}

/// @nodoc
class _$ChapterReferenceCopyWithImpl<$Res, $Val extends ChapterReference>
    implements $ChapterReferenceCopyWith<$Res> {
  _$ChapterReferenceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChapterReference
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? chapterNumber = freezed,
    Object? summary = freezed,
    Object? characterIds = null,
    Object? locationIds = null,
    Object? plotPointIds = null,
    Object? publishDate = freezed,
    Object? metadata = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            chapterNumber: freezed == chapterNumber
                ? _value.chapterNumber
                : chapterNumber // ignore: cast_nullable_to_non_nullable
                      as int?,
            summary: freezed == summary
                ? _value.summary
                : summary // ignore: cast_nullable_to_non_nullable
                      as String?,
            characterIds: null == characterIds
                ? _value.characterIds
                : characterIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            locationIds: null == locationIds
                ? _value.locationIds
                : locationIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            plotPointIds: null == plotPointIds
                ? _value.plotPointIds
                : plotPointIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            publishDate: freezed == publishDate
                ? _value.publishDate
                : publishDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ChapterReferenceImplCopyWith<$Res>
    implements $ChapterReferenceCopyWith<$Res> {
  factory _$$ChapterReferenceImplCopyWith(
    _$ChapterReferenceImpl value,
    $Res Function(_$ChapterReferenceImpl) then,
  ) = __$$ChapterReferenceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    int? chapterNumber,
    String? summary,
    List<String> characterIds,
    List<String> locationIds,
    List<String> plotPointIds,
    DateTime? publishDate,
    Map<String, dynamic> metadata,
  });
}

/// @nodoc
class __$$ChapterReferenceImplCopyWithImpl<$Res>
    extends _$ChapterReferenceCopyWithImpl<$Res, _$ChapterReferenceImpl>
    implements _$$ChapterReferenceImplCopyWith<$Res> {
  __$$ChapterReferenceImplCopyWithImpl(
    _$ChapterReferenceImpl _value,
    $Res Function(_$ChapterReferenceImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ChapterReference
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? chapterNumber = freezed,
    Object? summary = freezed,
    Object? characterIds = null,
    Object? locationIds = null,
    Object? plotPointIds = null,
    Object? publishDate = freezed,
    Object? metadata = null,
  }) {
    return _then(
      _$ChapterReferenceImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        chapterNumber: freezed == chapterNumber
            ? _value.chapterNumber
            : chapterNumber // ignore: cast_nullable_to_non_nullable
                  as int?,
        summary: freezed == summary
            ? _value.summary
            : summary // ignore: cast_nullable_to_non_nullable
                  as String?,
        characterIds: null == characterIds
            ? _value._characterIds
            : characterIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        locationIds: null == locationIds
            ? _value._locationIds
            : locationIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        plotPointIds: null == plotPointIds
            ? _value._plotPointIds
            : plotPointIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        publishDate: freezed == publishDate
            ? _value.publishDate
            : publishDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ChapterReferenceImpl implements _ChapterReference {
  const _$ChapterReferenceImpl({
    required this.id,
    required this.title,
    this.chapterNumber,
    this.summary,
    final List<String> characterIds = const [],
    final List<String> locationIds = const [],
    final List<String> plotPointIds = const [],
    this.publishDate,
    final Map<String, dynamic> metadata = const {},
  }) : _characterIds = characterIds,
       _locationIds = locationIds,
       _plotPointIds = plotPointIds,
       _metadata = metadata;

  factory _$ChapterReferenceImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChapterReferenceImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final int? chapterNumber;
  @override
  final String? summary;
  final List<String> _characterIds;
  @override
  @JsonKey()
  List<String> get characterIds {
    if (_characterIds is EqualUnmodifiableListView) return _characterIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_characterIds);
  }

  final List<String> _locationIds;
  @override
  @JsonKey()
  List<String> get locationIds {
    if (_locationIds is EqualUnmodifiableListView) return _locationIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_locationIds);
  }

  final List<String> _plotPointIds;
  @override
  @JsonKey()
  List<String> get plotPointIds {
    if (_plotPointIds is EqualUnmodifiableListView) return _plotPointIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_plotPointIds);
  }

  @override
  final DateTime? publishDate;
  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  String toString() {
    return 'ChapterReference(id: $id, title: $title, chapterNumber: $chapterNumber, summary: $summary, characterIds: $characterIds, locationIds: $locationIds, plotPointIds: $plotPointIds, publishDate: $publishDate, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChapterReferenceImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.chapterNumber, chapterNumber) ||
                other.chapterNumber == chapterNumber) &&
            (identical(other.summary, summary) || other.summary == summary) &&
            const DeepCollectionEquality().equals(
              other._characterIds,
              _characterIds,
            ) &&
            const DeepCollectionEquality().equals(
              other._locationIds,
              _locationIds,
            ) &&
            const DeepCollectionEquality().equals(
              other._plotPointIds,
              _plotPointIds,
            ) &&
            (identical(other.publishDate, publishDate) ||
                other.publishDate == publishDate) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    title,
    chapterNumber,
    summary,
    const DeepCollectionEquality().hash(_characterIds),
    const DeepCollectionEquality().hash(_locationIds),
    const DeepCollectionEquality().hash(_plotPointIds),
    publishDate,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of ChapterReference
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChapterReferenceImplCopyWith<_$ChapterReferenceImpl> get copyWith =>
      __$$ChapterReferenceImplCopyWithImpl<_$ChapterReferenceImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ChapterReferenceImplToJson(this);
  }
}

abstract class _ChapterReference implements ChapterReference {
  const factory _ChapterReference({
    required final String id,
    required final String title,
    final int? chapterNumber,
    final String? summary,
    final List<String> characterIds,
    final List<String> locationIds,
    final List<String> plotPointIds,
    final DateTime? publishDate,
    final Map<String, dynamic> metadata,
  }) = _$ChapterReferenceImpl;

  factory _ChapterReference.fromJson(Map<String, dynamic> json) =
      _$ChapterReferenceImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  int? get chapterNumber;
  @override
  String? get summary;
  @override
  List<String> get characterIds;
  @override
  List<String> get locationIds;
  @override
  List<String> get plotPointIds;
  @override
  DateTime? get publishDate;
  @override
  Map<String, dynamic> get metadata;

  /// Create a copy of ChapterReference
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChapterReferenceImplCopyWith<_$ChapterReferenceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
