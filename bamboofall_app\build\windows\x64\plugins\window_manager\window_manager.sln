﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{2A48FF8F-B71C-3BC7-B526-BD3F4FD894C9}"
	ProjectSection(ProjectDependencies) = postProject
		{6CFF5C95-96D6-3B67-99C6-77F1A05B8BC6} = {6CFF5C95-96D6-3B67-99C6-77F1A05B8BC6}
		{5E763A42-98CE-3C50-A351-34BBBA736C14} = {5E763A42-98CE-3C50-A351-34BBBA736C14}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{0EAD99EF-B379-3061-A35E-72ACB879DEDB}"
	ProjectSection(ProjectDependencies) = postProject
		{2A48FF8F-B71C-3BC7-B526-BD3F4FD894C9} = {2A48FF8F-B71C-3BC7-B526-BD3F4FD894C9}
		{6CFF5C95-96D6-3B67-99C6-77F1A05B8BC6} = {6CFF5C95-96D6-3B67-99C6-77F1A05B8BC6}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{6CFF5C95-96D6-3B67-99C6-77F1A05B8BC6}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{63E553C6-A951-3935-A66F-65DD0B457127}"
	ProjectSection(ProjectDependencies) = postProject
		{6CFF5C95-96D6-3B67-99C6-77F1A05B8BC6} = {6CFF5C95-96D6-3B67-99C6-77F1A05B8BC6}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{DE0A1C1E-44FF-3E8B-B25A-9F3FD40CFC6F}"
	ProjectSection(ProjectDependencies) = postProject
		{6CFF5C95-96D6-3B67-99C6-77F1A05B8BC6} = {6CFF5C95-96D6-3B67-99C6-77F1A05B8BC6}
		{63E553C6-A951-3935-A66F-65DD0B457127} = {63E553C6-A951-3935-A66F-65DD0B457127}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "window_manager_plugin", "window_manager_plugin.vcxproj", "{5E763A42-98CE-3C50-A351-34BBBA736C14}"
	ProjectSection(ProjectDependencies) = postProject
		{6CFF5C95-96D6-3B67-99C6-77F1A05B8BC6} = {6CFF5C95-96D6-3B67-99C6-77F1A05B8BC6}
		{63E553C6-A951-3935-A66F-65DD0B457127} = {63E553C6-A951-3935-A66F-65DD0B457127}
		{DE0A1C1E-44FF-3E8B-B25A-9F3FD40CFC6F} = {DE0A1C1E-44FF-3E8B-B25A-9F3FD40CFC6F}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{2A48FF8F-B71C-3BC7-B526-BD3F4FD894C9}.Debug|x64.ActiveCfg = Debug|x64
		{2A48FF8F-B71C-3BC7-B526-BD3F4FD894C9}.Debug|x64.Build.0 = Debug|x64
		{2A48FF8F-B71C-3BC7-B526-BD3F4FD894C9}.Profile|x64.ActiveCfg = Profile|x64
		{2A48FF8F-B71C-3BC7-B526-BD3F4FD894C9}.Profile|x64.Build.0 = Profile|x64
		{2A48FF8F-B71C-3BC7-B526-BD3F4FD894C9}.Release|x64.ActiveCfg = Release|x64
		{2A48FF8F-B71C-3BC7-B526-BD3F4FD894C9}.Release|x64.Build.0 = Release|x64
		{0EAD99EF-B379-3061-A35E-72ACB879DEDB}.Debug|x64.ActiveCfg = Debug|x64
		{0EAD99EF-B379-3061-A35E-72ACB879DEDB}.Profile|x64.ActiveCfg = Profile|x64
		{0EAD99EF-B379-3061-A35E-72ACB879DEDB}.Release|x64.ActiveCfg = Release|x64
		{6CFF5C95-96D6-3B67-99C6-77F1A05B8BC6}.Debug|x64.ActiveCfg = Debug|x64
		{6CFF5C95-96D6-3B67-99C6-77F1A05B8BC6}.Debug|x64.Build.0 = Debug|x64
		{6CFF5C95-96D6-3B67-99C6-77F1A05B8BC6}.Profile|x64.ActiveCfg = Profile|x64
		{6CFF5C95-96D6-3B67-99C6-77F1A05B8BC6}.Profile|x64.Build.0 = Profile|x64
		{6CFF5C95-96D6-3B67-99C6-77F1A05B8BC6}.Release|x64.ActiveCfg = Release|x64
		{6CFF5C95-96D6-3B67-99C6-77F1A05B8BC6}.Release|x64.Build.0 = Release|x64
		{63E553C6-A951-3935-A66F-65DD0B457127}.Debug|x64.ActiveCfg = Debug|x64
		{63E553C6-A951-3935-A66F-65DD0B457127}.Debug|x64.Build.0 = Debug|x64
		{63E553C6-A951-3935-A66F-65DD0B457127}.Profile|x64.ActiveCfg = Profile|x64
		{63E553C6-A951-3935-A66F-65DD0B457127}.Profile|x64.Build.0 = Profile|x64
		{63E553C6-A951-3935-A66F-65DD0B457127}.Release|x64.ActiveCfg = Release|x64
		{63E553C6-A951-3935-A66F-65DD0B457127}.Release|x64.Build.0 = Release|x64
		{DE0A1C1E-44FF-3E8B-B25A-9F3FD40CFC6F}.Debug|x64.ActiveCfg = Debug|x64
		{DE0A1C1E-44FF-3E8B-B25A-9F3FD40CFC6F}.Debug|x64.Build.0 = Debug|x64
		{DE0A1C1E-44FF-3E8B-B25A-9F3FD40CFC6F}.Profile|x64.ActiveCfg = Profile|x64
		{DE0A1C1E-44FF-3E8B-B25A-9F3FD40CFC6F}.Profile|x64.Build.0 = Profile|x64
		{DE0A1C1E-44FF-3E8B-B25A-9F3FD40CFC6F}.Release|x64.ActiveCfg = Release|x64
		{DE0A1C1E-44FF-3E8B-B25A-9F3FD40CFC6F}.Release|x64.Build.0 = Release|x64
		{5E763A42-98CE-3C50-A351-34BBBA736C14}.Debug|x64.ActiveCfg = Debug|x64
		{5E763A42-98CE-3C50-A351-34BBBA736C14}.Debug|x64.Build.0 = Debug|x64
		{5E763A42-98CE-3C50-A351-34BBBA736C14}.Profile|x64.ActiveCfg = Profile|x64
		{5E763A42-98CE-3C50-A351-34BBBA736C14}.Profile|x64.Build.0 = Profile|x64
		{5E763A42-98CE-3C50-A351-34BBBA736C14}.Release|x64.ActiveCfg = Release|x64
		{5E763A42-98CE-3C50-A351-34BBBA736C14}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A8E18967-2A2A-34D3-85AF-C5E06CC34E61}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
