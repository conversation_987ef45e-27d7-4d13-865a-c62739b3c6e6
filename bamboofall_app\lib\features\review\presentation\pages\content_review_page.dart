import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

import '../../domain/entities/chapter_version.dart';
import '../../domain/entities/version_diff.dart';
import '../../domain/usecases/manage_versions.dart';
import '../widgets/diff_viewer.dart';
import '../widgets/version_history_panel.dart';

/// 内容审阅页面状态
class ContentReviewState {
  final String? selectedChapterId;
  final List<ChapterVersion> versions;
  final ChapterVersion? selectedVersion;
  final ChapterVersion? compareVersion;
  final bool isLoading;
  final String? error;
  final ReviewMode reviewMode;

  const ContentReviewState({
    this.selectedChapterId,
    this.versions = const [],
    this.selectedVersion,
    this.compareVersion,
    this.isLoading = false,
    this.error,
    this.reviewMode = ReviewMode.versionHistory,
  });

  ContentReviewState copyWith({
    String? selectedChapterId,
    List<ChapterVersion>? versions,
    ChapterVersion? selectedVersion,
    ChapterVersion? compareVersion,
    bool? isLoading,
    String? error,
    ReviewMode? reviewMode,
  }) {
    return ContentReviewState(
      selectedChapterId: selectedChapterId ?? this.selectedChapterId,
      versions: versions ?? this.versions,
      selectedVersion: selectedVersion ?? this.selectedVersion,
      compareVersion: compareVersion ?? this.compareVersion,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      reviewMode: reviewMode ?? this.reviewMode,
    );
  }
}

/// 审阅模式
enum ReviewMode {
  /// 版本历史
  versionHistory,
  
  /// 差异对比
  diffComparison,
  
  /// 内容审阅
  contentReview,
}

/// 内容审阅页面状态管理
final contentReviewProvider = StateNotifierProvider<ContentReviewNotifier, ContentReviewState>((ref) {
  final useCase = ref.read(manageVersionsUseCaseProvider);
  return ContentReviewNotifier(useCase);
});

class ContentReviewNotifier extends StateNotifier<ContentReviewState> {
  final ManageVersionsUseCase _useCase;

  ContentReviewNotifier(this._useCase) : super(const ContentReviewState());

  /// 选择章节
  Future<void> selectChapter(String chapterId) async {
    state = state.copyWith(
      selectedChapterId: chapterId,
      isLoading: true,
      error: null,
    );

    try {
      final versions = await _useCase.getVersionHistory(chapterId);
      state = state.copyWith(
        versions: versions,
        selectedVersion: versions.isNotEmpty ? versions.first : null,
        compareVersion: null,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 选择版本
  void selectVersion(ChapterVersion version) {
    state = state.copyWith(selectedVersion: version);
  }

  /// 选择对比版本
  void selectCompareVersion(ChapterVersion version) {
    state = state.copyWith(compareVersion: version);
    if (state.reviewMode != ReviewMode.diffComparison) {
      setReviewMode(ReviewMode.diffComparison);
    }
  }

  /// 设置审阅模式
  void setReviewMode(ReviewMode mode) {
    state = state.copyWith(reviewMode: mode);
  }

  /// 批准版本
  Future<void> approveVersion(String versionId) async {
    try {
      await _useCase.updateVersionStatus(versionId, VersionStatus.approved);
      await _refreshVersions();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 拒绝版本
  Future<void> rejectVersion(String versionId) async {
    try {
      await _useCase.updateVersionStatus(versionId, VersionStatus.rejected);
      await _refreshVersions();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 发布版本
  Future<void> publishVersion(String versionId) async {
    try {
      await _useCase.updateVersionStatus(versionId, VersionStatus.published);
      await _useCase.setMainVersion(versionId);
      await _refreshVersions();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 恢复版本
  Future<void> restoreVersion(String versionId, String createdBy) async {
    try {
      await _useCase.restoreVersion(versionId, createdBy);
      await _refreshVersions();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 删除版本
  Future<void> deleteVersion(String versionId) async {
    try {
      await _useCase.deleteVersion(versionId);
      await _refreshVersions();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 刷新版本列表
  Future<void> _refreshVersions() async {
    if (state.selectedChapterId != null) {
      final versions = await _useCase.getVersionHistory(state.selectedChapterId!);
      state = state.copyWith(versions: versions);
    }
  }
}

/// 内容审阅页面
/// 提供版本历史查看、差异对比和内容审阅功能
class ContentReviewPage extends ConsumerStatefulWidget {
  final String? initialChapterId;

  const ContentReviewPage({
    super.key,
    this.initialChapterId,
  });

  @override
  ConsumerState<ContentReviewPage> createState() => _ContentReviewPageState();
}

class _ContentReviewPageState extends ConsumerState<ContentReviewPage> {
  @override
  Widget build(BuildContext context) {
    final state = ref.watch(contentReviewProvider);
    final notifier = ref.read(contentReviewProvider.notifier);

    // 初始化选择章节
    if (widget.initialChapterId != null && state.selectedChapterId != widget.initialChapterId) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifier.selectChapter(widget.initialChapterId!);
      });
    }

    return fluent.ScaffoldPage(
      header: _buildHeader(context, state, notifier),
      content: _buildContent(context, state, notifier),
    );
  }

  Widget _buildHeader(BuildContext context, ContentReviewState state, ContentReviewNotifier notifier) {
    return fluent.PageHeader(
      title: const Text('内容审阅'),
      commandBar: fluent.CommandBar(
        primaryItems: [
          fluent.CommandBarButton(
            icon: const fluent.Icon(fluent.FluentIcons.history),
            label: const Text('版本历史'),
            onPressed: state.reviewMode == ReviewMode.versionHistory 
                ? null 
                : () => notifier.setReviewMode(ReviewMode.versionHistory),
          ),
          fluent.CommandBarButton(
            icon: const fluent.Icon(fluent.FluentIcons.compare),
            label: const Text('差异对比'),
            onPressed: state.reviewMode == ReviewMode.diffComparison 
                ? null 
                : () => notifier.setReviewMode(ReviewMode.diffComparison),
          ),
          fluent.CommandBarButton(
            icon: const fluent.Icon(fluent.FluentIcons.review_solid),
            label: const Text('内容审阅'),
            onPressed: state.reviewMode == ReviewMode.contentReview 
                ? null 
                : () => notifier.setReviewMode(ReviewMode.contentReview),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, ContentReviewState state, ContentReviewNotifier notifier) {
    if (state.selectedChapterId == null) {
      return _buildChapterSelector(context, notifier);
    }

    if (state.isLoading) {
      return const Center(
        child: fluent.ProgressRing(),
      );
    }

    if (state.error != null) {
      return _buildErrorView(context, state.error!, notifier);
    }

    switch (state.reviewMode) {
      case ReviewMode.versionHistory:
        return _buildVersionHistoryView(context, state, notifier);
      case ReviewMode.diffComparison:
        return _buildDiffComparisonView(context, state, notifier);
      case ReviewMode.contentReview:
        return _buildContentReviewView(context, state, notifier);
    }
  }

  Widget _buildChapterSelector(BuildContext context, ContentReviewNotifier notifier) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const fluent.Icon(
            fluent.FluentIcons.document,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            '选择要审阅的章节',
            style: fluent.FluentTheme.of(context).typography.subtitle,
          ),
          const SizedBox(height: 24),
          fluent.FilledButton(
            onPressed: () {
              // TODO: 显示章节选择对话框
              // 临时使用示例章节ID
              notifier.selectChapter('chapter_1');
            },
            child: const Text('选择章节'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(BuildContext context, String error, ContentReviewNotifier notifier) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const fluent.Icon(
            fluent.FluentIcons.error,
            size: 48,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            '加载失败',
            style: fluent.FluentTheme.of(context).typography.subtitle,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: fluent.FluentTheme.of(context).typography.caption,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          fluent.Button(
            onPressed: () {
              final currentState = ref.read(contentReviewProvider);
              if (currentState.selectedChapterId != null) {
                notifier.selectChapter(currentState.selectedChapterId!);
              }
            },
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  Widget _buildVersionHistoryView(BuildContext context, ContentReviewState state, ContentReviewNotifier notifier) {
    return Row(
      children: [
        // 版本历史面板
        SizedBox(
          width: 300,
          child: VersionHistoryPanel(
            versions: state.versions,
            selectedVersion: state.selectedVersion,
            onVersionSelected: notifier.selectVersion,
            onVersionAction: (version, action) => _handleVersionAction(context, notifier, version, action),
          ),
        ),
        
        // 分隔线
        Container(
          width: 1,
          color: fluent.FluentTheme.of(context).resources.dividerStrokeColorDefault,
        ),
        
        // 版本内容显示
        Expanded(
          child: state.selectedVersion != null
              ? _buildVersionContentView(context, state.selectedVersion!)
              : const Center(child: Text('选择一个版本查看内容')),
        ),
      ],
    );
  }

  Widget _buildDiffComparisonView(BuildContext context, ContentReviewState state, ContentReviewNotifier notifier) {
    if (state.selectedVersion == null || state.compareVersion == null) {
      return Row(
        children: [
          // 版本选择面板
          SizedBox(
            width: 300,
            child: VersionHistoryPanel(
              versions: state.versions,
              selectedVersion: state.selectedVersion,
              compareVersion: state.compareVersion,
              onVersionSelected: notifier.selectVersion,
              onCompareVersionSelected: notifier.selectCompareVersion,
              showCompareMode: true,
            ),
          ),
          
          // 分隔线
          Container(
            width: 1,
            color: fluent.FluentTheme.of(context).resources.dividerStrokeColorDefault,
          ),
          
          // 提示信息
          const Expanded(
            child: Center(
              child: Text('选择两个版本进行对比'),
            ),
          ),
        ],
      );
    }

    return Row(
      children: [
        // 版本选择面板
        SizedBox(
          width: 300,
          child: VersionHistoryPanel(
            versions: state.versions,
            selectedVersion: state.selectedVersion,
            compareVersion: state.compareVersion,
            onVersionSelected: notifier.selectVersion,
            onCompareVersionSelected: notifier.selectCompareVersion,
            showCompareMode: true,
          ),
        ),
        
        // 分隔线
        Container(
          width: 1,
          color: fluent.FluentTheme.of(context).resources.dividerStrokeColorDefault,
        ),
        
        // 差异查看器
        Expanded(
          child: DiffViewer(
            sourceVersionId: state.selectedVersion!.id,
            targetVersionId: state.compareVersion!.id,
            onAcceptChanges: (diff) => _handleAcceptChanges(context, notifier, diff),
            onRejectChanges: (diff) => _handleRejectChanges(context, notifier, diff),
          ),
        ),
      ],
    );
  }

  Widget _buildContentReviewView(BuildContext context, ContentReviewState state, ContentReviewNotifier notifier) {
    if (state.selectedVersion == null) {
      return const Center(child: Text('选择一个版本进行审阅'));
    }

    return Row(
      children: [
        // 版本信息面板
        SizedBox(
          width: 300,
          child: _buildReviewPanel(context, state.selectedVersion!, notifier),
        ),
        
        // 分隔线
        Container(
          width: 1,
          color: fluent.FluentTheme.of(context).resources.dividerStrokeColorDefault,
        ),
        
        // 内容显示
        Expanded(
          child: _buildVersionContentView(context, state.selectedVersion!),
        ),
      ],
    );
  }

  Widget _buildVersionContentView(BuildContext context, ChapterVersion version) {
    return Column(
      children: [
        // 版本信息头部
        Container(
          height: 60,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: fluent.FluentTheme.of(context).resources.layerFillColorDefault,
            border: Border(
              bottom: BorderSide(
                color: fluent.FluentTheme.of(context).resources.dividerStrokeColorDefault,
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    version.title,
                    style: fluent.FluentTheme.of(context).typography.bodyStrong,
                  ),
                  Text(
                    '${version.versionLabel} · ${version.createdBy} · ${version.wordCount}字',
                    style: fluent.FluentTheme.of(context).typography.caption,
                  ),
                ],
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Color(int.parse('0xFF${version.status.colorHex.substring(1)}')).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  version.status.displayName,
                  style: fluent.FluentTheme.of(context).typography.caption?.copyWith(
                        color: Color(int.parse('0xFF${version.status.colorHex.substring(1)}')),
                      ),
                ),
              ),
            ],
          ),
        ),
        
        // 内容区域
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: SelectableText(
              version.content,
              style: const TextStyle(
                fontSize: 16,
                height: 1.6,
                fontFamily: 'Consolas',
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildReviewPanel(BuildContext context, ChapterVersion version, ContentReviewNotifier notifier) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '审阅操作',
            style: fluent.FluentTheme.of(context).typography.bodyStrong,
          ),
          const SizedBox(height: 16),
          
          // 状态信息
          _buildInfoCard(
            context,
            '版本信息',
            [
              '版本: ${version.versionLabel}',
              '状态: ${version.status.displayName}',
              '创建者: ${version.createdBy}',
              '字数: ${version.wordCount}',
              '创建时间: ${_formatDateTime(version.createdAt)}',
            ],
          ),
          
          const SizedBox(height: 16),
          
          // 审阅操作按钮
          if (version.status == VersionStatus.pendingReview || version.status == VersionStatus.inReview) ...[
            SizedBox(
              width: double.infinity,
              child: fluent.FilledButton(
                onPressed: () => notifier.approveVersion(version.id),
                child: const Text('批准'),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: fluent.Button(
                onPressed: () => notifier.rejectVersion(version.id),
                child: const Text('拒绝'),
              ),
            ),
          ],
          
          if (version.status == VersionStatus.approved) ...[
            SizedBox(
              width: double.infinity,
              child: fluent.FilledButton(
                onPressed: () => notifier.publishVersion(version.id),
                child: const Text('发布'),
              ),
            ),
          ],
          
          const SizedBox(height: 16),
          
          // 其他操作
          SizedBox(
            width: double.infinity,
            child: fluent.Button(
              onPressed: () => notifier.restoreVersion(version.id, 'current_user'),
              child: const Text('恢复此版本'),
            ),
          ),
          
          if (version.status.canDelete) ...[
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: fluent.Button(
                onPressed: () => _confirmDeleteVersion(context, notifier, version),
                child: const Text('删除版本'),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoCard(BuildContext context, String title, List<String> items) {
    return fluent.Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: fluent.FluentTheme.of(context).typography.bodyStrong,
            ),
            const SizedBox(height: 8),
            ...items.map((item) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    item,
                    style: fluent.FluentTheme.of(context).typography.caption,
                  ),
                )),
          ],
        ),
      ),
    );
  }

  void _handleVersionAction(BuildContext context, ContentReviewNotifier notifier, ChapterVersion version, String action) {
    switch (action) {
      case 'approve':
        notifier.approveVersion(version.id);
        break;
      case 'reject':
        notifier.rejectVersion(version.id);
        break;
      case 'publish':
        notifier.publishVersion(version.id);
        break;
      case 'restore':
        notifier.restoreVersion(version.id, 'current_user');
        break;
      case 'delete':
        _confirmDeleteVersion(context, notifier, version);
        break;
    }
  }

  void _handleAcceptChanges(BuildContext context, ContentReviewNotifier notifier, VersionDiff diff) {
    // TODO: 实现接受变更逻辑
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('变更已接受')),
    );
  }

  void _handleRejectChanges(BuildContext context, ContentReviewNotifier notifier, VersionDiff diff) {
    // TODO: 实现拒绝变更逻辑
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('变更已拒绝')),
    );
  }

  void _confirmDeleteVersion(BuildContext context, ContentReviewNotifier notifier, ChapterVersion version) {
    showDialog(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: const Text('删除版本'),
        content: Text('确定要删除版本 "${version.versionLabel}" 吗？\n\n此操作不可撤销。'),
        actions: [
          fluent.Button(
            child: const Text('取消'),
            onPressed: () => Navigator.pop(context),
          ),
          fluent.FilledButton(
            child: const Text('删除'),
            onPressed: () {
              Navigator.pop(context);
              notifier.deleteVersion(version.id);
            },
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}