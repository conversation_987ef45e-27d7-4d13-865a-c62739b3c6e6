import '../../domain/entities/prompt_template.dart';

/// 提示词模板数据模型
class TemplateModel {
  final String id;
  final String name;
  final String? description;
  final String content;
  final TemplateCategory category;
  final List<String> tags;
  final List<TemplateVariable> variables;
  final String version;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isBuiltIn;
  final int usageCount;

  const TemplateModel({
    required this.id,
    required this.name,
    this.description,
    required this.content,
    required this.category,
    this.tags = const [],
    this.variables = const [],
    this.version = '1.0',
    required this.createdAt,
    required this.updatedAt,
    this.isBuiltIn = false,
    this.usageCount = 0,
  });

  factory TemplateModel.fromJson(Map<String, dynamic> json) {
    return TemplateModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      content: json['content'] as String,
      category: TemplateCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => TemplateCategory.custom,
      ),
      tags: (json['tags'] as List<dynamic>?)?.cast<String>() ?? [],
      variables: (json['variables'] as List<dynamic>?)
          ?.map((v) => TemplateVariable(
                name: v['name'],
                displayName: v['displayName'],
                description: v['description'],
                type: VariableType.values.firstWhere(
                  (e) => e.name == v['type'],
                  orElse: () => VariableType.text,
                ),
                isRequired: v['isRequired'] ?? false,
                defaultValue: v['defaultValue'],
              ))
          .toList() ?? [],
      version: json['version'] as String? ?? '1.0',
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isBuiltIn: json['isBuiltIn'] as bool? ?? false,
      usageCount: json['usageCount'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'content': content,
      'category': category.name,
      'tags': tags,
      'variables': variables.map((v) => {
        'name': v.name,
        'displayName': v.displayName,
        'description': v.description,
        'type': v.type.name,
        'isRequired': v.isRequired,
        'defaultValue': v.defaultValue,
      }).toList(),
      'version': version,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isBuiltIn': isBuiltIn,
      'usageCount': usageCount,
    };
  }

  /// 转换为领域实体
  PromptTemplate toEntity() {
    return PromptTemplate(
      id: id,
      name: name,
      description: description ?? '',
      content: content,
      category: category,
      tags: tags,
      variables: variables,
      version: version,
      createdAt: createdAt,
      updatedAt: updatedAt,
      isBuiltIn: isBuiltIn,
      usageCount: usageCount,
    );
  }

  /// 从领域实体创建
  factory TemplateModel.fromEntity(PromptTemplate entity) {
    return TemplateModel(
      id: entity.id,
      name: entity.name,
      description: entity.description,
      content: entity.content,
      category: entity.category,
      tags: entity.tags,
      variables: entity.variables,
      version: entity.version,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      isBuiltIn: entity.isBuiltIn,
      usageCount: entity.usageCount,
    );
  }
}

/// 模板使用记录模型
class TemplateUsageModel {
  final String id;
  final String templateId;
  final String userId;
  final DateTime usedAt;
  final Map<String, dynamic> variableValues;
  final String? generatedContent;

  const TemplateUsageModel({
    required this.id,
    required this.templateId,
    required this.userId,
    required this.usedAt,
    required this.variableValues,
    this.generatedContent,
  });

  factory TemplateUsageModel.fromJson(Map<String, dynamic> json) {
    return TemplateUsageModel(
      id: json['id'] as String,
      templateId: json['templateId'] as String,
      userId: json['userId'] as String,
      usedAt: DateTime.parse(json['usedAt'] as String),
      variableValues: json['variableValues'] as Map<String, dynamic>,
      generatedContent: json['generatedContent'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'templateId': templateId,
      'userId': userId,
      'usedAt': usedAt.toIso8601String(),
      'variableValues': variableValues,
      'generatedContent': generatedContent,
    };
  }
}

/// 模板分享记录模型
class TemplateShareModel {
  final String id;
  final String templateId;
  final String sharedBy;
  final String? sharedWith;
  final DateTime sharedAt;
  final String shareType;
  final bool isPublic;

  const TemplateShareModel({
    required this.id,
    required this.templateId,
    required this.sharedBy,
    this.sharedWith,
    required this.sharedAt,
    required this.shareType,
    this.isPublic = false,
  });

  factory TemplateShareModel.fromJson(Map<String, dynamic> json) {
    return TemplateShareModel(
      id: json['id'] as String,
      templateId: json['templateId'] as String,
      sharedBy: json['sharedBy'] as String,
      sharedWith: json['sharedWith'] as String?,
      sharedAt: DateTime.parse(json['sharedAt'] as String),
      shareType: json['shareType'] as String,
      isPublic: json['isPublic'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'templateId': templateId,
      'sharedBy': sharedBy,
      'sharedWith': sharedWith,
      'sharedAt': sharedAt.toIso8601String(),
      'shareType': shareType,
      'isPublic': isPublic,
    };
  }
}
