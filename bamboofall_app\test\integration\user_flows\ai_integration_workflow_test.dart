import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:bamboofall_app/features/ai_integration/domain/entities/ai_model.dart';
import 'package:bamboofall_app/features/ai_integration/domain/entities/generation_request.dart';
import 'package:bamboofall_app/features/ai_integration/domain/entities/generation_result.dart';
import 'package:bamboofall_app/features/ai_integration/domain/repositories/llm_repository.dart';
import 'package:bamboofall_app/features/ai_integration/presentation/pages/ai_assistant_page.dart';
import 'package:bamboofall_app/features/templates/domain/entities/prompt_template.dart';
import 'package:bamboofall_app/features/templates/domain/repositories/template_repository.dart';
import 'package:bamboofall_app/features/ai_integration/domain/exceptions/ai_generation_exception.dart';
import '../test_helpers.dart';

import 'ai_integration_workflow_test.mocks.dart';

@GenerateMocks([LLMRepository, TemplateRepository])
void main() {
  group('AI Integration Workflow Integration Tests', () {
    late MockLLMRepository mockLLMRepository;
    late MockTemplateRepository mockTemplateRepository;
    late ProviderContainer container;

    setUp(() {
      mockLLMRepository = MockLLMRepository();
      mockTemplateRepository = MockTemplateRepository();
      
      container = ProviderContainer(
        overrides: [
          // Override providers with mocks
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    testWidgets('Complete AI content generation workflow: model selection -> template -> variables -> generation -> save', (WidgetTester tester) async {
      // Arrange - 准备测试数据
      final testModel = AIModel(
        id: 'gpt-4',
        name: 'GPT-4',
        displayName: 'GPT-4',
        provider: AIProvider.openai,
        type: AIModelType.chat,
        status: AIModelStatus.available,
        maxTokens: 4000,
        supportedFeatures: ['completion', 'streaming'],
        isActive: true,
        description: 'GPT-4模型',
        version: '1.0.0',
      );

      final testTemplate = PromptTemplate(
        id: 'character-template',
        name: '角色描述模板',
        description: '用于生成角色描述的模板',
        category: TemplateCategory.character,
        content: '请描述一个{{character_type}}角色，名字叫{{character_name}}，具有{{character_traits}}特征。',
        variables: [
          TemplateVariable(
            name: 'character_type',
            displayName: '角色类型',
            type: VariableType.text,
            isRequired: true,
            description: '角色的类型，如主角、反派等',
          ),
          TemplateVariable(
            name: 'character_name',
            displayName: '角色姓名',
            type: VariableType.text,
            isRequired: true,
            description: '角色的姓名',
          ),
          TemplateVariable(
            name: 'character_traits',
            displayName: '角色特征',
            type: VariableType.text,
            isRequired: false,
            description: '角色的性格特征',
          ),
        ],
        version: '1.0.0',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isBuiltIn: true,
        usageCount: 0,
        tags: ['角色', '描述'],
      );

      final generationResult = GenerationResult(
        id: 'result-1',
        content: '这是一个勇敢的主角角色，名字叫李明，具有坚韧不拔、富有正义感的特征。',
        modelId: 'gpt-4',
        tokensUsed: 150,
        generationTime: const Duration(seconds: 3),
        createdAt: DateTime.now(),
        metadata: {
          'template_id': 'character-template',
          'variables': {
            'character_type': '主角',
            'character_name': '李明',
            'character_traits': '坚韧不拔、富有正义感',
          },
        },
      );

      // Mock repository responses
      when(mockLLMRepository.getAvailableModels()).thenAnswer((_) async => [testModel]);
      when(mockLLMRepository.generateContent(any)).thenAnswer((_) async => generationResult);
      when(mockTemplateRepository.getTemplatesByCategory(TemplateCategory.character))
          .thenAnswer((_) async => [testTemplate]);

      // Act & Assert - 执行完整的工作流程测试
      
      // 1. 启动AI助手页面并验证初始状态
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: const MaterialApp(
            home: AIAssistantPage(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // 验证页面初始状态
      expect(find.text('AI写作助手'), findsOneWidget);
      expect(find.text('选择模型'), findsOneWidget);
      expect(find.text('选择模板'), findsOneWidget);

      // 2. 选择AI模型
      await tester.tap(find.byKey(const Key('model_selector')));
      await tester.pumpAndSettle();

      // 验证模型列表显示
      expect(find.text('GPT-4'), findsOneWidget);
      expect(find.text('可用'), findsOneWidget);

      // 选择GPT-4模型
      await tester.tap(find.text('GPT-4'));
      await tester.pumpAndSettle();

      // 验证模型选择成功
      expect(find.text('当前模型: GPT-4'), findsOneWidget);
      verify(mockLLMRepository.getAvailableModels()).called(1);

      // 3. 选择提示模板
      await tester.tap(find.byKey(const Key('template_selector')));
      await tester.pumpAndSettle();

      // 验证模板分类显示
      expect(find.text('角色'), findsOneWidget);
      
      await tester.tap(find.text('角色'));
      await tester.pumpAndSettle();

      // 验证模板加载
      verify(mockTemplateRepository.getTemplatesByCategory(TemplateCategory.character)).called(1);

      // 选择角色描述模板
      expect(find.text('角色描述模板'), findsOneWidget);
      await tester.tap(find.text('角色描述模板'));
      await tester.pumpAndSettle();

      // 验证模板选择成功
      expect(find.text('当前模板: 角色描述模板'), findsOneWidget);

      // 4. 填写模板变量
      expect(find.text('角色类型'), findsOneWidget);
      expect(find.text('角色姓名'), findsOneWidget);
      expect(find.text('角色特征'), findsOneWidget);

      await tester.enterText(find.byKey(const Key('variable_character_type')), '主角');
      await tester.enterText(find.byKey(const Key('variable_character_name')), '李明');
      await tester.enterText(find.byKey(const Key('variable_character_traits')), '坚韧不拔、富有正义感');

      // 验证变量填写完成
      expect(find.text('主角'), findsOneWidget);
      expect(find.text('李明'), findsOneWidget);
      expect(find.text('坚韧不拔、富有正义感'), findsOneWidget);

      // 5. 生成内容
      await tester.tap(find.text('生成内容'));
      await tester.pumpAndSettle();

      // 验证生成开始状态
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('正在生成...'), findsOneWidget);

      // 等待生成完成
      await tester.pump(const Duration(seconds: 4));

      // 验证生成调用和结果
      verify(mockLLMRepository.generateContent(any)).called(1);
      expect(find.textContaining('这是一个勇敢的主角角色，名字叫李明'), findsOneWidget);
      expect(find.text('生成完成'), findsOneWidget);

      // 6. 验证生成统计信息
      expect(find.text('使用Token: 150'), findsOneWidget);
      expect(find.text('生成时间: 3秒'), findsOneWidget);
      expect(find.text('模型: GPT-4'), findsOneWidget);

      // 7. 保存生成结果
      await tester.tap(find.byKey(const Key('save_result_button')));
      await tester.pumpAndSettle();

      // 验证保存成功
      expect(find.text('已保存到剪贴板'), findsOneWidget);
      
      // 8. 验证完整工作流程完成
      expect(find.text('工作流程完成'), findsOneWidget);
    });

    testWidgets('Streaming content generation workflow with real-time updates', (WidgetTester tester) async {
      // Arrange
      final streamingModel = AIModel(
        id: 'gpt-4-turbo',
        name: 'GPT-4 Turbo',
        displayName: 'GPT-4 Turbo',
        provider: AIProvider.openai,
        type: AIModelType.chat,
        status: AIModelStatus.available,
        maxTokens: 128000,
        supportedFeatures: ['completion', 'streaming'],
        isActive: true,
        description: 'GPT-4 Turbo模型，支持流式生成',
        version: '1.0.0',
      );

      // Mock streaming response
      when(mockLLMRepository.getAvailableModels()).thenAnswer((_) async => [streamingModel]);
      when(mockLLMRepository.generateContentStream(any)).thenAnswer((_) {
        return Stream.fromIterable([
          '这是',
          '一个',
          '流式',
          '生成',
          '的',
          '示例',
          '内容。'
        ]).asyncMap((chunk) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return GenerationResult(
            id: 'stream-chunk-${DateTime.now().millisecondsSinceEpoch}',
            content: chunk,
            modelId: 'gpt-4-turbo',
            tokensUsed: 1,
            generationTime: const Duration(milliseconds: 100),
            createdAt: DateTime.now(),
            isStreaming: true,
            metadata: {'chunk_index': chunk.hashCode},
          );
        });
      });

      // Act & Assert
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: const MaterialApp(
            home: AIAssistantPage(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // 选择支持流式的模型
      await tester.tap(find.byKey(const Key('model_selector')));
      await tester.pumpAndSettle();
      await tester.tap(find.text('GPT-4 Turbo'));
      await tester.pumpAndSettle();

      // 启用流式生成
      await tester.tap(find.byKey(const Key('streaming_toggle')));
      await tester.pumpAndSettle();

      // 输入提示词
      await tester.enterText(find.byKey(const Key('prompt_input')), '写一个简短的故事开头');

      // 开始流式生成
      await tester.tap(find.text('开始生成'));
      await tester.pumpAndSettle();

      // 验证流式生成过程
      expect(find.text('正在流式生成...'), findsOneWidget);
      expect(find.byType(LinearProgressIndicator), findsOneWidget);

      // 模拟流式内容逐步显示和验证
      final expectedChunks = ['这是', '一个', '流式', '生成', '的', '示例', '内容。'];
      String accumulatedContent = '';
      
      for (int i = 0; i < expectedChunks.length; i++) {
        await tester.pump(const Duration(milliseconds: 150));
        accumulatedContent += expectedChunks[i];
        // 验证内容逐步累积显示
        expect(find.textContaining(accumulatedContent), findsOneWidget);
      }

      // 验证流式生成完成
      await tester.pumpAndSettle();
      expect(find.text('这是一个流式生成的示例内容。'), findsOneWidget);
      expect(find.text('流式生成完成'), findsOneWidget);
      expect(find.text('总Token: 7'), findsOneWidget);
      
      // 验证流式生成调用
      verify(mockLLMRepository.generateContentStream(any)).called(1);
    });

    testWidgets('Model switching and configuration workflow with validation', (WidgetTester tester) async {
      // Arrange
      final models = [
        AIModel(
          id: 'gpt-3.5',
          name: 'GPT-3.5',
          displayName: 'GPT-3.5',
          provider: AIProvider.openai,
          type: AIModelType.chat,
          status: AIModelStatus.available,
          maxTokens: 4096,
          supportedFeatures: ['completion'],
          isActive: true,
          description: 'GPT-3.5模型',
          version: '1.0.0',
        ),
        AIModel(
          id: 'gemini-pro',
          name: 'Gemini Pro',
          displayName: 'Gemini Pro',
          provider: AIProvider.google,
          type: AIModelType.chat,
          status: AIModelStatus.unavailable, // 模拟不可用状态
          maxTokens: 32768,
          supportedFeatures: ['completion', 'multimodal'],
          isActive: false,
          description: 'Gemini Pro模型，支持多模态',
          version: '1.0.0',
        ),
      ];

      when(mockLLMRepository.getAvailableModels()).thenAnswer((_) async => models);
      when(mockLLMRepository.updateModelConfiguration(any, any)).thenAnswer((_) async => true);
      when(mockLLMRepository.validateModelConfiguration(any)).thenAnswer((_) async => true);

      // Act & Assert
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: const MaterialApp(
            home: AIAssistantPage(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // 打开模型配置
      await tester.tap(find.byKey(const Key('model_config_button')));
      await tester.pumpAndSettle();

      // 验证模型列表显示
      expect(find.text('GPT-3.5'), findsOneWidget);
      expect(find.text('Gemini Pro'), findsOneWidget);

      // 验证不可用模型的状态
      expect(find.text('不可用'), findsOneWidget);
      expect(find.byIcon(Icons.warning), findsOneWidget);

      // 选择可用模型
      await tester.tap(find.text('GPT-3.5'));
      await tester.pumpAndSettle();

      // 验证模型选择成功
      expect(find.text('当前模型: GPT-3.5'), findsOneWidget);

      // 打开模型配置
      await tester.tap(find.byKey(const Key('model_settings_button')));
      await tester.pumpAndSettle();

      // 验证配置界面显示
      expect(find.text('模型配置'), findsOneWidget);
      expect(find.text('温度'), findsOneWidget);
      expect(find.text('最大Token数'), findsOneWidget);

      // 调整温度参数
      await tester.drag(find.byKey(const Key('temperature_slider')), const Offset(50, 0));
      await tester.pumpAndSettle();

      // 调整最大Token数
      await tester.enterText(find.byKey(const Key('max_tokens_field')), '2000');
      await tester.pumpAndSettle();

      // 验证配置预览
      expect(find.text('温度: 0.7'), findsOneWidget);
      expect(find.text('最大Token: 2000'), findsOneWidget);

      // 保存配置
      await tester.tap(find.text('保存配置'));
      await tester.pumpAndSettle();

      // 验证配置保存和验证
      verify(mockLLMRepository.validateModelConfiguration(any)).called(1);
      verify(mockLLMRepository.updateModelConfiguration('gpt-3.5', any)).called(1);
      expect(find.text('配置已保存'), findsOneWidget);
    });

    testWidgets('Comprehensive error handling in AI generation workflow', (WidgetTester tester) async {
      // Arrange
      final testModel = AIModel(
        id: 'error-model',
        name: 'Error Test Model',
        displayName: 'Error Test Model',
        provider: AIProvider.openai,
        type: AIModelType.chat,
        status: AIModelStatus.available,
        maxTokens: 1000,
        supportedFeatures: ['completion'],
        isActive: true,
        description: '用于测试错误处理的模型',
        version: '1.0.0',
      );

      when(mockLLMRepository.getAvailableModels()).thenAnswer((_) async => [testModel]);
      when(mockLLMRepository.generateContent(any)).thenThrow(
        AIGenerationException(
          message: 'API配额已用完',
          errorCode: 'QUOTA_EXCEEDED',
          retryable: true,
        ),
      );

      // Act & Assert
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: const MaterialApp(
            home: AIAssistantPage(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // 选择模型
      await tester.tap(find.byKey(const Key('model_selector')));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Error Test Model'));
      await tester.pumpAndSettle();

      // 输入提示词
      await tester.enterText(find.byKey(const Key('prompt_input')), '测试错误处理');

      // 尝试生成内容
      await tester.tap(find.text('生成内容'));
      await tester.pumpAndSettle();

      // 等待错误发生
      await tester.pump(const Duration(seconds: 2));

      // 验证错误处理UI
      expect(find.text('生成失败'), findsOneWidget);
      expect(find.text('API配额已用完'), findsOneWidget);
      expect(find.byIcon(Icons.error), findsOneWidget);

      // 验证错误详情和操作按钮
      expect(find.text('错误代码: QUOTA_EXCEEDED'), findsOneWidget);
      expect(find.text('重试'), findsOneWidget);
      expect(find.text('查看详情'), findsOneWidget);

      // 测试重试功能
      await tester.tap(find.text('重试'));
      await tester.pumpAndSettle();

      // 验证重试调用和状态
      verify(mockLLMRepository.generateContent(any)).called(2);
      expect(find.text('正在重试...'), findsOneWidget);

      // 等待重试完成（仍然失败）
      await tester.pump(const Duration(seconds: 2));
      expect(find.text('重试失败'), findsOneWidget);
    });

    testWidgets('Advanced batch content generation workflow with progress tracking', (WidgetTester tester) async {
      // Arrange
      final batchModel = AIModel(
        id: 'batch-model',
        name: 'Batch Model',
        displayName: 'Batch Processing Model',
        provider: AIProvider.openai,
        type: AIModelType.chat,
        status: AIModelStatus.available,
        maxTokens: 4000,
        supportedFeatures: ['completion', 'batch_processing'],
        isActive: true,
        description: '支持批量处理的AI模型',
        version: '1.0.0',
      );

      final batchResults = [
        GenerationResult(
          id: 'batch-1',
          content: '第一个生成结果：这是一个关于勇气的故事开头。',
          modelId: 'batch-model',
          tokensUsed: 50,
          generationTime: const Duration(seconds: 2),
          createdAt: DateTime.now(),
          metadata: {
            'batch_index': 0,
            'prompt': '写一个关于勇气的故事开头',
          },
        ),
        GenerationResult(
          id: 'batch-2',
          content: '第二个生成结果：这是一个关于友谊的故事开头。',
          modelId: 'batch-model',
          tokensUsed: 60,
          generationTime: const Duration(seconds: 2),
          createdAt: DateTime.now(),
          metadata: {
            'batch_index': 1,
            'prompt': '写一个关于友谊的故事开头',
          },
        ),
      ];

      when(mockLLMRepository.getAvailableModels()).thenAnswer((_) async => [batchModel]);
      when(mockLLMRepository.generateBatchContent(any)).thenAnswer((_) async => batchResults);
      when(mockLLMRepository.getBatchGenerationProgress(any)).thenAnswer((_) {
        return Stream.fromIterable([0.0, 0.5, 1.0]).asyncMap((progress) async {
          await Future.delayed(const Duration(seconds: 1));
          return progress;
        });
      });

      // Act & Assert
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: const MaterialApp(
            home: AIAssistantPage(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // 切换到批量生成模式
      await tester.tap(find.byKey(const Key('batch_mode_toggle')));
      await tester.pumpAndSettle();

      // 选择支持批量处理的模型
      await tester.tap(find.byKey(const Key('model_selector')));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Batch Model'));
      await tester.pumpAndSettle();

      // 验证批量模式界面
      expect(find.text('批量生成模式'), findsOneWidget);
      expect(find.text('添加提示词'), findsOneWidget);

      // 添加多个提示词
      await tester.tap(find.byKey(const Key('add_prompt_button')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('prompt_input_0')), '写一个关于勇气的故事开头');
      
      await tester.tap(find.byKey(const Key('add_prompt_button')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('prompt_input_1')), '写一个关于友谊的故事开头');

      // 验证提示词列表
      expect(find.text('提示词 1'), findsOneWidget);
      expect(find.text('提示词 2'), findsOneWidget);
      expect(find.text('总计: 2个提示词'), findsOneWidget);

      // 开始批量生成
      await tester.tap(find.text('批量生成'));
      await tester.pumpAndSettle();

      // 验证批量生成开始
      expect(find.text('批量生成中...'), findsOneWidget);
      expect(find.byType(LinearProgressIndicator), findsOneWidget);
      expect(find.text('进度: 0/2'), findsOneWidget);

      // 模拟进度更新
      await tester.pump(const Duration(seconds: 1));
      expect(find.text('进度: 1/2'), findsOneWidget);
      
      await tester.pump(const Duration(seconds: 1));
      expect(find.text('进度: 2/2'), findsOneWidget);

      // 等待批量生成完成
      await tester.pump(const Duration(seconds: 1));
      await tester.pumpAndSettle();

      // 验证批量生成调用和结果
      verify(mockLLMRepository.generateBatchContent(any)).called(1);
      expect(find.textContaining('第一个生成结果：这是一个关于勇气的故事开头'), findsOneWidget);
      expect(find.textContaining('第二个生成结果：这是一个关于友谊的故事开头'), findsOneWidget);
      expect(find.text('批量生成完成'), findsOneWidget);

      // 验证批量统计信息
      expect(find.text('总Token使用: 110'), findsOneWidget);
      expect(find.text('平均生成时间: 2秒'), findsOneWidget);
      expect(find.text('成功率: 100%'), findsOneWidget);
      
      // 验证批量结果操作
      expect(find.text('导出全部'), findsOneWidget);
      expect(find.text('保存到项目'), findsOneWidget);
    });
  });
}