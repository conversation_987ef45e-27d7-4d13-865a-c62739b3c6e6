import 'dart:io';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:logger/logger.dart';

/// 文件存储管理类
/// 处理项目文件、文档、配置文件等的存储和管理
class FileStorage {
  static FileStorage? _instance;
  static FileStorage get instance => _instance ??= FileStorage._();
  
  FileStorage._();
  
  final Logger _logger = Logger();
  late Directory _appDir;
  late Directory _projectsDir;
  late Directory _templatesDir;
  late Directory _configDir;
  bool _initialized = false;
  
  /// 初始化文件存储
  Future<void> initialize() async {
    if (_initialized) return;
    
    try {
      _appDir = await getApplicationDocumentsDirectory();
      final bambooFallDir = Directory('${_appDir.path}/BambooFall');
      
      if (!await bambooFallDir.exists()) {
        await bambooFallDir.create(recursive: true);
      }
      
      // 创建子目录
      _projectsDir = Directory('${bambooFallDir.path}/Projects');
      _templatesDir = Directory('${bambooFallDir.path}/Templates');
      _configDir = Directory('${bambooFallDir.path}/Config');
      
      await _projectsDir.create(recursive: true);
      await _templatesDir.create(recursive: true);
      await _configDir.create(recursive: true);
      
      _initialized = true;
      _logger.i('File storage initialized successfully');
      _logger.i('Projects directory: ${_projectsDir.path}');
      _logger.i('Templates directory: ${_templatesDir.path}');
      _logger.i('Config directory: ${_configDir.path}');
    } catch (e) {
      _logger.e('Failed to initialize file storage: $e');
      rethrow;
    }
  }
  
  /// 确保文件存储已初始化
  void _ensureInitialized() {
    if (!_initialized) {
      throw StateError('File storage not initialized. Call initialize() first.');
    }
  }
  
  // ==================== 项目管理 ====================
  
  /// 创建新项目目录
  Future<String> createProject(String projectName) async {
    _ensureInitialized();
    
    try {
      final projectDir = Directory('${_projectsDir.path}/$projectName');
      
      if (await projectDir.exists()) {
        throw Exception('Project $projectName already exists');
      }
      
      await projectDir.create(recursive: true);
      
      // 创建项目子目录
      final subDirs = ['Chapters', 'Bible', 'Exports', 'Backups'];
      for (final subDir in subDirs) {
        await Directory('${projectDir.path}/$subDir').create();
      }
      
      // 创建项目配置文件
      final projectConfig = {
        'name': projectName,
        'createdAt': DateTime.now().toIso8601String(),
        'version': '1.0.0',
        'settings': {
          'autoSave': true,
          'backupInterval': 300, // 5分钟
          'wordCountTarget': 0,
        },
      };
      
      await writeProjectConfig(projectName, projectConfig);
      
      _logger.i('Created project: $projectName');
      return projectDir.path;
    } catch (e) {
      _logger.e('Failed to create project $projectName: $e');
      rethrow;
    }
  }
  
  /// 获取所有项目
  Future<List<Map<String, dynamic>>> getProjects() async {
    _ensureInitialized();
    
    try {
      final projects = <Map<String, dynamic>>[];
      
      await for (final entity in _projectsDir.list()) {
        if (entity is Directory) {
          final projectName = entity.path.split(Platform.pathSeparator).last;
          final config = await readProjectConfig(projectName);
          
          if (config != null) {
            projects.add({
              'name': projectName,
              'path': entity.path,
              'config': config,
            });
          }
        }
      }
      
      // 按创建时间排序
      projects.sort((a, b) {
        final aTime = DateTime.parse(a['config']['createdAt']);
        final bTime = DateTime.parse(b['config']['createdAt']);
        return bTime.compareTo(aTime);
      });
      
      return projects;
    } catch (e) {
      _logger.e('Failed to get projects: $e');
      return [];
    }
  }
  
  /// 删除项目
  Future<void> deleteProject(String projectName) async {
    _ensureInitialized();
    
    try {
      final projectDir = Directory('${_projectsDir.path}/$projectName');
      
      if (await projectDir.exists()) {
        await projectDir.delete(recursive: true);
        _logger.i('Deleted project: $projectName');
      }
    } catch (e) {
      _logger.e('Failed to delete project $projectName: $e');
      rethrow;
    }
  }
  
  /// 检查项目是否存在
  Future<bool> projectExists(String projectName) async {
    _ensureInitialized();
    
    final projectDir = Directory('${_projectsDir.path}/$projectName');
    return await projectDir.exists();
  }
  
  // ==================== 项目配置 ====================
  
  /// 读取项目配置
  Future<Map<String, dynamic>?> readProjectConfig(String projectName) async {
    _ensureInitialized();
    
    try {
      final configFile = File('${_projectsDir.path}/$projectName/project.json');
      
      if (!await configFile.exists()) {
        return null;
      }
      
      final content = await configFile.readAsString();
      return jsonDecode(content);
    } catch (e) {
      _logger.e('Failed to read project config for $projectName: $e');
      return null;
    }
  }
  
  /// 写入项目配置
  Future<void> writeProjectConfig(String projectName, Map<String, dynamic> config) async {
    _ensureInitialized();
    
    try {
      final configFile = File('${_projectsDir.path}/$projectName/project.json');
      config['updatedAt'] = DateTime.now().toIso8601String();
      
      await configFile.writeAsString(jsonEncode(config));
      _logger.d('Updated project config for $projectName');
    } catch (e) {
      _logger.e('Failed to write project config for $projectName: $e');
      rethrow;
    }
  }
  
  // ==================== 文件操作 ====================
  
  /// 读取文件内容
  Future<String?> readFile(String filePath) async {
    try {
      final file = File(filePath);
      
      if (!await file.exists()) {
        return null;
      }
      
      return await file.readAsString();
    } catch (e) {
      _logger.e('Failed to read file $filePath: $e');
      return null;
    }
  }
  
  /// 写入文件内容
  Future<void> writeFile(String filePath, String content) async {
    try {
      final file = File(filePath);
      
      // 确保目录存在
      await file.parent.create(recursive: true);
      
      await file.writeAsString(content);
      _logger.d('Written file: $filePath');
    } catch (e) {
      _logger.e('Failed to write file $filePath: $e');
      rethrow;
    }
  }
  
  /// 删除文件
  Future<void> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      
      if (await file.exists()) {
        await file.delete();
        _logger.d('Deleted file: $filePath');
      }
    } catch (e) {
      _logger.e('Failed to delete file $filePath: $e');
      rethrow;
    }
  }
  
  /// 复制文件
  Future<void> copyFile(String sourcePath, String targetPath) async {
    try {
      final sourceFile = File(sourcePath);
      final targetFile = File(targetPath);
      
      // 确保目标目录存在
      await targetFile.parent.create(recursive: true);
      
      await sourceFile.copy(targetPath);
      _logger.d('Copied file from $sourcePath to $targetPath');
    } catch (e) {
      _logger.e('Failed to copy file from $sourcePath to $targetPath: $e');
      rethrow;
    }
  }
  
  // ==================== 模板管理 ====================
  
  /// 保存模板
  Future<void> saveTemplate(String templateName, String content, String type) async {
    _ensureInitialized();
    
    try {
      final templateFile = File('${_templatesDir.path}/${type}_$templateName.md');
      await templateFile.writeAsString(content);
      _logger.d('Saved template: $templateName ($type)');
    } catch (e) {
      _logger.e('Failed to save template $templateName: $e');
      rethrow;
    }
  }
  
  /// 读取模板
  Future<String?> readTemplate(String templateName, String type) async {
    _ensureInitialized();
    
    try {
      final templateFile = File('${_templatesDir.path}/${type}_$templateName.md');
      
      if (!await templateFile.exists()) {
        return null;
      }
      
      return await templateFile.readAsString();
    } catch (e) {
      _logger.e('Failed to read template $templateName: $e');
      return null;
    }
  }
  
  /// 获取所有模板
  Future<List<Map<String, String>>> getTemplates() async {
    _ensureInitialized();
    
    try {
      final templates = <Map<String, String>>[];
      
      await for (final entity in _templatesDir.list()) {
        if (entity is File && entity.path.endsWith('.md')) {
          final fileName = entity.path.split(Platform.pathSeparator).last;
          final parts = fileName.replaceAll('.md', '').split('_');
          
          if (parts.length >= 2) {
            templates.add({
              'name': parts.sublist(1).join('_'),
              'type': parts[0],
              'path': entity.path,
            });
          }
        }
      }
      
      return templates;
    } catch (e) {
      _logger.e('Failed to get templates: $e');
      return [];
    }
  }
  
  // ==================== 备份管理 ====================
  
  /// 创建项目备份
  Future<String> createBackup(String projectName) async {
    _ensureInitialized();
    
    try {
      final projectDir = Directory('${_projectsDir.path}/$projectName');
      final backupDir = Directory('${projectDir.path}/Backups');
      
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final backupName = 'backup_$timestamp';
      final backupPath = '${backupDir.path}/$backupName';
      
      await Directory(backupPath).create(recursive: true);
      
      // 复制项目文件（除了Backups目录）
      await for (final entity in projectDir.list()) {
        if (entity is File) {
          final fileName = entity.path.split(Platform.pathSeparator).last;
          await copyFile(entity.path, '$backupPath/$fileName');
        } else if (entity is Directory && !entity.path.endsWith('Backups')) {
          final dirName = entity.path.split(Platform.pathSeparator).last;
          await _copyDirectory(entity.path, '$backupPath/$dirName');
        }
      }
      
      _logger.i('Created backup for project $projectName: $backupName');
      return backupPath;
    } catch (e) {
      _logger.e('Failed to create backup for project $projectName: $e');
      rethrow;
    }
  }
  
  /// 递归复制目录
  Future<void> _copyDirectory(String sourcePath, String targetPath) async {
    final sourceDir = Directory(sourcePath);
    final targetDir = Directory(targetPath);
    
    await targetDir.create(recursive: true);
    
    await for (final entity in sourceDir.list()) {
      final name = entity.path.split(Platform.pathSeparator).last;
      
      if (entity is File) {
        await copyFile(entity.path, '$targetPath/$name');
      } else if (entity is Directory) {
        await _copyDirectory(entity.path, '$targetPath/$name');
      }
    }
  }
  
  // ==================== 工具方法 ====================
  
  /// 获取存储统计信息
  Future<Map<String, dynamic>> getStorageStats() async {
    _ensureInitialized();
    
    try {
      final projects = await getProjects();
      final templates = await getTemplates();
      
      return {
        'projectsCount': projects.length,
        'templatesCount': templates.length,
        'projectsDir': _projectsDir.path,
        'templatesDir': _templatesDir.path,
        'configDir': _configDir.path,
      };
    } catch (e) {
      _logger.e('Failed to get storage stats: $e');
      return {};
    }
  }
}