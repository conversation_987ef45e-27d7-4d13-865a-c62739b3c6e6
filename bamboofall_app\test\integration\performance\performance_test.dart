import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:bamboofall_app/features/project/domain/entities/project.dart';
import 'package:bamboofall_app/features/writing/domain/entities/chapter.dart';
import 'package:bamboofall_app/features/bible/domain/entities/character_bible.dart';
import 'package:bamboofall_app/features/ai_integration/domain/entities/llm_model.dart';
import 'package:bamboofall_app/features/project/domain/repositories/project_repository.dart';
import 'package:bamboofall_app/features/writing/domain/repositories/chapter_repository.dart';
import 'package:bamboofall_app/features/bible/domain/repositories/bible_repository.dart';
import 'package:bamboofall_app/features/ai_integration/domain/repositories/llm_repository.dart';

import '../test_helpers.dart';
import 'performance_test.mocks.dart';

@GenerateMocks([ProjectRepository, ChapterRepository, BibleRepository, LLMRepository])
void main() {
  group('Performance Integration Tests', () {
    late MockProjectRepository mockProjectRepository;
    late MockChapterRepository mockChapterRepository;
    late MockBibleRepository mockBibleRepository;
    late MockLLMRepository mockLLMRepository;

    setUp(() {
      mockProjectRepository = MockProjectRepository();
      mockChapterRepository = MockChapterRepository();
      mockBibleRepository = MockBibleRepository();
      mockLLMRepository = MockLLMRepository();
    });

    group('Data Loading Performance', () {
      test('should load large project list efficiently', () async {
        // Arrange
        final largeProjectList = List.generate(1000, (index) => Project(
          id: 'project-$index',
          title: '项目 $index',
          description: '这是第 $index 个测试项目的详细描述，包含了丰富的内容信息。',
          genre: ProjectGenre.values[index % ProjectGenre.values.length],
          status: ProjectStatus.values[index % ProjectStatus.values.length],
          createdAt: DateTime.now().subtract(Duration(days: index)),
          updatedAt: DateTime.now().subtract(Duration(hours: index)),
          settings: const ProjectSettings(),
        ));

        when(mockProjectRepository.getProjects()).thenAnswer((_) async => largeProjectList);

        // Act & Assert
        await verifyExecutionTime(
          () async {
            final projects = await mockProjectRepository.getProjects();
            expect(projects.length, equals(1000));
          },
          maxDuration: const Duration(milliseconds: 500), // Should complete within 500ms
        );

        verify(mockProjectRepository.getProjects()).called(1);
      });

      test('should handle large chapter content efficiently', () async {
        // Arrange
        final largeChapter = Chapter(
          id: 'large-chapter',
          projectId: 'project-1',
          title: '超大章节',
          content: 'x' * 100000, // 100KB of content
          order: 1,
          status: ChapterStatus.draft,
          wordCount: 100000,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        when(mockChapterRepository.getChapter('large-chapter')).thenAnswer((_) async => largeChapter);

        // Act & Assert
        await verifyExecutionTime(
          () async {
            final chapter = await mockChapterRepository.getChapter('large-chapter');
            expect(chapter?.content.length, equals(100000));
          },
          maxDuration: const Duration(milliseconds: 200),
        );

        verify(mockChapterRepository.getChapter('large-chapter')).called(1);
      });

      test('should load complex bible data efficiently', () async {
        // Arrange
        final complexCharacters = List.generate(500, (index) => CharacterBible(
          id: 'char-$index',
          projectId: 'project-1',
          name: '角色$index',
          age: 20 + (index % 50),
          gender: index % 2 == 0 ? Gender.male : Gender.female,
          occupation: '职业$index',
          personality: List.generate(10, (i) => '性格特征${index}_$i'),
          appearance: '外貌描述' * 50, // Long description
          background: '背景故事' * 100, // Very long background
          relationships: Map.fromEntries(
            List.generate(5, (i) => MapEntry(
              'char-${(index + i + 1) % 500}',
              CharacterRelationship(
                targetCharacterId: 'char-${(index + i + 1) % 500}',
                relationshipType: RelationshipType.values[i % RelationshipType.values.length],
                description: '关系描述$i',
                strength: (i + 1) * 0.2,
              ),
            )),
          ),
          skills: List.generate(15, (i) => '技能${index}_$i'),
          goals: List.generate(8, (i) => '目标${index}_$i'),
          fears: List.generate(6, (i) => '恐惧${index}_$i'),
          secrets: List.generate(4, (i) => '秘密${index}_$i'),
          characterArc: '角色弧线' * 20,
          notes: '备注' * 30,
          tags: List.generate(12, (i) => '标签${index}_$i'),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ));

        when(mockBibleRepository.getCharacterBibles('project-1')).thenAnswer((_) async => complexCharacters);

        // Act & Assert
        await verifyExecutionTime(
          () async {
            final characters = await mockBibleRepository.getCharacterBibles('project-1');
            expect(characters.length, equals(500));
            expect(characters.first.relationships.length, equals(5));
          },
          maxDuration: const Duration(seconds: 1), // Allow up to 1 second for complex data
        );

        verify(mockBibleRepository.getCharacterBibles('project-1')).called(1);
      });
    });

    group('Concurrent Operations Performance', () {
      test('should handle multiple simultaneous chapter operations', () async {
        // Arrange
        final chapters = List.generate(50, (index) => Chapter(
          id: 'chapter-$index',
          projectId: 'project-1',
          title: '章节$index',
          content: '内容' * 1000, // 1KB per chapter
          order: index + 1,
          status: ChapterStatus.draft,
          wordCount: 1000,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ));

        // Mock concurrent operations
        when(mockChapterRepository.createChapter(any)).thenAnswer((invocation) async {
          final chapter = invocation.positionalArguments[0] as Chapter;
          await Future.delayed(const Duration(milliseconds: 50)); // Simulate processing time
          return chapter;
        });

        // Act & Assert
        await verifyExecutionTime(
          () async {
            final futures = chapters.map((chapter) => mockChapterRepository.createChapter(chapter));
            final results = await Future.wait(futures);
            expect(results.length, equals(50));
          },
          maxDuration: const Duration(seconds: 3), // Should complete within 3 seconds
        );

        verify(mockChapterRepository.createChapter(any)).called(50);
      });

      test('should handle concurrent AI model requests', () async {
        // Arrange
        final models = List.generate(10, (index) => LLMModel(
          id: 'model-$index',
          name: '模型$index',
          provider: LLMProvider.values[index % LLMProvider.values.length],
          maxTokens: 4000,
          supportedFeatures: [ModelFeature.completion, ModelFeature.streaming],
          pricing: ModelPricing(
            inputTokenPrice: 0.001,
            outputTokenPrice: 0.002,
            currency: 'USD',
          ),
          isAvailable: true,
          description: '模型描述$index',
          version: '1.0.$index',
        ));

        when(mockLLMRepository.getAvailableModels()).thenAnswer((_) async => models);
        when(mockLLMRepository.getModelById(any)).thenAnswer((invocation) async {
          final id = invocation.positionalArguments[0] as String;
          return models.firstWhere((model) => model.id == id);
        });

        // Act & Assert
        await verifyExecutionTime(
          () async {
            final availableModels = await mockLLMRepository.getAvailableModels();
            final futures = availableModels.map((model) => mockLLMRepository.getModelById(model.id));
            final results = await Future.wait(futures);
            expect(results.length, equals(10));
          },
          maxDuration: const Duration(milliseconds: 800),
        );

        verify(mockLLMRepository.getAvailableModels()).called(1);
        verify(mockLLMRepository.getModelById(any)).called(10);
      });

      test('should handle mixed repository operations concurrently', () async {
        // Arrange
        final project = Project(
          id: 'concurrent-project',
          title: '并发测试项目',
          description: '测试并发操作',
          genre: ProjectGenre.fantasy,
          status: ProjectStatus.active,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          settings: const ProjectSettings(),
        );

        final chapters = List.generate(20, (index) => Chapter(
          id: 'chapter-$index',
          projectId: 'concurrent-project',
          title: '章节$index',
          content: '内容$index',
          order: index + 1,
          status: ChapterStatus.draft,
          wordCount: 100,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ));

        final characters = List.generate(10, (index) => CharacterBible(
          id: 'char-$index',
          projectId: 'concurrent-project',
          name: '角色$index',
          age: 25,
          gender: Gender.male,
          occupation: '职业$index',
          personality: [],
          appearance: '',
          background: '',
          relationships: {},
          skills: [],
          goals: [],
          fears: [],
          secrets: [],
          characterArc: '',
          notes: '',
          tags: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ));

        // Mock operations
        when(mockProjectRepository.createProject(any)).thenAnswer((_) async => project);
        when(mockChapterRepository.createChapter(any)).thenAnswer((invocation) async {
          final chapter = invocation.positionalArguments[0] as Chapter;
          await Future.delayed(const Duration(milliseconds: 30));
          return chapter;
        });
        when(mockBibleRepository.createCharacterBible(any)).thenAnswer((invocation) async {
          final character = invocation.positionalArguments[0] as CharacterBible;
          await Future.delayed(const Duration(milliseconds: 20));
          return character;
        });

        // Act & Assert
        await verifyExecutionTime(
          () async {
            final projectFuture = mockProjectRepository.createProject(project);
            final chapterFutures = chapters.map((chapter) => mockChapterRepository.createChapter(chapter));
            final characterFutures = characters.map((character) => mockBibleRepository.createCharacterBible(character));

            final results = await Future.wait([
              projectFuture,
              ...chapterFutures,
              ...characterFutures,
            ]);

            expect(results.length, equals(31)); // 1 project + 20 chapters + 10 characters
          },
          maxDuration: const Duration(seconds: 2),
        );

        verify(mockProjectRepository.createProject(any)).called(1);
        verify(mockChapterRepository.createChapter(any)).called(20);
        verify(mockBibleRepository.createCharacterBible(any)).called(10);
      });
    });

    group('Memory Usage Performance', () {
      test('should handle large data sets without memory issues', () async {
        // Arrange
        final massiveProjectList = List.generate(5000, (index) => Project(
          id: 'project-$index',
          title: '项目$index',
          description: '描述' * 100, // Large description
          genre: ProjectGenre.fantasy,
          status: ProjectStatus.active,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          settings: const ProjectSettings(),
        ));

        when(mockProjectRepository.getProjects()).thenAnswer((_) async => massiveProjectList);

        // Act & Assert
        await verifyExecutionTime(
          () async {
            final projects = await mockProjectRepository.getProjects();
            expect(projects.length, equals(5000));
            
            // Process data to simulate real usage
            final totalDescriptionLength = projects
                .map((p) => p.description.length)
                .reduce((a, b) => a + b);
            
            expect(totalDescriptionLength, greaterThan(0));
          },
          maxDuration: const Duration(seconds: 2),
        );

        verify(mockProjectRepository.getProjects()).called(1);
      });

      test('should efficiently process streaming data', () async {
        // Arrange
        final streamController = StreamController<String>();
        final largeContent = List.generate(1000, (index) => '数据块$index ').join();

        when(mockLLMRepository.generateContentStream(any, any)).thenAnswer((_) {
          // Simulate streaming large content in chunks
          Future.delayed(Duration.zero, () {
            for (int i = 0; i < largeContent.length; i += 50) {
              final chunk = largeContent.substring(i, (i + 50).clamp(0, largeContent.length));
              streamController.add(chunk);
            }
            streamController.close();
          });
          return streamController.stream;
        });

        // Act & Assert
        await verifyExecutionTime(
          () async {
            final stream = mockLLMRepository.generateContentStream('test-model', 'test prompt');
            final chunks = <String>[];
            
            await for (final chunk in stream) {
              chunks.add(chunk);
            }
            
            final fullContent = chunks.join();
            expect(fullContent.length, equals(largeContent.length));
          },
          maxDuration: const Duration(seconds: 1),
        );

        verify(mockLLMRepository.generateContentStream(any, any)).called(1);
      });

      test('should handle rapid state updates efficiently', () async {
        // Arrange
        final updates = List.generate(1000, (index) => Chapter(
          id: 'chapter-1',
          projectId: 'project-1',
          title: '章节标题',
          content: '内容更新$index',
          order: 1,
          status: ChapterStatus.draft,
          wordCount: 10 + index,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now().add(Duration(milliseconds: index)),
        ));

        when(mockChapterRepository.updateChapter(any)).thenAnswer((invocation) async {
          final chapter = invocation.positionalArguments[0] as Chapter;
          await Future.delayed(const Duration(milliseconds: 1)); // Minimal processing time
          return chapter;
        });

        // Act & Assert
        await verifyExecutionTime(
          () async {
            for (final update in updates) {
              await mockChapterRepository.updateChapter(update);
            }
          },
          maxDuration: const Duration(seconds: 3),
        );

        verify(mockChapterRepository.updateChapter(any)).called(1000);
      });
    });

    group('Search and Filter Performance', () {
      test('should search through large datasets efficiently', () async {
        // Arrange
        final largeCharacterList = List.generate(2000, (index) => CharacterBible(
          id: 'char-$index',
          projectId: 'project-1',
          name: '角色$index',
          age: 20 + (index % 60),
          gender: index % 2 == 0 ? Gender.male : Gender.female,
          occupation: '职业${index % 50}',
          personality: ['性格${index % 20}'],
          appearance: '外貌$index',
          background: '背景$index',
          relationships: {},
          skills: ['技能${index % 30}'],
          goals: [],
          fears: [],
          secrets: [],
          characterArc: '',
          notes: '备注包含搜索关键词$index',
          tags: ['标签${index % 10}', '类别${index % 5}'],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ));

        when(mockBibleRepository.searchCharacters('project-1', any)).thenAnswer((invocation) async {
          final query = invocation.positionalArguments[1] as String;
          return largeCharacterList.where((char) => 
            char.name.contains(query) || 
            char.notes.contains(query) ||
            char.tags.any((tag) => tag.contains(query))
          ).toList();
        });

        // Act & Assert
        await verifyExecutionTime(
          () async {
            final results = await mockBibleRepository.searchCharacters('project-1', '角色1');
            expect(results.length, greaterThan(0));
            expect(results.length, lessThan(largeCharacterList.length));
          },
          maxDuration: const Duration(milliseconds: 300),
        );

        verify(mockBibleRepository.searchCharacters('project-1', any)).called(1);
      });

      test('should filter complex data structures efficiently', () async {
        // Arrange
        final complexProjects = List.generate(1000, (index) => Project(
          id: 'project-$index',
          title: '项目$index',
          description: '描述$index',
          genre: ProjectGenre.values[index % ProjectGenre.values.length],
          status: ProjectStatus.values[index % ProjectStatus.values.length],
          createdAt: DateTime.now().subtract(Duration(days: index % 365)),
          updatedAt: DateTime.now().subtract(Duration(hours: index % 24)),
          settings: const ProjectSettings(),
        ));

        when(mockProjectRepository.getProjectsByFilter(any)).thenAnswer((invocation) async {
          final filter = invocation.positionalArguments[0] as Map<String, dynamic>;
          return complexProjects.where((project) {
            if (filter.containsKey('genre')) {
              return project.genre == filter['genre'];
            }
            if (filter.containsKey('status')) {
              return project.status == filter['status'];
            }
            return true;
          }).toList();
        });

        // Act & Assert
        await verifyExecutionTime(
          () async {
            final fantasyProjects = await mockProjectRepository.getProjectsByFilter({
              'genre': ProjectGenre.fantasy,
            });
            
            final activeProjects = await mockProjectRepository.getProjectsByFilter({
              'status': ProjectStatus.active,
            });

            expect(fantasyProjects.length, greaterThan(0));
            expect(activeProjects.length, greaterThan(0));
          },
          maxDuration: const Duration(milliseconds: 400),
        );

        verify(mockProjectRepository.getProjectsByFilter(any)).called(2);
      });
    });

    group('Batch Operations Performance', () {
      test('should handle batch chapter operations efficiently', () async {
        // Arrange
        final batchChapters = List.generate(100, (index) => Chapter(
          id: 'batch-chapter-$index',
          projectId: 'project-1',
          title: '批量章节$index',
          content: '批量内容$index',
          order: index + 1,
          status: ChapterStatus.draft,
          wordCount: 100,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ));

        when(mockChapterRepository.createChaptersBatch(any)).thenAnswer((invocation) async {
          final chapters = invocation.positionalArguments[0] as List<Chapter>;
          await Future.delayed(Duration(milliseconds: chapters.length * 2)); // Simulate batch processing
          return chapters;
        });

        // Act & Assert
        await verifyExecutionTime(
          () async {
            final results = await mockChapterRepository.createChaptersBatch(batchChapters);
            expect(results.length, equals(100));
          },
          maxDuration: const Duration(milliseconds: 500),
        );

        verify(mockChapterRepository.createChaptersBatch(any)).called(1);
      });

      test('should handle batch bible operations efficiently', () async {
        // Arrange
        final batchCharacters = List.generate(50, (index) => CharacterBible(
          id: 'batch-char-$index',
          projectId: 'project-1',
          name: '批量角色$index',
          age: 25,
          gender: Gender.male,
          occupation: '职业$index',
          personality: [],
          appearance: '',
          background: '',
          relationships: {},
          skills: [],
          goals: [],
          fears: [],
          secrets: [],
          characterArc: '',
          notes: '',
          tags: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ));

        when(mockBibleRepository.createCharacterBiblesBatch(any)).thenAnswer((invocation) async {
          final characters = invocation.positionalArguments[0] as List<CharacterBible>;
          await Future.delayed(Duration(milliseconds: characters.length * 3));
          return characters;
        });

        // Act & Assert
        await verifyExecutionTime(
          () async {
            final results = await mockBibleRepository.createCharacterBiblesBatch(batchCharacters);
            expect(results.length, equals(50));
          },
          maxDuration: const Duration(milliseconds: 400),
        );

        verify(mockBibleRepository.createCharacterBiblesBatch(any)).called(1);
      });
    });

    group('Stress Testing', () {
      test('should maintain performance under high load', () async {
        // Arrange
        const operationCount = 500;
        final operations = <Future>[];

        // Mock various operations
        when(mockProjectRepository.getProject(any)).thenAnswer((_) async => Project(
          id: 'test-project',
          title: '测试项目',
          description: '描述',
          genre: ProjectGenre.fantasy,
          status: ProjectStatus.active,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          settings: const ProjectSettings(),
        ));

        when(mockChapterRepository.getChapter(any)).thenAnswer((_) async => Chapter(
          id: 'test-chapter',
          projectId: 'test-project',
          title: '测试章节',
          content: '内容',
          order: 1,
          status: ChapterStatus.draft,
          wordCount: 50,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ));

        when(mockBibleRepository.getCharacterBible(any)).thenAnswer((_) async => CharacterBible(
          id: 'test-char',
          projectId: 'test-project',
          name: '测试角色',
          age: 25,
          gender: Gender.male,
          occupation: '职业',
          personality: [],
          appearance: '',
          background: '',
          relationships: {},
          skills: [],
          goals: [],
          fears: [],
          secrets: [],
          characterArc: '',
          notes: '',
          tags: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ));

        // Act & Assert
        await verifyExecutionTime(
          () async {
            // Create mixed operations
            for (int i = 0; i < operationCount; i++) {
              switch (i % 3) {
                case 0:
                  operations.add(mockProjectRepository.getProject('project-$i'));
                  break;
                case 1:
                  operations.add(mockChapterRepository.getChapter('chapter-$i'));
                  break;
                case 2:
                  operations.add(mockBibleRepository.getCharacterBible('char-$i'));
                  break;
              }
            }

            final results = await Future.wait(operations);
            expect(results.length, equals(operationCount));
          },
          maxDuration: const Duration(seconds: 5),
        );

        // Verify all operations were called
        verify(mockProjectRepository.getProject(any)).called(167); // ~500/3
        verify(mockChapterRepository.getChapter(any)).called(167);
        verify(mockBibleRepository.getCharacterBible(any)).called(166);
      });
    });
  });
}