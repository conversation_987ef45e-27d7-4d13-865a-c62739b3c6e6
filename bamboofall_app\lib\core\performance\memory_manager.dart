import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/painting.dart';

/// 内存管理器
class MemoryManager {
  static final MemoryManager _instance = MemoryManager._internal();
  factory MemoryManager() => _instance;
  MemoryManager._internal();

  Timer? _monitoringTimer;
  final List<MemorySnapshot> _snapshots = [];
  final StreamController<MemoryInfo> _memoryInfoController = StreamController<MemoryInfo>.broadcast();
  
  // 内存阈值配置
  static const int _warningThresholdMB = 400;
  static const int _criticalThresholdMB = 500;
  static const int _maxSnapshotsCount = 100;
  
  // 监控间隔
  static const Duration _monitoringInterval = Duration(seconds: 5);
  
  /// 内存信息流
  Stream<MemoryInfo> get memoryInfoStream => _memoryInfoController.stream;
  
  /// 当前内存快照列表
  List<MemorySnapshot> get snapshots => List.unmodifiable(_snapshots);
  
  /// 启动内存监控
  void startMonitoring() {
    if (_monitoringTimer?.isActive == true) return;
    
    _monitoringTimer = Timer.periodic(_monitoringInterval, (_) {
      _collectMemoryInfo();
    });
    
    developer.log('Memory monitoring started', name: 'MemoryManager');
  }
  
  /// 停止内存监控
  void stopMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    developer.log('Memory monitoring stopped', name: 'MemoryManager');
  }
  
  /// 收集内存信息
  Future<void> _collectMemoryInfo() async {
    try {
      final memoryInfo = await _getCurrentMemoryInfo();
      
      // 添加快照
      final snapshot = MemorySnapshot(
        timestamp: DateTime.now(),
        memoryInfo: memoryInfo,
      );
      
      _snapshots.add(snapshot);
      
      // 限制快照数量
      if (_snapshots.length > _maxSnapshotsCount) {
        _snapshots.removeAt(0);
      }
      
      // 发送内存信息
      _memoryInfoController.add(memoryInfo);
      
      // 检查内存警告
      _checkMemoryWarnings(memoryInfo);
      
    } catch (e) {
      developer.log('Failed to collect memory info: $e', name: 'MemoryManager');
    }
  }
  
  /// 获取当前内存信息
  Future<MemoryInfo> _getCurrentMemoryInfo() async {
    final ProcessInfo processInfo = await Process.run('tasklist', [
      '/FI', 'PID eq $pid',
      '/FO', 'CSV',
      '/NH'
    ]).then((result) {
      if (result.exitCode == 0) {
        final lines = result.stdout.toString().split('\n');
        if (lines.isNotEmpty) {
          final parts = lines[0].split(',');
          if (parts.length >= 5) {
            final memoryStr = parts[4].replaceAll('"', '').replaceAll(',', '').replaceAll(' K', '');
            final memoryKB = int.tryParse(memoryStr) ?? 0;
            return ProcessInfo(
              pid: pid,
              memoryUsageKB: memoryKB,
            );
          }
        }
      }
      return ProcessInfo(pid: pid, memoryUsageKB: 0);
    }).catchError((_) => ProcessInfo(pid: pid, memoryUsageKB: 0));
    
    // 获取Dart VM内存信息
    final vmMemory = _getVMMemoryInfo();
    
    return MemoryInfo(
      processMemoryMB: processInfo.memoryUsageKB / 1024,
      dartVMMemoryMB: vmMemory.heapUsage / (1024 * 1024),
      dartVMHeapCapacityMB: vmMemory.heapCapacity / (1024 * 1024),
      externalMemoryMB: vmMemory.externalUsage / (1024 * 1024),
      timestamp: DateTime.now(),
    );
  }
  
  /// 获取VM内存信息
  VMMemoryInfo _getVMMemoryInfo() {
    try {
      // 在发布模式下，某些内存信息可能不可用
      if (kReleaseMode) {
        return VMMemoryInfo(
          heapUsage: 0,
          heapCapacity: 0,
          externalUsage: 0,
        );
      }
      
      // 这里应该使用dart:developer的Service API获取详细信息
      // 为了简化，返回模拟数据
      return VMMemoryInfo(
        heapUsage: 50 * 1024 * 1024, // 50MB
        heapCapacity: 100 * 1024 * 1024, // 100MB
        externalUsage: 10 * 1024 * 1024, // 10MB
      );
    } catch (e) {
      return VMMemoryInfo(
        heapUsage: 0,
        heapCapacity: 0,
        externalUsage: 0,
      );
    }
  }
  
  /// 检查内存警告
  void _checkMemoryWarnings(MemoryInfo memoryInfo) {
    final totalMemoryMB = memoryInfo.processMemoryMB;
    
    if (totalMemoryMB > _criticalThresholdMB) {
      developer.log(
        'CRITICAL: Memory usage is ${totalMemoryMB.toStringAsFixed(1)}MB (>${_criticalThresholdMB}MB)',
        name: 'MemoryManager',
        level: 1000, // SEVERE
      );
      
      // 触发紧急内存清理
      _performEmergencyCleanup();
      
    } else if (totalMemoryMB > _warningThresholdMB) {
      developer.log(
        'WARNING: Memory usage is ${totalMemoryMB.toStringAsFixed(1)}MB (>${_warningThresholdMB}MB)',
        name: 'MemoryManager',
        level: 900, // WARNING
      );
      
      // 触发常规内存清理
      _performRegularCleanup();
    }
  }
  
  /// 执行紧急内存清理
  void _performEmergencyCleanup() {
    developer.log('Performing emergency memory cleanup', name: 'MemoryManager');
    
    // 强制垃圾回收
    _forceGarbageCollection();
    
    // 清理图片缓存
    _clearImageCache();
    
    // 清理其他缓存
    _clearOtherCaches();
  }
  
  /// 执行常规内存清理
  void _performRegularCleanup() {
    developer.log('Performing regular memory cleanup', name: 'MemoryManager');
    
    // 建议垃圾回收
    _suggestGarbageCollection();
    
    // 清理过期缓存
    _clearExpiredCaches();
  }
  
  /// 强制垃圾回收
  void _forceGarbageCollection() {
    try {
      // 在Dart中，我们不能直接强制GC，但可以创建一些压力
      for (int i = 0; i < 10; i++) {
        final list = List.generate(1000, (index) => index);
        list.clear();
      }
    } catch (e) {
      developer.log('Failed to force GC: $e', name: 'MemoryManager');
    }
  }
  
  /// 建议垃圾回收
  void _suggestGarbageCollection() {
    // 创建一些临时对象来触发GC
    final temp = List.generate(100, (index) => 'temp_$index');
    temp.clear();
  }
  
  /// 清理图片缓存
  void _clearImageCache() {
    try {
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();
      developer.log('Image cache cleared', name: 'MemoryManager');
    } catch (e) {
      developer.log('Failed to clear image cache: $e', name: 'MemoryManager');
    }
  }
  
  /// 清理其他缓存
  void _clearOtherCaches() {
    // 这里可以清理应用特定的缓存
    // 例如：文档缓存、AI响应缓存等
    developer.log('Other caches cleared', name: 'MemoryManager');
  }
  
  /// 清理过期缓存
  void _clearExpiredCaches() {
    // 清理过期的缓存项
    developer.log('Expired caches cleared', name: 'MemoryManager');
  }

  /// 执行内存清理（公共方法）
  void performCleanup() {
    _performRegularCleanup();
  }
  
  /// 获取内存使用统计
  MemoryStatistics getMemoryStatistics() {
    if (_snapshots.isEmpty) {
      return MemoryStatistics.empty();
    }
    
    final memoryValues = _snapshots.map((s) => s.memoryInfo.processMemoryMB).toList();
    memoryValues.sort();
    
    final count = memoryValues.length;
    final sum = memoryValues.reduce((a, b) => a + b);
    final average = sum / count;
    final min = memoryValues.first;
    final max = memoryValues.last;
    final median = count % 2 == 0
        ? (memoryValues[count ~/ 2 - 1] + memoryValues[count ~/ 2]) / 2
        : memoryValues[count ~/ 2];
    
    return MemoryStatistics(
      averageMemoryMB: average,
      minMemoryMB: min,
      maxMemoryMB: max,
      medianMemoryMB: median,
      currentMemoryMB: _snapshots.last.memoryInfo.processMemoryMB,
      snapshotCount: count,
      monitoringDuration: _snapshots.last.timestamp.difference(_snapshots.first.timestamp),
    );
  }
  
  /// 导出内存报告
  String exportMemoryReport() {
    final statistics = getMemoryStatistics();
    final buffer = StringBuffer();
    
    buffer.writeln('=== 内存使用报告 ===');
    buffer.writeln('生成时间: ${DateTime.now()}');
    buffer.writeln('监控时长: ${statistics.monitoringDuration}');
    buffer.writeln('快照数量: ${statistics.snapshotCount}');
    buffer.writeln('');
    buffer.writeln('内存统计:');
    buffer.writeln('  当前内存: ${statistics.currentMemoryMB.toStringAsFixed(1)} MB');
    buffer.writeln('  平均内存: ${statistics.averageMemoryMB.toStringAsFixed(1)} MB');
    buffer.writeln('  最小内存: ${statistics.minMemoryMB.toStringAsFixed(1)} MB');
    buffer.writeln('  最大内存: ${statistics.maxMemoryMB.toStringAsFixed(1)} MB');
    buffer.writeln('  中位数: ${statistics.medianMemoryMB.toStringAsFixed(1)} MB');
    buffer.writeln('');
    buffer.writeln('内存快照:');
    
    for (final snapshot in _snapshots.take(20)) { // 只显示最近20个快照
      buffer.writeln('  ${snapshot.timestamp}: ${snapshot.memoryInfo.processMemoryMB.toStringAsFixed(1)} MB');
    }
    
    return buffer.toString();
  }
  
  /// 清理资源
  void dispose() {
    stopMonitoring();
    _memoryInfoController.close();
    _snapshots.clear();
  }
}

/// 内存信息
class MemoryInfo {
  final double processMemoryMB;
  final double dartVMMemoryMB;
  final double dartVMHeapCapacityMB;
  final double externalMemoryMB;
  final DateTime timestamp;

  const MemoryInfo({
    required this.processMemoryMB,
    required this.dartVMMemoryMB,
    required this.dartVMHeapCapacityMB,
    required this.externalMemoryMB,
    required this.timestamp,
  });

  double get totalMemoryMB => processMemoryMB + externalMemoryMB;
  
  double get heapUsagePercentage => 
      dartVMHeapCapacityMB > 0 ? (dartVMMemoryMB / dartVMHeapCapacityMB) * 100 : 0;
}

/// 内存快照
class MemorySnapshot {
  final DateTime timestamp;
  final MemoryInfo memoryInfo;

  const MemorySnapshot({
    required this.timestamp,
    required this.memoryInfo,
  });
}

/// VM内存信息
class VMMemoryInfo {
  final int heapUsage;
  final int heapCapacity;
  final int externalUsage;

  const VMMemoryInfo({
    required this.heapUsage,
    required this.heapCapacity,
    required this.externalUsage,
  });
}

/// 进程信息
class ProcessInfo {
  final int pid;
  final int memoryUsageKB;

  const ProcessInfo({
    required this.pid,
    required this.memoryUsageKB,
  });
}

/// 内存统计信息
class MemoryStatistics {
  final double averageMemoryMB;
  final double minMemoryMB;
  final double maxMemoryMB;
  final double medianMemoryMB;
  final double currentMemoryMB;
  final int snapshotCount;
  final Duration monitoringDuration;

  const MemoryStatistics({
    required this.averageMemoryMB,
    required this.minMemoryMB,
    required this.maxMemoryMB,
    required this.medianMemoryMB,
    required this.currentMemoryMB,
    required this.snapshotCount,
    required this.monitoringDuration,
  });

  factory MemoryStatistics.empty() {
    return const MemoryStatistics(
      averageMemoryMB: 0,
      minMemoryMB: 0,
      maxMemoryMB: 0,
      medianMemoryMB: 0,
      currentMemoryMB: 0,
      snapshotCount: 0,
      monitoringDuration: Duration.zero,
    );
  }
}

/// 内存管理器扩展功能
extension MemoryManagerExtensions on MemoryManager {
  /// 检查内存泄漏
  bool hasMemoryLeak() {
    if (_snapshots.length < 10) return false;
    
    final recent = _snapshots.length >= 10
        ? _snapshots.sublist(_snapshots.length - 10)
        : _snapshots;
    final trend = _calculateMemoryTrend(recent);
    
    // 如果内存持续增长且增长率超过阈值，可能存在内存泄漏
    return trend > 5.0; // 每个快照增长5MB以上
  }
  
  /// 计算内存增长趋势
  double _calculateMemoryTrend(List<MemorySnapshot> snapshots) {
    if (snapshots.length < 2) return 0;
    
    final first = snapshots.first.memoryInfo.processMemoryMB;
    final last = snapshots.last.memoryInfo.processMemoryMB;
    
    return (last - first) / snapshots.length;
  }
  
  /// 获取内存健康状态
  MemoryHealthStatus getMemoryHealthStatus() {
    final current = _snapshots.lastOrNull?.memoryInfo.processMemoryMB ?? 0;
    
    if (current > MemoryManager._criticalThresholdMB) {
      return MemoryHealthStatus.critical;
    } else if (current > MemoryManager._warningThresholdMB) {
      return MemoryHealthStatus.warning;
    } else if (hasMemoryLeak()) {
      return MemoryHealthStatus.leak;
    } else {
      return MemoryHealthStatus.healthy;
    }
  }
}

/// 内存健康状态
enum MemoryHealthStatus {
  healthy,
  warning,
  critical,
  leak,
}

/// 内存健康状态扩展
extension MemoryHealthStatusExtension on MemoryHealthStatus {
  String get displayName {
    switch (this) {
      case MemoryHealthStatus.healthy:
        return '健康';
      case MemoryHealthStatus.warning:
        return '警告';
      case MemoryHealthStatus.critical:
        return '严重';
      case MemoryHealthStatus.leak:
        return '疑似泄漏';
    }
  }
  
  String get description {
    switch (this) {
      case MemoryHealthStatus.healthy:
        return '内存使用正常';
      case MemoryHealthStatus.warning:
        return '内存使用较高，建议清理';
      case MemoryHealthStatus.critical:
        return '内存使用过高，需要立即处理';
      case MemoryHealthStatus.leak:
        return '检测到可能的内存泄漏';
    }
  }
}