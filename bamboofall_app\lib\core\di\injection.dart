import 'package:get_it/get_it.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../storage/database.dart';
import '../storage/file_storage.dart';
import '../storage/secure_storage.dart';

final GetIt getIt = GetIt.instance;

Future<void> configureDependencies() async {
  // Core services
  getIt.registerLazySingleton<Logger>(() => Logger());
  
  // Storage services
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(sharedPreferences);
  
  const secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
    wOptions: WindowsOptions(
      useBackwardCompatibility: false,
    ),
  );
  getIt.registerSingleton<FlutterSecureStorage>(secureStorage);
  
  // Initialize and register storage managers
  await LocalDatabase.instance.initialize();
  getIt.registerSingleton<LocalDatabase>(LocalDatabase.instance);
  
  await FileStorage.instance.initialize();
  getIt.registerSingleton<FileStorage>(FileStorage.instance);
  
  await SecureStorageManager.instance.initialize();
  getIt.registerSingleton<SecureStorageManager>(SecureStorageManager.instance);
}