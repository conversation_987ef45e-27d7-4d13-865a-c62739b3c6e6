import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:bamboofall_app/core/storage/database.dart';
import 'package:bamboofall_app/features/project/data/datasources/project_local_datasource.dart';
import 'package:bamboofall_app/features/project/data/repositories/project_repository_impl.dart';
import 'package:bamboofall_app/features/project/domain/entities/project.dart';
import 'package:bamboofall_app/features/writing/data/datasources/chapter_local_datasource.dart';
import 'package:bamboofall_app/features/writing/data/repositories/chapter_repository_impl.dart';
import 'package:bamboofall_app/features/writing/domain/entities/chapter.dart';
import 'package:bamboofall_app/features/bible/data/datasources/bible_local_datasource.dart';
import 'package:bamboofall_app/features/bible/data/repositories/bible_repository_impl.dart';
import 'package:bamboofall_app/features/bible/domain/entities/character.dart';

import 'repository_integration_test.mocks.dart';

@GenerateMocks([
  LocalDatabase,
  ProjectLocalDatasource,
  ChapterLocalDatasource,
  BibleLocalDatasource,
])
void main() {
  group('Repository Integration Tests', () {
    late MockLocalDatabase mockDatabase;
    late MockProjectLocalDatasource mockProjectDatasource;
    late MockChapterLocalDatasource mockChapterDatasource;
    late MockBibleLocalDatasource mockBibleDatasource;
    
    late ProjectRepositoryImpl projectRepository;
    late ChapterRepositoryImpl chapterRepository;
    late BibleRepositoryImpl bibleRepository;

    setUp(() {
      mockDatabase = MockLocalDatabase();
      mockProjectDatasource = MockProjectLocalDatasource();
      mockChapterDatasource = MockChapterLocalDatasource();
      mockBibleDatasource = MockBibleLocalDatasource();
      
      projectRepository = ProjectRepositoryImpl(mockProjectDatasource);
      chapterRepository = ChapterRepositoryImpl(mockChapterDatasource);
      bibleRepository = BibleRepositoryImpl(mockBibleDatasource);
    });

    group('Project Repository Integration', () {
      test('should create project and maintain data consistency', () async {
        // Arrange
        final project = Project(
          id: 'project-1',
          name: '测试项目',
          description: '这是一个测试项目',
          type: ProjectType.fantasy,
          status: ProjectStatus.active,
          createdBy: 'test-user',
          config: ProjectConfig(
            autoSave: true,
            backupEnabled: true,
            wordCountTarget: 50000,
            chapterTarget: 20,
          ),
          statistics: ProjectStatistics(
            wordCount: 0,
            chapterCount: 0,
            characterCount: 0,
            lastModified: DateTime.now(),
          ),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          settings: const ProjectSettings(),
        );

        when(mockProjectDatasource.createProject(any)).thenAnswer((_) async => project);
        when(mockProjectDatasource.getProject('project-1')).thenAnswer((_) async => project);

        // Act
        final createdProject = await projectRepository.createProject(project);
        final retrievedProject = await projectRepository.getProject('project-1');

        // Assert
        expect(createdProject.id, equals(project.id));
        expect(createdProject.title, equals(project.title));
        expect(retrievedProject?.id, equals(project.id));
        expect(retrievedProject?.title, equals(project.title));

        // Verify datasource interactions
        verify(mockProjectDatasource.createProject(any)).called(1);
        verify(mockProjectDatasource.getProject('project-1')).called(1);
      });

      test('should handle project updates with proper versioning', () async {
        // Arrange
        final originalProject = Project(
          id: 'project-1',
          title: '原始标题',
          description: '原始描述',
          genre: ProjectGenre.romance,
          status: ProjectStatus.active,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          settings: const ProjectSettings(),
        );

        final updatedProject = originalProject.copyWith(
          title: '更新后的标题',
          description: '更新后的描述',
          updatedAt: DateTime.now().add(const Duration(minutes: 1)),
        );

        when(mockProjectDatasource.getProject('project-1')).thenAnswer((_) async => originalProject);
        when(mockProjectDatasource.updateProject(any)).thenAnswer((_) async => updatedProject);

        // Act
        final result = await projectRepository.updateProject(updatedProject);

        // Assert
        expect(result.name, equals('更新后的标题'));
        expect(result.description, equals('更新后的描述'));
        expect(result.updatedAt.isAfter(originalProject.updatedAt), isTrue);

        verify(mockProjectDatasource.updateProject(any)).called(1);
      });

      test('should handle project deletion with cascade operations', () async {
        // Arrange
        const projectId = 'project-to-delete';
        
        when(mockProjectDatasource.deleteProject(projectId)).thenAnswer((_) async => {});
        when(mockChapterDatasource.deleteChaptersByProject(projectId)).thenAnswer((_) async => {});
        when(mockBibleDatasource.deleteBiblesByProject(projectId)).thenAnswer((_) async => {});

        // Act
        await projectRepository.deleteProject(projectId);

        // Assert
        verify(mockProjectDatasource.deleteProject(projectId)).called(1);
        // Note: In real implementation, cascade deletion would be handled
      });
    });

    group('Chapter Repository Integration', () {
      test('should create chapters with proper ordering', () async {
        // Arrange
        final chapters = [
          Chapter(
            id: 'chapter-1',
            projectId: 'project-1',
            title: '第一章',
            content: '第一章内容',
            order: 1,
            status: ChapterStatus.draft,
            wordCount: 100,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Chapter(
            id: 'chapter-2',
            projectId: 'project-1',
            title: '第二章',
            content: '第二章内容',
            order: 2,
            status: ChapterStatus.draft,
            wordCount: 150,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        when(mockChapterDatasource.createChapter(any)).thenAnswer((invocation) async {
          final chapter = invocation.positionalArguments[0] as Chapter;
          return chapter;
        });
        when(mockChapterDatasource.getChaptersByProject('project-1')).thenAnswer((_) async => chapters);

        // Act
        await chapterRepository.createChapter(chapters[0]);
        await chapterRepository.createChapter(chapters[1]);
        final retrievedChapters = await chapterRepository.getChaptersByProject('project-1');

        // Assert
        expect(retrievedChapters.length, equals(2));
        expect(retrievedChapters[0].order, equals(1));
        expect(retrievedChapters[1].order, equals(2));
        expect(retrievedChapters[0].title, equals('第一章'));
        expect(retrievedChapters[1].title, equals('第二章'));

        verify(mockChapterDatasource.createChapter(any)).called(2);
        verify(mockChapterDatasource.getChaptersByProject('project-1')).called(1);
      });

      test('should handle chapter reordering correctly', () async {
        // Arrange
        final originalChapters = [
          Chapter(
            id: 'chapter-1',
            projectId: 'project-1',
            title: '第一章',
            content: '内容1',
            order: 1,
            status: ChapterStatus.draft,
            wordCount: 100,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Chapter(
            id: 'chapter-2',
            projectId: 'project-1',
            title: '第二章',
            content: '内容2',
            order: 2,
            status: ChapterStatus.draft,
            wordCount: 150,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        final reorderedChapters = [
          originalChapters[1].copyWith(order: 1),
          originalChapters[0].copyWith(order: 2),
        ];

        when(mockChapterDatasource.getChaptersByProject('project-1')).thenAnswer((_) async => originalChapters);
        when(mockChapterDatasource.reorderChapters('project-1', any)).thenAnswer((_) async => {});
        when(mockChapterDatasource.getChaptersByProject('project-1')).thenAnswer((_) async => reorderedChapters);

        // Act
        await chapterRepository.reorderChapters('project-1', ['chapter-2', 'chapter-1']);
        final result = await chapterRepository.getChaptersByProject('project-1');

        // Assert
        expect(result[0].id, equals('chapter-2'));
        expect(result[0].order, equals(1));
        expect(result[1].id, equals('chapter-1'));
        expect(result[1].order, equals(2));

        verify(mockChapterDatasource.reorderChapters('project-1', any)).called(1);
      });

      test('should maintain word count consistency', () async {
        // Arrange
        final chapter = Chapter(
          id: 'chapter-1',
          projectId: 'project-1',
          title: '测试章节',
          content: '这是一个测试章节的内容，用于验证字数统计功能。',
          order: 1,
          status: ChapterStatus.draft,
          wordCount: 0, // Will be calculated
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final updatedChapter = chapter.copyWith(
          content: '这是一个更新后的测试章节内容，字数应该会相应更新。新增了更多的文字内容来测试字数统计的准确性。',
          wordCount: 42, // Expected word count
        );

        when(mockChapterDatasource.updateChapter(any)).thenAnswer((_) async => updatedChapter);

        // Act
        final result = await chapterRepository.updateChapter(updatedChapter);

        // Assert
        expect(result.wordCount, equals(42));
        expect(result.content.length, greaterThan(chapter.content.length));

        verify(mockChapterDatasource.updateChapter(any)).called(1);
      });
    });

    group('Bible Repository Integration', () {
      test('should create character bible with relationships', () async {
        // Arrange
        final character = CharacterBible(
          id: 'char-1',
          projectId: 'project-1',
          name: '李明',
          age: 25,
          gender: Gender.male,
          occupation: '软件工程师',
          personality: ['内向', '聪明'],
          appearance: '中等身材',
          background: '程序员背景',
          relationships: {
            'char-2': CharacterRelationship(
              targetCharacterId: 'char-2',
              relationshipType: RelationshipType.friend,
              description: '大学同学',
              strength: 0.8,
            ),
          },
          skills: ['编程', '数学'],
          goals: ['成为技术专家'],
          fears: ['失败'],
          secrets: ['暗恋同事'],
          characterArc: '成长历程',
          notes: '主角',
          tags: ['主角', '程序员'],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        when(mockBibleDatasource.createCharacter(any)).thenAnswer((_) async => character);
        when(mockBibleDatasource.getCharacter('char-1')).thenAnswer((_) async => character);

        // Act
        final createdCharacter = await bibleRepository.createCharacter(character);
        final retrievedCharacter = await bibleRepository.getCharacter('char-1');

        // Assert
        expect(createdCharacter.name, equals('李明'));
        expect(createdCharacter.relationships.length, equals(1));
        expect(createdCharacter.relationships['char-2']?.relationshipType, equals(RelationshipType.friend));
        
        expect(retrievedCharacter?.name, equals('李明'));
        expect(retrievedCharacter?.relationships.length, equals(1));

        verify(mockBibleDatasource.createCharacter(any)).called(1);
        verify(mockBibleDatasource.getCharacter('char-1')).called(1);
      });

      test('should validate cross-references between bible entities', () async {
        // Arrange
        final character = Character(
          id: 'char-1',
          projectId: 'project-1',
          name: '李明',
          status: CharacterStatus.active,
          appearance: CharacterAppearance(
            age: 25,
            gender: Gender.male,
            height: '175cm',
            build: '中等',
            hairColor: '黑色',
            eyeColor: '棕色',
            distinctiveFeatures: '戴眼镜',
            clothing: '休闲装',
          ),
          personality: CharacterPersonality(
            traits: ['聪明', '内向'],
            motivations: ['成功'],
            fears: ['失败'],
            values: ['诚实'],
            flaws: ['完美主义'],
            strengths: ['逻辑思维'],
          ),
          occupation: '软件工程师',
          skills: [],
          relationships: {
            'char-2': CharacterRelationship(
              targetCharacterId: 'char-2',
              relationshipType: RelationshipType.friend,
              description: '朋友',
              strength: 0.7,
            ),
          },
          notes: '',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final violations = [
          ConstraintViolation(
            id: 'violation-1',
            type: ViolationType.invalidReference,
            severity: ViolationSeverity.error,
            message: '角色引用了不存在的角色: char-2',
            entityType: 'character',
            entityId: 'char-1',
            conflictingEntityId: 'char-2',
            suggestions: ['创建对应的角色', '移除无效引用'],
            createdAt: DateTime.now(),
          ),
        ];

        when(mockBibleDatasource.getCharacters('project-1')).thenAnswer((_) async => [character]);
        when(mockBibleDatasource.validateConstraints('project-1')).thenAnswer((_) async => violations);

        // Act
        final characters = await bibleRepository.getCharacters('project-1');
        final validationResult = await bibleRepository.validateConstraints('project-1');

        // Assert
        expect(characters.length, equals(1));
        expect(characters[0].relationships.containsKey('char-2'), isTrue);
        
        expect(validationResult.length, equals(1));
        expect(validationResult[0].type, equals(ViolationType.invalidReference));
        expect(validationResult[0].message, contains('char-2'));

        verify(mockBibleDatasource.getCharacters('project-1')).called(1);
        verify(mockBibleDatasource.validateConstraints('project-1')).called(1);
      });
    });

    group('Cross-Repository Data Flow', () {
      test('should maintain consistency across project, chapter, and bible data', () async {
        // Arrange
        const projectId = 'integrated-project';
        
        final project = Project(
          id: projectId,
          name: '集成测试项目',
          description: '测试跨Repository数据一致性',
          type: ProjectType.fantasy,
          status: ProjectStatus.active,
          createdBy: 'test-user',
          config: ProjectConfig(
            autoSave: true,
            backupEnabled: true,
            wordCountTarget: 50000,
            chapterTarget: 20,
          ),
          statistics: ProjectStatistics(
            wordCount: 0,
            chapterCount: 0,
            characterCount: 0,
            lastModified: DateTime.now(),
          ),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final chapter = Chapter(
          id: 'chapter-1',
          projectId: projectId,
          title: '第一章',
          content: '章节内容',
          order: 1,
          status: ChapterStatus.draft,
          wordCount: 50,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final character = Character(
          id: 'char-1',
          projectId: projectId,
          name: '主角',
          status: CharacterStatus.active,
          appearance: CharacterAppearance(
            age: 20,
            gender: Gender.male,
            height: '170cm',
            build: '中等',
            hairColor: '黑色',
            eyeColor: '棕色',
            distinctiveFeatures: '',
            clothing: '冒险者装备',
          ),
          personality: CharacterPersonality(
            traits: ['勇敢', '善良'],
            motivations: ['拯救世界'],
            fears: ['失去朋友'],
            values: ['正义'],
            flaws: ['冲动'],
            strengths: ['坚韧'],
          ),
          occupation: '冒险者',
          skills: [],
          relationships: {},
          notes: '',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Mock responses
        when(mockProjectDatasource.createProject(any)).thenAnswer((_) async => project);
        when(mockProjectDatasource.getProject(projectId)).thenAnswer((_) async => project);
        
        when(mockChapterDatasource.createChapter(any)).thenAnswer((_) async => chapter);
        when(mockChapterDatasource.getChaptersByProject(projectId)).thenAnswer((_) async => [chapter]);
        
        when(mockBibleDatasource.createCharacter(any)).thenAnswer((_) async => character);
        when(mockBibleDatasource.getCharacters(projectId)).thenAnswer((_) async => [character]);

        // Act
        final createdProject = await projectRepository.createProject(project);
        final createdChapter = await chapterRepository.createChapter(chapter);
        final createdCharacter = await bibleRepository.createCharacter(character);

        // Verify data consistency
        final retrievedProject = await projectRepository.getProject(projectId);
        final retrievedChapters = await chapterRepository.getChaptersByProject(projectId);
        final retrievedCharacters = await bibleRepository.getCharacters(projectId);

        // Assert
        expect(createdProject.id, equals(projectId));
        expect(createdChapter.projectId, equals(projectId));
        expect(createdCharacter.projectId, equals(projectId));

        expect(retrievedProject?.id, equals(projectId));
        expect(retrievedChapters.first.projectId, equals(projectId));
        expect(retrievedCharacters.first.projectId, equals(projectId));

        // Verify all entities belong to the same project
        expect(retrievedChapters.every((c) => c.projectId == projectId), isTrue);
        expect(retrievedCharacters.every((c) => c.projectId == projectId), isTrue);
      });

      test('should handle transaction rollback on failure', () async {
        // Arrange
        const projectId = 'transaction-test-project';
        
        final project = Project(
          id: projectId,
          name: '事务测试项目',
          description: '测试事务回滚',
          type: ProjectType.sciFi,
          status: ProjectStatus.active,
          createdBy: 'test-user',
          config: ProjectConfig(
            autoSave: true,
            backupEnabled: true,
            wordCountTarget: 50000,
            chapterTarget: 20,
          ),
          statistics: ProjectStatistics(
            wordCount: 0,
            chapterCount: 0,
            characterCount: 0,
            lastModified: DateTime.now(),
          ),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final chapter = Chapter(
          id: 'chapter-1',
          projectId: projectId,
          title: '第一章',
          content: '内容',
          order: 1,
          status: ChapterStatus.draft,
          wordCount: 10,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Mock successful project creation but failed chapter creation
        when(mockProjectDatasource.createProject(any)).thenAnswer((_) async => project);
        when(mockChapterDatasource.createChapter(any)).thenThrow(Exception('数据库连接失败'));

        // Act & Assert
        await projectRepository.createProject(project);
        
        expect(
          () async => await chapterRepository.createChapter(chapter),
          throwsException,
        );

        // Verify project was created but chapter was not
        verify(mockProjectDatasource.createProject(any)).called(1);
        verify(mockChapterDatasource.createChapter(any)).called(1);
      });

      test('should handle concurrent operations correctly', () async {
        // Arrange
        const projectId = 'concurrent-test-project';
        
        final chapters = List.generate(5, (index) => Chapter(
          id: 'chapter-${index + 1}',
          projectId: projectId,
          title: '第${index + 1}章',
          content: '章节${index + 1}内容',
          order: index + 1,
          status: ChapterStatus.draft,
          wordCount: 100,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ));

        // Mock concurrent chapter creation
        when(mockChapterDatasource.createChapter(any)).thenAnswer((invocation) async {
          final chapter = invocation.positionalArguments[0] as Chapter;
          // Simulate some processing time
          await Future.delayed(const Duration(milliseconds: 100));
          return chapter;
        });

        // Act - Create chapters concurrently
        final futures = chapters.map((chapter) => chapterRepository.createChapter(chapter));
        final results = await Future.wait(futures);

        // Assert
        expect(results.length, equals(5));
        for (int i = 0; i < results.length; i++) {
          expect(results[i].id, equals('chapter-${i + 1}'));
          expect(results[i].order, equals(i + 1));
        }

        verify(mockChapterDatasource.createChapter(any)).called(5);
      });
    });

    group('Error Handling and Recovery', () {
      test('should handle network failures gracefully', () async {
        // Arrange
        when(mockProjectDatasource.getProjects()).thenThrow(Exception('网络连接超时'));

        // Act & Assert
        expect(
          () async => await projectRepository.getProjects(),
          throwsException,
        );

        verify(mockProjectDatasource.getProjects()).called(1);
      });

      test('should handle data corruption scenarios', () async {
        // Arrange
        when(mockChapterDatasource.getChapter('corrupted-chapter')).thenThrow(FormatException('数据格式错误'));

        // Act & Assert
        expect(
          () async => await chapterRepository.getChapter('corrupted-chapter'),
          throwsA(isA<FormatException>()),
        );

        verify(mockChapterDatasource.getChapter('corrupted-chapter')).called(1);
      });

      test('should handle storage quota exceeded', () async {
        // Arrange
        final largeChapter = Chapter(
          id: 'large-chapter',
          projectId: 'project-1',
          title: '大章节',
          content: 'x' * 1000000, // Very large content
          order: 1,
          status: ChapterStatus.draft,
          wordCount: 1000000,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        when(mockChapterDatasource.createChapter(any)).thenThrow(Exception('存储空间不足'));

        // Act & Assert
        expect(
          () async => await chapterRepository.createChapter(largeChapter),
          throwsException,
        );

        verify(mockChapterDatasource.createChapter(any)).called(1);
      });
    });
  });
}