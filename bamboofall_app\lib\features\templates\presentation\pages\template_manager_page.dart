import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/template.dart';
import '../widgets/template_editor.dart';
import '../widgets/template_list_item.dart';
import '../widgets/template_search_bar.dart';
import '../widgets/template_category_filter.dart';
import '../providers/template_providers.dart';

/// 模板管理页面
class TemplateManagerPage extends ConsumerStatefulWidget {
  const TemplateManagerPage({super.key});

  @override
  ConsumerState<TemplateManagerPage> createState() => _TemplateManagerPageState();
}

class _TemplateManagerPageState extends ConsumerState<TemplateManagerPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  TemplateCategory? _selectedCategory;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('模板管理'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: '所有模板', icon: Icon(Icons.list)),
            Tab(text: '我的模板', icon: Icon(Icons.person)),
            Tab(text: '统计信息', icon: Icon(Icons.analytics)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showCreateTemplateDialog(),
            tooltip: '创建新模板',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'import',
                child: ListTile(
                  leading: Icon(Icons.file_upload),
                  title: Text('导入模板'),
                ),
              ),
              const PopupMenuItem(
                value: 'export_all',
                child: ListTile(
                  leading: Icon(Icons.file_download),
                  title: Text('导出所有模板'),
                ),
              ),
              const PopupMenuItem(
                value: 'cleanup',
                child: ListTile(
                  leading: Icon(Icons.cleaning_services),
                  title: Text('清理未使用模板'),
                ),
              ),
            ],
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllTemplatesTab(),
          _buildUserTemplatesTab(),
          _buildStatisticsTab(),
        ],
      ),
    );
  }

  /// 构建所有模板标签页
  Widget _buildAllTemplatesTab() {
    return Column(
      children: [
        _buildSearchAndFilter(),
        Expanded(
          child: Consumer(
            builder: (context, ref, child) {
              final templatesAsync = ref.watch(filteredTemplatesProvider(
                TemplateFilter(
                  query: _searchQuery,
                  category: _selectedCategory,
                  userOnly: false,
                ),
              ));

              return templatesAsync.when(
                data: (templates) => _buildTemplateList(templates),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error, size: 64, color: Colors.red),
                      const SizedBox(height: 16),
                      Text('加载模板失败: $error'),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => ref.refresh(allTemplatesProvider),
                        child: const Text('重试'),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// 构建用户模板标签页
  Widget _buildUserTemplatesTab() {
    return Column(
      children: [
        _buildSearchAndFilter(),
        Expanded(
          child: Consumer(
            builder: (context, ref, child) {
              final templatesAsync = ref.watch(filteredTemplatesProvider(
                TemplateFilter(
                  query: _searchQuery,
                  category: _selectedCategory,
                  userOnly: true,
                ),
              ));

              return templatesAsync.when(
                data: (templates) => templates.isEmpty
                    ? _buildEmptyUserTemplates()
                    : _buildTemplateList(templates),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => Center(
                  child: Text('加载失败: $error'),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// 构建统计信息标签页
  Widget _buildStatisticsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final statisticsAsync = ref.watch(templateStatisticsProvider);

        return statisticsAsync.when(
          data: (statistics) => _buildStatisticsContent(statistics),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Text('加载统计信息失败: $error'),
          ),
        );
      },
    );
  }

  /// 构建搜索和过滤器
  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          TemplateSearchBar(
            onSearchChanged: (query) {
              setState(() {
                _searchQuery = query;
              });
            },
          ),
          const SizedBox(height: 8),
          TemplateCategoryFilter(
            selectedCategory: _selectedCategory,
            onCategoryChanged: (category) {
              setState(() {
                _selectedCategory = category;
              });
            },
          ),
        ],
      ),
    );
  }

  /// 构建模板列表
  Widget _buildTemplateList(List<PromptTemplate> templates) {
    if (templates.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('没有找到匹配的模板'),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: templates.length,
      itemBuilder: (context, index) {
        final template = templates[index];
        return TemplateListItem(
          template: template,
          onTap: () => _showTemplateDetails(template),
          onEdit: () => _editTemplate(template),
          onDuplicate: () => _duplicateTemplate(template),
          onDelete: template.isBuiltIn ? null : () => _deleteTemplate(template),
          onUse: () => _useTemplate(template),
        );
      },
    );
  }

  /// 构建空的用户模板提示
  Widget _buildEmptyUserTemplates() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.note_add, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          const Text(
            '您还没有创建任何模板',
            style: TextStyle(fontSize: 18, color: Colors.grey),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _showCreateTemplateDialog,
            icon: const Icon(Icons.add),
            label: const Text('创建第一个模板'),
          ),
        ],
      ),
    );
  }

  /// 构建统计信息内容
  Widget _buildStatisticsContent(TemplateStatistics statistics) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatisticsCard(
            '模板概览',
            [
              _buildStatItem('总模板数', statistics.totalTemplates.toString()),
              _buildStatItem('系统模板', statistics.systemTemplates.toString()),
              _buildStatItem('用户模板', statistics.userTemplates.toString()),
              _buildStatItem('最近创建', statistics.recentTemplates.length.toString()),
            ],
          ),
          const SizedBox(height: 16),
          _buildStatisticsCard(
            '使用统计',
            [
              _buildStatItem('总使用次数', statistics.totalUsage.toString()),
              _buildStatItem('平均使用次数', (statistics.totalUsage / statistics.totalTemplates).toStringAsFixed(1)),
              _buildStatItem('最受欢迎分类', '暂无数据'),
            ],
          ),
          const SizedBox(height: 16),
          _buildCategoryStatistics(statistics.categoryCount),
        ],
      ),
    );
  }

  /// 构建统计卡片
  Widget _buildStatisticsCard(String title, List<Widget> children) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  /// 构建分类统计
  Widget _buildCategoryStatistics(Map<TemplateCategory, int> categoryCount) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '分类统计',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            ...categoryCount.entries.map((entry) =>
              _buildStatItem(entry.key.displayName, entry.value.toString()),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示创建模板对话框
  void _showCreateTemplateDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: SizedBox(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.8,
          child: const TemplateEditor(),
        ),
      ),
    );
  }

  /// 显示模板详情
  void _showTemplateDetails(PromptTemplate template) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: SizedBox(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.8,
          child: TemplateEditor(template: template, readOnly: true),
        ),
      ),
    );
  }

  /// 编辑模板
  void _editTemplate(PromptTemplate template) {
    if (template.isBuiltIn) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('无法编辑内置模板，请复制后编辑')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: SizedBox(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.8,
          child: TemplateEditor(template: template),
        ),
      ),
    );
  }

  /// 复制模板
  void _duplicateTemplate(PromptTemplate template) async {
    final nameController = TextEditingController(text: '${template.name} - 副本');
    
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('复制模板'),
        content: TextField(
          controller: nameController,
          decoration: const InputDecoration(
            labelText: '新模板名称',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(nameController.text),
            child: const Text('复制'),
          ),
        ],
      ),
    );

    if (result != null && result.trim().isNotEmpty) {
      try {
        await ref.read(templateManagerProvider.notifier)
            .duplicateTemplate(template.id, result.trim());
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('模板复制成功')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('复制失败: $e')),
          );
        }
      }
    }
  }

  /// 删除模板
  void _deleteTemplate(PromptTemplate template) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除模板'),
        content: Text('确定要删除模板 "${template.name}" 吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(templateManagerProvider.notifier)
            .deleteTemplate(template.id);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('模板删除成功')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('删除失败: $e')),
          );
        }
      }
    }
  }

  /// 使用模板
  void _useTemplate(PromptTemplate template) {
    // 这里应该导航到使用模板的页面或显示模板使用对话框
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: SizedBox(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.8,
          child: TemplateEditor(template: template, useMode: true),
        ),
      ),
    );
  }

  /// 处理菜单操作
  void _handleMenuAction(String action) async {
    switch (action) {
      case 'import':
        await _importTemplates();
        break;
      case 'export_all':
        await _exportAllTemplates();
        break;
      case 'cleanup':
        await _cleanupUnusedTemplates();
        break;
    }
  }

  /// 导入模板
  Future<void> _importTemplates() async {
    // 这里应该打开文件选择器或显示导入对话框
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('导入功能开发中...')),
    );
  }

  /// 导出所有模板
  Future<void> _exportAllTemplates() async {
    try {
      await ref.read(templateManagerProvider.notifier)
          .backupAllTemplates();

      // 这里应该保存到文件或显示导出对话框
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('导出成功')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('导出失败: $e')),
      );
    }
  }

  /// 清理未使用的模板
  Future<void> _cleanupUnusedTemplates() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清理未使用模板'),
        content: const Text('将删除30天内未使用的用户模板，确定继续吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('清理'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final count = await ref.read(templateManagerProvider.notifier)
            .cleanupUnusedTemplates();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('已清理 $count 个未使用的模板')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('清理失败: $e')),
          );
        }
      }
    }
  }
}

/// 模板过滤器
class TemplateFilter {
  final String query;
  final TemplateCategory? category;
  final bool userOnly;

  const TemplateFilter({
    required this.query,
    this.category,
    required this.userOnly,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TemplateFilter &&
          runtimeType == other.runtimeType &&
          query == other.query &&
          category == other.category &&
          userOnly == other.userOnly;

  @override
  int get hashCode => query.hashCode ^ category.hashCode ^ userOnly.hashCode;
}