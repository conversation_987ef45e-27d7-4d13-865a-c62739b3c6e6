import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

/// AI助手消息类型
enum MessageType {
  user,
  assistant,
  system,
}

/// AI助手消息
class AiMessage {
  final String id;
  final String content;
  final MessageType type;
  final DateTime timestamp;
  final bool isLoading;

  const AiMessage({
    required this.id,
    required this.content,
    required this.type,
    required this.timestamp,
    this.isLoading = false,
  });

  AiMessage copyWith({
    String? id,
    String? content,
    MessageType? type,
    DateTime? timestamp,
    bool? isLoading,
  }) {
    return AiMessage(
      id: id ?? this.id,
      content: content ?? this.content,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

/// AI助手状态
class AiAssistantState {
  final List<AiMessage> messages;
  final bool isProcessing;
  final String? selectedModel;

  const AiAssistantState({
    this.messages = const [],
    this.isProcessing = false,
    this.selectedModel,
  });

  AiAssistantState copyWith({
    List<AiMessage>? messages,
    bool? isProcessing,
    String? selectedModel,
  }) {
    return AiAssistantState(
      messages: messages ?? this.messages,
      isProcessing: isProcessing ?? this.isProcessing,
      selectedModel: selectedModel ?? this.selectedModel,
    );
  }
}

/// AI助手状态管理
final aiAssistantProvider = StateNotifierProvider<AiAssistantNotifier, AiAssistantState>((ref) {
  return AiAssistantNotifier();
});

class AiAssistantNotifier extends StateNotifier<AiAssistantState> {
  AiAssistantNotifier() : super(const AiAssistantState());

  void addMessage(AiMessage message) {
    state = state.copyWith(
      messages: [...state.messages, message],
    );
  }

  void updateMessage(String id, AiMessage updatedMessage) {
    final messages = state.messages.map((msg) {
      return msg.id == id ? updatedMessage : msg;
    }).toList();
    
    state = state.copyWith(messages: messages);
  }

  void setProcessing(bool isProcessing) {
    state = state.copyWith(isProcessing: isProcessing);
  }

  void setSelectedModel(String model) {
    state = state.copyWith(selectedModel: model);
  }

  void clearMessages() {
    state = state.copyWith(messages: []);
  }
}

/// AI助手面板
/// 提供AI交互和写作建议功能
class AiAssistantPanel extends ConsumerStatefulWidget {
  const AiAssistantPanel({super.key});

  @override
  ConsumerState<AiAssistantPanel> createState() => _AiAssistantPanelState();
}

class _AiAssistantPanelState extends ConsumerState<AiAssistantPanel> {
  late TextEditingController _inputController;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _inputController = TextEditingController();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _inputController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final assistantState = ref.watch(aiAssistantProvider);

    return Column(
      children: [
        // AI模型选择和工具栏
        _buildToolbar(context, assistantState),
        
        // 消息列表
        Expanded(
          child: _buildMessageList(context, assistantState),
        ),
        
        // 输入区域
        _buildInputArea(context, assistantState),
      ],
    );
  }

  Widget _buildToolbar(BuildContext context, AiAssistantState assistantState) {
    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: fluent.FluentTheme.of(context).resources.dividerStrokeColorDefault,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // AI模型选择
          fluent.ComboBox<String>(
            value: assistantState.selectedModel ?? 'GPT-4',
            items: const [
              fluent.ComboBoxItem(value: 'GPT-4', child: Text('GPT-4')),
              fluent.ComboBoxItem(value: 'Claude', child: Text('Claude')),
              fluent.ComboBoxItem(value: 'Gemini', child: Text('Gemini')),
            ],
            onChanged: (value) {
              if (value != null) {
                ref.read(aiAssistantProvider.notifier).setSelectedModel(value);
              }
            },
          ),
          
          const Spacer(),
          
          // 清空对话
          fluent.IconButton(
            icon: const fluent.Icon(fluent.FluentIcons.clear, size: 16),
            onPressed: () {
              ref.read(aiAssistantProvider.notifier).clearMessages();
            },
          ),
          
          // 更多选项
          fluent.IconButton(
            icon: const fluent.Icon(fluent.FluentIcons.more, size: 16),
            onPressed: () {
              // TODO: 显示更多选项
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMessageList(BuildContext context, AiAssistantState assistantState) {
    if (assistantState.messages.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(8),
      itemCount: assistantState.messages.length,
      itemBuilder: (context, index) {
        final message = assistantState.messages[index];
        return _MessageBubble(message: message);
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const fluent.Icon(
            fluent.FluentIcons.robot,
            size: 48,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            'AI助手已就绪',
            style: fluent.FluentTheme.of(context).typography.subtitle,
          ),
          const SizedBox(height: 8),
          Text(
            '输入您的问题或需求，我将为您提供写作建议',
            style: fluent.FluentTheme.of(context).typography.caption,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          _buildQuickActions(context),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    final quickActions = [
      '续写当前章节',
      '优化文本表达',
      '检查语法错误',
      '生成角色对话',
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: quickActions.map((action) {
        return fluent.Button(
          onPressed: () {
            _sendMessage(action);
          },
          child: Text(action),
        );
      }).toList(),
    );
  }

  Widget _buildInputArea(BuildContext context, AiAssistantState assistantState) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: fluent.FluentTheme.of(context).resources.dividerStrokeColorDefault,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: fluent.TextBox(
              controller: _inputController,
              placeholder: '输入您的问题或需求...',
              maxLines: 3,
              minLines: 1,
              enabled: !assistantState.isProcessing,
              onSubmitted: (value) {
                if (value.trim().isNotEmpty) {
                  _sendMessage(value.trim());
                }
              },
            ),
          ),
          const SizedBox(width: 8),
          fluent.Button(
            onPressed: assistantState.isProcessing
                ? null
                : () {
                    final text = _inputController.text.trim();
                    if (text.isNotEmpty) {
                      _sendMessage(text);
                    }
                  },
            child: assistantState.isProcessing
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: fluent.ProgressRing(strokeWidth: 2),
                  )
                : const fluent.Icon(fluent.FluentIcons.send, size: 16),
          ),
        ],
      ),
    );
  }

  void _sendMessage(String content) {
    final notifier = ref.read(aiAssistantProvider.notifier);
    
    // 添加用户消息
    final userMessage = AiMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      type: MessageType.user,
      timestamp: DateTime.now(),
    );
    notifier.addMessage(userMessage);
    
    // 清空输入框
    _inputController.clear();
    
    // 滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    });
    
    // 模拟AI响应
    _simulateAiResponse(content);
  }

  void _simulateAiResponse(String userInput) {
    final notifier = ref.read(aiAssistantProvider.notifier);
    notifier.setProcessing(true);
    
    // 模拟处理延迟
    Future.delayed(const Duration(seconds: 2), () {
      final aiMessage = AiMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: _generateMockResponse(userInput),
        type: MessageType.assistant,
        timestamp: DateTime.now(),
      );
      
      notifier.addMessage(aiMessage);
      notifier.setProcessing(false);
      
      // 滚动到底部
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      });
    });
  }

  String _generateMockResponse(String userInput) {
    // 简单的模拟响应逻辑
    if (userInput.contains('续写')) {
      return '基于当前章节的内容和风格，我建议您可以从以下几个方向继续发展情节：\n\n1. 深入探索主角的内心世界\n2. 引入新的冲突或转折点\n3. 发展角色之间的关系\n\n您希望我为您生成具体的续写内容吗？';
    } else if (userInput.contains('优化')) {
      return '我已经分析了您的文本，以下是一些优化建议：\n\n• 可以使用更生动的形容词来增强描述\n• 建议调整句子结构，提高可读性\n• 某些段落可以合并以改善流畅度\n\n您希望我提供具体的修改建议吗？';
    } else {
      return '我理解您的需求。作为您的写作助手，我可以帮助您：\n\n• 提供创作灵感和建议\n• 优化文本表达和结构\n• 检查语法和用词\n• 生成角色对话和情节发展\n\n请告诉我您具体需要什么帮助？';
    }
  }
}

/// 消息气泡组件
class _MessageBubble extends StatelessWidget {
  final AiMessage message;

  const _MessageBubble({required this.message});

  @override
  Widget build(BuildContext context) {
    final isUser = message.type == MessageType.user;
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            const CircleAvatar(
              radius: 16,
              backgroundColor: Colors.blue,
              child: fluent.Icon(
                fluent.FluentIcons.robot,
                size: 16,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 8),
          ],
          
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isUser
                    ? fluent.FluentTheme.of(context).accentColor
                    : fluent.FluentTheme.of(context).resources.cardBackgroundFillColorDefault,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.content,
                    style: TextStyle(
                      color: isUser ? Colors.white : null,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatTime(message.timestamp),
                    style: fluent.FluentTheme.of(context).typography.caption?.copyWith(
                          color: isUser
                      ? Colors.white.withValues(alpha: 0.7)
                      : fluent.FluentTheme.of(context).resources.textFillColorSecondary,
                        ),
                  ),
                ],
              ),
            ),
          ),
          
          if (isUser) ...[
            const SizedBox(width: 8),
            const CircleAvatar(
              radius: 16,
              backgroundColor: Colors.green,
              child: fluent.Icon(
                fluent.FluentIcons.contact,
                size: 16,
                color: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}