import '../entities/ai_model.dart';
import '../repositories/llm_repository.dart';

/// 内容生成用例
/// 封装AI内容生成的业务逻辑
class GenerateContentUseCase {
  final LLMRepository _repository;
  
  const GenerateContentUseCase(this._repository);
  
  // ==================== 基础内容生成 ====================
  
  /// 生成文本内容
  Future<GenerateContentResult> generateText({
    required String modelId,
    required String prompt,
    String? systemPrompt,
    double? temperature,
    int? maxTokens,
    List<String>? stop,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // 验证模型可用性
      final isAvailable = await _repository.isModelAvailable(modelId);
      if (!isAvailable) {
        throw ModelUnavailableException('Model $modelId is not available');
      }
      
      // 构建请求
      final request = AIRequest(
        modelId: modelId,
        prompt: prompt,
        systemPrompt: systemPrompt,
        temperature: temperature ?? 0.7,
        maxTokens: maxTokens ?? 4096,
        stop: stop,
        metadata: metadata,
        requestId: _repository.generateRequestId(),
      );
      
      // 验证请求
      if (!_repository.validateRequest(request)) {
        throw ValidationException('Invalid request parameters');
      }
      
      // 发送请求
      final response = await _repository.completion(request);
      
      // 记录使用统计
      await _repository.recordUsage(modelId, response.usage);
      
      return GenerateContentResult.success(
        content: response.content,
        usage: response.usage,
        metadata: response.metadata,
        responseTime: response.responseTimeMs,
      );
    } catch (e) {
      // 记录错误
      if (e is LLMRepositoryException) {
        await _repository.recordError(modelId, AIError(
          code: e.code ?? 'unknown',
          message: e.message,
          timestamp: DateTime.now(),
        ));
      }
      
      return GenerateContentResult.failure(
        error: e.toString(),
        errorType: _getErrorType(e),
      );
    }
  }
  
  /// 流式生成文本内容
  Stream<GenerateContentResult> generateTextStream({
    required String modelId,
    required String prompt,
    String? systemPrompt,
    double? temperature,
    int? maxTokens,
    List<String>? stop,
    Map<String, dynamic>? metadata,
  }) async* {
    try {
      // 验证模型可用性
      final isAvailable = await _repository.isModelAvailable(modelId);
      if (!isAvailable) {
        yield GenerateContentResult.failure(
          error: 'Model $modelId is not available',
          errorType: GenerateContentErrorType.modelUnavailable,
        );
        return;
      }
      
      // 构建请求
      final request = AIRequest(
        modelId: modelId,
        prompt: prompt,
        systemPrompt: systemPrompt,
        temperature: temperature ?? 0.7,
        maxTokens: maxTokens ?? 4096,
        stop: stop,
        metadata: metadata,
        requestId: _repository.generateRequestId(),
      );
      
      // 验证请求
      if (!_repository.validateRequest(request)) {
        yield GenerateContentResult.failure(
          error: 'Invalid request parameters',
          errorType: GenerateContentErrorType.validation,
        );
        return;
      }
      
      // 发送流式请求
      await for (final response in _repository.completionStream(request)) {
        yield GenerateContentResult.success(
          content: response.content,
          usage: response.usage,
          metadata: response.metadata,
          responseTime: response.responseTimeMs,
          isStreaming: true,
        );
      }
    } catch (e) {
      // 记录错误
      if (e is LLMRepositoryException) {
        await _repository.recordError(modelId, AIError(
          code: e.code ?? 'unknown',
          message: e.message,
          timestamp: DateTime.now(),
        ));
      }
      
      yield GenerateContentResult.failure(
        error: e.toString(),
        errorType: _getErrorType(e),
      );
    }
  }
  
  // ==================== 对话生成 ====================
  
  /// 生成对话回复
  Future<GenerateContentResult> generateChatReply({
    required String modelId,
    required List<AIMessage> messages,
    String? systemPrompt,
    double? temperature,
    int? maxTokens,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // 验证模型可用性
      final isAvailable = await _repository.isModelAvailable(modelId);
      if (!isAvailable) {
        throw ModelUnavailableException('Model $modelId is not available');
      }
      
      // 构建请求
      final request = AIRequest(
        modelId: modelId,
        prompt: '', // 对话模式不需要prompt
        messages: messages,
        systemPrompt: systemPrompt,
        temperature: temperature ?? 0.7,
        maxTokens: maxTokens ?? 4096,
        metadata: metadata,
        requestId: _repository.generateRequestId(),
      );
      
      // 发送聊天请求
      final response = await _repository.chat(request);
      
      // 记录使用统计
      await _repository.recordUsage(modelId, response.usage);
      
      return GenerateContentResult.success(
        content: response.content,
        usage: response.usage,
        metadata: response.metadata,
        responseTime: response.responseTimeMs,
      );
    } catch (e) {
      // 记录错误
      if (e is LLMRepositoryException) {
        await _repository.recordError(modelId, AIError(
          code: e.code ?? 'unknown',
          message: e.message,
          timestamp: DateTime.now(),
        ));
      }
      
      return GenerateContentResult.failure(
        error: e.toString(),
        errorType: _getErrorType(e),
      );
    }
  }
  
  /// 流式生成对话回复
  Stream<GenerateContentResult> generateChatReplyStream({
    required String modelId,
    required List<AIMessage> messages,
    String? systemPrompt,
    double? temperature,
    int? maxTokens,
    Map<String, dynamic>? metadata,
  }) async* {
    try {
      // 验证模型可用性
      final isAvailable = await _repository.isModelAvailable(modelId);
      if (!isAvailable) {
        yield GenerateContentResult.failure(
          error: 'Model $modelId is not available',
          errorType: GenerateContentErrorType.modelUnavailable,
        );
        return;
      }
      
      // 构建请求
      final request = AIRequest(
        modelId: modelId,
        prompt: '', // 对话模式不需要prompt
        messages: messages,
        systemPrompt: systemPrompt,
        temperature: temperature ?? 0.7,
        maxTokens: maxTokens ?? 4096,
        metadata: metadata,
        requestId: _repository.generateRequestId(),
      );
      
      // 发送流式聊天请求
      await for (final response in _repository.chatStream(request)) {
        yield GenerateContentResult.success(
          content: response.content,
          usage: response.usage,
          metadata: response.metadata,
          responseTime: response.responseTimeMs,
          isStreaming: true,
        );
      }
    } catch (e) {
      // 记录错误
      if (e is LLMRepositoryException) {
        await _repository.recordError(modelId, AIError(
          code: e.code ?? 'unknown',
          message: e.message,
          timestamp: DateTime.now(),
        ));
      }
      
      yield GenerateContentResult.failure(
        error: e.toString(),
        errorType: _getErrorType(e),
      );
    }
  }
  
  // ==================== 批量生成 ====================
  
  /// 批量生成内容
  Future<List<GenerateContentResult>> generateBatch({
    required String modelId,
    required List<String> prompts,
    String? systemPrompt,
    double? temperature,
    int? maxTokens,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // 验证模型可用性
      final isAvailable = await _repository.isModelAvailable(modelId);
      if (!isAvailable) {
        throw ModelUnavailableException('Model $modelId is not available');
      }
      
      // 构建请求列表
      final requests = prompts.map((prompt) => AIRequest(
        modelId: modelId,
        prompt: prompt,
        systemPrompt: systemPrompt,
        temperature: temperature ?? 0.7,
        maxTokens: maxTokens ?? 4096,
        metadata: metadata,
        requestId: _repository.generateRequestId(),
      )).toList();
      
      // 发送批量请求
      final responses = await _repository.batchProcess(requests);
      
      // 记录使用统计
      for (final response in responses) {
        await _repository.recordUsage(modelId, response.usage);
      }
      
      return responses.map((response) => GenerateContentResult.success(
        content: response.content,
        usage: response.usage,
        metadata: response.metadata,
        responseTime: response.responseTimeMs,
      )).toList();
    } catch (e) {
      // 记录错误
      if (e is LLMRepositoryException) {
        await _repository.recordError(modelId, AIError(
          code: e.code ?? 'unknown',
          message: e.message,
          timestamp: DateTime.now(),
        ));
      }
      
      // 返回失败结果列表
      return prompts.map((_) => GenerateContentResult.failure(
        error: e.toString(),
        errorType: _getErrorType(e),
      )).toList();
    }
  }
  
  // ==================== 工具方法 ====================
  
  /// 估算生成成本
  Future<double> estimateGenerationCost({
    required String modelId,
    required String prompt,
    int? maxTokens,
  }) async {
    final inputTokens = _repository.estimateTokens(prompt, modelId: modelId);
    final outputTokens = maxTokens ?? 1000;
    
    final usage = AIUsage(
      promptTokens: inputTokens,
      completionTokens: outputTokens,
      totalTokens: inputTokens + outputTokens,
    );
    
    return await _repository.calculateCost(modelId, usage);
  }
  
  /// 获取错误类型
  GenerateContentErrorType _getErrorType(dynamic error) {
    if (error is NetworkException) {
      return GenerateContentErrorType.network;
    } else if (error is AuthenticationException) {
      return GenerateContentErrorType.authentication;
    } else if (error is QuotaExceededException) {
      return GenerateContentErrorType.quotaExceeded;
    } else if (error is ModelUnavailableException) {
      return GenerateContentErrorType.modelUnavailable;
    } else if (error is ValidationException) {
      return GenerateContentErrorType.validation;
    } else if (error is TimeoutException) {
      return GenerateContentErrorType.timeout;
    } else {
      return GenerateContentErrorType.unknown;
    }
  }
}

/// 内容生成结果
class GenerateContentResult {
  final bool isSuccess;
  final String? content;
  final AIUsage? usage;
  final Map<String, dynamic>? metadata;
  final int? responseTime;
  final bool isStreaming;
  final String? error;
  final GenerateContentErrorType? errorType;
  
  const GenerateContentResult._({
    required this.isSuccess,
    this.content,
    this.usage,
    this.metadata,
    this.responseTime,
    this.isStreaming = false,
    this.error,
    this.errorType,
  });
  
  factory GenerateContentResult.success({
    required String content,
    required AIUsage usage,
    Map<String, dynamic>? metadata,
    int? responseTime,
    bool isStreaming = false,
  }) {
    return GenerateContentResult._(
      isSuccess: true,
      content: content,
      usage: usage,
      metadata: metadata,
      responseTime: responseTime,
      isStreaming: isStreaming,
    );
  }
  
  factory GenerateContentResult.failure({
    required String error,
    required GenerateContentErrorType errorType,
  }) {
    return GenerateContentResult._(
      isSuccess: false,
      error: error,
      errorType: errorType,
    );
  }
}

/// 内容生成错误类型
enum GenerateContentErrorType {
  network,
  authentication,
  quotaExceeded,
  modelUnavailable,
  validation,
  timeout,
  unknown,
}