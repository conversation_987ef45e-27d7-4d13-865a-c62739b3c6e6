import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../entities/chapter_version.dart';
import '../entities/version_diff.dart';
import '../entities/version_branch.dart';
import '../repositories/version_repository.dart';
import '../../data/repositories/version_repository_impl.dart';

/// 版本管理用例
/// 提供版本的创建、读取、更新、删除等业务操作
class ManageVersionsUseCase {
  final VersionRepository _repository;

  ManageVersionsUseCase(this._repository);

  /// 生成版本ID
  String _generateVersionId() {
    return 'version_${DateTime.now().millisecondsSinceEpoch}_${(1000 + (999 * (DateTime.now().microsecond / 1000000))).round()}';
  }

  /// 创建合并版本
  ChapterVersion _createMergedVersion({
    required String chapterId,
    required List<ChapterVersion> sourceVersions,
    required String mergedContent,
    required String title,
    required String createdBy,
    String? description,
    List<String>? tags,
  }) {
    if (sourceVersions.isEmpty) {
      throw ArgumentError('Source versions cannot be empty');
    }
    
    final now = DateTime.now();
    final maxVersionNumber = sourceVersions.map((v) => v.versionNumber).reduce((a, b) => a > b ? a : b);
    final newVersionNumber = maxVersionNumber + 1;
    
    return ChapterVersion(
      id: _generateVersionId(),
      chapterId: chapterId,
      versionNumber: newVersionNumber,
      title: title,
      content: mergedContent,
      createdAt: now,
      createdBy: createdBy,
      description: description ?? '合并版本',
      tags: tags ?? ['merged'],
      status: VersionStatus.draft,
      mergedFromVersionIds: sourceVersions.map((v) => v.id).toList(),
    ).calculateStats();
  }

  /// 构建版本分支
  VersionBranch _buildVersionBranch(
    ChapterVersion version,
    List<ChapterVersion> allVersions,
    Set<String> processedVersions,
  ) {
    if (processedVersions.contains(version.id)) {
      return VersionBranch(version: version, children: []);
    }

    processedVersions.add(version.id);

    // 找到子版本
    final childVersions = allVersions
        .where((v) => v.parentVersionId == version.id)
        .toList();

    final childBranches = childVersions
        .map((child) => _buildVersionBranch(child, allVersions, processedVersions))
        .toList();

    return VersionBranch(version: version, children: childBranches);
  }

  /// 获取章节的所有版本
  Future<List<ChapterVersion>> getChapterVersions(String chapterId) async {
    final versions = await _repository.getChapterVersions(chapterId);
    return versions..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  /// 根据ID获取版本
  Future<ChapterVersion?> getVersionById(String versionId) async {
    return await _repository.getVersionById(versionId);
  }

  /// 获取版本历史（按时间排序）
  Future<List<ChapterVersion>> getVersionHistory(String chapterId) async {
    final versions = await getChapterVersions(chapterId);
    return versions.where((v) => !v.isAutoSave).toList();
  }

  /// 获取最新版本
  Future<ChapterVersion?> getLatestVersion(String chapterId) async {
    return await _repository.getLatestVersion(chapterId);
  }

  /// 获取主版本
  Future<ChapterVersion?> getMainVersion(String chapterId) async {
    return await _repository.getMainVersion(chapterId);
  }

  /// 创建新版本
  Future<ChapterVersion> createVersion({
    required String chapterId,
    required String title,
    required String content,
    required String createdBy,
    String? description,
    List<String>? tags,
    VersionStatus status = VersionStatus.draft,
    String? parentVersionId,
  }) async {
    // 获取当前最新版本号
    final existingVersions = await _repository.getChapterVersions(chapterId);
    final maxVersionNumber = existingVersions.isEmpty 
        ? 0 
        : existingVersions.map((v) => v.versionNumber).reduce((a, b) => a > b ? a : b);

    final version = ChapterVersion(
      id: _generateVersionId(),
      chapterId: chapterId,
      versionNumber: maxVersionNumber + 1,
      title: title,
      content: content,
      createdAt: DateTime.now(),
      createdBy: createdBy,
      description: description ?? '',
      tags: tags ?? [],
      status: status,
      parentVersionId: parentVersionId,
    ).calculateStats();

    return await _repository.createVersion(version);
  }

  /// 创建基于现有版本的新版本
  Future<ChapterVersion> createVersionFromExisting({
    required String baseVersionId,
    required String newContent,
    String? newTitle,
    String? description,
    String? createdBy,
    List<String>? tags,
    VersionStatus? status,
  }) async {
    final baseVersion = await _repository.getVersionById(baseVersionId);
    if (baseVersion == null) {
      throw Exception('Base version not found: $baseVersionId');
    }

    final newVersion = baseVersion.createNewVersion(
      newContent: newContent,
      newTitle: newTitle,
      description: description,
      createdBy: createdBy,
      tags: tags,
      status: status,
    );

    return await _repository.createVersion(newVersion);
  }

  /// 创建分支版本
  Future<ChapterVersion> createBranch({
    required String baseVersionId,
    required String newContent,
    String? description,
    String? createdBy,
    List<String>? tags,
  }) async {
    final baseVersion = await _repository.getVersionById(baseVersionId);
    if (baseVersion == null) {
      throw Exception('Base version not found: $baseVersionId');
    }

    final branchVersion = baseVersion.createBranch(
      newContent: newContent,
      description: description,
      createdBy: createdBy,
      tags: tags,
    );

    return await _repository.createVersion(branchVersion);
  }

  /// 创建合并版本
  Future<ChapterVersion> createMergedVersion({
    required String chapterId,
    required List<String> sourceVersionIds,
    required String mergedContent,
    required String title,
    required String createdBy,
    String? description,
    List<String>? tags,
  }) async {
    final sourceVersions = <ChapterVersion>[];
    
    for (final versionId in sourceVersionIds) {
      final version = await _repository.getVersionById(versionId);
      if (version != null) {
        sourceVersions.add(version);
      }
    }

    if (sourceVersions.isEmpty) {
      throw Exception('No valid source versions found');
    }

    final mergedVersion = _createMergedVersion(
      chapterId: chapterId,
      sourceVersions: sourceVersions,
      mergedContent: mergedContent,
      title: title,
      createdBy: createdBy,
      description: description,
      tags: tags,
    );

    return await _repository.createVersion(mergedVersion);
  }

  /// 创建自动保存版本
  Future<ChapterVersion> createAutoSave({
    required String baseVersionId,
    required String content,
    String? createdBy,
  }) async {
    final baseVersion = await _repository.getVersionById(baseVersionId);
    if (baseVersion == null) {
      throw Exception('Base version not found: $baseVersionId');
    }

    final autoSaveVersion = baseVersion.createAutoSave(
      newContent: content,
      createdBy: createdBy,
    );

    final savedVersion = await _repository.createVersion(autoSaveVersion);

    // 清理旧的自动保存版本
    await _repository.cleanupAutoSaveVersions(baseVersion.chapterId);

    return savedVersion;
  }

  /// 更新版本状态
  Future<ChapterVersion> updateVersionStatus(
    String versionId,
    VersionStatus newStatus,
  ) async {
    final version = await _repository.getVersionById(versionId);
    if (version == null) {
      throw Exception('Version not found: $versionId');
    }

    final updatedVersion = version.copyWith(status: newStatus);
    return await _repository.updateVersion(updatedVersion);
  }

  /// 设置主版本
  Future<ChapterVersion> setMainVersion(String versionId) async {
    final version = await _repository.getVersionById(versionId);
    if (version == null) {
      throw Exception('Version not found: $versionId');
    }

    // 取消其他版本的主版本标记
    final allVersions = await _repository.getChapterVersions(version.chapterId);
    for (final v in allVersions) {
      if (v.isMainVersion && v.id != versionId) {
        await _repository.updateVersion(v.copyWith(isMainVersion: false));
      }
    }

    // 设置当前版本为主版本
    final updatedVersion = version.copyWith(isMainVersion: true);
    return await _repository.updateVersion(updatedVersion);
  }

  /// 删除版本
  Future<void> deleteVersion(String versionId) async {
    final version = await _repository.getVersionById(versionId);
    if (version == null) {
      throw Exception('Version not found: $versionId');
    }

    // 检查是否可以删除
    if (!version.status.canDelete) {
      throw Exception('Cannot delete version with status: ${version.status.displayName}');
    }

    // 检查是否为主版本
    if (version.isMainVersion) {
      throw Exception('Cannot delete main version. Please set another version as main first.');
    }

    await _repository.deleteVersion(versionId);
  }

  /// 恢复版本
  Future<ChapterVersion> restoreVersion(String versionId, String createdBy) async {
    final version = await _repository.getVersionById(versionId);
    if (version == null) {
      throw Exception('Version not found: $versionId');
    }

    // 创建基于此版本的新版本
    return await createVersionFromExisting(
      baseVersionId: versionId,
      newContent: version.content,
      newTitle: version.title,
      description: '恢复自版本 ${version.versionLabel}',
      createdBy: createdBy,
      tags: [...version.tags, 'restored'],
    );
  }

  /// 添加版本标签
  Future<ChapterVersion> addVersionTag(String versionId, String tag) async {
    final version = await _repository.getVersionById(versionId);
    if (version == null) {
      throw Exception('Version not found: $versionId');
    }

    if (version.tags.contains(tag)) {
      return version; // 标签已存在
    }

    final updatedTags = [...version.tags, tag];
    final updatedVersion = version.copyWith(tags: updatedTags);
    return await _repository.updateVersion(updatedVersion);
  }

  /// 移除版本标签
  Future<ChapterVersion> removeVersionTag(String versionId, String tag) async {
    final version = await _repository.getVersionById(versionId);
    if (version == null) {
      throw Exception('Version not found: $versionId');
    }

    final updatedTags = version.tags.where((t) => t != tag).toList();
    final updatedVersion = version.copyWith(tags: updatedTags);
    return await _repository.updateVersion(updatedVersion);
  }

  /// 更新版本描述
  Future<ChapterVersion> updateVersionDescription(String versionId, String description) async {
    final version = await _repository.getVersionById(versionId);
    if (version == null) {
      throw Exception('Version not found: $versionId');
    }

    final updatedVersion = version.copyWith(description: description);
    return await _repository.updateVersion(updatedVersion);
  }

  /// 根据标签获取版本
  Future<List<ChapterVersion>> getVersionsByTag(String chapterId, String tag) async {
    final versions = await _repository.getChapterVersions(chapterId);
    return versions.where((v) => v.tags.contains(tag)).toList();
  }

  /// 获取所有标签
  Future<List<String>> getAllTags(String chapterId) async {
    final versions = await _repository.getChapterVersions(chapterId);
    final allTags = <String>{};
    
    for (final version in versions) {
      allTags.addAll(version.tags);
    }
    
    return allTags.toList()..sort();
  }

  /// 回滚到指定版本
  Future<ChapterVersion> rollbackToVersion(String versionId, String createdBy) async {
    final version = await _repository.getVersionById(versionId);
    if (version == null) {
      throw Exception('Version not found: $versionId');
    }

    // 创建基于目标版本的新版本，作为回滚操作
    return await createVersionFromExisting(
      baseVersionId: versionId,
      newContent: version.content,
      newTitle: version.title,
      description: '回滚到版本 ${version.versionLabel} (${version.createdAt.toString().substring(0, 16)})',
      createdBy: createdBy,
      tags: [...version.tags, 'rollback'],
      status: VersionStatus.draft,
    );
  }

  /// 获取版本分支树
  Future<List<VersionBranch>> getVersionBranches(String chapterId) async {
    final versions = await _repository.getChapterVersions(chapterId);
    final branches = <VersionBranch>[];
    final processedVersions = <String>{};

    // 找到根版本（没有父版本的版本）
    final rootVersions = versions.where((v) => v.parentVersionId == null).toList();
    
    for (final rootVersion in rootVersions) {
      final branch = _buildVersionBranch(rootVersion, versions, processedVersions);
      branches.add(branch);
    }

    return branches;
  }

  /// 比较两个版本
  Future<VersionDiff> compareVersions(
    String sourceVersionId,
    String targetVersionId,
    {DiffType diffType = DiffType.lineDiff}
  ) async {
    final sourceVersion = await _repository.getVersionById(sourceVersionId);
    final targetVersion = await _repository.getVersionById(targetVersionId);

    if (sourceVersion == null) {
      throw Exception('Source version not found: $sourceVersionId');
    }
    if (targetVersion == null) {
      throw Exception('Target version not found: $targetVersionId');
    }

    final diff = DiffBuilder.calculateDiff(
      sourceVersionId: sourceVersionId,
      targetVersionId: targetVersionId,
      sourceText: sourceVersion.content,
      targetText: targetVersion.content,
      diffType: diffType,
    );

    return await _repository.saveVersionDiff(diff);
  }

  /// 获取版本差异
  Future<VersionDiff> getVersionDiff(
    String sourceVersionId,
    String targetVersionId,
  ) async {
    try {
      return await _repository.getVersionDiff(sourceVersionId, targetVersionId);
    } catch (e) {
      // 如果差异不存在，则计算并保存
      return await compareVersions(sourceVersionId, targetVersionId);
    }
  }

  /// 搜索版本
  Future<List<ChapterVersion>> searchVersions(String query) async {
    if (query.trim().isEmpty) {
      return [];
    }

    return await _repository.searchVersions(query);
  }

  /// 获取版本统计信息
  Future<VersionStatistics> getVersionStatistics(String chapterId) async {
    final versions = await _repository.getChapterVersions(chapterId);
    
    if (versions.isEmpty) {
      return const VersionStatistics(
        totalVersions: 0,
        totalWordCount: 0,
        statusCounts: {},
        creatorCounts: {},
        averageVersionSize: 0,
        oldestVersion: null,
        newestVersion: null,
      );
    }

    final statusCounts = <VersionStatus, int>{};
    final creatorCounts = <String, int>{};
    int totalWordCount = 0;
    int totalSize = 0;

    for (final version in versions) {
      statusCounts[version.status] = (statusCounts[version.status] ?? 0) + 1;
      creatorCounts[version.createdBy] = (creatorCounts[version.createdBy] ?? 0) + 1;
      totalWordCount += version.wordCount;
      totalSize += version.sizeInBytes;
    }

    versions.sort((a, b) => a.createdAt.compareTo(b.createdAt));

    return VersionStatistics(
      totalVersions: versions.length,
      totalWordCount: totalWordCount,
      statusCounts: statusCounts,
      creatorCounts: creatorCounts,
      averageVersionSize: totalSize / versions.length,
      oldestVersion: versions.first,
      newestVersion: versions.last,
    );
  }

  /// 清理版本历史
  Future<void> cleanupVersionHistory(
    String chapterId, {
    int keepRecentCount = 50,
    Duration keepRecentDuration = const Duration(days: 30),
    bool keepMainVersions = true,
    bool keepPublishedVersions = true,
  }) async {
    final versions = await _repository.getChapterVersions(chapterId);
    final now = DateTime.now();
    final cutoffDate = now.subtract(keepRecentDuration);

    final versionsToDelete = <ChapterVersion>[];

    for (final version in versions) {
      // 跳过主版本
      if (keepMainVersions && version.isMainVersion) continue;
      
      // 跳过已发布版本
      if (keepPublishedVersions && version.status == VersionStatus.published) continue;
      
      // 跳过最近的版本
      if (version.createdAt.isAfter(cutoffDate)) continue;
      
      versionsToDelete.add(version);
    }

    // 按创建时间排序，保留最新的指定数量
    versionsToDelete.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    if (versionsToDelete.length > keepRecentCount) {
      final toDelete = versionsToDelete.skip(keepRecentCount).toList();
      
      for (final version in toDelete) {
        await _repository.deleteVersion(version.id);
      }
    }
  }

  /// 验证版本完整性
  Future<List<String>> validateVersionIntegrity(String chapterId) async {
    final versions = await _repository.getChapterVersions(chapterId);
    final issues = <String>[];

    for (final version in versions) {
      // 检查校验和
      if (!version.validateIntegrity()) {
        issues.add('版本 ${version.versionLabel} 校验和不匹配');
      }

      // 检查父版本引用
      if (version.parentVersionId != null) {
        final parentExists = versions.any((v) => v.id == version.parentVersionId);
        if (!parentExists) {
          issues.add('版本 ${version.versionLabel} 的父版本不存在');
        }
      }

      // 检查合并版本引用
      for (final mergedId in version.mergedFromVersionIds) {
        final mergedExists = versions.any((v) => v.id == mergedId);
        if (!mergedExists) {
          issues.add('版本 ${version.versionLabel} 的合并源版本不存在: $mergedId');
        }
      }
    }

    return issues;
  }
}

/// 版本统计信息
class VersionStatistics {
  final int totalVersions;
  final int totalWordCount;
  final Map<VersionStatus, int> statusCounts;
  final Map<String, int> creatorCounts;
  final double averageVersionSize;
  final ChapterVersion? oldestVersion;
  final ChapterVersion? newestVersion;

  const VersionStatistics({
    required this.totalVersions,
    required this.totalWordCount,
    required this.statusCounts,
    required this.creatorCounts,
    required this.averageVersionSize,
    required this.oldestVersion,
    required this.newestVersion,
  });

  /// 获取平均字数
  int get averageWordCount => totalVersions > 0 ? totalWordCount ~/ totalVersions : 0;

  /// 获取版本跨度（天数）
  int get versionSpanDays {
    if (oldestVersion == null || newestVersion == null) return 0;
    return newestVersion!.createdAt.difference(oldestVersion!.createdAt).inDays;
  }

  /// 获取活跃创建者数量
  int get activeCreators => creatorCounts.length;

  /// 获取最活跃的创建者
  String? get mostActiveCreator {
    if (creatorCounts.isEmpty) return null;
    
    String? mostActive;
    int maxCount = 0;
    
    creatorCounts.forEach((creator, count) {
      if (count > maxCount) {
        maxCount = count;
        mostActive = creator;
      }
    });
    
    return mostActive;
  }
}

/// 版本管理用例提供者
final manageVersionsUseCaseProvider = Provider<ManageVersionsUseCase>((ref) {
  final repository = ref.read(versionRepositoryProvider);
  return ManageVersionsUseCase(repository);
});

// 版本仓库提供者已在 version_repository_impl.dart 中定义