import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'dart:io';

import 'package:bamboofall_app/main.dart' as app;

/// 跨平台兼容性端到端测试
/// 测试应用在不同平台上的表现和功能一致性
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Cross-Platform Compatibility E2E Tests', () {
    testWidgets('Platform-specific UI adaptations', (WidgetTester tester) async {
      // 启动应用
      app.main();
      await tester.pumpAndSettle();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // ==================== 平台检测 ====================
      
      final currentPlatform = Platform.operatingSystem;
      print('🖥️ 当前测试平台: $currentPlatform');

      // 验证平台特定的UI元素
      if (Platform.isWindows) {
        // Windows特定UI测试
        await _testWindowsSpecificUI(tester);
      } else if (Platform.isMacOS) {
        // macOS特定UI测试
        await _testMacOSSpecificUI(tester);
      } else if (Platform.isLinux) {
        // Linux特定UI测试
        await _testLinuxSpecificUI(tester);
      }

      // ==================== 通用功能测试 ====================
      
      // 创建项目（所有平台通用）
      await tester.tap(find.byKey(const Key('create_project_button')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('project_name_field')), '跨平台测试项目');
      await tester.tap(find.byKey(const Key('confirm_create_project')));
      await tester.pumpAndSettle();

      // 验证项目创建成功
      expect(find.text('跨平台测试项目'), findsOneWidget);

      // ==================== 文件系统操作测试 ====================
      
      // 测试文件保存
      await tester.tap(find.byKey(const Key('add_chapter_button')));
      await tester.pumpAndSettle();
      await tester.enterText(find.byKey(const Key('chapter_title_field')), '测试章节');
      await tester.tap(find.byKey(const Key('confirm_add_chapter')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('markdown_editor')), '这是跨平台测试内容。');
      await tester.tap(find.byKey(const Key('save_chapter_button')));
      await tester.pumpAndSettle();

      // 验证保存成功
      expect(find.text('保存成功'), findsOneWidget);

      // 测试文件导出
      await tester.tap(find.byKey(const Key('export_project_button')));
      await tester.pumpAndSettle();

      await tester.tap(find.byKey(const Key('export_format_markdown')));
      await tester.tap(find.byKey(const Key('confirm_export_button')));
      await tester.pumpAndSettle();

      // 验证导出成功
      expect(find.text('导出成功'), findsOneWidget);

      // ==================== 窗口管理测试 ====================
      
      if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
        // 测试窗口大小调整
        await _testWindowResizing(tester);
        
        // 测试全屏模式
        await _testFullscreenMode(tester);
        
        // 测试多窗口支持（如果支持）
        await _testMultiWindowSupport(tester);
      }

      print('✅ 平台特定UI适配测试通过！');
    });

    testWidgets('Keyboard shortcuts across platforms', (WidgetTester tester) async {
      // 启动应用
      app.main();
      await tester.pumpAndSettle();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // 创建测试项目
      await _createTestProject(tester, '快捷键测试项目');

      // ==================== 平台特定快捷键测试 ====================
      
      final isApplePlatform = Platform.isMacOS;
      final cmdOrCtrl = isApplePlatform ? 'Cmd' : 'Ctrl';

      // 测试新建项目快捷键
      await tester.sendKeyDownEvent(
        isApplePlatform ? LogicalKeyboardKey.meta : LogicalKeyboardKey.control
      );
      await tester.sendKeyDownEvent(LogicalKeyboardKey.keyN);
      await tester.sendKeyUpEvent(LogicalKeyboardKey.keyN);
      await tester.sendKeyUpEvent(
        isApplePlatform ? LogicalKeyboardKey.meta : LogicalKeyboardKey.control
      );
      await tester.pumpAndSettle();

      // 验证新建项目对话框打开
      expect(find.byKey(const Key('create_project_dialog')), findsOneWidget);

      // 取消对话框
      await tester.sendKeyDownEvent(LogicalKeyboardKey.escape);
      await tester.sendKeyUpEvent(LogicalKeyboardKey.escape);
      await tester.pumpAndSettle();

      // 创建章节并测试编辑快捷键
      await tester.tap(find.byKey(const Key('add_chapter_button')));
      await tester.pumpAndSettle();
      await tester.enterText(find.byKey(const Key('chapter_title_field')), '快捷键测试章节');
      await tester.tap(find.byKey(const Key('confirm_add_chapter')));
      await tester.pumpAndSettle();

      // 输入测试文本
      await tester.enterText(find.byKey(const Key('markdown_editor')), '这是测试文本，用于验证快捷键功能。');
      await tester.pumpAndSettle();

      // 测试全选快捷键 (Ctrl/Cmd + A)
      await tester.sendKeyDownEvent(
        isApplePlatform ? LogicalKeyboardKey.meta : LogicalKeyboardKey.control
      );
      await tester.sendKeyDownEvent(LogicalKeyboardKey.keyA);
      await tester.sendKeyUpEvent(LogicalKeyboardKey.keyA);
      await tester.sendKeyUpEvent(
        isApplePlatform ? LogicalKeyboardKey.meta : LogicalKeyboardKey.control
      );
      await tester.pumpAndSettle();

      // 测试复制快捷键 (Ctrl/Cmd + C)
      await tester.sendKeyDownEvent(
        isApplePlatform ? LogicalKeyboardKey.meta : LogicalKeyboardKey.control
      );
      await tester.sendKeyDownEvent(LogicalKeyboardKey.keyC);
      await tester.sendKeyUpEvent(LogicalKeyboardKey.keyC);
      await tester.sendKeyUpEvent(
        isApplePlatform ? LogicalKeyboardKey.meta : LogicalKeyboardKey.control
      );
      await tester.pumpAndSettle();

      // 测试粘贴快捷键 (Ctrl/Cmd + V)
      await tester.sendKeyDownEvent(
        isApplePlatform ? LogicalKeyboardKey.meta : LogicalKeyboardKey.control
      );
      await tester.sendKeyDownEvent(LogicalKeyboardKey.keyV);
      await tester.sendKeyUpEvent(LogicalKeyboardKey.keyV);
      await tester.sendKeyUpEvent(
        isApplePlatform ? LogicalKeyboardKey.meta : LogicalKeyboardKey.control
      );
      await tester.pumpAndSettle();

      // 测试保存快捷键 (Ctrl/Cmd + S)
      await tester.sendKeyDownEvent(
        isApplePlatform ? LogicalKeyboardKey.meta : LogicalKeyboardKey.control
      );
      await tester.sendKeyDownEvent(LogicalKeyboardKey.keyS);
      await tester.sendKeyUpEvent(LogicalKeyboardKey.keyS);
      await tester.sendKeyUpEvent(
        isApplePlatform ? LogicalKeyboardKey.meta : LogicalKeyboardKey.control
      );
      await tester.pumpAndSettle();

      // 验证保存成功
      expect(find.text('保存成功'), findsOneWidget);

      // 测试撤销快捷键 (Ctrl/Cmd + Z)
      await tester.sendKeyDownEvent(
        isApplePlatform ? LogicalKeyboardKey.meta : LogicalKeyboardKey.control
      );
      await tester.sendKeyDownEvent(LogicalKeyboardKey.keyZ);
      await tester.sendKeyUpEvent(LogicalKeyboardKey.keyZ);
      await tester.sendKeyUpEvent(
        isApplePlatform ? LogicalKeyboardKey.meta : LogicalKeyboardKey.control
      );
      await tester.pumpAndSettle();

      // 测试重做快捷键 (Ctrl/Cmd + Y 或 Ctrl/Cmd + Shift + Z)
      await tester.sendKeyDownEvent(
        isApplePlatform ? LogicalKeyboardKey.meta : LogicalKeyboardKey.control
      );
      if (isApplePlatform) {
        await tester.sendKeyDownEvent(LogicalKeyboardKey.shift);
        await tester.sendKeyDownEvent(LogicalKeyboardKey.keyZ);
        await tester.sendKeyUpEvent(LogicalKeyboardKey.keyZ);
        await tester.sendKeyUpEvent(LogicalKeyboardKey.shift);
      } else {
        await tester.sendKeyDownEvent(LogicalKeyboardKey.keyY);
        await tester.sendKeyUpEvent(LogicalKeyboardKey.keyY);
      }
      await tester.sendKeyUpEvent(
        isApplePlatform ? LogicalKeyboardKey.meta : LogicalKeyboardKey.control
      );
      await tester.pumpAndSettle();

      print('✅ 跨平台快捷键测试通过！');
    });

    testWidgets('File system and path handling', (WidgetTester tester) async {
      // 启动应用
      app.main();
      await tester.pumpAndSettle();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // 创建测试项目
      await _createTestProject(tester, '文件系统测试项目');

      // ==================== 路径处理测试 ====================
      
      // 测试项目文件夹创建
      await tester.tap(find.byKey(const Key('project_settings_button')));
      await tester.pumpAndSettle();

      // 验证项目路径显示正确
      expect(find.byKey(const Key('project_path_display')), findsOneWidget);

      // 获取项目路径并验证平台特定格式
      final projectPathFinder = find.byKey(const Key('project_path_text'));
      if (tester.any(projectPathFinder)) {
        final projectPathWidget = tester.widget<Text>(projectPathFinder);
        final projectPath = projectPathWidget.data ?? '';
        
        if (Platform.isWindows) {
          expect(projectPath.contains('\\'), isTrue, reason: 'Windows路径应使用反斜杠');
          expect(projectPath.contains(':'), isTrue, reason: 'Windows路径应包含盘符');
        } else {
          expect(projectPath.startsWith('/'), isTrue, reason: 'Unix路径应以斜杠开头');
        }
      }

      // ==================== 文件操作测试 ====================
      
      // 创建章节文件
      await tester.tap(find.byKey(const Key('add_chapter_button')));
      await tester.pumpAndSettle();
      await tester.enterText(find.byKey(const Key('chapter_title_field')), '文件测试章节');
      await tester.tap(find.byKey(const Key('confirm_add_chapter')));
      await tester.pumpAndSettle();

      // 添加内容
      const testContent = '''
# 文件系统测试

这是一个测试文件，用于验证跨平台文件操作。

## 特殊字符测试
- 中文：你好世界
- 符号：@#\$%^&*()
- 路径：/path/to/file
- Windows路径：C:\\\\Users\\\\<USER>