import 'dart:math' as math;

import 'package:freezed_annotation/freezed_annotation.dart';

part 'chapter.freezed.dart';
part 'chapter.g.dart';

/// 章节实体
/// 表示小说中的一个章节，支持层级结构
@freezed
class Chapter with _$Chapter {
  const factory Chapter({
    /// 章节唯一标识符
    required String id,
    
    /// 章节标题
    required String title,
    
    /// 章节内容（Markdown格式）
    @Default('') String content,
    
    /// 父章节ID（null表示根章节）
    String? parentId,
    
    /// 章节层级（0为根章节）
    @Default(0) int level,
    
    /// 章节在同级中的排序
    @Default(0) int order,
    
    /// 章节状态
    @Default(ChapterStatus.draft) ChapterStatus status,
    
    /// 字数统计
    @Default(0) int wordCount,
    
    /// 创建时间
    required DateTime createdAt,
    
    /// 最后修改时间
    required DateTime updatedAt,
    
    /// 章节标签
    @Default([]) List<String> tags,
    
    /// 章节备注
    @Default('') String notes,
    
    /// 是否展开（用于UI显示）
    @Default(true) bool isExpanded,
    
    /// 子章节列表
    @Default([]) List<Chapter> children,
  }) = _Chapter;

  factory Chapter.fromJson(Map<String, dynamic> json) => _$ChapterFromJson(json);
}

/// 章节状态枚举
enum ChapterStatus {
  /// 草稿
  draft,
  
  /// 进行中
  inProgress,
  
  /// 已完成
  completed,
  
  /// 已发布
  published,
  
  /// 已归档
  archived,
}

/// 章节状态扩展
extension ChapterStatusExtension on ChapterStatus {
  /// 获取状态显示名称
  String get displayName {
    switch (this) {
      case ChapterStatus.draft:
        return '草稿';
      case ChapterStatus.inProgress:
        return '进行中';
      case ChapterStatus.completed:
        return '已完成';
      case ChapterStatus.published:
        return '已发布';
      case ChapterStatus.archived:
        return '已归档';
    }
  }
  
  /// 获取状态颜色
  String get colorHex {
    switch (this) {
      case ChapterStatus.draft:
        return '#9CA3AF'; // 灰色
      case ChapterStatus.inProgress:
        return '#3B82F6'; // 蓝色
      case ChapterStatus.completed:
        return '#10B981'; // 绿色
      case ChapterStatus.published:
        return '#8B5CF6'; // 紫色
      case ChapterStatus.archived:
        return '#6B7280'; // 深灰色
    }
  }
}

/// 章节实体扩展方法
extension ChapterExtension on Chapter {
  /// 是否为根章节
  bool get isRoot => parentId == null;
  
  /// 是否有子章节
  bool get hasChildren => children.isNotEmpty;
  
  /// 获取所有后代章节
  List<Chapter> get allDescendants {
    final descendants = <Chapter>[];
    
    void collectDescendants(Chapter chapter) {
      for (final child in chapter.children) {
        descendants.add(child);
        collectDescendants(child);
      }
    }
    
    collectDescendants(this);
    return descendants;
  }
  
  /// 获取章节路径（从根到当前章节的标题路径）
  List<String> getPath(List<Chapter> allChapters) {
    final path = <String>[];
    Chapter? current = this;
    
    while (current != null) {
      path.insert(0, current.title);
      if (current.parentId != null) {
        current = allChapters.firstWhere(
          (c) => c.id == current!.parentId,
          orElse: () => throw Exception('Parent chapter not found: ${current!.parentId}'),
        );
      } else {
        current = null;
      }
    }
    
    return path;
  }
  
  /// 计算总字数（包括子章节）
  int getTotalWordCount() {
    int total = wordCount;
    for (final child in children) {
      total += child.getTotalWordCount();
    }
    return total;
  }
  
  /// 获取章节深度
  int getDepth() {
    int maxDepth = 0;
    for (final child in children) {
      maxDepth = math.max(maxDepth, child.getDepth() + 1);
    }
    return maxDepth;
  }
  
  /// 查找子章节
  Chapter? findChild(String childId) {
    for (final child in children) {
      if (child.id == childId) {
        return child;
      }
      final found = child.findChild(childId);
      if (found != null) {
        return found;
      }
    }
    return null;
  }
  
  /// 添加子章节
  Chapter addChild(Chapter child) {
    return copyWith(
      children: [...children, child.copyWith(parentId: id, level: level + 1)],
      updatedAt: DateTime.now(),
    );
  }
  
  /// 移除子章节
  Chapter removeChild(String childId) {
    return copyWith(
      children: children.where((c) => c.id != childId).toList(),
      updatedAt: DateTime.now(),
    );
  }
  
  /// 更新子章节
  Chapter updateChild(Chapter updatedChild) {
    final updatedChildren = children.map((child) {
      if (child.id == updatedChild.id) {
        return updatedChild;
      } else if (child.hasChildren) {
        return child.updateChild(updatedChild);
      }
      return child;
    }).toList();
    
    return copyWith(
      children: updatedChildren,
      updatedAt: DateTime.now(),
    );
  }
  
  /// 重新排序子章节
  Chapter reorderChildren(List<String> newOrder) {
    final reorderedChildren = <Chapter>[];
    
    for (int i = 0; i < newOrder.length; i++) {
      final childId = newOrder[i];
      final child = children.firstWhere((c) => c.id == childId);
      reorderedChildren.add(child.copyWith(order: i));
    }
    
    return copyWith(
      children: reorderedChildren,
      updatedAt: DateTime.now(),
    );
  }
}