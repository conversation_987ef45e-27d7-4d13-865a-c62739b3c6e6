import '../entities/template.dart';
import '../repositories/template_repository.dart';

/// 管理模板用例
class ManageTemplatesUseCase {
  final TemplateRepository _repository;

  ManageTemplatesUseCase(this._repository);

  /// 获取所有模板
  Future<List<PromptTemplate>> getAllTemplates() async {
    return await _repository.getAllTemplates();
  }

  /// 根据分类获取模板
  Future<List<PromptTemplate>> getTemplatesByCategory(TemplateCategory category) async {
    return await _repository.getTemplatesByCategory(category);
  }

  /// 搜索模板
  Future<List<PromptTemplate>> searchTemplates(String query) async {
    if (query.trim().isEmpty) {
      return await getAllTemplates();
    }
    return await _repository.searchTemplates(query);
  }

  /// 创建新模板
  Future<void> createTemplate({
    required String name,
    required String description,
    required TemplateCategory category,
    required String content,
    required List<TemplateVariable> variables,
    List<String> tags = const [],
  }) async {
    final template = PromptTemplate(
      id: _generateUniqueId(),
      name: name.trim(),
      description: description.trim(),
      category: category,
      content: content.trim(),
      variables: variables,
      version: '1.0.0',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      tags: tags,
    );

    // 验证模板
    final isValid = await _repository.validateTemplate(template);
    if (!isValid) {
      throw Exception('模板验证失败：请检查模板内容和变量设置');
    }

    await _repository.createTemplate(template);
  }

  /// 更新模板
  Future<void> updateTemplate({
    required String id,
    String? name,
    String? description,
    TemplateCategory? category,
    String? content,
    List<TemplateVariable>? variables,
    List<String>? tags,
  }) async {
    final existingTemplate = await _repository.getTemplateById(id);
    if (existingTemplate == null) {
      throw Exception('模板不存在');
    }

    if (existingTemplate.isBuiltIn) {
      throw Exception('无法修改内置模板');
    }

    final updatedTemplate = existingTemplate.copyWith(
      name: name?.trim(),
      description: description?.trim(),
      category: category,
      content: content?.trim(),
      variables: variables,
      tags: tags,
      updatedAt: DateTime.now(),
    );

    // 验证更新后的模板
    final isValid = await _repository.validateTemplate(updatedTemplate);
    if (!isValid) {
      throw Exception('模板验证失败：请检查模板内容和变量设置');
    }

    await _repository.updateTemplate(updatedTemplate);
  }

  /// 删除模板
  Future<void> deleteTemplate(String id) async {
    final template = await _repository.getTemplateById(id);
    if (template == null) {
      throw Exception('模板不存在');
    }

    if (template.isBuiltIn) {
      throw Exception('无法删除内置模板');
    }

    await _repository.deleteTemplate(id);
  }

  /// 复制模板
  Future<PromptTemplate> duplicateTemplate(String id, String newName) async {
    if (newName.trim().isEmpty) {
      throw Exception('模板名称不能为空');
    }

    return await _repository.duplicateTemplate(id, newName.trim());
  }

  /// 使用模板生成内容
  Future<String> useTemplate(String id, Map<String, dynamic> variableValues) async {
    final template = await _repository.getTemplateById(id);
    if (template == null) {
      throw Exception('模板不存在');
    }

    // 验证必需变量
    for (final variable in template.variables) {
      if (variable.isRequired && !variableValues.containsKey(variable.name)) {
        throw Exception('缺少必需变量：${variable.displayName}');
      }
    }

    // 增加使用次数
    await _repository.incrementUsageCount(id);

    // 渲染模板
    return template.render(variableValues);
  }

  /// 获取模板推荐
  Future<List<PromptTemplate>> getRecommendedTemplates({
    TemplateCategory? category,
    List<String>? tags,
    int limit = 5,
  }) async {
    List<PromptTemplate> candidates;

    if (category != null) {
      candidates = await _repository.getTemplatesByCategory(category);
    } else if (tags != null && tags.isNotEmpty) {
      candidates = await _repository.getTemplatesByTags(tags);
    } else {
      candidates = await _repository.getMostUsedTemplates(limit: limit * 2);
    }

    // 按使用次数和创建时间排序
    candidates.sort((a, b) {
      final usageComparison = b.usageCount.compareTo(a.usageCount);
      if (usageComparison != 0) return usageComparison;
      return b.createdAt.compareTo(a.createdAt);
    });

    return candidates.take(limit).toList();
  }

  /// 获取模板统计
  Future<TemplateStatistics> getTemplateStatistics() async {
    return await _repository.getTemplateStatistics();
  }

  /// 导出模板
  Future<String> exportTemplate(String id) async {
    return await _repository.exportTemplate(id);
  }

  /// 导入模板
  Future<PromptTemplate> importTemplate(String templateData) async {
    return await _repository.importTemplate(templateData);
  }

  /// 批量导入模板
  Future<List<PromptTemplate>> importTemplates(List<String> templatesData) async {
    return await _repository.importTemplates(templatesData);
  }

  /// 清理未使用的模板
  Future<int> cleanupUnusedTemplates({int daysThreshold = 30}) async {
    return await _repository.cleanupUnusedTemplates(daysThreshold: daysThreshold);
  }

  /// 生成唯一ID
  String _generateUniqueId() {
    return 'template_${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecond}';
  }
}

/// 模板验证用例
class ValidateTemplateUseCase {
  ValidateTemplateUseCase();

  /// 验证模板内容
  Future<TemplateValidationResult> validateTemplate(PromptTemplate template) async {
    final errors = <String>[];
    final warnings = <String>[];

    // 基本验证
    if (template.name.trim().isEmpty) {
      errors.add('模板名称不能为空');
    }

    if (template.content.trim().isEmpty) {
      errors.add('模板内容不能为空');
    }

    // 变量验证
    final variableNames = <String>{};
    for (final variable in template.variables) {
      if (variable.name.trim().isEmpty) {
        errors.add('变量名称不能为空');
      }

      if (variableNames.contains(variable.name)) {
        errors.add('变量名称重复：${variable.name}');
      }
      variableNames.add(variable.name);

      if (variable.displayName.trim().isEmpty) {
        warnings.add('变量 ${variable.name} 缺少显示名称');
      }
    }

    // 检查模板内容中的变量占位符
    final usedVariables = template.getUsedVariables();
    final definedVariables = template.variables.map((v) => v.name).toSet();

    for (final usedVar in usedVariables) {
      if (!definedVariables.contains(usedVar)) {
        errors.add('模板中使用了未定义的变量：$usedVar');
      }
    }

    for (final variable in template.variables) {
      if (variable.isRequired && !usedVariables.contains(variable.name)) {
        warnings.add('必需变量 ${variable.name} 在模板中未使用');
      }
    }

    // 内容长度检查
    if (template.content.length > 10000) {
      warnings.add('模板内容过长，可能影响性能');
    }

    return TemplateValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// 验证变量值
  TemplateVariableValidationResult validateVariableValues(
    PromptTemplate template,
    Map<String, dynamic> values,
  ) {
    final errors = <String, String>{};
    final warnings = <String, String>{};

    for (final variable in template.variables) {
      final value = values[variable.name];

      // 检查必需变量
      if (variable.isRequired && (value == null || value.toString().trim().isEmpty)) {
        errors[variable.name] = '${variable.displayName} 是必需的';
        continue;
      }

      if (value == null) continue;

      final stringValue = value.toString();

      // 长度验证
      if (variable.minLength != null && stringValue.length < variable.minLength!) {
        errors[variable.name] = '${variable.displayName} 长度不能少于 ${variable.minLength} 个字符';
      }

      if (variable.maxLength != null && stringValue.length > variable.maxLength!) {
        errors[variable.name] = '${variable.displayName} 长度不能超过 ${variable.maxLength} 个字符';
      }

      // 类型验证
      switch (variable.type) {
        case VariableType.number:
          if (double.tryParse(stringValue) == null) {
            errors[variable.name] = '${variable.displayName} 必须是数字';
          }
          break;
        case VariableType.select:
          if (variable.options != null && !variable.options!.contains(stringValue)) {
            errors[variable.name] = '${variable.displayName} 的值不在可选范围内';
          }
          break;
        case VariableType.boolean:
          if (stringValue.toLowerCase() != 'true' && stringValue.toLowerCase() != 'false') {
            errors[variable.name] = '${variable.displayName} 必须是 true 或 false';
          }
          break;
        default:
          break;
      }

      // 警告检查
      if (variable.type == VariableType.longText && stringValue.length < 10) {
        warnings[variable.name] = '${variable.displayName} 内容较短，可能需要更多详细信息';
      }
    }

    return TemplateVariableValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }
}

/// 模板验证结果
class TemplateValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const TemplateValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });
}

/// 模板变量验证结果
class TemplateVariableValidationResult {
  final bool isValid;
  final Map<String, String> errors;
  final Map<String, String> warnings;

  const TemplateVariableValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });
}