import '../entities/story_bible.dart';
import '../entities/character.dart';
import '../entities/location.dart';
import '../entities/world_settings.dart';

/// 圣经系统仓库接口
/// 定义圣经数据访问的统一接口
abstract class BibleRepository {
  // ==================== StoryBible 操作 ====================
  
  /// 保存故事圣经
  Future<void> saveStoryBible(StoryBible storyBible);
  
  /// 获取故事圣经
  Future<StoryBible?> getStoryBible(String id);
  
  /// 根据项目ID获取故事圣经
  Future<StoryBible?> getStoryBibleByProjectId(String projectId);
  
  /// 更新故事圣经
  Future<void> updateStoryBible(StoryBible storyBible);
  
  /// 删除故事圣经
  Future<void> deleteStoryBible(String id);
  
  /// 获取所有故事圣经
  Future<List<StoryBible>> getAllStoryBibles();
  
  // ==================== Character 操作 ====================
  
  /// 保存角色
  Future<void> saveCharacter(Character character);
  
  /// 获取角色
  Future<Character?> getCharacter(String id);
  
  /// 更新角色
  Future<void> updateCharacter(Character character);
  
  /// 删除角色
  Future<void> deleteCharacter(String id);
  
  /// 根据类型获取角色
  Future<List<Character>> getCharactersByType(CharacterType type);
  
  /// 根据状态获取角色
  Future<List<Character>> getCharactersByStatus(CharacterStatus status);
  
  /// 搜索角色
  Future<List<Character>> searchCharacters(String query);
  
  // ==================== Location 操作 ====================
  
  /// 保存地点
  Future<void> saveLocation(Location location);
  
  /// 获取地点
  Future<Location?> getLocation(String id);
  
  /// 更新地点
  Future<void> updateLocation(Location location);
  
  /// 删除地点
  Future<void> deleteLocation(String id);
  
  /// 根据类型获取地点
  Future<List<Location>> getLocationsByType(LocationType type);
  
  /// 根据父级地点获取子地点
  Future<List<Location>> getChildLocations(String parentId);
  
  // ==================== WorldSettings 操作 ====================
  
  /// 保存世界设定
  Future<void> saveWorldSettings(WorldSettings worldSettings);
  
  /// 获取世界设定
  Future<WorldSettings?> getWorldSettings(String id);
  
  /// 更新世界设定
  Future<void> updateWorldSettings(WorldSettings worldSettings);
  
  /// 删除世界设定
  Future<void> deleteWorldSettings(String id);
  
  // ==================== 数据验证和完整性 ====================
  
  /// 验证数据完整性
  Future<List<String>> validateDataIntegrity();
  
  /// 验证角色约束
  Future<bool> validateCharacterConstraints(Character character);
  
  /// 验证地点约束
  Future<bool> validateLocationConstraints(Location location);
  
  // ==================== 统计和分析 ====================
  
  /// 获取统计信息
  Future<Map<String, dynamic>> getStatistics();
  
  /// 分析角色关系
  Future<Map<String, dynamic>> analyzeCharacterRelationships();
  
  /// 分析地点层级
  Future<Map<String, dynamic>> analyzeLocationHierarchy();
  
  // ==================== 数据导入导出 ====================
  
  /// 导出数据
  Future<Map<String, dynamic>> exportData();
  
  /// 导入数据
  Future<void> importData(Map<String, dynamic> data);
  
  // ==================== 清理和维护 ====================
  
  /// 清理数据库
  Future<void> cleanup();
}

/// 圣经仓库异常
class BibleRepositoryException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;
  
  const BibleRepositoryException(
    this.message, {
    this.code,
    this.originalError,
  });
  
  @override
  String toString() {
    if (code != null) {
      return 'BibleRepositoryException($code): $message';
    }
    return 'BibleRepositoryException: $message';
  }
}

/// 数据验证异常
class DataValidationException extends BibleRepositoryException {
  const DataValidationException(super.message, {super.code, super.originalError});
}

/// 数据完整性异常
class DataIntegrityException extends BibleRepositoryException {
  const DataIntegrityException(super.message, {super.code, super.originalError});
}

/// 约束违反异常
class ConstraintViolationException extends BibleRepositoryException {
  const ConstraintViolationException(super.message, {super.code, super.originalError});
}