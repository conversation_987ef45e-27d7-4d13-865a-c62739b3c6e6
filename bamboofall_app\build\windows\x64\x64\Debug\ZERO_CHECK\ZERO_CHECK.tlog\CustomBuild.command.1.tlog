^E:\PROJECT\BAMBOOFALL\BAMBOOFALL_APP\BUILD\WINDOWS\X64\CMAKEFILES\2675E1151F6346BED42A16876FA1D17A\GENERATE.STAMP.RULE
setlocal
"D:\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/project/bamboofall/bamboofall_app/windows -BE:/project/bamboofall/bamboofall_app/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/project/bamboofall/bamboofall_app/build/windows/x64/bamboofall_app.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
