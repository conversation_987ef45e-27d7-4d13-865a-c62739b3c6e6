import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

import '../widgets/three_column_layout.dart';
import '../widgets/outline_panel.dart';
import '../widgets/editor_panel.dart';
import '../widgets/ai_assistant_panel.dart';

/// 创作工作台页面
/// 提供三栏布局的创作环境：大纲、编辑器、AI助手
class WritingWorkspace extends ConsumerStatefulWidget {
  const WritingWorkspace({super.key});

  @override
  ConsumerState<WritingWorkspace> createState() => _WritingWorkspaceState();
}

class _WritingWorkspaceState extends ConsumerState<WritingWorkspace> {
  @override
  Widget build(BuildContext context) {
    return fluent.ScaffoldPage(
      header: _buildHeader(),
      content: const ThreeColumnLayout(
        leftPanel: OutlinePanel(),
        centerPanel: EditorPanel(),
        rightPanel: AiAssistantPanel(),
      ),
    );
  }

  Widget _buildHeader() {
    return fluent.PageHeader(
      title: const Text('创作工作台'),
      commandBar: fluent.CommandBar(
        primaryItems: [
          fluent.CommandBarButton(
            icon: const fluent.Icon(fluent.FluentIcons.save),
            label: const Text('保存'),
            onPressed: () {
              // TODO: 实现保存功能
            },
          ),
          fluent.CommandBarButton(
            icon: const fluent.Icon(fluent.FluentIcons.undo),
            label: const Text('撤销'),
            onPressed: () {
              // TODO: 实现撤销功能
            },
          ),
          fluent.CommandBarButton(
            icon: const fluent.Icon(fluent.FluentIcons.redo),
            label: const Text('重做'),
            onPressed: () {
              // TODO: 实现重做功能
            },
          ),
        ],
        secondaryItems: [
          fluent.CommandBarButton(
            icon: const fluent.Icon(fluent.FluentIcons.settings),
            label: const Text('设置'),
            onPressed: () {
              // TODO: 打开设置页面
            },
          ),
        ],
      ),
    );
  }
}