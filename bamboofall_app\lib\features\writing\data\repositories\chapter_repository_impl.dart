import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/entities/chapter.dart';
import '../../domain/repositories/chapter_repository.dart';

/// 章节仓库实现（内存存储）
/// 提供章节数据的内存存储和访问
class ChapterRepositoryImpl implements ChapterRepository {
  final List<Chapter> _chapters = [];

  @override
  Future<List<Chapter>> getAllChapters() async {
    return List.from(_chapters);
  }

  @override
  Future<Chapter?> getChapterById(String id) async {
    try {
      return _chapters.firstWhere((chapter) => chapter.id == id);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<List<Chapter>> getChildChapters(String parentId) async {
    return _chapters
        .where((chapter) => chapter.parentId == parentId)
        .toList()
      ..sort((a, b) => a.order.compareTo(b.order));
  }

  @override
  Future<Chapter> createChapter(Chapter chapter) async {
    _chapters.add(chapter);
    return chapter;
  }

  @override
  Future<Chapter> updateChapter(Chapter chapter) async {
    final index = _chapters.indexWhere((c) => c.id == chapter.id);
    if (index != -1) {
      _chapters[index] = chapter;
      return chapter;
    } else {
      throw Exception('Chapter not found: ${chapter.id}');
    }
  }

  @override
  Future<void> deleteChapter(String id) async {
    _chapters.removeWhere((chapter) => chapter.id == id);
  }

  @override
  Future<void> updateChapters(List<Chapter> chapters) async {
    for (final chapter in chapters) {
      await updateChapter(chapter);
    }
  }

  @override
  Future<List<Chapter>> getChaptersByStatus(ChapterStatus status) async {
    return _chapters.where((chapter) => chapter.status == status).toList();
  }

  @override
  Future<List<Chapter>> getChaptersByTag(String tag) async {
    return _chapters
        .where((chapter) => chapter.tags.contains(tag))
        .toList();
  }

  @override
  Future<List<Chapter>> searchChapters(String query) async {
    if (query.trim().isEmpty) {
      return [];
    }

    final lowercaseQuery = query.toLowerCase();
    return _chapters.where((chapter) {
      return chapter.title.toLowerCase().contains(lowercaseQuery) ||
             chapter.content.toLowerCase().contains(lowercaseQuery) ||
             chapter.notes.toLowerCase().contains(lowercaseQuery) ||
             chapter.tags.any((tag) => tag.toLowerCase().contains(lowercaseQuery));
    }).toList();
  }

  @override
  Future<int> getChapterCount() async {
    return _chapters.length;
  }

  @override
  Future<void> clearAllChapters() async {
    _chapters.clear();
  }

  /// 初始化示例数据
  Future<void> initializeSampleData() async {
    if (_chapters.isNotEmpty) return;

    final now = DateTime.now();
    
    // 创建示例章节
    final chapters = [
      Chapter(
        id: 'chapter_1',
        title: '第一章：开始',
        content: '这是第一章的内容...',
        level: 0,
        order: 0,
        status: ChapterStatus.completed,
        wordCount: 1500,
        createdAt: now.subtract(const Duration(days: 7)),
        updatedAt: now.subtract(const Duration(days: 1)),
        tags: ['开头', '介绍'],
      ),
      Chapter(
        id: 'chapter_1_1',
        title: '1.1 序幕',
        content: '序幕的内容...',
        parentId: 'chapter_1',
        level: 1,
        order: 0,
        status: ChapterStatus.completed,
        wordCount: 800,
        createdAt: now.subtract(const Duration(days: 6)),
        updatedAt: now.subtract(const Duration(days: 1)),
      ),
      Chapter(
        id: 'chapter_1_2',
        title: '1.2 初遇',
        content: '初遇的内容...',
        parentId: 'chapter_1',
        level: 1,
        order: 1,
        status: ChapterStatus.completed,
        wordCount: 700,
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(days: 1)),
      ),
      Chapter(
        id: 'chapter_2',
        title: '第二章：发展',
        content: '这是第二章的内容...',
        level: 0,
        order: 1,
        status: ChapterStatus.inProgress,
        wordCount: 2000,
        createdAt: now.subtract(const Duration(days: 4)),
        updatedAt: now.subtract(const Duration(hours: 2)),
        tags: ['发展', '冲突'],
      ),
      Chapter(
        id: 'chapter_2_1',
        title: '2.1 深入',
        content: '深入的内容...',
        parentId: 'chapter_2',
        level: 1,
        order: 0,
        status: ChapterStatus.inProgress,
        wordCount: 1200,
        createdAt: now.subtract(const Duration(days: 3)),
        updatedAt: now.subtract(const Duration(hours: 1)),
      ),
      Chapter(
        id: 'chapter_3',
        title: '第三章：高潮',
        content: '',
        level: 0,
        order: 2,
        status: ChapterStatus.draft,
        wordCount: 0,
        createdAt: now.subtract(const Duration(days: 1)),
        updatedAt: now.subtract(const Duration(days: 1)),
        tags: ['高潮', '转折'],
      ),
    ];

    _chapters.addAll(chapters);
  }
}

/// 章节仓库实现提供者
final chapterRepositoryImplProvider = Provider<ChapterRepositoryImpl>((ref) {
  final repository = ChapterRepositoryImpl();
  // 初始化示例数据
  repository.initializeSampleData();
  return repository;
});

/// 重新定义章节仓库提供者
final chapterRepositoryProvider = Provider<ChapterRepository>((ref) {
  return ref.read(chapterRepositoryImplProvider);
});