import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import 'package:bamboofall_app/main.dart' as app;

/// 完整写作旅程端到端测试
/// 测试从项目创建到内容发布的完整用户流程
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Complete Writing Journey E2E Tests', () {
    testWidgets('Complete novel writing workflow from start to finish', (WidgetTester tester) async {
      // 启动应用
      app.main();
      await tester.pumpAndSettle();

      // 等待应用初始化完成
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // ==================== 第一阶段：项目创建 ====================
      
      // 1. 验证欢迎界面显示
      expect(find.text('笔落 - BambooFall'), findsOneWidget);
      
      // 2. 点击创建新项目
      await tester.tap(find.byKey(const Key('create_project_button')));
      await tester.pumpAndSettle();

      // 3. 填写项目信息
      await tester.enterText(find.byKey(const Key('project_name_field')), '我的第一部小说');
      await tester.enterText(find.byKey(const Key('project_description_field')), '这是一个关于勇敢骑士的冒险故事');
      
      // 4. 选择项目类型
      await tester.tap(find.byKey(const Key('project_type_novel')));
      await tester.pumpAndSettle();

      // 5. 创建项目
      await tester.tap(find.byKey(const Key('confirm_create_project')));
      await tester.pumpAndSettle();

      // 6. 验证项目创建成功，进入项目工作台
      expect(find.text('我的第一部小说'), findsOneWidget);
      expect(find.byKey(const Key('writing_workspace')), findsOneWidget);

      // ==================== 第二阶段：世界观设定 ====================
      
      // 7. 打开圣经管理
      await tester.tap(find.byKey(const Key('bible_management_tab')));
      await tester.pumpAndSettle();

      // 8. 创建主角角色
      await tester.tap(find.byKey(const Key('add_character_button')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('character_name_field')), '亚瑟');
      await tester.enterText(find.byKey(const Key('character_description_field')), '一位年轻勇敢的骑士');
      await tester.enterText(find.byKey(const Key('character_age_field')), '25');
      
      // 选择性别
      await tester.tap(find.byKey(const Key('character_gender_male')));
      await tester.pumpAndSettle();

      // 添加性格特征
      await tester.enterText(find.byKey(const Key('character_personality_field')), '勇敢,正直,善良');
      
      // 保存角色
      await tester.tap(find.byKey(const Key('save_character_button')));
      await tester.pumpAndSettle();

      // 9. 验证角色创建成功
      expect(find.text('亚瑟'), findsOneWidget);
      expect(find.text('一位年轻勇敢的骑士'), findsOneWidget);

      // 10. 创建主要地点
      await tester.tap(find.byKey(const Key('locations_tab')));
      await tester.pumpAndSettle();

      await tester.tap(find.byKey(const Key('add_location_button')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('location_name_field')), '卡美洛城');
      await tester.enterText(find.byKey(const Key('location_description_field')), '亚瑟王的城堡，骑士们的家园');
      
      // 选择地点类型
      await tester.tap(find.byKey(const Key('location_type_castle')));
      await tester.pumpAndSettle();

      // 保存地点
      await tester.tap(find.byKey(const Key('save_location_button')));
      await tester.pumpAndSettle();

      // 11. 验证地点创建成功
      expect(find.text('卡美洛城'), findsOneWidget);

      // ==================== 第三阶段：章节创建和写作 ====================
      
      // 12. 切换到写作工作台
      await tester.tap(find.byKey(const Key('writing_workspace_tab')));
      await tester.pumpAndSettle();

      // 13. 创建第一章
      await tester.tap(find.byKey(const Key('add_chapter_button')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('chapter_title_field')), '第一章：命运的召唤');
      await tester.tap(find.byKey(const Key('confirm_add_chapter')));
      await tester.pumpAndSettle();

      // 14. 验证章节创建成功并自动打开编辑器
      expect(find.text('第一章：命运的召唤'), findsOneWidget);
      expect(find.byKey(const Key('markdown_editor')), findsOneWidget);

      // 15. 开始写作内容
      const chapterContent = '''
# 第一章：命运的召唤

在卡美洛城的高塔上，年轻的骑士亚瑟凝视着远方的地平线。晨曦的第一缕阳光洒在他的盔甲上，闪闪发光。

"今天将是改变一切的日子。"他轻声自语道。

突然，一只信鸽飞到了窗台上，腿上绑着一封紧急的信件。亚瑟小心地取下信件，展开阅读。信中的内容让他的脸色变得凝重。

"黑暗势力正在集结，王国面临前所未有的威胁。只有传说中的圣剑才能拯救我们。"

亚瑟知道，他的冒险即将开始。
      ''';

      await tester.enterText(find.byKey(const Key('markdown_editor')), chapterContent);
      await tester.pumpAndSettle();

      // 16. 验证字数统计更新
      expect(find.textContaining('字数'), findsOneWidget);
      expect(find.textContaining('200'), findsOneWidget); // 大约字数

      // 17. 保存章节
      await tester.tap(find.byKey(const Key('save_chapter_button')));
      await tester.pumpAndSettle();

      // 验证保存成功提示
      expect(find.text('保存成功'), findsOneWidget);

      // ==================== 第四阶段：AI辅助写作 ====================
      
      // 18. 打开AI助手面板
      await tester.tap(find.byKey(const Key('ai_assistant_panel')));
      await tester.pumpAndSettle();

      // 19. 配置AI模型
      await tester.tap(find.byKey(const Key('ai_settings_button')));
      await tester.pumpAndSettle();

      // 选择GPT-4模型
      await tester.tap(find.byKey(const Key('select_gpt4_model')));
      await tester.pumpAndSettle();

      // 20. 使用AI生成下一段内容
      await tester.enterText(
        find.byKey(const Key('ai_prompt_input')), 
        '请为亚瑟骑士的冒险故事写一个精彩的第二章开头，描述他离开城堡开始寻找圣剑的场景。'
      );

      await tester.tap(find.byKey(const Key('generate_content_button')));
      await tester.pumpAndSettle();

      // 等待AI生成内容
      await tester.pump(const Duration(seconds: 5));

      // 21. 验证AI生成的内容显示
      expect(find.byKey(const Key('ai_generated_content')), findsOneWidget);
      expect(find.textContaining('第二章'), findsOneWidget);

      // 22. 将AI生成的内容插入到编辑器
      await tester.tap(find.byKey(const Key('insert_ai_content_button')));
      await tester.pumpAndSettle();

      // 验证内容已插入
      expect(find.textContaining('第二章'), findsOneWidget);

      // ==================== 第五阶段：版本管理 ====================
      
      // 23. 创建版本快照
      await tester.tap(find.byKey(const Key('version_control_button')));
      await tester.pumpAndSettle();

      await tester.tap(find.byKey(const Key('create_version_button')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('version_name_field')), '初稿完成');
      await tester.enterText(find.byKey(const Key('version_description_field')), '完成了前两章的初稿');

      await tester.tap(find.byKey(const Key('confirm_create_version')));
      await tester.pumpAndSettle();

      // 24. 验证版本创建成功
      expect(find.text('初稿完成'), findsOneWidget);
      expect(find.text('版本创建成功'), findsOneWidget);

      // ==================== 第六阶段：模板使用 ====================
      
      // 25. 打开模板管理
      await tester.tap(find.byKey(const Key('template_manager_button')));
      await tester.pumpAndSettle();

      // 26. 选择角色描述模板
      await tester.tap(find.byKey(const Key('character_description_template')));
      await tester.pumpAndSettle();

      // 27. 填写模板变量
      await tester.enterText(find.byKey(const Key('template_var_name')), '梅林');
      await tester.enterText(find.byKey(const Key('template_var_role')), '智慧的法师');
      await tester.enterText(find.byKey(const Key('template_var_power')), '预知未来的能力');

      // 28. 生成模板内容
      await tester.tap(find.byKey(const Key('generate_from_template_button')));
      await tester.pumpAndSettle();

      // 验证模板生成的内容
      expect(find.textContaining('梅林'), findsOneWidget);
      expect(find.textContaining('智慧的法师'), findsOneWidget);

      // ==================== 第七阶段：项目统计和分析 ====================
      
      // 29. 查看项目统计
      await tester.tap(find.byKey(const Key('project_statistics_button')));
      await tester.pumpAndSettle();

      // 30. 验证统计信息显示
      expect(find.text('项目统计'), findsOneWidget);
      expect(find.textContaining('总字数'), findsOneWidget);
      expect(find.textContaining('章节数'), findsOneWidget);
      expect(find.textContaining('角色数'), findsOneWidget);
      expect(find.textContaining('地点数'), findsOneWidget);

      // 验证具体数值
      expect(find.textContaining('2'), findsOneWidget); // 2个章节
      expect(find.textContaining('1'), findsOneWidget); // 1个角色
      expect(find.textContaining('1'), findsOneWidget); // 1个地点

      // ==================== 第八阶段：导出和分享 ====================
      
      // 31. 导出项目
      await tester.tap(find.byKey(const Key('export_project_button')));
      await tester.pumpAndSettle();

      // 32. 选择导出格式
      await tester.tap(find.byKey(const Key('export_format_markdown')));
      await tester.pumpAndSettle();

      // 33. 确认导出
      await tester.tap(find.byKey(const Key('confirm_export_button')));
      await tester.pumpAndSettle();

      // 34. 验证导出成功
      expect(find.text('导出成功'), findsOneWidget);

      // ==================== 第九阶段：设置和个性化 ====================
      
      // 35. 打开设置
      await tester.tap(find.byKey(const Key('settings_button')));
      await tester.pumpAndSettle();

      // 36. 切换到暗色主题
      await tester.tap(find.byKey(const Key('dark_theme_toggle')));
      await tester.pumpAndSettle();

      // 验证主题切换成功
      expect(find.byKey(const Key('dark_theme_indicator')), findsOneWidget);

      // 37. 调整字体大小
      await tester.tap(find.byKey(const Key('font_size_slider')));
      await tester.drag(find.byKey(const Key('font_size_slider')), const Offset(50, 0));
      await tester.pumpAndSettle();

      // 38. 保存设置
      await tester.tap(find.byKey(const Key('save_settings_button')));
      await tester.pumpAndSettle();

      // 验证设置保存成功
      expect(find.text('设置已保存'), findsOneWidget);

      // ==================== 第十阶段：完整性验证 ====================
      
      // 39. 返回主工作台
      await tester.tap(find.byKey(const Key('back_to_workspace_button')));
      await tester.pumpAndSettle();

      // 40. 验证所有创建的内容都存在
      expect(find.text('我的第一部小说'), findsOneWidget);
      expect(find.text('第一章：命运的召唤'), findsOneWidget);
      
      // 41. 验证项目状态
      expect(find.byKey(const Key('project_active_indicator')), findsOneWidget);
      
      // 42. 最终验证 - 检查数据持久化
      // 重启应用模拟
      await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
        'flutter/platform',
        null,
        (data) {},
      );
      
      // 重新启动应用
      app.main();
      await tester.pumpAndSettle();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // 43. 验证数据持久化成功
      expect(find.text('我的第一部小说'), findsOneWidget);
      
      // 打开项目
      await tester.tap(find.text('我的第一部小说'));
      await tester.pumpAndSettle();

      // 验证章节和内容都还在
      expect(find.text('第一章：命运的召唤'), findsOneWidget);
      
      // 打开章节验证内容
      await tester.tap(find.text('第一章：命运的召唤'));
      await tester.pumpAndSettle();

      expect(find.textContaining('亚瑟'), findsOneWidget);
      expect(find.textContaining('卡美洛城'), findsOneWidget);

      print('✅ 完整写作旅程端到端测试通过！');
    });

    testWidgets('Multi-project management workflow', (WidgetTester tester) async {
      // 启动应用
      app.main();
      await tester.pumpAndSettle();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // ==================== 创建多个项目 ====================
      
      // 创建第一个项目（小说）
      await tester.tap(find.byKey(const Key('create_project_button')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('project_name_field')), '科幻小说项目');
      await tester.enterText(find.byKey(const Key('project_description_field')), '未来世界的冒险故事');
      await tester.tap(find.byKey(const Key('project_type_novel')));
      await tester.tap(find.byKey(const Key('confirm_create_project')));
      await tester.pumpAndSettle();

      // 返回项目列表
      await tester.tap(find.byKey(const Key('back_to_project_list')));
      await tester.pumpAndSettle();

      // 创建第二个项目（散文）
      await tester.tap(find.byKey(const Key('create_project_button')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('project_name_field')), '生活随笔集');
      await tester.enterText(find.byKey(const Key('project_description_field')), '记录生活中的美好瞬间');
      await tester.tap(find.byKey(const Key('project_type_essay')));
      await tester.tap(find.byKey(const Key('confirm_create_project')));
      await tester.pumpAndSettle();

      // 返回项目列表
      await tester.tap(find.byKey(const Key('back_to_project_list')));
      await tester.pumpAndSettle();

      // ==================== 项目切换和管理 ====================
      
      // 验证两个项目都显示在列表中
      expect(find.text('科幻小说项目'), findsOneWidget);
      expect(find.text('生活随笔集'), findsOneWidget);

      // 切换到第一个项目
      await tester.tap(find.text('科幻小说项目'));
      await tester.pumpAndSettle();

      // 在第一个项目中创建内容
      await tester.tap(find.byKey(const Key('add_chapter_button')));
      await tester.pumpAndSettle();
      await tester.enterText(find.byKey(const Key('chapter_title_field')), '第一章：星际旅行');
      await tester.tap(find.byKey(const Key('confirm_add_chapter')));
      await tester.pumpAndSettle();

      // 切换回项目列表
      await tester.tap(find.byKey(const Key('back_to_project_list')));
      await tester.pumpAndSettle();

      // 切换到第二个项目
      await tester.tap(find.text('生活随笔集'));
      await tester.pumpAndSettle();

      // 在第二个项目中创建内容
      await tester.tap(find.byKey(const Key('add_chapter_button')));
      await tester.pumpAndSettle();
      await tester.enterText(find.byKey(const Key('chapter_title_field')), '春日漫步');
      await tester.tap(find.byKey(const Key('confirm_add_chapter')));
      await tester.pumpAndSettle();

      // ==================== 验证项目隔离 ====================
      
      // 验证当前项目只显示自己的章节
      expect(find.text('春日漫步'), findsOneWidget);
      expect(find.text('第一章：星际旅行'), findsNothing);

      // 切换回第一个项目验证隔离
      await tester.tap(find.byKey(const Key('back_to_project_list')));
      await tester.pumpAndSettle();
      await tester.tap(find.text('科幻小说项目'));
      await tester.pumpAndSettle();

      expect(find.text('第一章：星际旅行'), findsOneWidget);
      expect(find.text('春日漫步'), findsNothing);

      print('✅ 多项目管理工作流程测试通过！');
    });

    testWidgets('Collaborative features and sharing workflow', (WidgetTester tester) async {
      // 启动应用
      app.main();
      await tester.pumpAndSettle();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // 创建项目
      await tester.tap(find.byKey(const Key('create_project_button')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('project_name_field')), '协作写作项目');
      await tester.tap(find.byKey(const Key('confirm_create_project')));
      await tester.pumpAndSettle();

      // ==================== 版本控制协作 ====================
      
      // 创建初始内容
      await tester.tap(find.byKey(const Key('add_chapter_button')));
      await tester.pumpAndSettle();
      await tester.enterText(find.byKey(const Key('chapter_title_field')), '协作章节');
      await tester.tap(find.byKey(const Key('confirm_add_chapter')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('markdown_editor')), '这是初始内容。');
      await tester.tap(find.byKey(const Key('save_chapter_button')));
      await tester.pumpAndSettle();

      // 创建版本1
      await tester.tap(find.byKey(const Key('version_control_button')));
      await tester.pumpAndSettle();
      await tester.tap(find.byKey(const Key('create_version_button')));
      await tester.pumpAndSettle();
      await tester.enterText(find.byKey(const Key('version_name_field')), '版本1');
      await tester.tap(find.byKey(const Key('confirm_create_version')));
      await tester.pumpAndSettle();

      // 修改内容
      await tester.enterText(find.byKey(const Key('markdown_editor')), '这是修改后的内容，添加了更多细节。');
      await tester.tap(find.byKey(const Key('save_chapter_button')));
      await tester.pumpAndSettle();

      // 创建版本2
      await tester.tap(find.byKey(const Key('create_version_button')));
      await tester.pumpAndSettle();
      await tester.enterText(find.byKey(const Key('version_name_field')), '版本2');
      await tester.tap(find.byKey(const Key('confirm_create_version')));
      await tester.pumpAndSettle();

      // ==================== 版本比较和回滚 ====================
      
      // 打开版本历史
      await tester.tap(find.byKey(const Key('version_history_button')));
      await tester.pumpAndSettle();

      // 验证版本列表
      expect(find.text('版本1'), findsOneWidget);
      expect(find.text('版本2'), findsOneWidget);

      // 比较版本
      await tester.tap(find.byKey(const Key('compare_versions_button')));
      await tester.pumpAndSettle();

      // 验证差异显示
      expect(find.byKey(const Key('version_diff_viewer')), findsOneWidget);
      expect(find.textContaining('添加了更多细节'), findsOneWidget);

      // 回滚到版本1
      await tester.tap(find.byKey(const Key('rollback_to_version1')));
      await tester.pumpAndSettle();
      await tester.tap(find.byKey(const Key('confirm_rollback')));
      await tester.pumpAndSettle();

      // 验证回滚成功
      expect(find.text('回滚成功'), findsOneWidget);

      // ==================== 导出和分享 ====================
      
      // 生成分享链接
      await tester.tap(find.byKey(const Key('share_project_button')));
      await tester.pumpAndSettle();

      await tester.tap(find.byKey(const Key('generate_share_link')));
      await tester.pumpAndSettle();

      // 验证分享链接生成
      expect(find.byKey(const Key('share_link_display')), findsOneWidget);
      expect(find.text('分享链接已生成'), findsOneWidget);

      // 导出为多种格式
      await tester.tap(find.byKey(const Key('export_project_button')));
      await tester.pumpAndSettle();

      // 导出为PDF
      await tester.tap(find.byKey(const Key('export_format_pdf')));
      await tester.tap(find.byKey(const Key('confirm_export_button')));
      await tester.pumpAndSettle();
      expect(find.text('PDF导出成功'), findsOneWidget);

      // 导出为Word
      await tester.tap(find.byKey(const Key('export_format_docx')));
      await tester.tap(find.byKey(const Key('confirm_export_button')));
      await tester.pumpAndSettle();
      expect(find.text('Word导出成功'), findsOneWidget);

      print('✅ 协作功能和分享工作流程测试通过！');
    });
  });
}

/// 测试辅助工具类
class E2ETestHelpers {
  /// 等待元素出现
  static Future<void> waitForElement(WidgetTester tester, Finder finder, {Duration timeout = const Duration(seconds: 10)}) async {
    final endTime = DateTime.now().add(timeout);
    
    while (DateTime.now().isBefore(endTime)) {
      await tester.pumpAndSettle();
      if (tester.any(finder)) {
        return;
      }
      await Future.delayed(const Duration(milliseconds: 100));
    }
    
    throw Exception('Element not found within timeout: $finder');
  }

  /// 模拟用户输入延迟
  static Future<void> simulateUserTyping(WidgetTester tester, Finder finder, String text) async {
    for (int i = 0; i < text.length; i++) {
      await tester.enterText(finder, text.substring(0, i + 1));
      await tester.pump(const Duration(milliseconds: 50));
    }
  }

  /// 验证页面加载完成
  static Future<void> waitForPageLoad(WidgetTester tester) async {
    await tester.pumpAndSettle();
    await Future.delayed(const Duration(milliseconds: 500));
    await tester.pumpAndSettle();
  }

  /// 截图保存（用于调试）
  static Future<void> takeScreenshot(WidgetTester tester, String name) async {
    // 在实际实现中，这里可以保存截图用于调试
    print('📸 Screenshot taken: $name');
  }
}