import 'package:freezed_annotation/freezed_annotation.dart';

import 'chapter_version.dart';

part 'version_branch.freezed.dart';
part 'version_branch.g.dart';

/// 版本分支实体
/// 表示版本的树形结构，用于显示版本分支关系
@freezed
class VersionBranch with _$VersionBranch {
  const factory VersionBranch({
    /// 当前版本
    required ChapterVersion version,
    
    /// 子分支列表
    @Default([]) List<VersionBranch> children,
    
    /// 分支深度
    @Default(0) int depth,
    
    /// 是否展开
    @Default(true) bool isExpanded,
    
    /// 分支类型
    @Default(BranchType.main) BranchType branchType,
    
    /// 分支颜色（用于UI显示）
    String? branchColor,
  }) = _VersionBranch;

  factory VersionBranch.fromJson(Map<String, dynamic> json) => _$VersionBranchFromJson(json);
}

/// 分支类型枚举
enum BranchType {
  /// 主分支
  main,
  
  /// 功能分支
  feature,
  
  /// 修复分支
  hotfix,
  
  /// 实验分支
  experimental,
  
  /// 合并分支
  merge,
}

/// 分支类型扩展
extension BranchTypeExtension on BranchType {
  /// 显示名称
  String get displayName {
    switch (this) {
      case BranchType.main:
        return '主分支';
      case BranchType.feature:
        return '功能分支';
      case BranchType.hotfix:
        return '修复分支';
      case BranchType.experimental:
        return '实验分支';
      case BranchType.merge:
        return '合并分支';
    }
  }

  /// 分支颜色
  String get color {
    switch (this) {
      case BranchType.main:
        return '#2196F3'; // 蓝色
      case BranchType.feature:
        return '#4CAF50'; // 绿色
      case BranchType.hotfix:
        return '#FF5722'; // 红色
      case BranchType.experimental:
        return '#FF9800'; // 橙色
      case BranchType.merge:
        return '#9C27B0'; // 紫色
    }
  }

  /// 分支图标
  String get icon {
    switch (this) {
      case BranchType.main:
        return '🌟';
      case BranchType.feature:
        return '🚀';
      case BranchType.hotfix:
        return '🔧';
      case BranchType.experimental:
        return '🧪';
      case BranchType.merge:
        return '🔀';
    }
  }
}

/// 版本分支扩展方法
extension VersionBranchExtension on VersionBranch {
  /// 获取所有后代版本
  List<ChapterVersion> get allDescendants {
    final descendants = <ChapterVersion>[];
    
    void collectDescendants(VersionBranch branch) {
      for (final child in branch.children) {
        descendants.add(child.version);
        collectDescendants(child);
      }
    }
    
    collectDescendants(this);
    return descendants;
  }

  /// 获取叶子节点（没有子分支的版本）
  List<ChapterVersion> get leafVersions {
    if (children.isEmpty) {
      return [version];
    }
    
    final leaves = <ChapterVersion>[];
    for (final child in children) {
      leaves.addAll(child.leafVersions);
    }
    return leaves;
  }

  /// 获取分支的总深度
  int get maxDepth {
    if (children.isEmpty) {
      return depth;
    }
    
    int maxChildDepth = depth;
    for (final child in children) {
      final childDepth = child.maxDepth;
      if (childDepth > maxChildDepth) {
        maxChildDepth = childDepth;
      }
    }
    return maxChildDepth;
  }

  /// 获取分支中的版本总数
  int get totalVersionCount {
    int count = 1; // 当前版本
    for (final child in children) {
      count += child.totalVersionCount;
    }
    return count;
  }

  /// 查找指定版本
  VersionBranch? findVersion(String versionId) {
    if (version.id == versionId) {
      return this;
    }
    
    for (final child in children) {
      final found = child.findVersion(versionId);
      if (found != null) {
        return found;
      }
    }
    
    return null;
  }

  /// 获取到根节点的路径
  List<ChapterVersion> getPathToRoot() {
    final path = <ChapterVersion>[version];
    
    // 这里需要从父节点向上追溯，但由于我们只有子节点信息
    // 实际实现中可能需要额外的逻辑来构建完整路径
    
    return path;
  }

  /// 展开/折叠分支
  VersionBranch toggleExpanded() {
    return copyWith(isExpanded: !isExpanded);
  }

  /// 展开所有子分支
  VersionBranch expandAll() {
    final expandedChildren = children.map((child) => child.expandAll()).toList();
    return copyWith(isExpanded: true, children: expandedChildren);
  }

  /// 折叠所有子分支
  VersionBranch collapseAll() {
    final collapsedChildren = children.map((child) => child.collapseAll()).toList();
    return copyWith(isExpanded: false, children: collapsedChildren);
  }

  /// 设置分支深度
  VersionBranch withDepth(int newDepth) {
    final updatedChildren = children
        .map((child) => child.withDepth(newDepth + 1))
        .toList();
    return copyWith(depth: newDepth, children: updatedChildren);
  }
}