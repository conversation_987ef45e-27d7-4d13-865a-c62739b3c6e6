// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in bamboofall_app/test/unit/features/ai_integration/domain/usecases/generate_content_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:bamboofall_app/features/ai_integration/domain/entities/ai_model.dart'
    as _i2;
import 'package:bamboofall_app/features/ai_integration/domain/repositories/llm_repository.dart'
    as _i3;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAICapabilities_0 extends _i1.SmartFake
    implements _i2.AICapabilities {
  _FakeAICapabilities_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAIResponse_1 extends _i1.SmartFake implements _i2.AIResponse {
  _FakeAIResponse_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAIUsage_2 extends _i1.SmartFake implements _i2.AIUsage {
  _FakeAIUsage_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [LLMRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockLLMRepository extends _i1.Mock implements _i3.LLMRepository {
  MockLLMRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<List<_i2.AIModel>> getAvailableModels() =>
      (super.noSuchMethod(
            Invocation.method(#getAvailableModels, []),
            returnValue: _i4.Future<List<_i2.AIModel>>.value(<_i2.AIModel>[]),
          )
          as _i4.Future<List<_i2.AIModel>>);

  @override
  _i4.Future<List<_i2.AIModel>> getModelsByProvider(_i2.AIProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#getModelsByProvider, [provider]),
            returnValue: _i4.Future<List<_i2.AIModel>>.value(<_i2.AIModel>[]),
          )
          as _i4.Future<List<_i2.AIModel>>);

  @override
  _i4.Future<_i2.AIModel?> getModelById(String? modelId) =>
      (super.noSuchMethod(
            Invocation.method(#getModelById, [modelId]),
            returnValue: _i4.Future<_i2.AIModel?>.value(),
          )
          as _i4.Future<_i2.AIModel?>);

  @override
  _i4.Future<bool> isModelAvailable(String? modelId) =>
      (super.noSuchMethod(
            Invocation.method(#isModelAvailable, [modelId]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<_i2.AICapabilities> getModelCapabilities(String? modelId) =>
      (super.noSuchMethod(
            Invocation.method(#getModelCapabilities, [modelId]),
            returnValue: _i4.Future<_i2.AICapabilities>.value(
              _FakeAICapabilities_0(
                this,
                Invocation.method(#getModelCapabilities, [modelId]),
              ),
            ),
          )
          as _i4.Future<_i2.AICapabilities>);

  @override
  _i4.Future<void> saveModelConfig(_i2.AIModelConfig? config) =>
      (super.noSuchMethod(
            Invocation.method(#saveModelConfig, [config]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<_i2.AIModelConfig?> getModelConfig(String? modelId) =>
      (super.noSuchMethod(
            Invocation.method(#getModelConfig, [modelId]),
            returnValue: _i4.Future<_i2.AIModelConfig?>.value(),
          )
          as _i4.Future<_i2.AIModelConfig?>);

  @override
  _i4.Future<List<_i2.AIModelConfig>> getAllModelConfigs() =>
      (super.noSuchMethod(
            Invocation.method(#getAllModelConfigs, []),
            returnValue: _i4.Future<List<_i2.AIModelConfig>>.value(
              <_i2.AIModelConfig>[],
            ),
          )
          as _i4.Future<List<_i2.AIModelConfig>>);

  @override
  _i4.Future<void> deleteModelConfig(String? modelId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteModelConfig, [modelId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> updateModelConfig(
    String? modelId,
    _i2.AIModelConfig? config,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateModelConfig, [modelId, config]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<bool> testModelConnection(String? modelId) =>
      (super.noSuchMethod(
            Invocation.method(#testModelConnection, [modelId]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<_i2.AIResponse> chat(_i2.AIRequest? request) =>
      (super.noSuchMethod(
            Invocation.method(#chat, [request]),
            returnValue: _i4.Future<_i2.AIResponse>.value(
              _FakeAIResponse_1(this, Invocation.method(#chat, [request])),
            ),
          )
          as _i4.Future<_i2.AIResponse>);

  @override
  _i4.Stream<_i2.AIResponse> chatStream(_i2.AIRequest? request) =>
      (super.noSuchMethod(
            Invocation.method(#chatStream, [request]),
            returnValue: _i4.Stream<_i2.AIResponse>.empty(),
          )
          as _i4.Stream<_i2.AIResponse>);

  @override
  _i4.Future<_i2.AIResponse> completion(_i2.AIRequest? request) =>
      (super.noSuchMethod(
            Invocation.method(#completion, [request]),
            returnValue: _i4.Future<_i2.AIResponse>.value(
              _FakeAIResponse_1(
                this,
                Invocation.method(#completion, [request]),
              ),
            ),
          )
          as _i4.Future<_i2.AIResponse>);

  @override
  _i4.Stream<_i2.AIResponse> completionStream(_i2.AIRequest? request) =>
      (super.noSuchMethod(
            Invocation.method(#completionStream, [request]),
            returnValue: _i4.Stream<_i2.AIResponse>.empty(),
          )
          as _i4.Stream<_i2.AIResponse>);

  @override
  _i4.Future<List<_i2.AIResponse>> batchProcess(
    List<_i2.AIRequest>? requests,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#batchProcess, [requests]),
            returnValue: _i4.Future<List<_i2.AIResponse>>.value(
              <_i2.AIResponse>[],
            ),
          )
          as _i4.Future<List<_i2.AIResponse>>);

  @override
  _i4.Future<String> createSession({
    required String? modelId,
    String? systemPrompt,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createSession, [], {
              #modelId: modelId,
              #systemPrompt: systemPrompt,
              #metadata: metadata,
            }),
            returnValue: _i4.Future<String>.value(
              _i5.dummyValue<String>(
                this,
                Invocation.method(#createSession, [], {
                  #modelId: modelId,
                  #systemPrompt: systemPrompt,
                  #metadata: metadata,
                }),
              ),
            ),
          )
          as _i4.Future<String>);

  @override
  _i4.Future<List<_i2.AIMessage>> getSessionHistory(String? sessionId) =>
      (super.noSuchMethod(
            Invocation.method(#getSessionHistory, [sessionId]),
            returnValue: _i4.Future<List<_i2.AIMessage>>.value(
              <_i2.AIMessage>[],
            ),
          )
          as _i4.Future<List<_i2.AIMessage>>);

  @override
  _i4.Future<void> addMessageToSession(
    String? sessionId,
    _i2.AIMessage? message,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#addMessageToSession, [sessionId, message]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> clearSessionHistory(String? sessionId) =>
      (super.noSuchMethod(
            Invocation.method(#clearSessionHistory, [sessionId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> deleteSession(String? sessionId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteSession, [sessionId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<String>> getAllSessions() =>
      (super.noSuchMethod(
            Invocation.method(#getAllSessions, []),
            returnValue: _i4.Future<List<String>>.value(<String>[]),
          )
          as _i4.Future<List<String>>);

  @override
  _i4.Future<void> recordUsage(String? modelId, _i2.AIUsage? usage) =>
      (super.noSuchMethod(
            Invocation.method(#recordUsage, [modelId, usage]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<_i2.AIUsage> getUsageStats(
    String? modelId, {
    DateTime? startDate,
    DateTime? endDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getUsageStats,
              [modelId],
              {#startDate: startDate, #endDate: endDate},
            ),
            returnValue: _i4.Future<_i2.AIUsage>.value(
              _FakeAIUsage_2(
                this,
                Invocation.method(
                  #getUsageStats,
                  [modelId],
                  {#startDate: startDate, #endDate: endDate},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.AIUsage>);

  @override
  _i4.Future<Map<String, _i2.AIUsage>> getTotalUsageStats({
    DateTime? startDate,
    DateTime? endDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getTotalUsageStats, [], {
              #startDate: startDate,
              #endDate: endDate,
            }),
            returnValue: _i4.Future<Map<String, _i2.AIUsage>>.value(
              <String, _i2.AIUsage>{},
            ),
          )
          as _i4.Future<Map<String, _i2.AIUsage>>);

  @override
  _i4.Future<void> resetUsageStats(String? modelId) =>
      (super.noSuchMethod(
            Invocation.method(#resetUsageStats, [modelId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> recordError(String? modelId, _i2.AIError? error) =>
      (super.noSuchMethod(
            Invocation.method(#recordError, [modelId, error]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<_i2.AIError>> getErrorHistory(
    String? modelId, {
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getErrorHistory,
              [modelId],
              {#startDate: startDate, #endDate: endDate, #limit: limit},
            ),
            returnValue: _i4.Future<List<_i2.AIError>>.value(<_i2.AIError>[]),
          )
          as _i4.Future<List<_i2.AIError>>);

  @override
  _i4.Future<void> clearErrorHistory(String? modelId) =>
      (super.noSuchMethod(
            Invocation.method(#clearErrorHistory, [modelId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> cacheResponse(String? key, _i2.AIResponse? response) =>
      (super.noSuchMethod(
            Invocation.method(#cacheResponse, [key, response]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<_i2.AIResponse?> getCachedResponse(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#getCachedResponse, [key]),
            returnValue: _i4.Future<_i2.AIResponse?>.value(),
          )
          as _i4.Future<_i2.AIResponse?>);

  @override
  _i4.Future<void> clearCache() =>
      (super.noSuchMethod(
            Invocation.method(#clearCache, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> clearModelCache(String? modelId) =>
      (super.noSuchMethod(
            Invocation.method(#clearModelCache, [modelId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<Map<String, bool>> checkHealth() =>
      (super.noSuchMethod(
            Invocation.method(#checkHealth, []),
            returnValue: _i4.Future<Map<String, bool>>.value(<String, bool>{}),
          )
          as _i4.Future<Map<String, bool>>);

  @override
  _i4.Future<bool> checkModelHealth(String? modelId) =>
      (super.noSuchMethod(
            Invocation.method(#checkModelHealth, [modelId]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<Map<String, dynamic>> getServiceStatus() =>
      (super.noSuchMethod(
            Invocation.method(#getServiceStatus, []),
            returnValue: _i4.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i4.Future<Map<String, dynamic>>);

  @override
  int estimateTokens(String? text, {String? modelId}) =>
      (super.noSuchMethod(
            Invocation.method(#estimateTokens, [text], {#modelId: modelId}),
            returnValue: 0,
          )
          as int);

  @override
  bool validateRequest(_i2.AIRequest? request) =>
      (super.noSuchMethod(
            Invocation.method(#validateRequest, [request]),
            returnValue: false,
          )
          as bool);

  @override
  String generateRequestId() =>
      (super.noSuchMethod(
            Invocation.method(#generateRequestId, []),
            returnValue: _i5.dummyValue<String>(
              this,
              Invocation.method(#generateRequestId, []),
            ),
          )
          as String);

  @override
  String formatError(_i2.AIError? error) =>
      (super.noSuchMethod(
            Invocation.method(#formatError, [error]),
            returnValue: _i5.dummyValue<String>(
              this,
              Invocation.method(#formatError, [error]),
            ),
          )
          as String);

  @override
  _i4.Future<double> calculateCost(String? modelId, _i2.AIUsage? usage) =>
      (super.noSuchMethod(
            Invocation.method(#calculateCost, [modelId, usage]),
            returnValue: _i4.Future<double>.value(0.0),
          )
          as _i4.Future<double>);
}
