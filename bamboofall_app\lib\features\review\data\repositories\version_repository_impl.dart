import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/entities/chapter_version.dart';
import '../../domain/entities/version_diff.dart';
import '../../domain/repositories/version_repository.dart';

/// 版本仓库实现（内存存储）
/// 提供版本数据的内存存储和访问
class VersionRepositoryImpl implements VersionRepository {
  final List<ChapterVersion> _versions = [];
  final List<VersionDiff> _diffs = [];

  @override
  Future<List<ChapterVersion>> getChapterVersions(String chapterId) async {
    return _versions
        .where((version) => version.chapterId == chapterId)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  @override
  Future<ChapterVersion?> getVersionById(String versionId) async {
    try {
      return _versions.firstWhere((version) => version.id == versionId);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<ChapterVersion?> getLatestVersion(String chapterId) async {
    final versions = await getChapterVersions(chapterId);
    if (versions.isEmpty) return null;
    
    // 排除自动保存版本，获取最新的正式版本
    final nonAutoSaveVersions = versions.where((v) => !v.isAutoSave).toList();
    if (nonAutoSaveVersions.isEmpty) return versions.first;
    
    return nonAutoSaveVersions.first;
  }

  @override
  Future<ChapterVersion?> getMainVersion(String chapterId) async {
    final versions = await getChapterVersions(chapterId);
    try {
      return versions.firstWhere((version) => version.isMainVersion);
    } catch (e) {
      // 如果没有主版本，返回最新版本
      return await getLatestVersion(chapterId);
    }
  }

  @override
  Future<ChapterVersion> createVersion(ChapterVersion version) async {
    _versions.add(version);
    return version;
  }

  @override
  Future<ChapterVersion> updateVersion(ChapterVersion version) async {
    final index = _versions.indexWhere((v) => v.id == version.id);
    if (index != -1) {
      _versions[index] = version;
      return version;
    } else {
      throw Exception('Version not found: ${version.id}');
    }
  }

  @override
  Future<void> deleteVersion(String versionId) async {
    _versions.removeWhere((version) => version.id == versionId);
    // 同时删除相关的差异记录
    _diffs.removeWhere((diff) => 
        diff.sourceVersionId == versionId || diff.targetVersionId == versionId);
  }

  @override
  Future<List<ChapterVersion>> createVersions(List<ChapterVersion> versions) async {
    _versions.addAll(versions);
    return versions;
  }

  @override
  Future<List<ChapterVersion>> getVersionsByStatus(VersionStatus status) async {
    return _versions.where((version) => version.status == status).toList();
  }

  @override
  Future<List<ChapterVersion>> getVersionsByCreator(String createdBy) async {
    return _versions.where((version) => version.createdBy == createdBy).toList();
  }

  @override
  Future<List<ChapterVersion>> searchVersions(String query) async {
    if (query.trim().isEmpty) {
      return [];
    }

    final lowercaseQuery = query.toLowerCase();
    return _versions.where((version) {
      return version.title.toLowerCase().contains(lowercaseQuery) ||
             version.content.toLowerCase().contains(lowercaseQuery) ||
             version.description.toLowerCase().contains(lowercaseQuery) ||
             version.tags.any((tag) => tag.toLowerCase().contains(lowercaseQuery)) ||
             version.createdBy.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  @override
  Future<int> getVersionCount(String chapterId) async {
    return _versions.where((version) => version.chapterId == chapterId).length;
  }

  @override
  Future<List<ChapterVersion>> getAutoSaveVersions(String chapterId) async {
    return _versions
        .where((version) => version.chapterId == chapterId && version.isAutoSave)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  @override
  Future<void> cleanupAutoSaveVersions(String chapterId, {int keepCount = 10}) async {
    final autoSaveVersions = await getAutoSaveVersions(chapterId);
    
    if (autoSaveVersions.length > keepCount) {
      final versionsToDelete = autoSaveVersions.skip(keepCount).toList();
      for (final version in versionsToDelete) {
        await deleteVersion(version.id);
      }
    }
  }

  @override
  Future<VersionDiff> getVersionDiff(String sourceVersionId, String targetVersionId) async {
    try {
      return _diffs.firstWhere((diff) =>
          diff.sourceVersionId == sourceVersionId &&
          diff.targetVersionId == targetVersionId);
    } catch (e) {
      throw Exception('Version diff not found: $sourceVersionId -> $targetVersionId');
    }
  }

  @override
  Future<VersionDiff> saveVersionDiff(VersionDiff diff) async {
    // 检查是否已存在相同的差异记录
    final existingIndex = _diffs.indexWhere((d) =>
        d.sourceVersionId == diff.sourceVersionId &&
        d.targetVersionId == diff.targetVersionId);
    
    if (existingIndex != -1) {
      _diffs[existingIndex] = diff;
    } else {
      _diffs.add(diff);
    }
    
    return diff;
  }

  @override
  Future<List<VersionDiff>> getVersionDiffs(String versionId) async {
    return _diffs.where((diff) =>
        diff.sourceVersionId == versionId || diff.targetVersionId == versionId).toList();
  }

  @override
  Future<void> clearChapterVersions(String chapterId) async {
    _versions.removeWhere((version) => version.chapterId == chapterId);
    // 清理相关的差异记录
    final chapterVersionIds = _versions
        .where((v) => v.chapterId == chapterId)
        .map((v) => v.id)
        .toSet();
    _diffs.removeWhere((diff) =>
        chapterVersionIds.contains(diff.sourceVersionId) ||
        chapterVersionIds.contains(diff.targetVersionId));
  }

  @override
  Future<void> clearAllVersions() async {
    _versions.clear();
    _diffs.clear();
  }

  /// 初始化示例数据
  Future<void> initializeSampleData() async {
    if (_versions.isNotEmpty) return;

    final now = DateTime.now();
    
    // 创建示例版本数据
    final versions = [
      ChapterVersion(
        id: 'version_1_1',
        chapterId: 'chapter_1',
        versionNumber: 1,
        title: '第一章：开始',
        content: '''# 第一章：开始

这是第一章的初始内容。

## 序幕

在一个遥远的地方，故事开始了...

## 初遇

主人公第一次遇到了重要的人物。''',
        createdAt: now.subtract(const Duration(days: 10)),
        createdBy: 'author1',
        description: '初始版本',
        status: VersionStatus.published,
        isMainVersion: true,
        tags: ['initial', 'published'],
      ).calculateStats(),
      
      ChapterVersion(
        id: 'version_1_2',
        chapterId: 'chapter_1',
        versionNumber: 2,
        title: '第一章：开始',
        content: '''# 第一章：开始

这是第一章的修订内容，增加了更多细节。

## 序幕

在一个遥远而神秘的地方，我们的故事缓缓拉开帷幕...

## 初遇

主人公第一次遇到了改变他命运的重要人物。这次相遇将彻底改变他的人生轨迹。

## 冲突

初次见面就产生了意想不到的冲突。''',
        createdAt: now.subtract(const Duration(days: 8)),
        createdBy: 'author1',
        description: '增加细节和新章节',
        status: VersionStatus.approved,
        parentVersionId: 'version_1_1',
        tags: ['revision', 'expanded'],
      ).calculateStats(),
      
      ChapterVersion(
        id: 'version_1_3',
        chapterId: 'chapter_1',
        versionNumber: 3,
        title: '第一章：开始',
        content: '''# 第一章：开始

这是第一章的最新版本，经过编辑审阅。

## 序幕

在一个遥远而神秘的地方，我们的故事缓缓拉开帷幕。古老的传说在这里流传，等待着有缘人的到来。

## 初遇

主人公第一次遇到了改变他命运的重要人物。这次相遇将彻底改变他的人生轨迹，开启一段前所未有的冒险之旅。

## 冲突

初次见面就产生了意想不到的冲突，双方都没有预料到这种情况的发生。''',
        createdAt: now.subtract(const Duration(days: 5)),
        createdBy: 'editor1',
        description: '编辑审阅版本',
        status: VersionStatus.inReview,
        parentVersionId: 'version_1_2',
        tags: ['edited', 'review'],
      ).calculateStats(),
      
      // 自动保存版本
      ChapterVersion(
        id: 'version_1_auto_1',
        chapterId: 'chapter_1',
        versionNumber: 3,
        title: '第一章：开始',
        content: '''# 第一章：开始

这是第一章的自动保存版本...

## 序幕

在一个遥远而神秘的地方，我们的故事缓缓拉开帷幕。古老的传说在这里流传...''',
        createdAt: now.subtract(const Duration(hours: 2)),
        createdBy: 'author1',
        description: '自动保存',
        status: VersionStatus.draft,
        isAutoSave: true,
        parentVersionId: 'version_1_3',
        tags: ['autosave'],
      ).calculateStats(),
      
      // 第二章版本
      ChapterVersion(
        id: 'version_2_1',
        chapterId: 'chapter_2',
        versionNumber: 1,
        title: '第二章：发展',
        content: '''# 第二章：发展

故事进入发展阶段。

## 深入

主人公开始深入了解这个世界的秘密。

## 挑战

面临第一个重大挑战。''',
        createdAt: now.subtract(const Duration(days: 6)),
        createdBy: 'author1',
        description: '第二章初稿',
        status: VersionStatus.draft,
        isMainVersion: true,
        tags: ['draft'],
      ).calculateStats(),
    ];

    _versions.addAll(versions);

    // 创建示例差异数据
    final diff = DiffBuilder.calculateDiff(
      sourceVersionId: 'version_1_1',
      targetVersionId: 'version_1_2',
      sourceText: versions[0].content,
      targetText: versions[1].content,
    );
    
    _diffs.add(diff);
  }
}

/// 版本仓库实现提供者
final versionRepositoryImplProvider = Provider<VersionRepositoryImpl>((ref) {
  final repository = VersionRepositoryImpl();
  // 初始化示例数据
  repository.initializeSampleData();
  return repository;
});

/// 重新定义版本仓库提供者
final versionRepositoryProvider = Provider<VersionRepository>((ref) {
  return ref.read(versionRepositoryImplProvider);
});