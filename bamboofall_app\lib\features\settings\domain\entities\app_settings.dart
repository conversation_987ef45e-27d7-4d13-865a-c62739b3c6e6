import 'package:equatable/equatable.dart';
import '../../../../app/theme.dart';

/// 应用设置实体
class AppSettings extends Equatable {
  /// 主题模式
  final AppThemeMode themeMode;
  
  /// 语言设置
  final String language;
  
  /// 字体大小
  final double fontSize;
  
  /// 自动保存间隔（秒）
  final int autoSaveInterval;
  
  /// 是否启用自动保存
  final bool enableAutoSave;
  
  /// 是否启用拼写检查
  final bool enableSpellCheck;
  
  /// 是否启用语法检查
  final bool enableGrammarCheck;
  
  /// 是否启用AI建议
  final bool enableAISuggestions;
  
  /// AI响应延迟（毫秒）
  final int aiResponseDelay;
  
  /// 编辑器设置
  final EditorSettings editorSettings;
  
  /// 界面设置
  final UISettings uiSettings;
  
  /// 隐私设置
  final PrivacySettings privacySettings;
  
  /// 备份设置
  final BackupSettings backupSettings;
  
  /// 快捷键设置
  final Map<String, String> shortcuts;
  
  /// 最后更新时间
  final DateTime lastUpdated;

  const AppSettings({
    this.themeMode = AppThemeMode.system,
    this.language = 'zh_CN',
    this.fontSize = 14.0,
    this.autoSaveInterval = 30,
    this.enableAutoSave = true,
    this.enableSpellCheck = true,
    this.enableGrammarCheck = false,
    this.enableAISuggestions = true,
    this.aiResponseDelay = 1000,
    this.editorSettings = const EditorSettings(),
    this.uiSettings = const UISettings(),
    this.privacySettings = const PrivacySettings(),
    this.backupSettings = const BackupSettings(),
    this.shortcuts = const {},
    required this.lastUpdated,
  });

  /// 创建默认设置
  factory AppSettings.defaultSettings() {
    final instance = AppSettings(
      lastUpdated: DateTime.now(),
    );
    return instance.copyWith(
      shortcuts: instance.getDefaultShortcuts(),
    );
  }

  /// 复制并更新设置
  AppSettings copyWith({
    AppThemeMode? themeMode,
    String? language,
    double? fontSize,
    int? autoSaveInterval,
    bool? enableAutoSave,
    bool? enableSpellCheck,
    bool? enableGrammarCheck,
    bool? enableAISuggestions,
    int? aiResponseDelay,
    EditorSettings? editorSettings,
    UISettings? uiSettings,
    PrivacySettings? privacySettings,
    BackupSettings? backupSettings,
    Map<String, String>? shortcuts,
    DateTime? lastUpdated,
  }) {
    return AppSettings(
      themeMode: themeMode ?? this.themeMode,
      language: language ?? this.language,
      fontSize: fontSize ?? this.fontSize,
      autoSaveInterval: autoSaveInterval ?? this.autoSaveInterval,
      enableAutoSave: enableAutoSave ?? this.enableAutoSave,
      enableSpellCheck: enableSpellCheck ?? this.enableSpellCheck,
      enableGrammarCheck: enableGrammarCheck ?? this.enableGrammarCheck,
      enableAISuggestions: enableAISuggestions ?? this.enableAISuggestions,
      aiResponseDelay: aiResponseDelay ?? this.aiResponseDelay,
      editorSettings: editorSettings ?? this.editorSettings,
      uiSettings: uiSettings ?? this.uiSettings,
      privacySettings: privacySettings ?? this.privacySettings,
      backupSettings: backupSettings ?? this.backupSettings,
      shortcuts: shortcuts ?? this.shortcuts,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }

  /// 获取默认快捷键
  Map<String, String> getDefaultShortcuts() {
    return {
      'new_project': 'Ctrl+N',
      'open_project': 'Ctrl+O',
      'save': 'Ctrl+S',
      'save_as': 'Ctrl+Shift+S',
      'undo': 'Ctrl+Z',
      'redo': 'Ctrl+Y',
      'cut': 'Ctrl+X',
      'copy': 'Ctrl+C',
      'paste': 'Ctrl+V',
      'select_all': 'Ctrl+A',
      'find': 'Ctrl+F',
      'replace': 'Ctrl+H',
      'bold': 'Ctrl+B',
      'italic': 'Ctrl+I',
      'underline': 'Ctrl+U',
      'ai_assist': 'Ctrl+Space',
      'quick_save': 'F5',
      'preview': 'F12',
      'settings': 'Ctrl+,',
      'help': 'F1',
    };
  }

  @override
  List<Object?> get props => [
        themeMode,
        language,
        fontSize,
        autoSaveInterval,
        enableAutoSave,
        enableSpellCheck,
        enableGrammarCheck,
        enableAISuggestions,
        aiResponseDelay,
        editorSettings,
        uiSettings,
        privacySettings,
        backupSettings,
        shortcuts,
        lastUpdated,
      ];
}

/// 编辑器设置
class EditorSettings extends Equatable {
  /// 是否显示行号
  final bool showLineNumbers;
  
  /// 是否启用代码折叠
  final bool enableCodeFolding;
  
  /// 是否启用自动换行
  final bool enableWordWrap;
  
  /// 是否显示空白字符
  final bool showWhitespace;
  
  /// 缩进大小
  final int indentSize;
  
  /// 是否使用制表符
  final bool useTabsForIndent;
  
  /// 光标闪烁速度
  final int cursorBlinkRate;
  
  /// 是否启用括号匹配
  final bool enableBracketMatching;
  
  /// 是否启用自动补全
  final bool enableAutoComplete;
  
  /// 是否启用实时预览
  final bool enableLivePreview;

  const EditorSettings({
    this.showLineNumbers = true,
    this.enableCodeFolding = true,
    this.enableWordWrap = true,
    this.showWhitespace = false,
    this.indentSize = 2,
    this.useTabsForIndent = false,
    this.cursorBlinkRate = 500,
    this.enableBracketMatching = true,
    this.enableAutoComplete = true,
    this.enableLivePreview = false,
  });

  EditorSettings copyWith({
    bool? showLineNumbers,
    bool? enableCodeFolding,
    bool? enableWordWrap,
    bool? showWhitespace,
    int? indentSize,
    bool? useTabsForIndent,
    int? cursorBlinkRate,
    bool? enableBracketMatching,
    bool? enableAutoComplete,
    bool? enableLivePreview,
  }) {
    return EditorSettings(
      showLineNumbers: showLineNumbers ?? this.showLineNumbers,
      enableCodeFolding: enableCodeFolding ?? this.enableCodeFolding,
      enableWordWrap: enableWordWrap ?? this.enableWordWrap,
      showWhitespace: showWhitespace ?? this.showWhitespace,
      indentSize: indentSize ?? this.indentSize,
      useTabsForIndent: useTabsForIndent ?? this.useTabsForIndent,
      cursorBlinkRate: cursorBlinkRate ?? this.cursorBlinkRate,
      enableBracketMatching: enableBracketMatching ?? this.enableBracketMatching,
      enableAutoComplete: enableAutoComplete ?? this.enableAutoComplete,
      enableLivePreview: enableLivePreview ?? this.enableLivePreview,
    );
  }

  @override
  List<Object?> get props => [
        showLineNumbers,
        enableCodeFolding,
        enableWordWrap,
        showWhitespace,
        indentSize,
        useTabsForIndent,
        cursorBlinkRate,
        enableBracketMatching,
        enableAutoComplete,
        enableLivePreview,
      ];
}

/// 界面设置
class UISettings extends Equatable {
  /// 是否启用动画
  final bool enableAnimations;
  
  /// 动画速度
  final double animationSpeed;
  
  /// 是否显示工具栏
  final bool showToolbar;
  
  /// 是否显示状态栏
  final bool showStatusBar;
  
  /// 是否显示侧边栏
  final bool showSidebar;
  
  /// 侧边栏宽度
  final double sidebarWidth;
  
  /// 是否启用紧凑模式
  final bool enableCompactMode;
  
  /// 窗口透明度
  final double windowOpacity;
  
  /// 是否启用毛玻璃效果
  final bool enableBlurEffect;
  
  /// 界面缩放比例
  final double uiScale;

  const UISettings({
    this.enableAnimations = true,
    this.animationSpeed = 1.0,
    this.showToolbar = true,
    this.showStatusBar = true,
    this.showSidebar = true,
    this.sidebarWidth = 250.0,
    this.enableCompactMode = false,
    this.windowOpacity = 1.0,
    this.enableBlurEffect = false,
    this.uiScale = 1.0,
  });

  UISettings copyWith({
    bool? enableAnimations,
    double? animationSpeed,
    bool? showToolbar,
    bool? showStatusBar,
    bool? showSidebar,
    double? sidebarWidth,
    bool? enableCompactMode,
    double? windowOpacity,
    bool? enableBlurEffect,
    double? uiScale,
  }) {
    return UISettings(
      enableAnimations: enableAnimations ?? this.enableAnimations,
      animationSpeed: animationSpeed ?? this.animationSpeed,
      showToolbar: showToolbar ?? this.showToolbar,
      showStatusBar: showStatusBar ?? this.showStatusBar,
      showSidebar: showSidebar ?? this.showSidebar,
      sidebarWidth: sidebarWidth ?? this.sidebarWidth,
      enableCompactMode: enableCompactMode ?? this.enableCompactMode,
      windowOpacity: windowOpacity ?? this.windowOpacity,
      enableBlurEffect: enableBlurEffect ?? this.enableBlurEffect,
      uiScale: uiScale ?? this.uiScale,
    );
  }

  @override
  List<Object?> get props => [
        enableAnimations,
        animationSpeed,
        showToolbar,
        showStatusBar,
        showSidebar,
        sidebarWidth,
        enableCompactMode,
        windowOpacity,
        enableBlurEffect,
        uiScale,
      ];
}

/// 隐私设置
class PrivacySettings extends Equatable {
  /// 是否启用使用统计
  final bool enableUsageStats;
  
  /// 是否启用错误报告
  final bool enableErrorReporting;
  
  /// 是否启用自动更新检查
  final bool enableAutoUpdateCheck;
  
  /// 是否启用云同步
  final bool enableCloudSync;
  
  /// 是否启用本地数据加密
  final bool enableDataEncryption;
  
  /// 数据保留天数
  final int dataRetentionDays;
  
  /// 是否启用匿名模式
  final bool enableAnonymousMode;

  const PrivacySettings({
    this.enableUsageStats = false,
    this.enableErrorReporting = true,
    this.enableAutoUpdateCheck = true,
    this.enableCloudSync = false,
    this.enableDataEncryption = true,
    this.dataRetentionDays = 365,
    this.enableAnonymousMode = false,
  });

  PrivacySettings copyWith({
    bool? enableUsageStats,
    bool? enableErrorReporting,
    bool? enableAutoUpdateCheck,
    bool? enableCloudSync,
    bool? enableDataEncryption,
    int? dataRetentionDays,
    bool? enableAnonymousMode,
  }) {
    return PrivacySettings(
      enableUsageStats: enableUsageStats ?? this.enableUsageStats,
      enableErrorReporting: enableErrorReporting ?? this.enableErrorReporting,
      enableAutoUpdateCheck: enableAutoUpdateCheck ?? this.enableAutoUpdateCheck,
      enableCloudSync: enableCloudSync ?? this.enableCloudSync,
      enableDataEncryption: enableDataEncryption ?? this.enableDataEncryption,
      dataRetentionDays: dataRetentionDays ?? this.dataRetentionDays,
      enableAnonymousMode: enableAnonymousMode ?? this.enableAnonymousMode,
    );
  }

  @override
  List<Object?> get props => [
        enableUsageStats,
        enableErrorReporting,
        enableAutoUpdateCheck,
        enableCloudSync,
        enableDataEncryption,
        dataRetentionDays,
        enableAnonymousMode,
      ];
}

/// 备份设置
class BackupSettings extends Equatable {
  /// 是否启用自动备份
  final bool enableAutoBackup;
  
  /// 备份间隔（小时）
  final int backupInterval;
  
  /// 备份保留数量
  final int maxBackupCount;
  
  /// 备份路径
  final String backupPath;
  
  /// 是否压缩备份
  final bool compressBackup;
  
  /// 是否包含设置
  final bool includeSettings;
  
  /// 是否包含模板
  final bool includeTemplates;
  
  /// 是否包含项目数据
  final bool includeProjects;

  const BackupSettings({
    this.enableAutoBackup = true,
    this.backupInterval = 24,
    this.maxBackupCount = 10,
    this.backupPath = '',
    this.compressBackup = true,
    this.includeSettings = true,
    this.includeTemplates = true,
    this.includeProjects = true,
  });

  BackupSettings copyWith({
    bool? enableAutoBackup,
    int? backupInterval,
    int? maxBackupCount,
    String? backupPath,
    bool? compressBackup,
    bool? includeSettings,
    bool? includeTemplates,
    bool? includeProjects,
  }) {
    return BackupSettings(
      enableAutoBackup: enableAutoBackup ?? this.enableAutoBackup,
      backupInterval: backupInterval ?? this.backupInterval,
      maxBackupCount: maxBackupCount ?? this.maxBackupCount,
      backupPath: backupPath ?? this.backupPath,
      compressBackup: compressBackup ?? this.compressBackup,
      includeSettings: includeSettings ?? this.includeSettings,
      includeTemplates: includeTemplates ?? this.includeTemplates,
      includeProjects: includeProjects ?? this.includeProjects,
    );
  }

  @override
  List<Object?> get props => [
        enableAutoBackup,
        backupInterval,
        maxBackupCount,
        backupPath,
        compressBackup,
        includeSettings,
        includeTemplates,
        includeProjects,
      ];
}

/// 支持的语言
enum SupportedLanguage {
  /// 简体中文
  zhCN,
  /// 繁体中文
  zhTW,
  /// 英语
  en,
  /// 日语
  ja,
  /// 韩语
  ko,
}

/// 语言扩展
extension SupportedLanguageExtension on SupportedLanguage {
  /// 获取语言代码
  String get code {
    switch (this) {
      case SupportedLanguage.zhCN:
        return 'zh_CN';
      case SupportedLanguage.zhTW:
        return 'zh_TW';
      case SupportedLanguage.en:
        return 'en';
      case SupportedLanguage.ja:
        return 'ja';
      case SupportedLanguage.ko:
        return 'ko';
    }
  }

  /// 获取显示名称
  String get displayName {
    switch (this) {
      case SupportedLanguage.zhCN:
        return '简体中文';
      case SupportedLanguage.zhTW:
        return '繁体中文';
      case SupportedLanguage.en:
        return 'English';
      case SupportedLanguage.ja:
        return '日本語';
      case SupportedLanguage.ko:
        return '한국어';
    }
  }

  /// 获取本地名称
  String get nativeName {
    switch (this) {
      case SupportedLanguage.zhCN:
        return '中文（简体）';
      case SupportedLanguage.zhTW:
        return '中文（繁體）';
      case SupportedLanguage.en:
        return 'English';
      case SupportedLanguage.ja:
        return '日本語';
      case SupportedLanguage.ko:
        return '한국어';
    }
  }
}