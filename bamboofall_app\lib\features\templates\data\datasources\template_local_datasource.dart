import 'package:sqflite/sqflite.dart';
import '../models/template_model.dart';
import '../../domain/entities/prompt_template.dart';

/// 模板本地数据源
class TemplateLocalDataSource {
  final Database _database;

  TemplateLocalDataSource(this._database);

  /// 获取所有模板
  Future<List<TemplateModel>> getAllTemplates() async {
    final List<Map<String, dynamic>> maps = await _database.query('templates');
    return maps.map((map) => TemplateModel.fromJson(map)).toList();
  }

  /// 根据分类获取模板
  Future<List<TemplateModel>> getTemplatesByCategory(TemplateCategory category) async {
    final List<Map<String, dynamic>> maps = await _database.query(
      'templates',
      where: 'category = ?',
      whereArgs: [category.name],
    );
    return maps.map((map) => TemplateModel.fromJson(map)).toList();
  }

  /// 根据ID获取模板
  Future<TemplateModel?> getTemplateById(String id) async {
    final List<Map<String, dynamic>> maps = await _database.query(
      'templates',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );
    if (maps.isNotEmpty) {
      return TemplateModel.fromJson(maps.first);
    }
    return null;
  }

  /// 搜索模板
  Future<List<TemplateModel>> searchTemplates(String query) async {
    final lowerQuery = query.toLowerCase();
    final List<Map<String, dynamic>> maps = await _database.query(
      'templates',
      where: 'LOWER(name) LIKE ? OR LOWER(description) LIKE ? OR LOWER(tags) LIKE ?',
      whereArgs: ['%$lowerQuery%', '%$lowerQuery%', '%$lowerQuery%'],
    );
    return maps.map((map) => TemplateModel.fromJson(map)).toList();
  }

  /// 根据标签获取模板
  Future<List<TemplateModel>> getTemplatesByTags(List<String> tags) async {
    if (tags.isEmpty) return [];
    
    final whereClause = tags.map((_) => 'tags LIKE ?').join(' AND ');
    final whereArgs = tags.map((tag) => '%$tag%').toList();
    
    final List<Map<String, dynamic>> maps = await _database.query(
      'templates',
      where: whereClause,
      whereArgs: whereArgs,
    );
    return maps.map((map) => TemplateModel.fromJson(map)).toList();
  }

  /// 创建模板
  Future<void> createTemplate(TemplateModel template) async {
    await _database.insert('templates', template.toJson());
  }

  /// 更新模板
  Future<void> updateTemplate(TemplateModel template) async {
    await _database.update(
      'templates',
      template.toJson(),
      where: 'id = ?',
      whereArgs: [template.id],
    );
  }

  /// 删除模板
  Future<void> deleteTemplate(String id) async {
    await _database.delete(
      'templates',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// 获取内置模板
  Future<List<TemplateModel>> getBuiltInTemplates() async {
    final List<Map<String, dynamic>> maps = await _database.query(
      'templates',
      where: 'isBuiltIn = ?',
      whereArgs: [1],
    );
    return maps.map((map) => TemplateModel.fromJson(map)).toList();
  }

  /// 获取用户模板
  Future<List<TemplateModel>> getUserTemplates() async {
    final List<Map<String, dynamic>> maps = await _database.query(
      'templates',
      where: 'isBuiltIn = ?',
      whereArgs: [0],
    );
    return maps.map((map) => TemplateModel.fromJson(map)).toList();
  }

  /// 获取模板统计信息
  Future<Map<String, dynamic>> getTemplateStatistics() async {
    final allTemplates = await getAllTemplates();
    
    final categoryCount = <TemplateCategory, int>{};
    int totalUsage = 0;
    
    for (final template in allTemplates) {
      categoryCount[template.category] = (categoryCount[template.category] ?? 0) + 1;
      totalUsage += template.usageCount;
    }
    
    // 获取最近使用的模板
    final recentTemplates = allTemplates
        .where((t) => t.usageCount > 0)
        .toList()
      ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
    
    return {
      'totalTemplates': allTemplates.length,
      'userTemplates': allTemplates.where((t) => !t.isBuiltIn).length,
      'systemTemplates': allTemplates.where((t) => t.isBuiltIn).length,
      'totalUsage': totalUsage,
      'categoryCount': categoryCount,
      'recentTemplates': recentTemplates.take(5).toList(),
    };
  }

  /// 增加使用次数
  Future<void> incrementUsageCount(String templateId) async {
    await _database.rawUpdate(
      'UPDATE templates SET usageCount = usageCount + 1, updatedAt = ? WHERE id = ?',
      [DateTime.now().toIso8601String(), templateId],
    );
  }

  /// 获取热门模板
  Future<List<TemplateModel>> getPopularTemplates({int limit = 10}) async {
    final List<Map<String, dynamic>> maps = await _database.query(
      'templates',
      orderBy: 'usageCount DESC',
      limit: limit,
    );
    return maps.map((map) => TemplateModel.fromJson(map)).toList();
  }

  /// 获取最近创建的模板
  Future<List<TemplateModel>> getRecentTemplates({int limit = 10}) async {
    final List<Map<String, dynamic>> maps = await _database.query(
      'templates',
      orderBy: 'createdAt DESC',
      limit: limit,
    );
    return maps.map((map) => TemplateModel.fromJson(map)).toList();
  }

  /// 记录模板使用
  Future<void> recordTemplateUsage(TemplateUsageModel usage) async {
    await _database.insert('template_usage', usage.toJson());
  }

  /// 获取模板使用历史
  Future<List<TemplateUsageModel>> getTemplateUsageHistory(String templateId) async {
    final List<Map<String, dynamic>> maps = await _database.query(
      'template_usage',
      where: 'templateId = ?',
      whereArgs: [templateId],
      orderBy: 'usedAt DESC',
    );
    return maps.map((map) => TemplateUsageModel.fromJson(map)).toList();
  }

  /// 分享模板
  Future<void> shareTemplate(TemplateShareModel share) async {
    await _database.insert('template_shares', share.toJson());
  }

  /// 获取分享的模板
  Future<List<TemplateShareModel>> getSharedTemplates() async {
    final List<Map<String, dynamic>> maps = await _database.query(
      'template_shares',
      orderBy: 'sharedAt DESC',
    );
    return maps.map((map) => TemplateShareModel.fromJson(map)).toList();
  }

  /// 清理过期数据
  Future<void> cleanupExpiredData() async {
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
    
    // 清理30天前的使用记录
    await _database.delete(
      'template_usage',
      where: 'usedAt < ?',
      whereArgs: [thirtyDaysAgo.toIso8601String()],
    );
  }
}
