// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'version_diff.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VersionDiffImpl _$$VersionDiffImplFromJson(Map<String, dynamic> json) =>
    _$VersionDiffImpl(
      id: json['id'] as String,
      sourceVersionId: json['sourceVersionId'] as String,
      targetVersionId: json['targetVersionId'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      diffType:
          $enumDecodeNullable(_$DiffTypeEnumMap, json['diffType']) ??
          DiffType.textDiff,
      operations:
          (json['operations'] as List<dynamic>?)
              ?.map((e) => DiffOperation.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      statistics: DiffStatistics.fromJson(
        json['statistics'] as Map<String, dynamic>,
      ),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$VersionDiffImplToJson(_$VersionDiffImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sourceVersionId': instance.sourceVersionId,
      'targetVersionId': instance.targetVersionId,
      'createdAt': instance.createdAt.toIso8601String(),
      'diffType': _$DiffTypeEnumMap[instance.diffType]!,
      'operations': instance.operations,
      'statistics': instance.statistics,
      'metadata': instance.metadata,
    };

const _$DiffTypeEnumMap = {
  DiffType.textDiff: 'textDiff',
  DiffType.lineDiff: 'lineDiff',
  DiffType.wordDiff: 'wordDiff',
  DiffType.characterDiff: 'characterDiff',
  DiffType.semanticDiff: 'semanticDiff',
};

_$DiffOperationImpl _$$DiffOperationImplFromJson(Map<String, dynamic> json) =>
    _$DiffOperationImpl(
      type: $enumDecode(_$OperationTypeEnumMap, json['type']),
      position: (json['position'] as num).toInt(),
      length: (json['length'] as num?)?.toInt() ?? 0,
      content: json['content'] as String? ?? '',
      lineNumber: (json['lineNumber'] as num?)?.toInt(),
      columnNumber: (json['columnNumber'] as num?)?.toInt(),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$DiffOperationImplToJson(_$DiffOperationImpl instance) =>
    <String, dynamic>{
      'type': _$OperationTypeEnumMap[instance.type]!,
      'position': instance.position,
      'length': instance.length,
      'content': instance.content,
      'lineNumber': instance.lineNumber,
      'columnNumber': instance.columnNumber,
      'metadata': instance.metadata,
    };

const _$OperationTypeEnumMap = {
  OperationType.insert: 'insert',
  OperationType.delete: 'delete',
  OperationType.replace: 'replace',
  OperationType.move: 'move',
  OperationType.equal: 'equal',
};

_$DiffStatisticsImpl _$$DiffStatisticsImplFromJson(Map<String, dynamic> json) =>
    _$DiffStatisticsImpl(
      insertedCharacters: (json['insertedCharacters'] as num?)?.toInt() ?? 0,
      deletedCharacters: (json['deletedCharacters'] as num?)?.toInt() ?? 0,
      insertedWords: (json['insertedWords'] as num?)?.toInt() ?? 0,
      deletedWords: (json['deletedWords'] as num?)?.toInt() ?? 0,
      insertedLines: (json['insertedLines'] as num?)?.toInt() ?? 0,
      deletedLines: (json['deletedLines'] as num?)?.toInt() ?? 0,
      modifiedLines: (json['modifiedLines'] as num?)?.toInt() ?? 0,
      similarity: (json['similarity'] as num?)?.toDouble() ?? 0.0,
      changeDensity: (json['changeDensity'] as num?)?.toDouble() ?? 0.0,
      complexityScore: (json['complexityScore'] as num?)?.toInt() ?? 1,
    );

Map<String, dynamic> _$$DiffStatisticsImplToJson(
  _$DiffStatisticsImpl instance,
) => <String, dynamic>{
  'insertedCharacters': instance.insertedCharacters,
  'deletedCharacters': instance.deletedCharacters,
  'insertedWords': instance.insertedWords,
  'deletedWords': instance.deletedWords,
  'insertedLines': instance.insertedLines,
  'deletedLines': instance.deletedLines,
  'modifiedLines': instance.modifiedLines,
  'similarity': instance.similarity,
  'changeDensity': instance.changeDensity,
  'complexityScore': instance.complexityScore,
};
