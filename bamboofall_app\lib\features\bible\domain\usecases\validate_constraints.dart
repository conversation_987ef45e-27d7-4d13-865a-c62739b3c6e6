import 'package:logger/logger.dart';

import '../entities/story_bible.dart';
import '../entities/character.dart';
import '../entities/location.dart';
import '../repositories/bible_repository.dart';

/// 约束验证用例
/// 实现圣经系统的约束验证和冲突检测
class ValidateConstraintsUseCase {
  final BibleRepository _repository;
  final Logger _logger;
  
  ValidateConstraintsUseCase({
    required BibleRepository repository,
    Logger? logger,
  }) : _repository = repository,
       _logger = logger ?? Logger();
  
  // ==================== 综合验证 ====================
  
  /// 验证整个故事圣经的约束
  Future<ValidationResult> validateStoryBible(StoryBible storyBible) async {
    try {
      final issues = <ValidationIssue>[];
      
      // 验证基本信息
      await _validateStoryBibleBasics(storyBible, issues);
      
      // 验证角色约束
      await _validateCharacterConstraints(storyBible.characters, issues);
      
      // 验证地点约束
      await _validateLocationConstraints(storyBible.locations, issues);
      
      // 验证关系约束
      await _validateRelationshipConstraints(storyBible, issues);
      
      // 验证情节约束
      await _validatePlotConstraints(storyBible.plotLines, issues);
      
      // 验证时间线约束
      await _validateTimelineConstraints(storyBible, issues);
      
      _logger.d('Story bible validation completed with ${issues.length} issues');
      return ValidationResult(
        isValid: issues.where((i) => i.severity == ValidationSeverity.error).isEmpty,
        issues: issues,
      );
    } catch (e) {
      _logger.e('Failed to validate story bible: $e');
      return ValidationResult(
        isValid: false,
        issues: [
          ValidationIssue(
            type: ValidationIssueType.system,
            severity: ValidationSeverity.error,
            message: 'Validation failed: $e',
            entityId: storyBible.id,
          ),
        ],
      );
    }
  }
  
  /// 验证角色约束
  Future<ValidationResult> validateCharacter(Character character) async {
    try {
      final issues = <ValidationIssue>[];
      
      // 基本信息验证
      _validateCharacterBasics(character, issues);
      
      // 状态一致性验证
      await _validateCharacterStatusConsistency(character, issues);
      
      // 关系验证
      await _validateCharacterRelationships(character, issues);
      
      // 能力验证
      _validateCharacterAbilities(character, issues);
      
      // 目标验证
      _validateCharacterGoals(character, issues);
      
      // 弧线验证
      _validateCharacterArcs(character, issues);
      
      _logger.d('Character validation completed with ${issues.length} issues');
      return ValidationResult(
        isValid: issues.where((i) => i.severity == ValidationSeverity.error).isEmpty,
        issues: issues,
      );
    } catch (e) {
      _logger.e('Failed to validate character: $e');
      return ValidationResult(
        isValid: false,
        issues: [
          ValidationIssue(
            type: ValidationIssueType.system,
            severity: ValidationSeverity.error,
            message: 'Character validation failed: $e',
            entityId: character.id,
          ),
        ],
      );
    }
  }
  
  /// 验证地点约束
  Future<ValidationResult> validateLocation(Location location) async {
    try {
      final issues = <ValidationIssue>[];
      
      // 基本信息验证
      _validateLocationBasics(location, issues);
      
      // 层级关系验证
      await _validateLocationHierarchy(location, issues);
      
      // 地理一致性验证
      _validateLocationGeography(location, issues);
      
      // 政治结构验证
      _validateLocationPolitics(location, issues);
      
      _logger.d('Location validation completed with ${issues.length} issues');
      return ValidationResult(
        isValid: issues.where((i) => i.severity == ValidationSeverity.error).isEmpty,
        issues: issues,
      );
    } catch (e) {
      _logger.e('Failed to validate location: $e');
      return ValidationResult(
        isValid: false,
        issues: [
          ValidationIssue(
            type: ValidationIssueType.system,
            severity: ValidationSeverity.error,
            message: 'Location validation failed: $e',
            entityId: location.id,
          ),
        ],
      );
    }
  }
  
  /// 检测冲突
  Future<ConflictDetectionResult> detectConflicts(StoryBible storyBible) async {
    try {
      final conflicts = <Conflict>[];
      
      // 检测角色冲突
      await _detectCharacterConflicts(storyBible.characters, conflicts);
      
      // 检测地点冲突
      await _detectLocationConflicts(storyBible.locations, conflicts);
      
      // 检测时间线冲突
      await _detectTimelineConflicts(storyBible, conflicts);
      
      // 检测逻辑冲突
      await _detectLogicalConflicts(storyBible, conflicts);
      
      _logger.d('Conflict detection completed with ${conflicts.length} conflicts');
      return ConflictDetectionResult(
        hasConflicts: conflicts.isNotEmpty,
        conflicts: conflicts,
      );
    } catch (e) {
      _logger.e('Failed to detect conflicts: $e');
      return ConflictDetectionResult(
        hasConflicts: true,
        conflicts: [
          Conflict(
            type: ConflictType.system,
            severity: ConflictSeverity.high,
            description: 'Conflict detection failed: $e',
            involvedEntities: [],
          ),
        ],
      );
    }
  }
  
  /// 生成解决建议
  Future<List<ResolutionSuggestion>> generateResolutionSuggestions(
    List<Conflict> conflicts,
  ) async {
    try {
      final suggestions = <ResolutionSuggestion>[];
      
      for (final conflict in conflicts) {
        switch (conflict.type) {
          case ConflictType.characterStatus:
            suggestions.addAll(_generateCharacterStatusSuggestions(conflict));
            break;
          case ConflictType.locationHierarchy:
            suggestions.addAll(_generateLocationHierarchySuggestions(conflict));
            break;
          case ConflictType.timeline:
            suggestions.addAll(_generateTimelineSuggestions(conflict));
            break;
          case ConflictType.relationship:
            suggestions.addAll(_generateRelationshipSuggestions(conflict));
            break;
          case ConflictType.logic:
            suggestions.addAll(_generateLogicSuggestions(conflict));
            break;
          case ConflictType.system:
            suggestions.addAll(_generateSystemSuggestions(conflict));
            break;
        }
      }
      
      _logger.d('Generated ${suggestions.length} resolution suggestions');
      return suggestions;
    } catch (e) {
      _logger.e('Failed to generate resolution suggestions: $e');
      return [];
    }
  }
  
  // ==================== 私有验证方法 ====================
  
  /// 验证故事圣经基本信息
  Future<void> _validateStoryBibleBasics(
    StoryBible storyBible,
    List<ValidationIssue> issues,
  ) async {
    if (storyBible.title.trim().isEmpty) {
      issues.add(ValidationIssue(
        type: ValidationIssueType.required,
        severity: ValidationSeverity.error,
        message: 'Story bible title is required',
        entityId: storyBible.id,
      ));
    }
    
    if (storyBible.projectId.trim().isEmpty) {
      issues.add(ValidationIssue(
        type: ValidationIssueType.required,
        severity: ValidationSeverity.error,
        message: 'Project ID is required',
        entityId: storyBible.id,
      ));
    }
  }
  
  /// 验证角色约束
  Future<void> _validateCharacterConstraints(
    List<Character> characters,
    List<ValidationIssue> issues,
  ) async {
    final names = <String>{};
    
    for (final character in characters) {
      // 检查名称重复
      if (names.contains(character.name.toLowerCase())) {
        issues.add(ValidationIssue(
          type: ValidationIssueType.duplicate,
          severity: ValidationSeverity.error,
          message: 'Duplicate character name: ${character.name}',
          entityId: character.id,
        ));
      }
      names.add(character.name.toLowerCase());
      
      // 验证单个角色
      final result = await validateCharacter(character);
      issues.addAll(result.issues);
    }
  }
  
  /// 验证地点约束
  Future<void> _validateLocationConstraints(
    List<Location> locations,
    List<ValidationIssue> issues,
  ) async {
    for (final location in locations) {
      // 验证单个地点
      final result = await validateLocation(location);
      issues.addAll(result.issues);
    }
  }
  
  /// 验证关系约束
  Future<void> _validateRelationshipConstraints(
    StoryBible storyBible,
    List<ValidationIssue> issues,
  ) async {
    // 检查角色关系的一致性
    final _ = storyBible.characters.map((c) => c.id).toSet();
    
    for (final character in storyBible.characters) {
      for (final _ in character.relationshipIds) {
        // 这里可以添加关系验证逻辑
        // 暂时跳过具体实现
      }
    }
  }
  
  /// 验证情节约束
  Future<void> _validatePlotConstraints(
    List<PlotLine> plotLines,
    List<ValidationIssue> issues,
  ) async {
    for (final plotLine in plotLines) {
      if (plotLine.name.trim().isEmpty) {
        issues.add(ValidationIssue(
          type: ValidationIssueType.required,
          severity: ValidationSeverity.error,
          message: 'Plot line name is required',
          entityId: plotLine.id,
        ));
      }
      
      // 验证情节点顺序
      final points = plotLine.plotPoints;
      for (int i = 0; i < points.length - 1; i++) {
        if (points[i].order >= points[i + 1].order) {
          issues.add(ValidationIssue(
            type: ValidationIssueType.logic,
            severity: ValidationSeverity.warning,
            message: 'Plot points order is inconsistent in ${plotLine.name}',
            entityId: plotLine.id,
          ));
        }
      }
    }
  }
  
  /// 验证时间线约束
  Future<void> _validateTimelineConstraints(
    StoryBible storyBible,
    List<ValidationIssue> issues,
  ) async {
    // 这里可以添加时间线验证逻辑
    // 暂时跳过具体实现
  }
  
  /// 验证角色基本信息
  void _validateCharacterBasics(Character character, List<ValidationIssue> issues) {
    if (character.name.trim().isEmpty) {
      issues.add(ValidationIssue(
        type: ValidationIssueType.required,
        severity: ValidationSeverity.error,
        message: 'Character name is required',
        entityId: character.id,
      ));
    }
    
    if (character.name.length > 100) {
      issues.add(ValidationIssue(
        type: ValidationIssueType.format,
        severity: ValidationSeverity.warning,
        message: 'Character name is too long (max 100 characters)',
        entityId: character.id,
      ));
    }
  }
  
  /// 验证角色状态一致性
  Future<void> _validateCharacterStatusConsistency(
    Character character,
    List<ValidationIssue> issues,
  ) async {
    if (character.status == CharacterStatus.dead && character.currentLocationId != null) {
      issues.add(ValidationIssue(
        type: ValidationIssueType.logic,
        severity: ValidationSeverity.error,
        message: 'Dead character cannot have a current location',
        entityId: character.id,
      ));
    }
    
    if (character.status == CharacterStatus.missing && character.currentLocationId != null) {
      issues.add(ValidationIssue(
        type: ValidationIssueType.logic,
        severity: ValidationSeverity.warning,
        message: 'Missing character should not have a known current location',
        entityId: character.id,
      ));
    }
  }
  
  /// 验证角色关系
  Future<void> _validateCharacterRelationships(
    Character character,
    List<ValidationIssue> issues,
  ) async {
    // 检查关系ID是否存在
    for (final _ in character.relationshipIds) {
      // 这里可以添加关系存在性验证
      // 暂时跳过具体实现
    }
  }
  
  /// 验证角色能力
  void _validateCharacterAbilities(Character character, List<ValidationIssue> issues) {
    for (final ability in character.abilities) {
      if (ability.level < 1 || ability.level > 10) {
        issues.add(ValidationIssue(
          type: ValidationIssueType.range,
          severity: ValidationSeverity.warning,
          message: 'Ability level should be between 1 and 10: ${ability.name}',
          entityId: character.id,
        ));
      }
    }
  }
  
  /// 验证角色目标
  void _validateCharacterGoals(Character character, List<ValidationIssue> issues) {
    final activeGoals = character.goals.where((g) => g.status == GoalStatus.active);
    if (activeGoals.length > 5) {
      issues.add(ValidationIssue(
        type: ValidationIssueType.logic,
        severity: ValidationSeverity.warning,
        message: 'Character has too many active goals (${activeGoals.length})',
        entityId: character.id,
      ));
    }
  }
  
  /// 验证角色弧线
  void _validateCharacterArcs(Character character, List<ValidationIssue> issues) {
    for (final arc in character.characterArcs) {
      if (arc.milestones.isEmpty) {
        issues.add(ValidationIssue(
          type: ValidationIssueType.logic,
          severity: ValidationSeverity.warning,
          message: 'Character arc has no milestones: ${arc.title}',
          entityId: character.id,
        ));
      }
    }
  }
  
  /// 验证地点基本信息
  void _validateLocationBasics(Location location, List<ValidationIssue> issues) {
    if (location.name.trim().isEmpty) {
      issues.add(ValidationIssue(
        type: ValidationIssueType.required,
        severity: ValidationSeverity.error,
        message: 'Location name is required',
        entityId: location.id,
      ));
    }
  }
  
  /// 验证地点层级
  Future<void> _validateLocationHierarchy(
    Location location,
    List<ValidationIssue> issues,
  ) async {
    // 检查自引用
    if (location.parentLocationIds.contains(location.id)) {
      issues.add(ValidationIssue(
        type: ValidationIssueType.logic,
        severity: ValidationSeverity.error,
        message: 'Location cannot be its own parent',
        entityId: location.id,
      ));
    }
    
    // 检查循环引用
    if (await _hasLocationCircularReference(location)) {
      issues.add(ValidationIssue(
        type: ValidationIssueType.logic,
        severity: ValidationSeverity.error,
        message: 'Location has circular reference in hierarchy',
        entityId: location.id,
      ));
    }
  }
  
  /// 验证地点地理
  void _validateLocationGeography(Location location, List<ValidationIssue> issues) {
    final geography = location.geography;
    if (geography != null) {
      if (geography.elevation != null && geography.elevation! < -500) {
        issues.add(ValidationIssue(
          type: ValidationIssueType.range,
          severity: ValidationSeverity.warning,
          message: 'Location elevation seems unusually low',
          entityId: location.id,
        ));
      }
    }
  }
  
  /// 验证地点政治
  void _validateLocationPolitics(Location location, List<ValidationIssue> issues) {
    final politics = location.politics;
    if (politics != null) {
      for (final leader in politics.leaders) {
        if (leader.influence != null && (leader.influence! < 1 || leader.influence! > 10)) {
          issues.add(ValidationIssue(
            type: ValidationIssueType.range,
            severity: ValidationSeverity.warning,
            message: 'Political figure influence should be between 1 and 10',
            entityId: location.id,
          ));
        }
      }
    }
  }
  
  /// 检查地点循环引用
  Future<bool> _hasLocationCircularReference(Location location) async {
    final visited = <String>{};
    return await _checkLocationCircularReference(location.id, visited);
  }
  
  /// 递归检查地点循环引用
  Future<bool> _checkLocationCircularReference(String locationId, Set<String> visited) async {
    if (visited.contains(locationId)) {
      return true;
    }
    
    visited.add(locationId);
    
    final location = await _repository.getLocation(locationId);
    if (location == null) {
      visited.remove(locationId);
      return false;
    }
    
    for (final parentId in location.parentLocationIds) {
      if (await _checkLocationCircularReference(parentId, visited)) {
        return true;
      }
    }
    
    visited.remove(locationId);
    return false;
  }
  
  // ==================== 冲突检测方法 ====================
  
  /// 检测角色冲突
  Future<void> _detectCharacterConflicts(
    List<Character> characters,
    List<Conflict> conflicts,
  ) async {
    // 检测同名角色
    final nameGroups = <String, List<Character>>{};
    for (final character in characters) {
      final name = character.name.toLowerCase();
      nameGroups[name] = (nameGroups[name] ?? [])..add(character);
    }
    
    for (final entry in nameGroups.entries) {
      if (entry.value.length > 1) {
        conflicts.add(Conflict(
          type: ConflictType.characterStatus,
          severity: ConflictSeverity.high,
          description: 'Multiple characters with the same name: ${entry.key}',
          involvedEntities: entry.value.map((c) => c.id).toList(),
        ));
      }
    }
  }
  
  /// 检测地点冲突
  Future<void> _detectLocationConflicts(
    List<Location> locations,
    List<Conflict> conflicts,
  ) async {
    // 检测地点层级冲突
    for (final location in locations) {
      if (await _hasLocationCircularReference(location)) {
        conflicts.add(Conflict(
          type: ConflictType.locationHierarchy,
          severity: ConflictSeverity.high,
          description: 'Circular reference in location hierarchy: ${location.name}',
          involvedEntities: [location.id],
        ));
      }
    }
  }
  
  /// 检测时间线冲突
  Future<void> _detectTimelineConflicts(
    StoryBible storyBible,
    List<Conflict> conflicts,
  ) async {
    // 这里可以添加时间线冲突检测逻辑
    // 暂时跳过具体实现
  }
  
  /// 检测逻辑冲突
  Future<void> _detectLogicalConflicts(
    StoryBible storyBible,
    List<Conflict> conflicts,
  ) async {
    // 检测死亡角色在活动中的冲突
    for (final character in storyBible.characters) {
      if (character.status == CharacterStatus.dead && character.currentLocationId != null) {
        conflicts.add(Conflict(
          type: ConflictType.logic,
          severity: ConflictSeverity.medium,
          description: 'Dead character has current location: ${character.name}',
          involvedEntities: [character.id],
        ));
      }
    }
  }
  
  // ==================== 解决建议生成方法 ====================
  
  /// 生成角色状态建议
  List<ResolutionSuggestion> _generateCharacterStatusSuggestions(Conflict conflict) {
    return [
      ResolutionSuggestion(
        type: ResolutionType.modify,
        description: 'Rename one of the duplicate characters',
        priority: ResolutionPriority.high,
        entityIds: conflict.involvedEntities,
      ),
      ResolutionSuggestion(
        type: ResolutionType.merge,
        description: 'Merge the duplicate characters if they represent the same entity',
        priority: ResolutionPriority.medium,
        entityIds: conflict.involvedEntities,
      ),
    ];
  }
  
  /// 生成地点层级建议
  List<ResolutionSuggestion> _generateLocationHierarchySuggestions(Conflict conflict) {
    return [
      ResolutionSuggestion(
        type: ResolutionType.modify,
        description: 'Remove circular reference by updating parent-child relationships',
        priority: ResolutionPriority.high,
        entityIds: conflict.involvedEntities,
      ),
    ];
  }
  
  /// 生成时间线建议
  List<ResolutionSuggestion> _generateTimelineSuggestions(Conflict conflict) {
    return [
      ResolutionSuggestion(
        type: ResolutionType.modify,
        description: 'Adjust event dates to resolve timeline conflicts',
        priority: ResolutionPriority.medium,
        entityIds: conflict.involvedEntities,
      ),
    ];
  }
  
  /// 生成关系建议
  List<ResolutionSuggestion> _generateRelationshipSuggestions(Conflict conflict) {
    return [
      ResolutionSuggestion(
        type: ResolutionType.modify,
        description: 'Update relationship definitions to resolve conflicts',
        priority: ResolutionPriority.medium,
        entityIds: conflict.involvedEntities,
      ),
    ];
  }
  
  /// 生成逻辑建议
  List<ResolutionSuggestion> _generateLogicSuggestions(Conflict conflict) {
    return [
      ResolutionSuggestion(
        type: ResolutionType.modify,
        description: 'Update character status or location to resolve logical inconsistency',
        priority: ResolutionPriority.high,
        entityIds: conflict.involvedEntities,
      ),
    ];
  }
  
  /// 生成系统建议
  List<ResolutionSuggestion> _generateSystemSuggestions(Conflict conflict) {
    return [
      ResolutionSuggestion(
        type: ResolutionType.review,
        description: 'Review system configuration and data integrity',
        priority: ResolutionPriority.low,
        entityIds: conflict.involvedEntities,
      ),
    ];
  }
}

/// 验证结果
class ValidationResult {
  final bool isValid;
  final List<ValidationIssue> issues;
  
  const ValidationResult({
    required this.isValid,
    required this.issues,
  });
  
  /// 获取错误数量
  int get errorCount => issues.where((i) => i.severity == ValidationSeverity.error).length;
  
  /// 获取警告数量
  int get warningCount => issues.where((i) => i.severity == ValidationSeverity.warning).length;
  
  /// 获取信息数量
  int get infoCount => issues.where((i) => i.severity == ValidationSeverity.info).length;
}

/// 验证问题
class ValidationIssue {
  final ValidationIssueType type;
  final ValidationSeverity severity;
  final String message;
  final String entityId;
  final String? field;
  final dynamic value;
  
  const ValidationIssue({
    required this.type,
    required this.severity,
    required this.message,
    required this.entityId,
    this.field,
    this.value,
  });
}

/// 验证问题类型
enum ValidationIssueType {
  required,
  format,
  range,
  duplicate,
  logic,
  reference,
  system,
}

/// 验证严重程度
enum ValidationSeverity {
  error,
  warning,
  info,
}

/// 冲突检测结果
class ConflictDetectionResult {
  final bool hasConflicts;
  final List<Conflict> conflicts;
  
  const ConflictDetectionResult({
    required this.hasConflicts,
    required this.conflicts,
  });
}

/// 冲突
class Conflict {
  final ConflictType type;
  final ConflictSeverity severity;
  final String description;
  final List<String> involvedEntities;
  final Map<String, dynamic>? metadata;
  
  const Conflict({
    required this.type,
    required this.severity,
    required this.description,
    required this.involvedEntities,
    this.metadata,
  });
}

/// 冲突类型
enum ConflictType {
  characterStatus,
  locationHierarchy,
  timeline,
  relationship,
  logic,
  system,
}

/// 冲突严重程度
enum ConflictSeverity {
  low,
  medium,
  high,
  critical,
}

/// 解决建议
class ResolutionSuggestion {
  final ResolutionType type;
  final String description;
  final ResolutionPriority priority;
  final List<String> entityIds;
  final Map<String, dynamic>? parameters;
  
  const ResolutionSuggestion({
    required this.type,
    required this.description,
    required this.priority,
    required this.entityIds,
    this.parameters,
  });
}

/// 解决类型
enum ResolutionType {
  modify,
  delete,
  merge,
  split,
  review,
}

/// 解决优先级
enum ResolutionPriority {
  low,
  medium,
  high,
  critical,
}