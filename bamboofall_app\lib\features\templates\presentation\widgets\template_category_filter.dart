import 'package:flutter/material.dart';
import '../../domain/entities/prompt_template.dart';

/// 模板分类过滤器
class TemplateCategoryFilter extends StatelessWidget {
  final TemplateCategory? selectedCategory;
  final Function(TemplateCategory?) onCategoryChanged;
  final bool showAllOption;

  const TemplateCategoryFilter({
    super.key,
    this.selectedCategory,
    required this.onCategoryChanged,
    this.showAllOption = true,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          if (showAllOption)
            _buildCategoryChip(
              context,
              '全部',
              null,
              selectedCategory == null,
            ),
          ...TemplateCategory.values.map((category) {
            return _buildCategoryChip(
              context,
              category.displayName,
              category,
              selectedCategory == category,
            );
          }),
        ],
      ),
    );
  }

  /// 构建分类标签
  Widget _buildCategoryChip(
    BuildContext context,
    String label,
    TemplateCategory? category,
    bool isSelected,
  ) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          onCategoryChanged(selected ? category : null);
        },
        backgroundColor: Colors.transparent,
        selectedColor: _getCategoryColor(category).withValues(alpha: 0.2),
        checkmarkColor: _getCategoryColor(category),
        side: BorderSide(
          color: isSelected
              ? _getCategoryColor(category)
              : Colors.grey.withValues(alpha: 0.3),
        ),
        labelStyle: TextStyle(
          color: isSelected
              ? _getCategoryColor(category)
              : Theme.of(context).colorScheme.onSurface,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
    );
  }

  /// 获取分类颜色
  Color _getCategoryColor(TemplateCategory? category) {
    if (category == null) return Colors.blue;
    
    switch (category) {
      case TemplateCategory.character:
        return Colors.purple;
      case TemplateCategory.plot:
        return Colors.orange;
      case TemplateCategory.dialogue:
        return Colors.green;
      case TemplateCategory.scene:
        return Colors.blue;
      case TemplateCategory.worldBuilding:
        return Colors.red;
      case TemplateCategory.polish:
        return Colors.teal;
      case TemplateCategory.inspiration:
        return Colors.pink;
      case TemplateCategory.writing:
        return Colors.indigo;
      case TemplateCategory.custom:
        return Colors.grey;
    }
  }
}

/// 分类统计过滤器
class CategoryStatisticsFilter extends StatelessWidget {
  final Map<TemplateCategory, int> categoryCount;
  final TemplateCategory? selectedCategory;
  final Function(TemplateCategory?) onCategoryChanged;
  final bool showAllOption;

  const CategoryStatisticsFilter({
    super.key,
    required this.categoryCount,
    this.selectedCategory,
    required this.onCategoryChanged,
    this.showAllOption = true,
  });

  @override
  Widget build(BuildContext context) {
    final totalCount = categoryCount.values.fold(0, (sum, count) => sum + count);

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          if (showAllOption)
            _buildStatisticsChip(
              context,
              '全部',
              totalCount,
              null,
              selectedCategory == null,
            ),
          ...categoryCount.entries.map((entry) {
            return _buildStatisticsChip(
              context,
              entry.key.displayName,
              entry.value,
              entry.key,
              selectedCategory == entry.key,
            );
          }),
        ],
      ),
    );
  }

  /// 构建统计标签
  Widget _buildStatisticsChip(
    BuildContext context,
    String label,
    int count,
    TemplateCategory? category,
    bool isSelected,
  ) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(label),
            const SizedBox(width: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: isSelected
                    ? Colors.white
                    : _getCategoryColor(category).withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                count.toString(),
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: isSelected
                      ? _getCategoryColor(category)
                      : _getCategoryColor(category).withValues(alpha: 0.8),
                ),
              ),
            ),
          ],
        ),
        selected: isSelected,
        onSelected: (selected) {
          onCategoryChanged(selected ? category : null);
        },
        backgroundColor: Colors.transparent,
        selectedColor: _getCategoryColor(category).withValues(alpha: 0.2),
        checkmarkColor: _getCategoryColor(category),
        side: BorderSide(
          color: isSelected
              ? _getCategoryColor(category)
              : Colors.grey.withValues(alpha: 0.3),
        ),
        labelStyle: TextStyle(
          color: isSelected
              ? _getCategoryColor(category)
              : Theme.of(context).colorScheme.onSurface,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
    );
  }

  /// 获取分类颜色
  Color _getCategoryColor(TemplateCategory? category) {
    if (category == null) return Colors.blue;
    
    switch (category) {
      case TemplateCategory.character:
        return Colors.purple;
      case TemplateCategory.plot:
        return Colors.orange;
      case TemplateCategory.dialogue:
        return Colors.green;
      case TemplateCategory.scene:
        return Colors.blue;
      case TemplateCategory.worldBuilding:
        return Colors.red;
      case TemplateCategory.polish:
        return Colors.teal;
      case TemplateCategory.inspiration:
        return Colors.pink;
      case TemplateCategory.writing:
        return Colors.indigo;
      case TemplateCategory.custom:
        return Colors.grey;
    }
  }
}

/// 分类网格选择器
class CategoryGridSelector extends StatelessWidget {
  final TemplateCategory? selectedCategory;
  final Function(TemplateCategory?) onCategoryChanged;
  final Map<TemplateCategory, int>? categoryCount;

  const CategoryGridSelector({
    super.key,
    this.selectedCategory,
    required this.onCategoryChanged,
    this.categoryCount,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: TemplateCategory.values.length,
      itemBuilder: (context, index) {
        final category = TemplateCategory.values[index];
        final count = categoryCount?[category] ?? 0;
        final isSelected = selectedCategory == category;

        return InkWell(
          onTap: () {
            onCategoryChanged(isSelected ? null : category);
          },
          borderRadius: BorderRadius.circular(12),
          child: Container(
            decoration: BoxDecoration(
              color: isSelected
                  ? _getCategoryColor(category).withValues(alpha: 0.2)
                  : Colors.transparent,
              border: Border.all(
                color: isSelected
                    ? _getCategoryColor(category)
                    : Colors.grey.withValues(alpha: 0.3),
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _getCategoryIcon(category),
                  color: _getCategoryColor(category),
                  size: 24,
                ),
                const SizedBox(height: 4),
                Text(
                  category.displayName,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected
                        ? _getCategoryColor(category)
                        : Theme.of(context).colorScheme.onSurface,
                  ),
                  textAlign: TextAlign.center,
                ),
                if (categoryCount != null)
                  Text(
                    '$count 个',
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey.shade600,
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 获取分类图标
  IconData _getCategoryIcon(TemplateCategory category) {
    switch (category) {
      case TemplateCategory.character:
        return Icons.person;
      case TemplateCategory.plot:
        return Icons.timeline;
      case TemplateCategory.dialogue:
        return Icons.chat_bubble;
      case TemplateCategory.scene:
        return Icons.landscape;
      case TemplateCategory.worldBuilding:
        return Icons.public;
      case TemplateCategory.polish:
        return Icons.auto_fix_high;
      case TemplateCategory.inspiration:
        return Icons.lightbulb;
      case TemplateCategory.writing:
        return Icons.edit;
      case TemplateCategory.custom:
        return Icons.extension;
    }
  }

  /// 获取分类颜色
  Color _getCategoryColor(TemplateCategory category) {
    switch (category) {
      case TemplateCategory.character:
        return Colors.purple;
      case TemplateCategory.plot:
        return Colors.orange;
      case TemplateCategory.dialogue:
        return Colors.green;
      case TemplateCategory.scene:
        return Colors.blue;
      case TemplateCategory.worldBuilding:
        return Colors.red;
      case TemplateCategory.polish:
        return Colors.teal;
      case TemplateCategory.inspiration:
        return Colors.pink;
      case TemplateCategory.writing:
        return Colors.indigo;
      case TemplateCategory.custom:
        return Colors.grey;
    }
  }
}