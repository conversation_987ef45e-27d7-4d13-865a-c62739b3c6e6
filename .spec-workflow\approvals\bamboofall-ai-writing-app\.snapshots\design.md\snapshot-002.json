{"id": "snapshot_1758814035379_vca1lm6b0", "approvalId": "approval_1758814027760_04b7n8vx9", "approvalTitle": "笔落APP技术设计文档审批", "version": 2, "timestamp": "2025-09-25T15:27:15.379Z", "trigger": "approved", "status": "pending", "content": "# Design Document\r\n\r\n## Overview\r\n\r\n笔落（BambooFall）是一款基于Flutter 3.24+的跨平台桌面AI辅助小说创作应用。该应用采用Clean Architecture分层架构，以\"圣经系统\"（Story Bible）为核心，集成多AI模型，提供结构化的小说创作工作流。设计遵循本地优先、人机协同、结构化约束、渐进增强的原则，确保数据安全、用户体验和系统可扩展性。\r\n\r\n## Steering Document Alignment\r\n\r\n### Technical Standards (tech.md)\r\n由于项目尚未建立steering文档，设计将遵循以下技术标准：\r\n- Flutter Clean Architecture模式（presentation, domain, data三层分离）\r\n- SOLID原则和依赖注入\r\n- 响应式编程和状态管理最佳实践\r\n- 本地优先的数据存储策略\r\n- 模块化和可测试的代码结构\r\n\r\n### Project Structure (structure.md)\r\n实现将遵循标准Flutter项目组织约定：\r\n- 功能模块化组织（features/）\r\n- 共享组件和工具分离（shared/）\r\n- 核心基础设施独立（core/）\r\n- 清晰的依赖关系和接口定义\r\n\r\n## Code Reuse Analysis\r\n\r\n### Existing Components to Leverage\r\n由于这是新项目，将建立以下可重用的基础组件：\r\n- **Flutter Framework**: 利用Flutter 3.24+的桌面应用能力\r\n- **Riverpod**: 用于状态管理和依赖注入\r\n- **Isar Database**: 高性能本地数据存储\r\n- **Dio HTTP Client**: 网络请求和AI模型集成\r\n- **Super Editor**: 富文本编辑功能\r\n\r\n### Integration Points\r\n- **AI Service APIs**: 通过统一接口集成多个AI服务提供商\r\n- **Local File System**: 项目文件和配置的本地存储\r\n- **Secure Storage**: API密钥和敏感信息的加密存储\r\n- **Cross-Platform APIs**: 桌面平台特定功能的集成\r\n\r\n## Architecture\r\n\r\n应用采用Clean Architecture分层架构，结合Flutter最佳实践，确保代码的可维护性、可测试性和可扩展性。\r\n\r\n### Modular Design Principles\r\n- **Single File Responsibility**: 每个文件专注于单一职责和领域\r\n- **Component Isolation**: 创建小而专注的组件，避免大型单体文件\r\n- **Service Layer Separation**: 分离数据访问、业务逻辑和表现层\r\n- **Utility Modularity**: 将工具类分解为专注的单一目的模块\r\n\r\n```mermaid\r\ngraph TD\r\n    A[Presentation Layer] --> B[Domain Layer]\r\n    B --> C[Data Layer]\r\n    \r\n    A1[Widgets/Pages] --> A\r\n    A2[Providers/Controllers] --> A\r\n    A3[UI Components] --> A\r\n    \r\n    B1[Entities] --> B\r\n    B2[Use Cases] --> B\r\n    B3[Repository Interfaces] --> B\r\n    \r\n    C1[Repository Implementations] --> C\r\n    C2[Data Sources] --> C\r\n    C3[Models/DTOs] --> C\r\n    \r\n    D[External Services] --> C\r\n    D1[AI APIs] --> D\r\n    D2[Local Storage] --> D\r\n    D3[File System] --> D\r\n```\r\n\r\n## Components and Interfaces\r\n\r\n### AI Integration Service\r\n- **Purpose:** 统一管理多AI模型的接入和调用\r\n- **Interfaces:** \r\n  - `generateText(prompt, model, parameters)` - 文本生成\r\n  - `testConnection(model)` - 连接测试\r\n  - `switchModel(modelId)` - 模型切换\r\n- **Dependencies:** Dio HTTP客户端、配置管理服务\r\n- **Reuses:** 网络请求基础设施、错误处理机制\r\n\r\n### Story Bible Management Service\r\n- **Purpose:** 管理世界观、角色、地点等创作元素\r\n- **Interfaces:**\r\n  - `createBibleElement(type, data)` - 创建圣经元素\r\n  - `updateBibleElement(id, data)` - 更新圣经元素\r\n  - `validateConstraints(content)` - 约束验证\r\n  - `detectConflicts(newContent)` - 冲突检测\r\n- **Dependencies:** 本地数据库、搜索引擎\r\n- **Reuses:** 数据持久化层、验证工具\r\n\r\n### Project Management Service\r\n- **Purpose:** 管理多个创作项目的生命周期\r\n- **Interfaces:**\r\n  - `createProject(template, settings)` - 创建项目\r\n  - `loadProject(projectId)` - 加载项目\r\n  - `switchProject(projectId)` - 切换项目\r\n  - `exportProject(projectId, options)` - 导出项目\r\n- **Dependencies:** 文件系统、数据库\r\n- **Reuses:** 文件操作工具、数据序列化\r\n\r\n### Writing Workspace Controller\r\n- **Purpose:** 管理三栏工作台的状态和交互\r\n- **Interfaces:**\r\n  - `updateChapterContent(content)` - 更新章节内容\r\n  - `generateContent(type, context)` - 生成内容\r\n  - `reviewContent(candidateContent)` - 审阅内容\r\n  - `saveVersion(content, metadata)` - 保存版本\r\n- **Dependencies:** AI服务、圣经系统、版本控制\r\n- **Reuses:** 状态管理、UI组件\r\n\r\n### Version Control Service\r\n- **Purpose:** 管理章节内容的版本历史和审阅流程\r\n- **Interfaces:**\r\n  - `createVersion(content, metadata)` - 创建版本\r\n  - `compareVersions(v1, v2)` - 版本对比\r\n  - `rollbackToVersion(versionId)` - 回滚版本\r\n  - `mergeChanges(changes)` - 合并变更\r\n- **Dependencies:** 本地存储、差异算法\r\n- **Reuses:** 文件操作、数据比较工具\r\n\r\n## Data Models\r\n\r\n### Project Model\r\n```dart\r\nclass Project {\r\n  final String id;\r\n  final String name;\r\n  final String description;\r\n  final List<String> tags;\r\n  final String author;\r\n  final ProjectStatus status;\r\n  final DateTime createdAt;\r\n  final DateTime updatedAt;\r\n  final int wordCount;\r\n  final int targetWordCount;\r\n  final ProjectSettings settings;\r\n}\r\n```\r\n\r\n### Story Bible Model\r\n```dart\r\nclass StoryBible {\r\n  final String id;\r\n  final String projectId;\r\n  final WorldSettings worldSettings;\r\n  final List<Character> characters;\r\n  final List<Location> locations;\r\n  final List<Item> items;\r\n  final PlotStructure plot;\r\n  final List<CanonRule> canonRules;\r\n}\r\n```\r\n\r\n### Chapter Model\r\n```dart\r\nclass Chapter {\r\n  final String id;\r\n  final String projectId;\r\n  final String title;\r\n  final int order;\r\n  final ChapterStatus status;\r\n  final String content;\r\n  final int wordCount;\r\n  final String notes;\r\n  final ChapterGoals goals;\r\n  final List<ChapterVersion> versionHistory;\r\n  final ChapterMetadata metadata;\r\n}\r\n```\r\n\r\n### AI Model Configuration\r\n```dart\r\nclass AIModelConfig {\r\n  final String id;\r\n  final String name;\r\n  final String provider;\r\n  final String apiUrl;\r\n  final String encryptedApiKey;\r\n  final Map<String, dynamic> parameters;\r\n  final List<String> capabilities;\r\n  final bool isActive;\r\n}\r\n```\r\n\r\n### Prompt Template Model\r\n```dart\r\nclass PromptTemplate {\r\n  final String id;\r\n  final String name;\r\n  final TemplateCategory category;\r\n  final String description;\r\n  final String template;\r\n  final List<TemplateVariable> variables;\r\n  final List<String> tags;\r\n  final DateTime createdAt;\r\n  final DateTime updatedAt;\r\n  final bool isSystem;\r\n}\r\n```\r\n\r\n## Error Handling\r\n\r\n### Error Scenarios\r\n1. **AI API调用失败**\r\n   - **Handling:** 自动重试机制，降级到备用模型，显示用户友好错误信息\r\n   - **User Impact:** 显示\"AI服务暂时不可用，正在尝试备用服务\"提示\r\n\r\n2. **本地数据损坏**\r\n   - **Handling:** 自动备份恢复，数据完整性检查，用户确认恢复选项\r\n   - **User Impact:** 显示数据恢复选项，最小化数据丢失\r\n\r\n3. **网络连接中断**\r\n   - **Handling:** 离线模式切换，本地缓存利用，连接恢复自动同步\r\n   - **User Impact:** 无缝切换到离线模式，保持基本功能可用\r\n\r\n4. **存储空间不足**\r\n   - **Handling:** 清理临时文件，压缩历史数据，提示用户清理选项\r\n   - **User Impact:** 显示存储管理界面，引导用户释放空间\r\n\r\n5. **圣经系统约束冲突**\r\n   - **Handling:** 实时冲突检测，提供解决建议，允许用户选择处理方式\r\n   - **User Impact:** 高亮冲突内容，提供修复建议和忽略选项\r\n\r\n## Testing Strategy\r\n\r\n### Unit Testing\r\n- **Repository层测试**: 数据访问逻辑、CRUD操作、数据转换\r\n- **Use Case测试**: 业务逻辑、约束验证、错误处理\r\n- **Service层测试**: AI集成、文件操作、加密解密\r\n- **工具类测试**: 数据验证、格式转换、算法逻辑\r\n- **目标覆盖率**: >80%\r\n\r\n### Integration Testing\r\n- **AI服务集成**: 多模型切换、参数传递、响应处理\r\n- **数据库集成**: 数据持久化、查询性能、事务处理\r\n- **文件系统集成**: 项目导入导出、备份恢复、权限管理\r\n- **跨组件通信**: 状态同步、事件传递、依赖注入\r\n\r\n### End-to-End Testing\r\n- **完整创作流程**: 项目创建→圣经设置→章节创作→AI生成→审阅保存\r\n- **多项目管理**: 项目切换、数据隔离、并发操作\r\n- **错误恢复场景**: 网络中断恢复、应用崩溃恢复、数据损坏恢复\r\n- **性能测试**: 大项目加载、长时间运行、内存使用\r\n\r\n### Performance Testing\r\n- **启动性能**: 应用启动时间<3秒，项目加载时间<2秒\r\n- **响应性能**: UI操作响应<200ms，AI生成开始<5秒\r\n- **内存性能**: 正常使用<500MB，峰值<1GB\r\n- **存储性能**: 大文件读写、数据库查询、索引构建\r\n\r\n### Security Testing\r\n- **数据加密**: API密钥加密存储、敏感数据保护\r\n- **访问控制**: 文件权限、数据隔离、用户认证\r\n- **网络安全**: HTTPS通信、证书验证、请求签名\r\n- **隐私保护**: 本地存储验证、数据不泄露、日志脱敏", "fileStats": {"size": 9175, "lines": 258, "lastModified": "2025-09-25T15:26:58.339Z"}, "comments": []}