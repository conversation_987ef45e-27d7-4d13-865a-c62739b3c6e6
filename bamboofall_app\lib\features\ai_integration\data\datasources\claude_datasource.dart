import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

import '../../domain/entities/ai_model.dart';
import '../../domain/repositories/llm_repository.dart';

/// Claude数据源
/// 实现Anthropic Claude API的具体集成
class ClaudeDataSource {
  final Dio _dio;
  final Logger _logger;
  
  static const String _baseUrl = 'https://api.anthropic.com/v1';
  static const String _messagesEndpoint = '/messages';
  static const String _apiVersion = '2023-06-01';
  
  ClaudeDataSource({
    Dio? dio,
    Logger? logger,
  }) : _dio = dio ?? Dio(),
       _logger = logger ?? Logger() {
    _setupDio();
  }
  
  /// 配置Dio实例
  void _setupDio() {
    _dio.options.baseUrl = _baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 60);
    _dio.options.sendTimeout = const Duration(seconds: 30);
    
    // 设置默认头部
    _dio.options.headers['anthropic-version'] = _apiVersion;
    _dio.options.headers['content-type'] = 'application/json';
    
    // 添加拦截器
    _dio.interceptors.add(LogInterceptor(
      requestBody: false, // 不记录请求体，避免泄露敏感信息
      responseBody: false, // 不记录响应体，避免日志过长
      logPrint: (obj) => _logger.d(obj),
    ));
    
    // 添加重试拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onError: (error, handler) async {
        if (_shouldRetry(error)) {
          try {
            final response = await _dio.fetch(error.requestOptions);
            handler.resolve(response);
            return;
          } catch (e) {
            // 重试失败，继续抛出原错误
          }
        }
        handler.next(error);
      },
    ));
  }
  
  /// 判断是否应该重试
  bool _shouldRetry(DioException error) {
    if (error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.receiveTimeout ||
        error.type == DioExceptionType.sendTimeout) {
      return true;
    }
    
    if (error.response?.statusCode == 429 || // Rate limit
        error.response?.statusCode == 502 || // Bad gateway
        error.response?.statusCode == 503 || // Service unavailable
        error.response?.statusCode == 504) { // Gateway timeout
      return true;
    }
    
    return false;
  }
  
  /// 设置API密钥
  void setApiKey(String apiKey) {
    _dio.options.headers['x-api-key'] = apiKey;
  }
  
  /// 发送消息
  Future<AIResponse> sendMessage(AIRequest request) async {
    try {
      _logger.d('Sending message request to Claude');
      
      final requestData = _buildMessageRequest(request);
      final response = await _dio.post(_messagesEndpoint, data: requestData);
      
      return _parseMessageResponse(response.data, request.requestId);
    } on DioException catch (e) {
      _logger.e('Claude message error: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      _logger.e('Unexpected error in send message: $e');
      throw LLMRepositoryException('Unexpected error: $e');
    }
  }
  
  /// 流式发送消息
  Stream<AIResponse> sendMessageStream(AIRequest request) async* {
    try {
      _logger.d('Sending streaming message request to Claude');
      
      final requestData = _buildMessageRequest(request, stream: true);
      
      final response = await _dio.post(
        _messagesEndpoint,
        data: requestData,
        options: Options(
          responseType: ResponseType.stream,
          headers: {'Accept': 'text/event-stream'},
        ),
      );
      
      await for (final chunk in _parseStreamResponse(response.data.stream)) {
        if (chunk != null) {
          yield chunk;
        }
      }
    } on DioException catch (e) {
      _logger.e('Claude streaming message error: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      _logger.e('Unexpected error in streaming send message: $e');
      throw LLMRepositoryException('Unexpected error: $e');
    }
  }
  
  /// 构建消息请求
  Map<String, dynamic> _buildMessageRequest(AIRequest request, {bool stream = false}) {
    // 构建消息列表
    final messages = <Map<String, dynamic>>[];
    
    if (request.messages != null && request.messages!.isNotEmpty) {
      // 使用提供的消息列表
      for (final msg in request.messages!) {
        if (msg.role != 'system') { // Claude的system消息单独处理
          messages.add({
            'role': msg.role == 'assistant' ? 'assistant' : 'user',
            'content': msg.content,
          });
        }
      }
    } else {
      // 使用单个prompt
      messages.add({
        'role': 'user',
        'content': request.prompt,
      });
    }
    
    final requestData = {
      'model': request.modelId,
      'messages': messages,
      'max_tokens': request.maxTokens,
      'temperature': request.temperature,
      'top_p': request.topP,
      if (request.systemPrompt != null) 'system': request.systemPrompt,
      if (request.stop != null && request.stop!.isNotEmpty) 'stop_sequences': request.stop,
      'stream': stream,
    };
    
    return requestData;
  }
  
  /// 解析消息响应
  AIResponse _parseMessageResponse(Map<String, dynamic> data, String? requestId) {
    final content = data['content'] as List;
    final textContent = content.firstWhere(
      (item) => item['type'] == 'text',
      orElse: () => {'text': ''},
    );
    
    final usage = data['usage'];
    
    return AIResponse(
      id: data['id'],
      modelId: data['model'],
      content: textContent['text'] ?? '',
      usage: AIUsage(
        promptTokens: usage['input_tokens'],
        completionTokens: usage['output_tokens'],
        totalTokens: usage['input_tokens'] + usage['output_tokens'],
      ),
      finishReason: data['stop_reason'],
      choices: [
        AIChoice(
          index: 0,
          message: AIMessage(
            role: data['role'],
            content: textContent['text'] ?? '',
          ),
          finishReason: data['stop_reason'],
        ),
      ],
      createdAt: DateTime.now(),
    );
  }
  
  /// 解析流式响应
  Stream<AIResponse?> _parseStreamResponse(Stream<List<int>> stream) async* {
    String buffer = '';
    
    await for (final chunk in stream) {
      buffer += utf8.decode(chunk);
      final lines = buffer.split('\n');
      buffer = lines.removeLast(); // 保留最后一行（可能不完整）
      
      for (final line in lines) {
        if (line.startsWith('data: ')) {
          final data = line.substring(6).trim();
          if (data == '[DONE]') {
            return;
          }
          
          try {
            final json = jsonDecode(data);
            
            if (json['type'] == 'content_block_delta') {
              final delta = json['delta'];
              if (delta['type'] == 'text_delta') {
                yield AIResponse(
                  id: 'stream',
                  modelId: 'claude',
                  content: delta['text'] ?? '',
                  usage: const AIUsage(
                    promptTokens: 0,
                    completionTokens: 0,
                    totalTokens: 0,
                  ),
                  createdAt: DateTime.now(),
                );
              }
            } else if (json['type'] == 'message_delta') {
              final delta = json['delta'];
              if (delta['stop_reason'] != null) {
                yield AIResponse(
                  id: 'stream',
                  modelId: 'claude',
                  content: '',
                  usage: json['usage'] != null ? AIUsage(
                    promptTokens: json['usage']['input_tokens'] ?? 0,
                    completionTokens: json['usage']['output_tokens'] ?? 0,
                    totalTokens: (json['usage']['input_tokens'] ?? 0) + (json['usage']['output_tokens'] ?? 0),
                  ) : const AIUsage(
                    promptTokens: 0,
                    completionTokens: 0,
                    totalTokens: 0,
                  ),
                  finishReason: delta['stop_reason'],
                  createdAt: DateTime.now(),
                );
              }
            }
          } catch (e) {
            _logger.w('Failed to parse streaming chunk: $e');
          }
        }
      }
    }
  }
  
  /// 获取支持的模型列表
  List<AIModel> getSupportedModels() {
    return [
      const AIModel(
        id: 'claude-3-5-sonnet-20241022',
        name: 'claude-3-5-sonnet-20241022',
        displayName: 'Claude 3.5 Sonnet',
        provider: AIProvider.anthropic,
        type: AIModelType.chat,
        status: AIModelStatus.available,
        description: 'Anthropic最强的对话模型',
        maxTokens: 200000,
        supportedFeatures: ['chat', 'vision', 'code_execution'],
      ),
      const AIModel(
        id: 'claude-3-5-haiku-20241022',
        name: 'claude-3-5-haiku-20241022',
        displayName: 'Claude 3.5 Haiku',
        provider: AIProvider.anthropic,
        type: AIModelType.chat,
        status: AIModelStatus.available,
        description: 'Anthropic的快速轻量级模型',
        maxTokens: 200000,
        supportedFeatures: ['chat', 'vision'],
      ),
      const AIModel(
        id: 'claude-3-opus-20240229',
        name: 'claude-3-opus-20240229',
        displayName: 'Claude 3 Opus',
        provider: AIProvider.anthropic,
        type: AIModelType.chat,
        status: AIModelStatus.available,
        description: 'Anthropic的高性能模型',
        maxTokens: 200000,
        supportedFeatures: ['chat', 'vision'],
      ),
      const AIModel(
        id: 'claude-3-haiku-20240307',
        name: 'claude-3-haiku-20240307',
        displayName: 'Claude 3 Haiku',
        provider: AIProvider.anthropic,
        type: AIModelType.chat,
        status: AIModelStatus.available,
        description: 'Anthropic的快速轻量级模型',
        maxTokens: 200000,
        supportedFeatures: ['chat', 'vision'],
      ),
    ];
  }
  
  /// 处理Dio异常
  LLMRepositoryException _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return TimeoutException('Request timeout: ${e.message}');
      
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final responseData = e.response?.data;
        
        switch (statusCode) {
          case 401:
            return AuthenticationException('Invalid API key');
          case 429:
            return QuotaExceededException('Rate limit exceeded');
          case 404:
            return ModelUnavailableException('Model not found');
          case 400:
            String message = 'Bad request';
            if (responseData is Map && responseData['error'] != null) {
              message = responseData['error']['message'] ?? message;
            }
            return ValidationException(message);
          case 500:
          case 502:
          case 503:
          case 504:
            return NetworkException('Server error: $statusCode');
          default:
            String message = 'HTTP error: $statusCode';
            if (responseData is Map && responseData['error'] != null) {
              message = responseData['error']['message'] ?? message;
            }
            return LLMRepositoryException(message, code: statusCode.toString());
        }
      
      case DioExceptionType.connectionError:
        return NetworkException('Connection error: ${e.message}');
      
      case DioExceptionType.badCertificate:
        return NetworkException('SSL certificate error: ${e.message}');
      
      case DioExceptionType.cancel:
        return LLMRepositoryException('Request cancelled');
      
      default:
        return LLMRepositoryException('Unknown error: ${e.message}');
    }
  }
}