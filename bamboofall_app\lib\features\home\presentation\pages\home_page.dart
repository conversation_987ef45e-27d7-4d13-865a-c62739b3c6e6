import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

import '../../../../core/providers/app_providers.dart';
import '../../../writing/presentation/pages/writing_workspace.dart';
import '../../../settings/presentation/pages/settings_page.dart';
import '../../../templates/presentation/pages/template_manager_page.dart';
import '../../../project/presentation/pages/project_list_page.dart';
import '../../../review/presentation/pages/content_review_page.dart';
import '../../../help/presentation/pages/help_page.dart';

class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    final appState = ref.watch(appStateProvider);
    final themeMode = ref.watch(themeModeProvider);

    return fluent.NavigationView(
      pane: fluent.NavigationPane(
        selected: _selectedIndex,
        onChanged: (index) => setState(() => _selectedIndex = index),
        displayMode: fluent.PaneDisplayMode.auto,
        items: <fluent.NavigationPaneItem>[
          fluent.PaneItem(
            icon: const fluent.Icon(fluent.FluentIcons.home),
            title: const Text('首页'),
            body: _buildHomePage(context, ref, appState, themeMode),
          ),
          fluent.PaneItem(
            icon: const fluent.Icon(fluent.FluentIcons.edit),
            title: const Text('创作工作台'),
            body: const WritingWorkspace(),
          ),
          fluent.PaneItem(
            icon: const fluent.Icon(fluent.FluentIcons.project_collection),
            title: const Text('项目管理'),
            body: const ProjectListPage(),
          ),
          fluent.PaneItem(
            icon: const fluent.Icon(fluent.FluentIcons.text_document_settings),
            title: const Text('模板管理'),
            body: const TemplateManagerPage(),
          ),
          fluent.PaneItem(
            icon: const fluent.Icon(fluent.FluentIcons.review_solid),
            title: const Text('内容审查'),
            body: const ContentReviewPage(),
          ),
          fluent.PaneItem(
            icon: const fluent.Icon(fluent.FluentIcons.settings),
            title: const Text('设置'),
            body: const SettingsPage(),
          ),
        ],
        footerItems: [
          fluent.PaneItem(
            icon: const fluent.Icon(fluent.FluentIcons.help),
            title: const Text('帮助'),
            body: const HelpPage(),
          ),
        ],
      ),
      appBar: fluent.NavigationAppBar(
        title: const Text('笔落 - BambooFall'),
        automaticallyImplyLeading: false,
        actions: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 主题切换按钮
            fluent.ToggleButton(
              checked: themeMode == ThemeMode.dark,
              onChanged: (value) {
                ref.read(themeModeProvider.notifier).setThemeMode(
                  value ? ThemeMode.dark : ThemeMode.light,
                );
              },
              child: Icon(
                themeMode == ThemeMode.dark 
                  ? fluent.FluentIcons.sunny 
                  : fluent.FluentIcons.clear_night,
              ),
            ),
            const SizedBox(width: 8),
          ],
        ),
      ),
    );
  }

  Widget _buildHomePage(BuildContext context, WidgetRef ref, AppState appState, ThemeMode themeMode) {
    return fluent.ScaffoldPage(
      header: const fluent.PageHeader(
        title: Text('项目管理'),
      ),
      content: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 欢迎信息
            fluent.Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '欢迎使用笔落',
                      style: fluent.FluentTheme.of(context).typography.title,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'AI辅助小说创作应用，通过圣经系统帮助您创作高质量的中长篇小说',
                      style: fluent.FluentTheme.of(context).typography.body,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            
            // 操作按钮
            Row(
              children: [
                fluent.FilledButton(
                  onPressed: () {
                    _showCreateProjectDialog(context, ref);
                  },
                  child: const Text('创建新项目'),
                ),
                const SizedBox(width: 16),
                fluent.Button(
                  onPressed: () {
                    _showOpenProjectDialog(context, ref);
                  },
                  child: const Text('打开项目'),
                ),
                const SizedBox(width: 16),
                fluent.Button(
                  onPressed: () {
                    _showSettingsDialog(context, ref);
                  },
                  child: const Text('设置'),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // 最近项目列表
            Text(
              '最近项目',
              style: fluent.FluentTheme.of(context).typography.subtitle,
            ),
            const SizedBox(height: 16),
            Expanded(
              child: _buildRecentProjectsList(context, ref, appState),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentProjectsList(BuildContext context, WidgetRef ref, AppState appState) {
    if (appState.recentProjects.isEmpty) {
      return fluent.Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const fluent.Icon(
                  fluent.FluentIcons.folder_open,
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(
                  '暂无项目',
                  style: fluent.FluentTheme.of(context).typography.body,
                ),
                const SizedBox(height: 8),
                Text(
                  '点击"创建新项目"开始您的创作之旅',
                  style: fluent.FluentTheme.of(context).typography.caption,
                ),
              ],
            ),
          ),
        ),
      );
    }

    return fluent.Card(
      child: ListView.builder(
        padding: const EdgeInsets.all(8.0),
        itemCount: appState.recentProjects.length,
        itemBuilder: (context, index) {
          final project = appState.recentProjects[index];
          final projectName = project['name'] as String;
          final projectPath = project['path'] as String;
          final config = project['config'] as Map<String, dynamic>;
          final createdAt = DateTime.parse(config['createdAt']);
          
          return fluent.ListTile(
            leading: const fluent.Icon(fluent.FluentIcons.folder),
            title: Text(projectName),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(projectPath),
                const SizedBox(height: 4),
                Text(
                  '创建于: ${createdAt.year}-${createdAt.month.toString().padLeft(2, '0')}-${createdAt.day.toString().padLeft(2, '0')}',
                  style: fluent.FluentTheme.of(context).typography.caption,
                ),
              ],
            ),
            trailing: fluent.IconButton(
              icon: const fluent.Icon(fluent.FluentIcons.delete),
              onPressed: () {
                _showDeleteProjectDialog(context, ref, projectName);
              },
            ),
            onPressed: () {
              ref.read(appStateProvider.notifier).setCurrentProject(projectPath);
              // TODO: 打开项目
            },
          );
        },
      ),
    );
  }

  void _showCreateProjectDialog(BuildContext context, WidgetRef ref) {
    final TextEditingController nameController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: const Text('创建新项目'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('请输入项目名称:'),
            const SizedBox(height: 8),
            fluent.TextBox(
              controller: nameController,
              placeholder: '例如: 我的小说',
              autofocus: true,
            ),
          ],
        ),
        actions: [
          fluent.Button(
            child: const Text('取消'),
            onPressed: () => Navigator.pop(context),
          ),
          fluent.FilledButton(
            child: const Text('创建'),
            onPressed: () async {
              final projectName = nameController.text.trim();
              if (projectName.isEmpty) {
                return;
              }
              
              Navigator.pop(context);
              
              try {
                await ref.read(appStateProvider.notifier).createProject(projectName);
                if (context.mounted) {
                  _showSuccessMessage(context, '项目 "$projectName" 创建成功！');
                }
              } catch (e) {
                if (context.mounted) {
                  _showErrorMessage(context, '创建项目失败: $e');
                }
              }
            },
          ),
        ],
      ),
    );
  }

  void _showOpenProjectDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: const Text('打开项目'),
        content: SizedBox(
          width: 400,
          height: 300,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('选择要打开的项目:'),
              const SizedBox(height: 16),
              Expanded(
                child: Consumer(
                  builder: (context, ref, child) {
                    final appState = ref.watch(appStateProvider);
                    if (appState.recentProjects.isEmpty) {
                      return const Center(
                        child: Text('暂无可打开的项目'),
                      );
                    }
                    
                    return ListView.builder(
                      itemCount: appState.recentProjects.length,
                      itemBuilder: (context, index) {
                        final project = appState.recentProjects[index];
                        final projectName = project['name'] as String;
                        final projectPath = project['path'] as String;
                        
                        return fluent.ListTile(
                          leading: const fluent.Icon(fluent.FluentIcons.folder),
                          title: Text(projectName),
                          subtitle: Text(projectPath),
                          onPressed: () {
                            Navigator.pop(context);
                            ref.read(appStateProvider.notifier).setCurrentProject(projectPath);
                            // 切换到创作工作台
                            setState(() => _selectedIndex = 1);
                            _showSuccessMessage(context, '项目 "$projectName" 已打开');
                          },
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          fluent.Button(
            child: const Text('浏览文件'),
            onPressed: () {
              Navigator.pop(context);
              _showBrowseProjectDialog(context, ref);
            },
          ),
          fluent.Button(
            child: const Text('取消'),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }

  void _showSettingsDialog(BuildContext context, WidgetRef ref) {
    Navigator.push(
      context,
      fluent.FluentPageRoute(
        builder: (context) => const SettingsPage(),
      ),
    );
  }
  
  void _showDeleteProjectDialog(BuildContext context, WidgetRef ref, String projectName) {
    showDialog(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: const Text('删除项目'),
        content: Text('确定要删除项目 "$projectName" 吗？\n\n此操作不可撤销，项目的所有数据将被永久删除。'),
        actions: [
          fluent.Button(
            child: const Text('取消'),
            onPressed: () => Navigator.pop(context),
          ),
          fluent.FilledButton(
            child: const Text('删除'),
            onPressed: () async {
              Navigator.pop(context);
              
              try {
                await ref.read(appStateProvider.notifier).deleteProject(projectName);
                if (context.mounted) {
                  _showSuccessMessage(context, '项目 "$projectName" 已删除');
                }
              } catch (e) {
                if (context.mounted) {
                  _showErrorMessage(context, '删除项目失败: $e');
                }
              }
            },
          ),
        ],
      ),
    );
  }
  
  void _showBrowseProjectDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: const Text('浏览项目文件'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('请选择项目文件夹或项目配置文件:'),
            SizedBox(height: 16),
            Text('支持的文件类型:'),
            Text('• .bamboofall 项目配置文件'),
            Text('• 包含 project.json 的文件夹'),
            SizedBox(height: 16),
            Text('文件浏览功能将在后续版本中实现。'),
          ],
        ),
        actions: [
          fluent.Button(
            child: const Text('确定'),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }
  
  void _showSuccessMessage(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: const Text('成功'),
        content: Text(message),
        actions: [
          fluent.Button(
            child: const Text('确定'),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }
  
  void _showErrorMessage(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: const Text('错误'),
        content: Text(message),
        actions: [
          fluent.Button(
            child: const Text('确定'),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }
}