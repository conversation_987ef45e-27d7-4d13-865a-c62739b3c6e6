import 'package:logger/logger.dart';

import '../../domain/entities/story_bible.dart';
import '../../domain/entities/character.dart';
import '../../domain/entities/location.dart';
import '../../domain/entities/world_settings.dart';
import '../../domain/repositories/bible_repository.dart';
import '../datasources/bible_local_datasource.dart';

/// 圣经系统仓库实现
class BibleRepositoryImpl implements BibleRepository {
  final BibleLocalDataSource _localDataSource;
  final Logger _logger;
  
  BibleRepositoryImpl({
    required BibleLocalDataSource localDataSource,
    Logger? logger,
  }) : _localDataSource = localDataSource,
       _logger = logger ?? Logger();
  
  // ==================== StoryBible 操作 ====================
  
  @override
  Future<void> saveStoryBible(StoryBible storyBible) async {
    try {
      await _localDataSource.saveStoryBible(storyBible);
      _logger.d('Repository: Saved story bible ${storyBible.id}');
    } catch (e) {
      _logger.e('Repository: Failed to save story bible: $e');
      rethrow;
    }
  }
  
  @override
  Future<StoryBible?> getStoryBible(String id) async {
    try {
      final result = await _localDataSource.getStoryBible(id);
      _logger.d('Repository: Retrieved story bible $id');
      return result;
    } catch (e) {
      _logger.e('Repository: Failed to get story bible: $e');
      return null;
    }
  }
  
  @override
  Future<StoryBible?> getStoryBibleByProjectId(String projectId) async {
    try {
      final result = await _localDataSource.getStoryBibleByProjectId(projectId);
      _logger.d('Repository: Retrieved story bible for project $projectId');
      return result;
    } catch (e) {
      _logger.e('Repository: Failed to get story bible by project ID: $e');
      return null;
    }
  }
  
  @override
  Future<void> updateStoryBible(StoryBible storyBible) async {
    try {
      await _localDataSource.updateStoryBible(storyBible);
      _logger.d('Repository: Updated story bible ${storyBible.id}');
    } catch (e) {
      _logger.e('Repository: Failed to update story bible: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> deleteStoryBible(String id) async {
    try {
      await _localDataSource.deleteStoryBible(id);
      _logger.d('Repository: Deleted story bible $id');
    } catch (e) {
      _logger.e('Repository: Failed to delete story bible: $e');
      rethrow;
    }
  }
  
  @override
  Future<List<StoryBible>> getAllStoryBibles() async {
    try {
      final result = await _localDataSource.getAllStoryBibles();
      _logger.d('Repository: Retrieved ${result.length} story bibles');
      return result;
    } catch (e) {
      _logger.e('Repository: Failed to get all story bibles: $e');
      return [];
    }
  }
  
  // ==================== Character 操作 ====================
  
  @override
  Future<void> saveCharacter(Character character) async {
    try {
      await _localDataSource.saveCharacter(character);
      _logger.d('Repository: Saved character ${character.id}');
    } catch (e) {
      _logger.e('Repository: Failed to save character: $e');
      rethrow;
    }
  }
  
  @override
  Future<Character?> getCharacter(String id) async {
    try {
      final result = await _localDataSource.getCharacter(id);
      _logger.d('Repository: Retrieved character $id');
      return result;
    } catch (e) {
      _logger.e('Repository: Failed to get character: $e');
      return null;
    }
  }
  
  @override
  Future<void> updateCharacter(Character character) async {
    try {
      await _localDataSource.updateCharacter(character);
      _logger.d('Repository: Updated character ${character.id}');
    } catch (e) {
      _logger.e('Repository: Failed to update character: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> deleteCharacter(String id) async {
    try {
      await _localDataSource.deleteCharacter(id);
      _logger.d('Repository: Deleted character $id');
    } catch (e) {
      _logger.e('Repository: Failed to delete character: $e');
      rethrow;
    }
  }
  
  @override
  Future<List<Character>> getCharactersByType(CharacterType type) async {
    try {
      final result = await _localDataSource.getCharactersByType(type);
      _logger.d('Repository: Retrieved ${result.length} characters of type ${type.name}');
      return result;
    } catch (e) {
      _logger.e('Repository: Failed to get characters by type: $e');
      return [];
    }
  }
  
  @override
  Future<List<Character>> getCharactersByStatus(CharacterStatus status) async {
    try {
      final result = await _localDataSource.getCharactersByStatus(status);
      _logger.d('Repository: Retrieved ${result.length} characters with status ${status.name}');
      return result;
    } catch (e) {
      _logger.e('Repository: Failed to get characters by status: $e');
      return [];
    }
  }
  
  @override
  Future<List<Character>> searchCharacters(String query) async {
    try {
      final result = await _localDataSource.searchCharacters(query);
      _logger.d('Repository: Found ${result.length} characters matching "$query"');
      return result;
    } catch (e) {
      _logger.e('Repository: Failed to search characters: $e');
      return [];
    }
  }
  
  // ==================== Location 操作 ====================
  
  @override
  Future<void> saveLocation(Location location) async {
    try {
      await _localDataSource.saveLocation(location);
      _logger.d('Repository: Saved location ${location.id}');
    } catch (e) {
      _logger.e('Repository: Failed to save location: $e');
      rethrow;
    }
  }
  
  @override
  Future<Location?> getLocation(String id) async {
    try {
      final result = await _localDataSource.getLocation(id);
      _logger.d('Repository: Retrieved location $id');
      return result;
    } catch (e) {
      _logger.e('Repository: Failed to get location: $e');
      return null;
    }
  }
  
  @override
  Future<void> updateLocation(Location location) async {
    try {
      await _localDataSource.updateLocation(location);
      _logger.d('Repository: Updated location ${location.id}');
    } catch (e) {
      _logger.e('Repository: Failed to update location: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> deleteLocation(String id) async {
    try {
      await _localDataSource.deleteLocation(id);
      _logger.d('Repository: Deleted location $id');
    } catch (e) {
      _logger.e('Repository: Failed to delete location: $e');
      rethrow;
    }
  }
  
  @override
  Future<List<Location>> getLocationsByType(LocationType type) async {
    try {
      final result = await _localDataSource.getLocationsByType(type);
      _logger.d('Repository: Retrieved ${result.length} locations of type ${type.name}');
      return result;
    } catch (e) {
      _logger.e('Repository: Failed to get locations by type: $e');
      return [];
    }
  }
  
  @override
  Future<List<Location>> getChildLocations(String parentId) async {
    try {
      final result = await _localDataSource.getChildLocations(parentId);
      _logger.d('Repository: Retrieved ${result.length} child locations for $parentId');
      return result;
    } catch (e) {
      _logger.e('Repository: Failed to get child locations: $e');
      return [];
    }
  }
  
  // ==================== WorldSettings 操作 ====================
  
  @override
  Future<void> saveWorldSettings(WorldSettings worldSettings) async {
    try {
      await _localDataSource.saveWorldSettings(worldSettings);
      _logger.d('Repository: Saved world settings ${worldSettings.id}');
    } catch (e) {
      _logger.e('Repository: Failed to save world settings: $e');
      rethrow;
    }
  }
  
  @override
  Future<WorldSettings?> getWorldSettings(String id) async {
    try {
      final result = await _localDataSource.getWorldSettings(id);
      _logger.d('Repository: Retrieved world settings $id');
      return result;
    } catch (e) {
      _logger.e('Repository: Failed to get world settings: $e');
      return null;
    }
  }
  
  @override
  Future<void> updateWorldSettings(WorldSettings worldSettings) async {
    try {
      await _localDataSource.updateWorldSettings(worldSettings);
      _logger.d('Repository: Updated world settings ${worldSettings.id}');
    } catch (e) {
      _logger.e('Repository: Failed to update world settings: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> deleteWorldSettings(String id) async {
    try {
      await _localDataSource.deleteWorldSettings(id);
      _logger.d('Repository: Deleted world settings $id');
    } catch (e) {
      _logger.e('Repository: Failed to delete world settings: $e');
      rethrow;
    }
  }
  
  // ==================== 数据验证和完整性 ====================
  
  @override
  Future<List<String>> validateDataIntegrity() async {
    try {
      final issues = await _localDataSource.validateDataIntegrity();
      _logger.d('Repository: Data integrity validation found ${issues.length} issues');
      return issues;
    } catch (e) {
      _logger.e('Repository: Failed to validate data integrity: $e');
      return ['Validation failed: $e'];
    }
  }
  
  @override
  Future<bool> validateCharacterConstraints(Character character) async {
    try {
      // 基本验证
      if (character.name.trim().isEmpty) {
        return false;
      }
      
      // 状态一致性验证
      if (character.status == CharacterStatus.dead && character.currentLocationId != null) {
        return false;
      }
      
      // 关系验证
      for (final _ in character.relationshipIds) {
        // 这里可以添加关系存在性验证
        // 暂时跳过具体验证逻辑
      }
      
      _logger.d('Repository: Character constraints validated for ${character.id}');
      return true;
    } catch (e) {
      _logger.e('Repository: Failed to validate character constraints: $e');
      return false;
    }
  }
  
  @override
  Future<bool> validateLocationConstraints(Location location) async {
    try {
      // 基本验证
      if (location.name.trim().isEmpty) {
        return false;
      }
      
      // 层级关系验证
      if (location.parentLocationIds.contains(location.id)) {
        return false; // 不能自己是自己的父级
      }
      
      // 循环引用检查
      if (await _hasLocationCircularReference(location)) {
        return false;
      }
      
      _logger.d('Repository: Location constraints validated for ${location.id}');
      return true;
    } catch (e) {
      _logger.e('Repository: Failed to validate location constraints: $e');
      return false;
    }
  }
  
  /// 检查地点是否存在循环引用
  Future<bool> _hasLocationCircularReference(Location location) async {
    final visited = <String>{};
    return await _checkLocationCircularReference(location.id, visited);
  }
  
  /// 递归检查地点循环引用
  Future<bool> _checkLocationCircularReference(String locationId, Set<String> visited) async {
    if (visited.contains(locationId)) {
      return true;
    }
    
    visited.add(locationId);
    
    final location = await _localDataSource.getLocation(locationId);
    if (location == null) {
      visited.remove(locationId);
      return false;
    }
    
    for (final parentId in location.parentLocationIds) {
      if (await _checkLocationCircularReference(parentId, visited)) {
        return true;
      }
    }
    
    visited.remove(locationId);
    return false;
  }
  
  // ==================== 统计和分析 ====================
  
  @override
  Future<Map<String, dynamic>> getStatistics() async {
    try {
      final stats = await _localDataSource.getStatistics();
      _logger.d('Repository: Retrieved statistics');
      return stats;
    } catch (e) {
      _logger.e('Repository: Failed to get statistics: $e');
      return {};
    }
  }
  
  @override
  Future<Map<String, dynamic>> analyzeCharacterRelationships() async {
    try {
      // 这里可以实现复杂的关系分析逻辑
      final analysis = <String, dynamic>{};
      
      // 获取所有角色
      final characters = await _localDataSource.searchCharacters('');
      
      // 分析角色类型分布
      final typeDistribution = <String, int>{};
      for (final character in characters) {
        final type = character.type.name;
        typeDistribution[type] = (typeDistribution[type] ?? 0) + 1;
      }
      
      analysis['characterTypeDistribution'] = typeDistribution;
      analysis['totalCharacters'] = characters.length;
      analysis['analyzedAt'] = DateTime.now().toIso8601String();
      
      _logger.d('Repository: Character relationship analysis completed');
      return analysis;
    } catch (e) {
      _logger.e('Repository: Failed to analyze character relationships: $e');
      return {};
    }
  }
  
  @override
  Future<Map<String, dynamic>> analyzeLocationHierarchy() async {
    try {
      final analysis = <String, dynamic>{};
      
      // 获取所有地点
      final locations = await _localDataSource.getLocationsByType(LocationType.continent);
      
      // 分析地点层级结构
      final hierarchyDepth = <String, int>{};
      for (final location in locations) {
        final depth = await _calculateLocationDepth(location.id);
        hierarchyDepth[location.name] = depth;
      }
      
      analysis['locationHierarchyDepth'] = hierarchyDepth;
      analysis['totalLocations'] = locations.length;
      analysis['analyzedAt'] = DateTime.now().toIso8601String();
      
      _logger.d('Repository: Location hierarchy analysis completed');
      return analysis;
    } catch (e) {
      _logger.e('Repository: Failed to analyze location hierarchy: $e');
      return {};
    }
  }
  
  /// 计算地点层级深度
  Future<int> _calculateLocationDepth(String locationId) async {
    final location = await _localDataSource.getLocation(locationId);
    if (location == null || location.parentLocationIds.isEmpty) {
      return 0;
    }
    
    int maxDepth = 0;
    for (final parentId in location.parentLocationIds) {
      final parentDepth = await _calculateLocationDepth(parentId);
      maxDepth = maxDepth > parentDepth ? maxDepth : parentDepth;
    }
    
    return maxDepth + 1;
  }
  
  // ==================== 数据导入导出 ====================
  
  @override
  Future<Map<String, dynamic>> exportData() async {
    try {
      final data = await _localDataSource.exportData();
      _logger.d('Repository: Data export completed');
      return data;
    } catch (e) {
      _logger.e('Repository: Failed to export data: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> importData(Map<String, dynamic> data) async {
    try {
      await _localDataSource.importData(data);
      _logger.d('Repository: Data import completed');
    } catch (e) {
      _logger.e('Repository: Failed to import data: $e');
      rethrow;
    }
  }
  
  // ==================== 清理和维护 ====================
  
  @override
  Future<void> cleanup() async {
    try {
      await _localDataSource.cleanup();
      _logger.d('Repository: Cleanup completed');
    } catch (e) {
      _logger.e('Repository: Failed to cleanup: $e');
      rethrow;
    }
  }
}