import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

import 'markdown_editor.dart';

/// 编辑器面板
/// 提供Markdown编辑功能和工具栏
class EditorPanel extends ConsumerWidget {
  const EditorPanel({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        // 编辑器标题栏
        _buildHeader(context),
        
        // Markdown编辑器
        const Expanded(
          child: MarkdownEditor(),
        ),
        
        // 状态栏
        _buildStatusBar(context, ref),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: fluent.FluentTheme.of(context).resources.layerFillColorDefault,
        border: Border(
          bottom: BorderSide(
            color: fluent.FluentTheme.of(context).resources.dividerStrokeColorDefault,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            '编辑器',
            style: fluent.FluentTheme.of(context).typography.bodyStrong,
          ),
          const Spacer(),
          fluent.IconButton(
            icon: const fluent.Icon(fluent.FluentIcons.more, size: 16),
            onPressed: () {
              // TODO: 显示编辑器选项菜单
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBar(BuildContext context, WidgetRef ref) {
    final editorState = ref.watch(markdownEditorProvider);
    final wordCount = _countWords(editorState.rawMarkdown);
    final charCount = editorState.rawMarkdown.length;
    
    return Container(
      height: 24,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: fluent.FluentTheme.of(context).resources.layerFillColorDefault,
        border: Border(
          top: BorderSide(
            color: fluent.FluentTheme.of(context).resources.dividerStrokeColorDefault,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            '字数: $wordCount',
            style: fluent.FluentTheme.of(context).typography.caption,
          ),
          const SizedBox(width: 16),
          Text(
            '字符: $charCount',
            style: fluent.FluentTheme.of(context).typography.caption,
          ),
          const Spacer(),
          if (editorState.isPreviewMode)
            Text(
              '预览模式',
              style: fluent.FluentTheme.of(context).typography.caption?.copyWith(
                    color: fluent.FluentTheme.of(context).accentColor,
                  ),
            )
          else
            Text(
              '编辑模式',
              style: fluent.FluentTheme.of(context).typography.caption,
            ),
        ],
      ),
    );
  }

  int _countWords(String text) {
    if (text.isEmpty) return 0;
    
    // 简单的中英文字数统计
    final chineseChars = text.replaceAll(RegExp(r'[^\u4e00-\u9fa5]'), '').length;
    final englishWords = text
        .replaceAll(RegExp(r'[\u4e00-\u9fa5]'), ' ')
        .split(RegExp(r'\s+'))
        .where((word) => word.isNotEmpty)
        .length;
    
    return chineseChars + englishWords;
  }
}