import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

import '../../domain/entities/chapter_version.dart';
import '../../domain/entities/version_diff.dart';
import '../../domain/usecases/manage_versions.dart';

/// 差异查看器状态
class DiffViewerState {
  final ChapterVersion? sourceVersion;
  final ChapterVersion? targetVersion;
  final VersionDiff? diff;
  final bool isLoading;
  final String? error;
  final DiffViewMode viewMode;
  final bool showLineNumbers;
  final bool showWhitespace;

  const DiffViewerState({
    this.sourceVersion,
    this.targetVersion,
    this.diff,
    this.isLoading = false,
    this.error,
    this.viewMode = DiffViewMode.sideBySide,
    this.showLineNumbers = true,
    this.showWhitespace = false,
  });

  DiffViewerState copyWith({
    ChapterVersion? sourceVersion,
    ChapterVersion? targetVersion,
    VersionDiff? diff,
    bool? isLoading,
    String? error,
    DiffViewMode? viewMode,
    bool? showLineNumbers,
    bool? showWhitespace,
  }) {
    return DiffViewerState(
      sourceVersion: sourceVersion ?? this.sourceVersion,
      targetVersion: targetVersion ?? this.targetVersion,
      diff: diff ?? this.diff,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      viewMode: viewMode ?? this.viewMode,
      showLineNumbers: showLineNumbers ?? this.showLineNumbers,
      showWhitespace: showWhitespace ?? this.showWhitespace,
    );
  }
}

/// 差异查看模式
enum DiffViewMode {
  /// 并排显示
  sideBySide,
  
  /// 统一显示
  unified,
  
  /// 仅显示变更
  changesOnly,
}

/// 差异查看器状态管理
final diffViewerProvider = StateNotifierProvider.family<DiffViewerNotifier, DiffViewerState, DiffViewerParams>((ref, params) {
  final useCase = ref.read(manageVersionsUseCaseProvider);
  return DiffViewerNotifier(useCase, params);
});

class DiffViewerParams {
  final String sourceVersionId;
  final String targetVersionId;

  const DiffViewerParams({
    required this.sourceVersionId,
    required this.targetVersionId,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DiffViewerParams &&
          runtimeType == other.runtimeType &&
          sourceVersionId == other.sourceVersionId &&
          targetVersionId == other.targetVersionId;

  @override
  int get hashCode => sourceVersionId.hashCode ^ targetVersionId.hashCode;
}

class DiffViewerNotifier extends StateNotifier<DiffViewerState> {
  final ManageVersionsUseCase _useCase;
  final DiffViewerParams _params;

  DiffViewerNotifier(this._useCase, this._params) : super(const DiffViewerState()) {
    loadDiff();
  }

  /// 加载差异数据
  Future<void> loadDiff() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final sourceVersion = await _useCase.getVersionById(_params.sourceVersionId);
      final targetVersion = await _useCase.getVersionById(_params.targetVersionId);

      if (sourceVersion == null) {
        throw Exception('Source version not found: ${_params.sourceVersionId}');
      }
      if (targetVersion == null) {
        throw Exception('Target version not found: ${_params.targetVersionId}');
      }

      final diff = await _useCase.getVersionDiff(_params.sourceVersionId, _params.targetVersionId);

      state = state.copyWith(
        sourceVersion: sourceVersion,
        targetVersion: targetVersion,
        diff: diff,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 切换查看模式
  void setViewMode(DiffViewMode mode) {
    state = state.copyWith(viewMode: mode);
  }

  /// 切换行号显示
  void toggleLineNumbers() {
    state = state.copyWith(showLineNumbers: !state.showLineNumbers);
  }

  /// 切换空白字符显示
  void toggleWhitespace() {
    state = state.copyWith(showWhitespace: !state.showWhitespace);
  }
}

/// 差异查看器组件
/// 提供版本间差异的可视化显示和对比功能
class DiffViewer extends ConsumerWidget {
  final String sourceVersionId;
  final String targetVersionId;
  final Function(VersionDiff diff)? onAcceptChanges;
  final Function(VersionDiff diff)? onRejectChanges;

  const DiffViewer({
    super.key,
    required this.sourceVersionId,
    required this.targetVersionId,
    this.onAcceptChanges,
    this.onRejectChanges,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final params = DiffViewerParams(
      sourceVersionId: sourceVersionId,
      targetVersionId: targetVersionId,
    );
    final state = ref.watch(diffViewerProvider(params));
    final notifier = ref.read(diffViewerProvider(params).notifier);

    return Column(
      children: [
        // 工具栏
        _buildToolbar(context, state, notifier),
        
        // 差异内容
        Expanded(
          child: _buildContent(context, state),
        ),
      ],
    );
  }

  Widget _buildToolbar(BuildContext context, DiffViewerState state, DiffViewerNotifier notifier) {
    return Container(
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: fluent.FluentTheme.of(context).resources.layerFillColorDefault,
        border: Border(
          bottom: BorderSide(
            color: fluent.FluentTheme.of(context).resources.dividerStrokeColorDefault,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 版本信息
          if (state.sourceVersion != null && state.targetVersion != null) ...[
            Text(
              '${state.sourceVersion!.versionLabel} → ${state.targetVersion!.versionLabel}',
              style: fluent.FluentTheme.of(context).typography.bodyStrong,
            ),
            const SizedBox(width: 16),
            if (state.diff != null)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: fluent.FluentTheme.of(context).accentColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  state.diff!.changeSummary,
                  style: fluent.FluentTheme.of(context).typography.caption?.copyWith(
                        color: fluent.FluentTheme.of(context).accentColor,
                      ),
                ),
              ),
          ],
          
          const Spacer(),
          
          // 查看模式切换
          fluent.ComboBox<DiffViewMode>(
            value: state.viewMode,
            items: DiffViewMode.values.map((mode) {
              return fluent.ComboBoxItem(
                value: mode,
                child: Text(_getViewModeLabel(mode)),
              );
            }).toList(),
            onChanged: (mode) {
              if (mode != null) {
                notifier.setViewMode(mode);
              }
            },
          ),
          
          const SizedBox(width: 8),
          
          // 显示选项
          fluent.ToggleButton(
            checked: state.showLineNumbers,
            onChanged: (_) => notifier.toggleLineNumbers(),
            child: const fluent.Icon(fluent.FluentIcons.number_symbol, size: 16),
          ),
          
          const SizedBox(width: 4),
          
          fluent.ToggleButton(
            checked: state.showWhitespace,
            onChanged: (_) => notifier.toggleWhitespace(),
            child: const fluent.Icon(fluent.FluentIcons.view_all, size: 16),
          ),
          
          const SizedBox(width: 8),
          
          // 操作按钮
          if (state.diff != null && state.diff!.hasChanges) ...[
            fluent.Button(
              onPressed: () => onAcceptChanges?.call(state.diff!),
              child: const Text('接受变更'),
            ),
            const SizedBox(width: 8),
            fluent.Button(
              onPressed: () => onRejectChanges?.call(state.diff!),
              child: const Text('拒绝变更'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, DiffViewerState state) {
    if (state.isLoading) {
      return const Center(
        child: fluent.ProgressRing(),
      );
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const fluent.Icon(
              fluent.FluentIcons.error,
              size: 48,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: fluent.FluentTheme.of(context).typography.subtitle,
            ),
            const SizedBox(height: 8),
            Text(
              state.error!,
              style: fluent.FluentTheme.of(context).typography.caption,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (state.diff == null) {
      return const Center(
        child: Text('无差异数据'),
      );
    }

    switch (state.viewMode) {
      case DiffViewMode.sideBySide:
        return _buildSideBySideView(context, state);
      case DiffViewMode.unified:
        return _buildUnifiedView(context, state);
      case DiffViewMode.changesOnly:
        return _buildChangesOnlyView(context, state);
    }
  }

  Widget _buildSideBySideView(BuildContext context, DiffViewerState state) {
    return Row(
      children: [
        // 源版本
        Expanded(
          child: Column(
            children: [
              _buildVersionHeader(context, state.sourceVersion!, '源版本'),
              Expanded(
                child: _buildTextView(
                  context,
                  state.sourceVersion!.content,
                  state.diff!,
                  isSource: true,
                  showLineNumbers: state.showLineNumbers,
                  showWhitespace: state.showWhitespace,
                ),
              ),
            ],
          ),
        ),
        
        // 分隔线
        Container(
          width: 1,
          color: fluent.FluentTheme.of(context).resources.dividerStrokeColorDefault,
        ),
        
        // 目标版本
        Expanded(
          child: Column(
            children: [
              _buildVersionHeader(context, state.targetVersion!, '目标版本'),
              Expanded(
                child: _buildTextView(
                  context,
                  state.targetVersion!.content,
                  state.diff!,
                  isSource: false,
                  showLineNumbers: state.showLineNumbers,
                  showWhitespace: state.showWhitespace,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUnifiedView(BuildContext context, DiffViewerState state) {
    return Column(
      children: [
        _buildVersionHeader(context, state.targetVersion!, '统一视图'),
        Expanded(
          child: _buildUnifiedTextView(
            context,
            state.diff!,
            showLineNumbers: state.showLineNumbers,
            showWhitespace: state.showWhitespace,
          ),
        ),
      ],
    );
  }

  Widget _buildChangesOnlyView(BuildContext context, DiffViewerState state) {
    return Column(
      children: [
        _buildVersionHeader(context, state.targetVersion!, '仅显示变更'),
        Expanded(
          child: _buildChangesListView(context, state.diff!),
        ),
      ],
    );
  }

  Widget _buildVersionHeader(BuildContext context, ChapterVersion version, String title) {
    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: fluent.FluentTheme.of(context).resources.layerFillColorDefault,
        border: Border(
          bottom: BorderSide(
            color: fluent.FluentTheme.of(context).resources.dividerStrokeColorDefault,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            title,
            style: fluent.FluentTheme.of(context).typography.bodyStrong,
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: _getStatusColor(version.status).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              version.versionLabel,
              style: fluent.FluentTheme.of(context).typography.caption?.copyWith(
                    color: _getStatusColor(version.status),
                  ),
            ),
          ),
          const Spacer(),
          Text(
            '${version.wordCount}字 · ${version.createdBy}',
            style: fluent.FluentTheme.of(context).typography.caption,
          ),
        ],
      ),
    );
  }

  Widget _buildTextView(
    BuildContext context,
    String content,
    VersionDiff diff,
    {
    required bool isSource,
    required bool showLineNumbers,
    required bool showWhitespace,
  }) {
    final lines = content.split('\n');
    
    return ListView.builder(
      itemCount: lines.length,
      itemBuilder: (context, index) {
        final line = lines[index];
        final lineNumber = index + 1;
        
        // 查找该行的差异操作
        final operations = diff.operations.where((op) => op.lineNumber == lineNumber).toList();
        
        return _buildLineWidget(
          context,
          line,
          lineNumber,
          operations,
          isSource: isSource,
          showLineNumbers: showLineNumbers,
          showWhitespace: showWhitespace,
        );
      },
    );
  }

  Widget _buildUnifiedTextView(
    BuildContext context,
    VersionDiff diff,
    {
    required bool showLineNumbers,
    required bool showWhitespace,
  }) {
    return ListView.builder(
      itemCount: diff.operations.length,
      itemBuilder: (context, index) {
        final operation = diff.operations[index];
        
        return _buildOperationWidget(
          context,
          operation,
          showLineNumbers: showLineNumbers,
          showWhitespace: showWhitespace,
        );
      },
    );
  }

  Widget _buildChangesListView(BuildContext context, VersionDiff diff) {
    final changeOperations = diff.changeOperations;
    
    if (changeOperations.isEmpty) {
      return const Center(
        child: Text('无变更'),
      );
    }
    
    return ListView.builder(
      itemCount: changeOperations.length,
      itemBuilder: (context, index) {
        final operation = changeOperations[index];
        
        return _buildChangeItemWidget(context, operation);
      },
    );
  }

  Widget _buildLineWidget(
    BuildContext context,
    String line,
    int lineNumber,
    List<DiffOperation> operations,
    {
    required bool isSource,
    required bool showLineNumbers,
    required bool showWhitespace,
  }) {
    Color? backgroundColor;
    Color? textColor;
    
    // 根据操作类型设置背景色
    for (final operation in operations) {
      switch (operation.type) {
        case OperationType.insert:
          if (!isSource) {
            backgroundColor = Colors.green.withValues(alpha: 0.1);
            textColor = Colors.green.shade700;
          }
          break;
        case OperationType.delete:
          if (isSource) {
            backgroundColor = Colors.red.withValues(alpha: 0.1);
            textColor = Colors.red.shade700;
          }
          break;
        case OperationType.replace:
          backgroundColor = Colors.orange.withValues(alpha: 0.1);
          textColor = Colors.orange.shade700;
          break;
        default:
          break;
      }
    }
    
    return Container(
      color: backgroundColor,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 行号
          if (showLineNumbers)
            Container(
              width: 50,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              color: fluent.FluentTheme.of(context).resources.layerFillColorDefault,
              child: Text(
                lineNumber.toString(),
                style: fluent.FluentTheme.of(context).typography.caption?.copyWith(
                      color: fluent.FluentTheme.of(context).resources.textFillColorSecondary,
                    ),
                textAlign: TextAlign.right,
              ),
            ),
          
          // 内容
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: Text(
                showWhitespace ? line.replaceAll(' ', '·').replaceAll('\t', '→') : line,
                style: TextStyle(
                  fontFamily: 'Consolas',
                  fontSize: 14,
                  color: textColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOperationWidget(
    BuildContext context,
    DiffOperation operation,
    {
    required bool showLineNumbers,
    required bool showWhitespace,
  }) {
    Color backgroundColor;
    Color textColor;
    String prefix;
    
    switch (operation.type) {
      case OperationType.insert:
        backgroundColor = Colors.green.withValues(alpha: 0.1);
        textColor = Colors.green.shade700;
        prefix = '+';
        break;
      case OperationType.delete:
        backgroundColor = Colors.red.withValues(alpha: 0.1);
        textColor = Colors.red.shade700;
        prefix = '-';
        break;
      case OperationType.replace:
        backgroundColor = Colors.orange.withValues(alpha: 0.1);
        textColor = Colors.orange.shade700;
        prefix = '~';
        break;
      default:
        backgroundColor = Colors.transparent;
        textColor = fluent.FluentTheme.of(context).resources.textFillColorPrimary;
        prefix = ' ';
    }
    
    return Container(
      color: backgroundColor,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 操作符号
          Container(
            width: 30,
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: Text(
              prefix,
              style: TextStyle(
                fontFamily: 'Consolas',
                fontSize: 14,
                color: textColor,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          
          // 行号
          if (showLineNumbers)
            Container(
              width: 50,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: Text(
                operation.lineNumber?.toString() ?? '',
                style: fluent.FluentTheme.of(context).typography.caption?.copyWith(
                      color: fluent.FluentTheme.of(context).resources.textFillColorSecondary,
                    ),
                textAlign: TextAlign.right,
              ),
            ),
          
          // 内容
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: Text(
                showWhitespace 
                    ? operation.content.replaceAll(' ', '·').replaceAll('\t', '→')
                    : operation.content,
                style: TextStyle(
                  fontFamily: 'Consolas',
                  fontSize: 14,
                  color: textColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChangeItemWidget(BuildContext context, DiffOperation operation) {
    return fluent.Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            // 操作类型图标
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: Color(int.parse('0xFF${operation.type.colorHex.substring(1)}')).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Center(
                child: Text(
                  operation.type.symbol,
                  style: TextStyle(
                    color: Color(int.parse('0xFF${operation.type.colorHex.substring(1)}')),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            
            const SizedBox(width: 12),
            
            // 操作信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    operation.type.displayName,
                    style: fluent.FluentTheme.of(context).typography.bodyStrong,
                  ),
                  if (operation.lineNumber != null)
                    Text(
                      '第${operation.lineNumber}行',
                      style: fluent.FluentTheme.of(context).typography.caption,
                    ),
                  if (operation.content.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(top: 4),
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: fluent.FluentTheme.of(context).resources.layerFillColorDefault,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        operation.content.length > 100 
                            ? '${operation.content.substring(0, 100)}...'
                            : operation.content,
                        style: const TextStyle(
                          fontFamily: 'Consolas',
                          fontSize: 12,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getViewModeLabel(DiffViewMode mode) {
    switch (mode) {
      case DiffViewMode.sideBySide:
        return '并排显示';
      case DiffViewMode.unified:
        return '统一显示';
      case DiffViewMode.changesOnly:
        return '仅显示变更';
    }
  }

  Color _getStatusColor(VersionStatus status) {
    return Color(int.parse('0xFF${status.colorHex.substring(1)}'));
  }
}