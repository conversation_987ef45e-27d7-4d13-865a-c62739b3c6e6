// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

Location _$LocationFromJson(Map<String, dynamic> json) {
  return _Location.fromJson(json);
}

/// @nodoc
mixin _$Location {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  List<String> get aliases => throw _privateConstructorUsedError; // 别名
  String? get description => throw _privateConstructorUsedError;
  LocationType get type => throw _privateConstructorUsedError;
  LocationStatus get status => throw _privateConstructorUsedError;
  LocationGeography? get geography => throw _privateConstructorUsedError;
  LocationClimate? get climate => throw _privateConstructorUsedError;
  LocationCulture? get culture => throw _privateConstructorUsedError;
  LocationEconomy? get economy => throw _privateConstructorUsedError;
  LocationPolitics? get politics => throw _privateConstructorUsedError;
  List<String> get parentLocationIds =>
      throw _privateConstructorUsedError; // 父级地点ID
  List<String> get childLocationIds =>
      throw _privateConstructorUsedError; // 子级地点ID
  List<String> get connectedLocationIds =>
      throw _privateConstructorUsedError; // 连接的地点ID
  List<LocationFeature> get features => throw _privateConstructorUsedError;
  List<String> get residentCharacterIds =>
      throw _privateConstructorUsedError; // 居民角色ID
  List<String> get visitingCharacterIds =>
      throw _privateConstructorUsedError; // 访问角色ID
  List<LocationEvent> get events => throw _privateConstructorUsedError;
  List<String> get availableItemIds =>
      throw _privateConstructorUsedError; // 可获得的物品ID
  List<LocationSecret> get secrets => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this Location to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LocationCopyWith<Location> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationCopyWith<$Res> {
  factory $LocationCopyWith(Location value, $Res Function(Location) then) =
      _$LocationCopyWithImpl<$Res, Location>;
  @useResult
  $Res call({
    String id,
    String name,
    List<String> aliases,
    String? description,
    LocationType type,
    LocationStatus status,
    LocationGeography? geography,
    LocationClimate? climate,
    LocationCulture? culture,
    LocationEconomy? economy,
    LocationPolitics? politics,
    List<String> parentLocationIds,
    List<String> childLocationIds,
    List<String> connectedLocationIds,
    List<LocationFeature> features,
    List<String> residentCharacterIds,
    List<String> visitingCharacterIds,
    List<LocationEvent> events,
    List<String> availableItemIds,
    List<LocationSecret> secrets,
    String? imageUrl,
    Map<String, dynamic> customAttributes,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });

  $LocationGeographyCopyWith<$Res>? get geography;
  $LocationClimateCopyWith<$Res>? get climate;
  $LocationCultureCopyWith<$Res>? get culture;
  $LocationEconomyCopyWith<$Res>? get economy;
  $LocationPoliticsCopyWith<$Res>? get politics;
}

/// @nodoc
class _$LocationCopyWithImpl<$Res, $Val extends Location>
    implements $LocationCopyWith<$Res> {
  _$LocationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? aliases = null,
    Object? description = freezed,
    Object? type = null,
    Object? status = null,
    Object? geography = freezed,
    Object? climate = freezed,
    Object? culture = freezed,
    Object? economy = freezed,
    Object? politics = freezed,
    Object? parentLocationIds = null,
    Object? childLocationIds = null,
    Object? connectedLocationIds = null,
    Object? features = null,
    Object? residentCharacterIds = null,
    Object? visitingCharacterIds = null,
    Object? events = null,
    Object? availableItemIds = null,
    Object? secrets = null,
    Object? imageUrl = freezed,
    Object? customAttributes = null,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            aliases: null == aliases
                ? _value.aliases
                : aliases // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as LocationType,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as LocationStatus,
            geography: freezed == geography
                ? _value.geography
                : geography // ignore: cast_nullable_to_non_nullable
                      as LocationGeography?,
            climate: freezed == climate
                ? _value.climate
                : climate // ignore: cast_nullable_to_non_nullable
                      as LocationClimate?,
            culture: freezed == culture
                ? _value.culture
                : culture // ignore: cast_nullable_to_non_nullable
                      as LocationCulture?,
            economy: freezed == economy
                ? _value.economy
                : economy // ignore: cast_nullable_to_non_nullable
                      as LocationEconomy?,
            politics: freezed == politics
                ? _value.politics
                : politics // ignore: cast_nullable_to_non_nullable
                      as LocationPolitics?,
            parentLocationIds: null == parentLocationIds
                ? _value.parentLocationIds
                : parentLocationIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            childLocationIds: null == childLocationIds
                ? _value.childLocationIds
                : childLocationIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            connectedLocationIds: null == connectedLocationIds
                ? _value.connectedLocationIds
                : connectedLocationIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            features: null == features
                ? _value.features
                : features // ignore: cast_nullable_to_non_nullable
                      as List<LocationFeature>,
            residentCharacterIds: null == residentCharacterIds
                ? _value.residentCharacterIds
                : residentCharacterIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            visitingCharacterIds: null == visitingCharacterIds
                ? _value.visitingCharacterIds
                : visitingCharacterIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            events: null == events
                ? _value.events
                : events // ignore: cast_nullable_to_non_nullable
                      as List<LocationEvent>,
            availableItemIds: null == availableItemIds
                ? _value.availableItemIds
                : availableItemIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            secrets: null == secrets
                ? _value.secrets
                : secrets // ignore: cast_nullable_to_non_nullable
                      as List<LocationSecret>,
            imageUrl: freezed == imageUrl
                ? _value.imageUrl
                : imageUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationGeographyCopyWith<$Res>? get geography {
    if (_value.geography == null) {
      return null;
    }

    return $LocationGeographyCopyWith<$Res>(_value.geography!, (value) {
      return _then(_value.copyWith(geography: value) as $Val);
    });
  }

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationClimateCopyWith<$Res>? get climate {
    if (_value.climate == null) {
      return null;
    }

    return $LocationClimateCopyWith<$Res>(_value.climate!, (value) {
      return _then(_value.copyWith(climate: value) as $Val);
    });
  }

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationCultureCopyWith<$Res>? get culture {
    if (_value.culture == null) {
      return null;
    }

    return $LocationCultureCopyWith<$Res>(_value.culture!, (value) {
      return _then(_value.copyWith(culture: value) as $Val);
    });
  }

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationEconomyCopyWith<$Res>? get economy {
    if (_value.economy == null) {
      return null;
    }

    return $LocationEconomyCopyWith<$Res>(_value.economy!, (value) {
      return _then(_value.copyWith(economy: value) as $Val);
    });
  }

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationPoliticsCopyWith<$Res>? get politics {
    if (_value.politics == null) {
      return null;
    }

    return $LocationPoliticsCopyWith<$Res>(_value.politics!, (value) {
      return _then(_value.copyWith(politics: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LocationImplCopyWith<$Res>
    implements $LocationCopyWith<$Res> {
  factory _$$LocationImplCopyWith(
    _$LocationImpl value,
    $Res Function(_$LocationImpl) then,
  ) = __$$LocationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    List<String> aliases,
    String? description,
    LocationType type,
    LocationStatus status,
    LocationGeography? geography,
    LocationClimate? climate,
    LocationCulture? culture,
    LocationEconomy? economy,
    LocationPolitics? politics,
    List<String> parentLocationIds,
    List<String> childLocationIds,
    List<String> connectedLocationIds,
    List<LocationFeature> features,
    List<String> residentCharacterIds,
    List<String> visitingCharacterIds,
    List<LocationEvent> events,
    List<String> availableItemIds,
    List<LocationSecret> secrets,
    String? imageUrl,
    Map<String, dynamic> customAttributes,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });

  @override
  $LocationGeographyCopyWith<$Res>? get geography;
  @override
  $LocationClimateCopyWith<$Res>? get climate;
  @override
  $LocationCultureCopyWith<$Res>? get culture;
  @override
  $LocationEconomyCopyWith<$Res>? get economy;
  @override
  $LocationPoliticsCopyWith<$Res>? get politics;
}

/// @nodoc
class __$$LocationImplCopyWithImpl<$Res>
    extends _$LocationCopyWithImpl<$Res, _$LocationImpl>
    implements _$$LocationImplCopyWith<$Res> {
  __$$LocationImplCopyWithImpl(
    _$LocationImpl _value,
    $Res Function(_$LocationImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? aliases = null,
    Object? description = freezed,
    Object? type = null,
    Object? status = null,
    Object? geography = freezed,
    Object? climate = freezed,
    Object? culture = freezed,
    Object? economy = freezed,
    Object? politics = freezed,
    Object? parentLocationIds = null,
    Object? childLocationIds = null,
    Object? connectedLocationIds = null,
    Object? features = null,
    Object? residentCharacterIds = null,
    Object? visitingCharacterIds = null,
    Object? events = null,
    Object? availableItemIds = null,
    Object? secrets = null,
    Object? imageUrl = freezed,
    Object? customAttributes = null,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$LocationImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        aliases: null == aliases
            ? _value._aliases
            : aliases // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as LocationType,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as LocationStatus,
        geography: freezed == geography
            ? _value.geography
            : geography // ignore: cast_nullable_to_non_nullable
                  as LocationGeography?,
        climate: freezed == climate
            ? _value.climate
            : climate // ignore: cast_nullable_to_non_nullable
                  as LocationClimate?,
        culture: freezed == culture
            ? _value.culture
            : culture // ignore: cast_nullable_to_non_nullable
                  as LocationCulture?,
        economy: freezed == economy
            ? _value.economy
            : economy // ignore: cast_nullable_to_non_nullable
                  as LocationEconomy?,
        politics: freezed == politics
            ? _value.politics
            : politics // ignore: cast_nullable_to_non_nullable
                  as LocationPolitics?,
        parentLocationIds: null == parentLocationIds
            ? _value._parentLocationIds
            : parentLocationIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        childLocationIds: null == childLocationIds
            ? _value._childLocationIds
            : childLocationIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        connectedLocationIds: null == connectedLocationIds
            ? _value._connectedLocationIds
            : connectedLocationIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        features: null == features
            ? _value._features
            : features // ignore: cast_nullable_to_non_nullable
                  as List<LocationFeature>,
        residentCharacterIds: null == residentCharacterIds
            ? _value._residentCharacterIds
            : residentCharacterIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        visitingCharacterIds: null == visitingCharacterIds
            ? _value._visitingCharacterIds
            : visitingCharacterIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        events: null == events
            ? _value._events
            : events // ignore: cast_nullable_to_non_nullable
                  as List<LocationEvent>,
        availableItemIds: null == availableItemIds
            ? _value._availableItemIds
            : availableItemIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        secrets: null == secrets
            ? _value._secrets
            : secrets // ignore: cast_nullable_to_non_nullable
                  as List<LocationSecret>,
        imageUrl: freezed == imageUrl
            ? _value.imageUrl
            : imageUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$LocationImpl implements _Location {
  const _$LocationImpl({
    required this.id,
    required this.name,
    final List<String> aliases = const [],
    this.description,
    required this.type,
    required this.status,
    this.geography,
    this.climate,
    this.culture,
    this.economy,
    this.politics,
    final List<String> parentLocationIds = const [],
    final List<String> childLocationIds = const [],
    final List<String> connectedLocationIds = const [],
    final List<LocationFeature> features = const [],
    final List<String> residentCharacterIds = const [],
    final List<String> visitingCharacterIds = const [],
    final List<LocationEvent> events = const [],
    final List<String> availableItemIds = const [],
    final List<LocationSecret> secrets = const [],
    this.imageUrl,
    final Map<String, dynamic> customAttributes = const {},
    final Map<String, dynamic> metadata = const {},
    this.createdAt,
    this.updatedAt,
  }) : _aliases = aliases,
       _parentLocationIds = parentLocationIds,
       _childLocationIds = childLocationIds,
       _connectedLocationIds = connectedLocationIds,
       _features = features,
       _residentCharacterIds = residentCharacterIds,
       _visitingCharacterIds = visitingCharacterIds,
       _events = events,
       _availableItemIds = availableItemIds,
       _secrets = secrets,
       _customAttributes = customAttributes,
       _metadata = metadata;

  factory _$LocationImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocationImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  final List<String> _aliases;
  @override
  @JsonKey()
  List<String> get aliases {
    if (_aliases is EqualUnmodifiableListView) return _aliases;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_aliases);
  }

  // 别名
  @override
  final String? description;
  @override
  final LocationType type;
  @override
  final LocationStatus status;
  @override
  final LocationGeography? geography;
  @override
  final LocationClimate? climate;
  @override
  final LocationCulture? culture;
  @override
  final LocationEconomy? economy;
  @override
  final LocationPolitics? politics;
  final List<String> _parentLocationIds;
  @override
  @JsonKey()
  List<String> get parentLocationIds {
    if (_parentLocationIds is EqualUnmodifiableListView)
      return _parentLocationIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_parentLocationIds);
  }

  // 父级地点ID
  final List<String> _childLocationIds;
  // 父级地点ID
  @override
  @JsonKey()
  List<String> get childLocationIds {
    if (_childLocationIds is EqualUnmodifiableListView)
      return _childLocationIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_childLocationIds);
  }

  // 子级地点ID
  final List<String> _connectedLocationIds;
  // 子级地点ID
  @override
  @JsonKey()
  List<String> get connectedLocationIds {
    if (_connectedLocationIds is EqualUnmodifiableListView)
      return _connectedLocationIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_connectedLocationIds);
  }

  // 连接的地点ID
  final List<LocationFeature> _features;
  // 连接的地点ID
  @override
  @JsonKey()
  List<LocationFeature> get features {
    if (_features is EqualUnmodifiableListView) return _features;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_features);
  }

  final List<String> _residentCharacterIds;
  @override
  @JsonKey()
  List<String> get residentCharacterIds {
    if (_residentCharacterIds is EqualUnmodifiableListView)
      return _residentCharacterIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_residentCharacterIds);
  }

  // 居民角色ID
  final List<String> _visitingCharacterIds;
  // 居民角色ID
  @override
  @JsonKey()
  List<String> get visitingCharacterIds {
    if (_visitingCharacterIds is EqualUnmodifiableListView)
      return _visitingCharacterIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_visitingCharacterIds);
  }

  // 访问角色ID
  final List<LocationEvent> _events;
  // 访问角色ID
  @override
  @JsonKey()
  List<LocationEvent> get events {
    if (_events is EqualUnmodifiableListView) return _events;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_events);
  }

  final List<String> _availableItemIds;
  @override
  @JsonKey()
  List<String> get availableItemIds {
    if (_availableItemIds is EqualUnmodifiableListView)
      return _availableItemIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_availableItemIds);
  }

  // 可获得的物品ID
  final List<LocationSecret> _secrets;
  // 可获得的物品ID
  @override
  @JsonKey()
  List<LocationSecret> get secrets {
    if (_secrets is EqualUnmodifiableListView) return _secrets;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_secrets);
  }

  @override
  final String? imageUrl;
  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'Location(id: $id, name: $name, aliases: $aliases, description: $description, type: $type, status: $status, geography: $geography, climate: $climate, culture: $culture, economy: $economy, politics: $politics, parentLocationIds: $parentLocationIds, childLocationIds: $childLocationIds, connectedLocationIds: $connectedLocationIds, features: $features, residentCharacterIds: $residentCharacterIds, visitingCharacterIds: $visitingCharacterIds, events: $events, availableItemIds: $availableItemIds, secrets: $secrets, imageUrl: $imageUrl, customAttributes: $customAttributes, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other._aliases, _aliases) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.geography, geography) ||
                other.geography == geography) &&
            (identical(other.climate, climate) || other.climate == climate) &&
            (identical(other.culture, culture) || other.culture == culture) &&
            (identical(other.economy, economy) || other.economy == economy) &&
            (identical(other.politics, politics) ||
                other.politics == politics) &&
            const DeepCollectionEquality().equals(
              other._parentLocationIds,
              _parentLocationIds,
            ) &&
            const DeepCollectionEquality().equals(
              other._childLocationIds,
              _childLocationIds,
            ) &&
            const DeepCollectionEquality().equals(
              other._connectedLocationIds,
              _connectedLocationIds,
            ) &&
            const DeepCollectionEquality().equals(other._features, _features) &&
            const DeepCollectionEquality().equals(
              other._residentCharacterIds,
              _residentCharacterIds,
            ) &&
            const DeepCollectionEquality().equals(
              other._visitingCharacterIds,
              _visitingCharacterIds,
            ) &&
            const DeepCollectionEquality().equals(other._events, _events) &&
            const DeepCollectionEquality().equals(
              other._availableItemIds,
              _availableItemIds,
            ) &&
            const DeepCollectionEquality().equals(other._secrets, _secrets) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    name,
    const DeepCollectionEquality().hash(_aliases),
    description,
    type,
    status,
    geography,
    climate,
    culture,
    economy,
    politics,
    const DeepCollectionEquality().hash(_parentLocationIds),
    const DeepCollectionEquality().hash(_childLocationIds),
    const DeepCollectionEquality().hash(_connectedLocationIds),
    const DeepCollectionEquality().hash(_features),
    const DeepCollectionEquality().hash(_residentCharacterIds),
    const DeepCollectionEquality().hash(_visitingCharacterIds),
    const DeepCollectionEquality().hash(_events),
    const DeepCollectionEquality().hash(_availableItemIds),
    const DeepCollectionEquality().hash(_secrets),
    imageUrl,
    const DeepCollectionEquality().hash(_customAttributes),
    const DeepCollectionEquality().hash(_metadata),
    createdAt,
    updatedAt,
  ]);

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationImplCopyWith<_$LocationImpl> get copyWith =>
      __$$LocationImplCopyWithImpl<_$LocationImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LocationImplToJson(this);
  }
}

abstract class _Location implements Location {
  const factory _Location({
    required final String id,
    required final String name,
    final List<String> aliases,
    final String? description,
    required final LocationType type,
    required final LocationStatus status,
    final LocationGeography? geography,
    final LocationClimate? climate,
    final LocationCulture? culture,
    final LocationEconomy? economy,
    final LocationPolitics? politics,
    final List<String> parentLocationIds,
    final List<String> childLocationIds,
    final List<String> connectedLocationIds,
    final List<LocationFeature> features,
    final List<String> residentCharacterIds,
    final List<String> visitingCharacterIds,
    final List<LocationEvent> events,
    final List<String> availableItemIds,
    final List<LocationSecret> secrets,
    final String? imageUrl,
    final Map<String, dynamic> customAttributes,
    final Map<String, dynamic> metadata,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$LocationImpl;

  factory _Location.fromJson(Map<String, dynamic> json) =
      _$LocationImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  List<String> get aliases; // 别名
  @override
  String? get description;
  @override
  LocationType get type;
  @override
  LocationStatus get status;
  @override
  LocationGeography? get geography;
  @override
  LocationClimate? get climate;
  @override
  LocationCulture? get culture;
  @override
  LocationEconomy? get economy;
  @override
  LocationPolitics? get politics;
  @override
  List<String> get parentLocationIds; // 父级地点ID
  @override
  List<String> get childLocationIds; // 子级地点ID
  @override
  List<String> get connectedLocationIds; // 连接的地点ID
  @override
  List<LocationFeature> get features;
  @override
  List<String> get residentCharacterIds; // 居民角色ID
  @override
  List<String> get visitingCharacterIds; // 访问角色ID
  @override
  List<LocationEvent> get events;
  @override
  List<String> get availableItemIds; // 可获得的物品ID
  @override
  List<LocationSecret> get secrets;
  @override
  String? get imageUrl;
  @override
  Map<String, dynamic> get customAttributes;
  @override
  Map<String, dynamic> get metadata;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationImplCopyWith<_$LocationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LocationGeography _$LocationGeographyFromJson(Map<String, dynamic> json) {
  return _LocationGeography.fromJson(json);
}

/// @nodoc
mixin _$LocationGeography {
  LocationCoordinates? get coordinates => throw _privateConstructorUsedError;
  String? get terrain => throw _privateConstructorUsedError; // 地形
  double? get elevation => throw _privateConstructorUsedError; // 海拔
  double? get area => throw _privateConstructorUsedError; // 面积
  List<String> get naturalFeatures =>
      throw _privateConstructorUsedError; // 自然特征
  List<String> get landmarks => throw _privateConstructorUsedError; // 地标
  String? get waterSources => throw _privateConstructorUsedError; // 水源
  String? get vegetation => throw _privateConstructorUsedError; // 植被
  List<String> get naturalResources =>
      throw _privateConstructorUsedError; // 自然资源
  Map<String, dynamic> get customGeography =>
      throw _privateConstructorUsedError;

  /// Serializes this LocationGeography to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LocationGeography
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LocationGeographyCopyWith<LocationGeography> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationGeographyCopyWith<$Res> {
  factory $LocationGeographyCopyWith(
    LocationGeography value,
    $Res Function(LocationGeography) then,
  ) = _$LocationGeographyCopyWithImpl<$Res, LocationGeography>;
  @useResult
  $Res call({
    LocationCoordinates? coordinates,
    String? terrain,
    double? elevation,
    double? area,
    List<String> naturalFeatures,
    List<String> landmarks,
    String? waterSources,
    String? vegetation,
    List<String> naturalResources,
    Map<String, dynamic> customGeography,
  });

  $LocationCoordinatesCopyWith<$Res>? get coordinates;
}

/// @nodoc
class _$LocationGeographyCopyWithImpl<$Res, $Val extends LocationGeography>
    implements $LocationGeographyCopyWith<$Res> {
  _$LocationGeographyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationGeography
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? coordinates = freezed,
    Object? terrain = freezed,
    Object? elevation = freezed,
    Object? area = freezed,
    Object? naturalFeatures = null,
    Object? landmarks = null,
    Object? waterSources = freezed,
    Object? vegetation = freezed,
    Object? naturalResources = null,
    Object? customGeography = null,
  }) {
    return _then(
      _value.copyWith(
            coordinates: freezed == coordinates
                ? _value.coordinates
                : coordinates // ignore: cast_nullable_to_non_nullable
                      as LocationCoordinates?,
            terrain: freezed == terrain
                ? _value.terrain
                : terrain // ignore: cast_nullable_to_non_nullable
                      as String?,
            elevation: freezed == elevation
                ? _value.elevation
                : elevation // ignore: cast_nullable_to_non_nullable
                      as double?,
            area: freezed == area
                ? _value.area
                : area // ignore: cast_nullable_to_non_nullable
                      as double?,
            naturalFeatures: null == naturalFeatures
                ? _value.naturalFeatures
                : naturalFeatures // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            landmarks: null == landmarks
                ? _value.landmarks
                : landmarks // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            waterSources: freezed == waterSources
                ? _value.waterSources
                : waterSources // ignore: cast_nullable_to_non_nullable
                      as String?,
            vegetation: freezed == vegetation
                ? _value.vegetation
                : vegetation // ignore: cast_nullable_to_non_nullable
                      as String?,
            naturalResources: null == naturalResources
                ? _value.naturalResources
                : naturalResources // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customGeography: null == customGeography
                ? _value.customGeography
                : customGeography // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }

  /// Create a copy of LocationGeography
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationCoordinatesCopyWith<$Res>? get coordinates {
    if (_value.coordinates == null) {
      return null;
    }

    return $LocationCoordinatesCopyWith<$Res>(_value.coordinates!, (value) {
      return _then(_value.copyWith(coordinates: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LocationGeographyImplCopyWith<$Res>
    implements $LocationGeographyCopyWith<$Res> {
  factory _$$LocationGeographyImplCopyWith(
    _$LocationGeographyImpl value,
    $Res Function(_$LocationGeographyImpl) then,
  ) = __$$LocationGeographyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    LocationCoordinates? coordinates,
    String? terrain,
    double? elevation,
    double? area,
    List<String> naturalFeatures,
    List<String> landmarks,
    String? waterSources,
    String? vegetation,
    List<String> naturalResources,
    Map<String, dynamic> customGeography,
  });

  @override
  $LocationCoordinatesCopyWith<$Res>? get coordinates;
}

/// @nodoc
class __$$LocationGeographyImplCopyWithImpl<$Res>
    extends _$LocationGeographyCopyWithImpl<$Res, _$LocationGeographyImpl>
    implements _$$LocationGeographyImplCopyWith<$Res> {
  __$$LocationGeographyImplCopyWithImpl(
    _$LocationGeographyImpl _value,
    $Res Function(_$LocationGeographyImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of LocationGeography
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? coordinates = freezed,
    Object? terrain = freezed,
    Object? elevation = freezed,
    Object? area = freezed,
    Object? naturalFeatures = null,
    Object? landmarks = null,
    Object? waterSources = freezed,
    Object? vegetation = freezed,
    Object? naturalResources = null,
    Object? customGeography = null,
  }) {
    return _then(
      _$LocationGeographyImpl(
        coordinates: freezed == coordinates
            ? _value.coordinates
            : coordinates // ignore: cast_nullable_to_non_nullable
                  as LocationCoordinates?,
        terrain: freezed == terrain
            ? _value.terrain
            : terrain // ignore: cast_nullable_to_non_nullable
                  as String?,
        elevation: freezed == elevation
            ? _value.elevation
            : elevation // ignore: cast_nullable_to_non_nullable
                  as double?,
        area: freezed == area
            ? _value.area
            : area // ignore: cast_nullable_to_non_nullable
                  as double?,
        naturalFeatures: null == naturalFeatures
            ? _value._naturalFeatures
            : naturalFeatures // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        landmarks: null == landmarks
            ? _value._landmarks
            : landmarks // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        waterSources: freezed == waterSources
            ? _value.waterSources
            : waterSources // ignore: cast_nullable_to_non_nullable
                  as String?,
        vegetation: freezed == vegetation
            ? _value.vegetation
            : vegetation // ignore: cast_nullable_to_non_nullable
                  as String?,
        naturalResources: null == naturalResources
            ? _value._naturalResources
            : naturalResources // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customGeography: null == customGeography
            ? _value._customGeography
            : customGeography // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$LocationGeographyImpl implements _LocationGeography {
  const _$LocationGeographyImpl({
    this.coordinates,
    this.terrain,
    this.elevation,
    this.area,
    final List<String> naturalFeatures = const [],
    final List<String> landmarks = const [],
    this.waterSources,
    this.vegetation,
    final List<String> naturalResources = const [],
    final Map<String, dynamic> customGeography = const {},
  }) : _naturalFeatures = naturalFeatures,
       _landmarks = landmarks,
       _naturalResources = naturalResources,
       _customGeography = customGeography;

  factory _$LocationGeographyImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocationGeographyImplFromJson(json);

  @override
  final LocationCoordinates? coordinates;
  @override
  final String? terrain;
  // 地形
  @override
  final double? elevation;
  // 海拔
  @override
  final double? area;
  // 面积
  final List<String> _naturalFeatures;
  // 面积
  @override
  @JsonKey()
  List<String> get naturalFeatures {
    if (_naturalFeatures is EqualUnmodifiableListView) return _naturalFeatures;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_naturalFeatures);
  }

  // 自然特征
  final List<String> _landmarks;
  // 自然特征
  @override
  @JsonKey()
  List<String> get landmarks {
    if (_landmarks is EqualUnmodifiableListView) return _landmarks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_landmarks);
  }

  // 地标
  @override
  final String? waterSources;
  // 水源
  @override
  final String? vegetation;
  // 植被
  final List<String> _naturalResources;
  // 植被
  @override
  @JsonKey()
  List<String> get naturalResources {
    if (_naturalResources is EqualUnmodifiableListView)
      return _naturalResources;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_naturalResources);
  }

  // 自然资源
  final Map<String, dynamic> _customGeography;
  // 自然资源
  @override
  @JsonKey()
  Map<String, dynamic> get customGeography {
    if (_customGeography is EqualUnmodifiableMapView) return _customGeography;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customGeography);
  }

  @override
  String toString() {
    return 'LocationGeography(coordinates: $coordinates, terrain: $terrain, elevation: $elevation, area: $area, naturalFeatures: $naturalFeatures, landmarks: $landmarks, waterSources: $waterSources, vegetation: $vegetation, naturalResources: $naturalResources, customGeography: $customGeography)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationGeographyImpl &&
            (identical(other.coordinates, coordinates) ||
                other.coordinates == coordinates) &&
            (identical(other.terrain, terrain) || other.terrain == terrain) &&
            (identical(other.elevation, elevation) ||
                other.elevation == elevation) &&
            (identical(other.area, area) || other.area == area) &&
            const DeepCollectionEquality().equals(
              other._naturalFeatures,
              _naturalFeatures,
            ) &&
            const DeepCollectionEquality().equals(
              other._landmarks,
              _landmarks,
            ) &&
            (identical(other.waterSources, waterSources) ||
                other.waterSources == waterSources) &&
            (identical(other.vegetation, vegetation) ||
                other.vegetation == vegetation) &&
            const DeepCollectionEquality().equals(
              other._naturalResources,
              _naturalResources,
            ) &&
            const DeepCollectionEquality().equals(
              other._customGeography,
              _customGeography,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    coordinates,
    terrain,
    elevation,
    area,
    const DeepCollectionEquality().hash(_naturalFeatures),
    const DeepCollectionEquality().hash(_landmarks),
    waterSources,
    vegetation,
    const DeepCollectionEquality().hash(_naturalResources),
    const DeepCollectionEquality().hash(_customGeography),
  );

  /// Create a copy of LocationGeography
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationGeographyImplCopyWith<_$LocationGeographyImpl> get copyWith =>
      __$$LocationGeographyImplCopyWithImpl<_$LocationGeographyImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$LocationGeographyImplToJson(this);
  }
}

abstract class _LocationGeography implements LocationGeography {
  const factory _LocationGeography({
    final LocationCoordinates? coordinates,
    final String? terrain,
    final double? elevation,
    final double? area,
    final List<String> naturalFeatures,
    final List<String> landmarks,
    final String? waterSources,
    final String? vegetation,
    final List<String> naturalResources,
    final Map<String, dynamic> customGeography,
  }) = _$LocationGeographyImpl;

  factory _LocationGeography.fromJson(Map<String, dynamic> json) =
      _$LocationGeographyImpl.fromJson;

  @override
  LocationCoordinates? get coordinates;
  @override
  String? get terrain; // 地形
  @override
  double? get elevation; // 海拔
  @override
  double? get area; // 面积
  @override
  List<String> get naturalFeatures; // 自然特征
  @override
  List<String> get landmarks; // 地标
  @override
  String? get waterSources; // 水源
  @override
  String? get vegetation; // 植被
  @override
  List<String> get naturalResources; // 自然资源
  @override
  Map<String, dynamic> get customGeography;

  /// Create a copy of LocationGeography
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationGeographyImplCopyWith<_$LocationGeographyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LocationCoordinates _$LocationCoordinatesFromJson(Map<String, dynamic> json) {
  return _LocationCoordinates.fromJson(json);
}

/// @nodoc
mixin _$LocationCoordinates {
  double get latitude => throw _privateConstructorUsedError; // 纬度
  double get longitude => throw _privateConstructorUsedError; // 经度
  String? get coordinateSystem => throw _privateConstructorUsedError; // 坐标系统
  Map<String, dynamic> get customCoordinates =>
      throw _privateConstructorUsedError;

  /// Serializes this LocationCoordinates to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LocationCoordinates
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LocationCoordinatesCopyWith<LocationCoordinates> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationCoordinatesCopyWith<$Res> {
  factory $LocationCoordinatesCopyWith(
    LocationCoordinates value,
    $Res Function(LocationCoordinates) then,
  ) = _$LocationCoordinatesCopyWithImpl<$Res, LocationCoordinates>;
  @useResult
  $Res call({
    double latitude,
    double longitude,
    String? coordinateSystem,
    Map<String, dynamic> customCoordinates,
  });
}

/// @nodoc
class _$LocationCoordinatesCopyWithImpl<$Res, $Val extends LocationCoordinates>
    implements $LocationCoordinatesCopyWith<$Res> {
  _$LocationCoordinatesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationCoordinates
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latitude = null,
    Object? longitude = null,
    Object? coordinateSystem = freezed,
    Object? customCoordinates = null,
  }) {
    return _then(
      _value.copyWith(
            latitude: null == latitude
                ? _value.latitude
                : latitude // ignore: cast_nullable_to_non_nullable
                      as double,
            longitude: null == longitude
                ? _value.longitude
                : longitude // ignore: cast_nullable_to_non_nullable
                      as double,
            coordinateSystem: freezed == coordinateSystem
                ? _value.coordinateSystem
                : coordinateSystem // ignore: cast_nullable_to_non_nullable
                      as String?,
            customCoordinates: null == customCoordinates
                ? _value.customCoordinates
                : customCoordinates // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$LocationCoordinatesImplCopyWith<$Res>
    implements $LocationCoordinatesCopyWith<$Res> {
  factory _$$LocationCoordinatesImplCopyWith(
    _$LocationCoordinatesImpl value,
    $Res Function(_$LocationCoordinatesImpl) then,
  ) = __$$LocationCoordinatesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    double latitude,
    double longitude,
    String? coordinateSystem,
    Map<String, dynamic> customCoordinates,
  });
}

/// @nodoc
class __$$LocationCoordinatesImplCopyWithImpl<$Res>
    extends _$LocationCoordinatesCopyWithImpl<$Res, _$LocationCoordinatesImpl>
    implements _$$LocationCoordinatesImplCopyWith<$Res> {
  __$$LocationCoordinatesImplCopyWithImpl(
    _$LocationCoordinatesImpl _value,
    $Res Function(_$LocationCoordinatesImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of LocationCoordinates
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latitude = null,
    Object? longitude = null,
    Object? coordinateSystem = freezed,
    Object? customCoordinates = null,
  }) {
    return _then(
      _$LocationCoordinatesImpl(
        latitude: null == latitude
            ? _value.latitude
            : latitude // ignore: cast_nullable_to_non_nullable
                  as double,
        longitude: null == longitude
            ? _value.longitude
            : longitude // ignore: cast_nullable_to_non_nullable
                  as double,
        coordinateSystem: freezed == coordinateSystem
            ? _value.coordinateSystem
            : coordinateSystem // ignore: cast_nullable_to_non_nullable
                  as String?,
        customCoordinates: null == customCoordinates
            ? _value._customCoordinates
            : customCoordinates // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$LocationCoordinatesImpl implements _LocationCoordinates {
  const _$LocationCoordinatesImpl({
    required this.latitude,
    required this.longitude,
    this.coordinateSystem,
    final Map<String, dynamic> customCoordinates = const {},
  }) : _customCoordinates = customCoordinates;

  factory _$LocationCoordinatesImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocationCoordinatesImplFromJson(json);

  @override
  final double latitude;
  // 纬度
  @override
  final double longitude;
  // 经度
  @override
  final String? coordinateSystem;
  // 坐标系统
  final Map<String, dynamic> _customCoordinates;
  // 坐标系统
  @override
  @JsonKey()
  Map<String, dynamic> get customCoordinates {
    if (_customCoordinates is EqualUnmodifiableMapView)
      return _customCoordinates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customCoordinates);
  }

  @override
  String toString() {
    return 'LocationCoordinates(latitude: $latitude, longitude: $longitude, coordinateSystem: $coordinateSystem, customCoordinates: $customCoordinates)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationCoordinatesImpl &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.coordinateSystem, coordinateSystem) ||
                other.coordinateSystem == coordinateSystem) &&
            const DeepCollectionEquality().equals(
              other._customCoordinates,
              _customCoordinates,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    latitude,
    longitude,
    coordinateSystem,
    const DeepCollectionEquality().hash(_customCoordinates),
  );

  /// Create a copy of LocationCoordinates
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationCoordinatesImplCopyWith<_$LocationCoordinatesImpl> get copyWith =>
      __$$LocationCoordinatesImplCopyWithImpl<_$LocationCoordinatesImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$LocationCoordinatesImplToJson(this);
  }
}

abstract class _LocationCoordinates implements LocationCoordinates {
  const factory _LocationCoordinates({
    required final double latitude,
    required final double longitude,
    final String? coordinateSystem,
    final Map<String, dynamic> customCoordinates,
  }) = _$LocationCoordinatesImpl;

  factory _LocationCoordinates.fromJson(Map<String, dynamic> json) =
      _$LocationCoordinatesImpl.fromJson;

  @override
  double get latitude; // 纬度
  @override
  double get longitude; // 经度
  @override
  String? get coordinateSystem; // 坐标系统
  @override
  Map<String, dynamic> get customCoordinates;

  /// Create a copy of LocationCoordinates
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationCoordinatesImplCopyWith<_$LocationCoordinatesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LocationClimate _$LocationClimateFromJson(Map<String, dynamic> json) {
  return _LocationClimate.fromJson(json);
}

/// @nodoc
mixin _$LocationClimate {
  String? get climateType => throw _privateConstructorUsedError; // 气候类型
  List<Season> get seasons => throw _privateConstructorUsedError;
  double? get averageTemperature => throw _privateConstructorUsedError; // 平均温度
  double? get averageRainfall => throw _privateConstructorUsedError; // 平均降雨量
  List<String> get weatherPatterns =>
      throw _privateConstructorUsedError; // 天气模式
  List<WeatherEvent> get extremeWeather =>
      throw _privateConstructorUsedError; // 极端天气
  Map<String, dynamic> get customClimate => throw _privateConstructorUsedError;

  /// Serializes this LocationClimate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LocationClimate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LocationClimateCopyWith<LocationClimate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationClimateCopyWith<$Res> {
  factory $LocationClimateCopyWith(
    LocationClimate value,
    $Res Function(LocationClimate) then,
  ) = _$LocationClimateCopyWithImpl<$Res, LocationClimate>;
  @useResult
  $Res call({
    String? climateType,
    List<Season> seasons,
    double? averageTemperature,
    double? averageRainfall,
    List<String> weatherPatterns,
    List<WeatherEvent> extremeWeather,
    Map<String, dynamic> customClimate,
  });
}

/// @nodoc
class _$LocationClimateCopyWithImpl<$Res, $Val extends LocationClimate>
    implements $LocationClimateCopyWith<$Res> {
  _$LocationClimateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationClimate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? climateType = freezed,
    Object? seasons = null,
    Object? averageTemperature = freezed,
    Object? averageRainfall = freezed,
    Object? weatherPatterns = null,
    Object? extremeWeather = null,
    Object? customClimate = null,
  }) {
    return _then(
      _value.copyWith(
            climateType: freezed == climateType
                ? _value.climateType
                : climateType // ignore: cast_nullable_to_non_nullable
                      as String?,
            seasons: null == seasons
                ? _value.seasons
                : seasons // ignore: cast_nullable_to_non_nullable
                      as List<Season>,
            averageTemperature: freezed == averageTemperature
                ? _value.averageTemperature
                : averageTemperature // ignore: cast_nullable_to_non_nullable
                      as double?,
            averageRainfall: freezed == averageRainfall
                ? _value.averageRainfall
                : averageRainfall // ignore: cast_nullable_to_non_nullable
                      as double?,
            weatherPatterns: null == weatherPatterns
                ? _value.weatherPatterns
                : weatherPatterns // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            extremeWeather: null == extremeWeather
                ? _value.extremeWeather
                : extremeWeather // ignore: cast_nullable_to_non_nullable
                      as List<WeatherEvent>,
            customClimate: null == customClimate
                ? _value.customClimate
                : customClimate // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$LocationClimateImplCopyWith<$Res>
    implements $LocationClimateCopyWith<$Res> {
  factory _$$LocationClimateImplCopyWith(
    _$LocationClimateImpl value,
    $Res Function(_$LocationClimateImpl) then,
  ) = __$$LocationClimateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String? climateType,
    List<Season> seasons,
    double? averageTemperature,
    double? averageRainfall,
    List<String> weatherPatterns,
    List<WeatherEvent> extremeWeather,
    Map<String, dynamic> customClimate,
  });
}

/// @nodoc
class __$$LocationClimateImplCopyWithImpl<$Res>
    extends _$LocationClimateCopyWithImpl<$Res, _$LocationClimateImpl>
    implements _$$LocationClimateImplCopyWith<$Res> {
  __$$LocationClimateImplCopyWithImpl(
    _$LocationClimateImpl _value,
    $Res Function(_$LocationClimateImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of LocationClimate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? climateType = freezed,
    Object? seasons = null,
    Object? averageTemperature = freezed,
    Object? averageRainfall = freezed,
    Object? weatherPatterns = null,
    Object? extremeWeather = null,
    Object? customClimate = null,
  }) {
    return _then(
      _$LocationClimateImpl(
        climateType: freezed == climateType
            ? _value.climateType
            : climateType // ignore: cast_nullable_to_non_nullable
                  as String?,
        seasons: null == seasons
            ? _value._seasons
            : seasons // ignore: cast_nullable_to_non_nullable
                  as List<Season>,
        averageTemperature: freezed == averageTemperature
            ? _value.averageTemperature
            : averageTemperature // ignore: cast_nullable_to_non_nullable
                  as double?,
        averageRainfall: freezed == averageRainfall
            ? _value.averageRainfall
            : averageRainfall // ignore: cast_nullable_to_non_nullable
                  as double?,
        weatherPatterns: null == weatherPatterns
            ? _value._weatherPatterns
            : weatherPatterns // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        extremeWeather: null == extremeWeather
            ? _value._extremeWeather
            : extremeWeather // ignore: cast_nullable_to_non_nullable
                  as List<WeatherEvent>,
        customClimate: null == customClimate
            ? _value._customClimate
            : customClimate // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$LocationClimateImpl implements _LocationClimate {
  const _$LocationClimateImpl({
    this.climateType,
    final List<Season> seasons = const [],
    this.averageTemperature,
    this.averageRainfall,
    final List<String> weatherPatterns = const [],
    final List<WeatherEvent> extremeWeather = const [],
    final Map<String, dynamic> customClimate = const {},
  }) : _seasons = seasons,
       _weatherPatterns = weatherPatterns,
       _extremeWeather = extremeWeather,
       _customClimate = customClimate;

  factory _$LocationClimateImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocationClimateImplFromJson(json);

  @override
  final String? climateType;
  // 气候类型
  final List<Season> _seasons;
  // 气候类型
  @override
  @JsonKey()
  List<Season> get seasons {
    if (_seasons is EqualUnmodifiableListView) return _seasons;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_seasons);
  }

  @override
  final double? averageTemperature;
  // 平均温度
  @override
  final double? averageRainfall;
  // 平均降雨量
  final List<String> _weatherPatterns;
  // 平均降雨量
  @override
  @JsonKey()
  List<String> get weatherPatterns {
    if (_weatherPatterns is EqualUnmodifiableListView) return _weatherPatterns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_weatherPatterns);
  }

  // 天气模式
  final List<WeatherEvent> _extremeWeather;
  // 天气模式
  @override
  @JsonKey()
  List<WeatherEvent> get extremeWeather {
    if (_extremeWeather is EqualUnmodifiableListView) return _extremeWeather;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_extremeWeather);
  }

  // 极端天气
  final Map<String, dynamic> _customClimate;
  // 极端天气
  @override
  @JsonKey()
  Map<String, dynamic> get customClimate {
    if (_customClimate is EqualUnmodifiableMapView) return _customClimate;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customClimate);
  }

  @override
  String toString() {
    return 'LocationClimate(climateType: $climateType, seasons: $seasons, averageTemperature: $averageTemperature, averageRainfall: $averageRainfall, weatherPatterns: $weatherPatterns, extremeWeather: $extremeWeather, customClimate: $customClimate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationClimateImpl &&
            (identical(other.climateType, climateType) ||
                other.climateType == climateType) &&
            const DeepCollectionEquality().equals(other._seasons, _seasons) &&
            (identical(other.averageTemperature, averageTemperature) ||
                other.averageTemperature == averageTemperature) &&
            (identical(other.averageRainfall, averageRainfall) ||
                other.averageRainfall == averageRainfall) &&
            const DeepCollectionEquality().equals(
              other._weatherPatterns,
              _weatherPatterns,
            ) &&
            const DeepCollectionEquality().equals(
              other._extremeWeather,
              _extremeWeather,
            ) &&
            const DeepCollectionEquality().equals(
              other._customClimate,
              _customClimate,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    climateType,
    const DeepCollectionEquality().hash(_seasons),
    averageTemperature,
    averageRainfall,
    const DeepCollectionEquality().hash(_weatherPatterns),
    const DeepCollectionEquality().hash(_extremeWeather),
    const DeepCollectionEquality().hash(_customClimate),
  );

  /// Create a copy of LocationClimate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationClimateImplCopyWith<_$LocationClimateImpl> get copyWith =>
      __$$LocationClimateImplCopyWithImpl<_$LocationClimateImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$LocationClimateImplToJson(this);
  }
}

abstract class _LocationClimate implements LocationClimate {
  const factory _LocationClimate({
    final String? climateType,
    final List<Season> seasons,
    final double? averageTemperature,
    final double? averageRainfall,
    final List<String> weatherPatterns,
    final List<WeatherEvent> extremeWeather,
    final Map<String, dynamic> customClimate,
  }) = _$LocationClimateImpl;

  factory _LocationClimate.fromJson(Map<String, dynamic> json) =
      _$LocationClimateImpl.fromJson;

  @override
  String? get climateType; // 气候类型
  @override
  List<Season> get seasons;
  @override
  double? get averageTemperature; // 平均温度
  @override
  double? get averageRainfall; // 平均降雨量
  @override
  List<String> get weatherPatterns; // 天气模式
  @override
  List<WeatherEvent> get extremeWeather; // 极端天气
  @override
  Map<String, dynamic> get customClimate;

  /// Create a copy of LocationClimate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationClimateImplCopyWith<_$LocationClimateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Season _$SeasonFromJson(Map<String, dynamic> json) {
  return _Season.fromJson(json);
}

/// @nodoc
mixin _$Season {
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  int? get durationDays => throw _privateConstructorUsedError; // 持续天数
  double? get averageTemperature => throw _privateConstructorUsedError;
  double? get averageRainfall => throw _privateConstructorUsedError;
  List<String> get characteristics => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this Season to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Season
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SeasonCopyWith<Season> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SeasonCopyWith<$Res> {
  factory $SeasonCopyWith(Season value, $Res Function(Season) then) =
      _$SeasonCopyWithImpl<$Res, Season>;
  @useResult
  $Res call({
    String name,
    String? description,
    int? durationDays,
    double? averageTemperature,
    double? averageRainfall,
    List<String> characteristics,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$SeasonCopyWithImpl<$Res, $Val extends Season>
    implements $SeasonCopyWith<$Res> {
  _$SeasonCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Season
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? durationDays = freezed,
    Object? averageTemperature = freezed,
    Object? averageRainfall = freezed,
    Object? characteristics = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            durationDays: freezed == durationDays
                ? _value.durationDays
                : durationDays // ignore: cast_nullable_to_non_nullable
                      as int?,
            averageTemperature: freezed == averageTemperature
                ? _value.averageTemperature
                : averageTemperature // ignore: cast_nullable_to_non_nullable
                      as double?,
            averageRainfall: freezed == averageRainfall
                ? _value.averageRainfall
                : averageRainfall // ignore: cast_nullable_to_non_nullable
                      as double?,
            characteristics: null == characteristics
                ? _value.characteristics
                : characteristics // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$SeasonImplCopyWith<$Res> implements $SeasonCopyWith<$Res> {
  factory _$$SeasonImplCopyWith(
    _$SeasonImpl value,
    $Res Function(_$SeasonImpl) then,
  ) = __$$SeasonImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    String? description,
    int? durationDays,
    double? averageTemperature,
    double? averageRainfall,
    List<String> characteristics,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$SeasonImplCopyWithImpl<$Res>
    extends _$SeasonCopyWithImpl<$Res, _$SeasonImpl>
    implements _$$SeasonImplCopyWith<$Res> {
  __$$SeasonImplCopyWithImpl(
    _$SeasonImpl _value,
    $Res Function(_$SeasonImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Season
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? durationDays = freezed,
    Object? averageTemperature = freezed,
    Object? averageRainfall = freezed,
    Object? characteristics = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$SeasonImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        durationDays: freezed == durationDays
            ? _value.durationDays
            : durationDays // ignore: cast_nullable_to_non_nullable
                  as int?,
        averageTemperature: freezed == averageTemperature
            ? _value.averageTemperature
            : averageTemperature // ignore: cast_nullable_to_non_nullable
                  as double?,
        averageRainfall: freezed == averageRainfall
            ? _value.averageRainfall
            : averageRainfall // ignore: cast_nullable_to_non_nullable
                  as double?,
        characteristics: null == characteristics
            ? _value._characteristics
            : characteristics // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SeasonImpl implements _Season {
  const _$SeasonImpl({
    required this.name,
    this.description,
    this.durationDays,
    this.averageTemperature,
    this.averageRainfall,
    final List<String> characteristics = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _characteristics = characteristics,
       _customAttributes = customAttributes;

  factory _$SeasonImpl.fromJson(Map<String, dynamic> json) =>
      _$$SeasonImplFromJson(json);

  @override
  final String name;
  @override
  final String? description;
  @override
  final int? durationDays;
  // 持续天数
  @override
  final double? averageTemperature;
  @override
  final double? averageRainfall;
  final List<String> _characteristics;
  @override
  @JsonKey()
  List<String> get characteristics {
    if (_characteristics is EqualUnmodifiableListView) return _characteristics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_characteristics);
  }

  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'Season(name: $name, description: $description, durationDays: $durationDays, averageTemperature: $averageTemperature, averageRainfall: $averageRainfall, characteristics: $characteristics, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SeasonImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.durationDays, durationDays) ||
                other.durationDays == durationDays) &&
            (identical(other.averageTemperature, averageTemperature) ||
                other.averageTemperature == averageTemperature) &&
            (identical(other.averageRainfall, averageRainfall) ||
                other.averageRainfall == averageRainfall) &&
            const DeepCollectionEquality().equals(
              other._characteristics,
              _characteristics,
            ) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    description,
    durationDays,
    averageTemperature,
    averageRainfall,
    const DeepCollectionEquality().hash(_characteristics),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of Season
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SeasonImplCopyWith<_$SeasonImpl> get copyWith =>
      __$$SeasonImplCopyWithImpl<_$SeasonImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SeasonImplToJson(this);
  }
}

abstract class _Season implements Season {
  const factory _Season({
    required final String name,
    final String? description,
    final int? durationDays,
    final double? averageTemperature,
    final double? averageRainfall,
    final List<String> characteristics,
    final Map<String, dynamic> customAttributes,
  }) = _$SeasonImpl;

  factory _Season.fromJson(Map<String, dynamic> json) = _$SeasonImpl.fromJson;

  @override
  String get name;
  @override
  String? get description;
  @override
  int? get durationDays; // 持续天数
  @override
  double? get averageTemperature;
  @override
  double? get averageRainfall;
  @override
  List<String> get characteristics;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of Season
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SeasonImplCopyWith<_$SeasonImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WeatherEvent _$WeatherEventFromJson(Map<String, dynamic> json) {
  return _WeatherEvent.fromJson(json);
}

/// @nodoc
mixin _$WeatherEvent {
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  WeatherEventType? get type => throw _privateConstructorUsedError;
  WeatherEventSeverity? get severity => throw _privateConstructorUsedError;
  int? get frequency => throw _privateConstructorUsedError; // 发生频率 (每年次数)
  int? get durationDays => throw _privateConstructorUsedError; // 持续天数
  List<String> get effects => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this WeatherEvent to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WeatherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WeatherEventCopyWith<WeatherEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WeatherEventCopyWith<$Res> {
  factory $WeatherEventCopyWith(
    WeatherEvent value,
    $Res Function(WeatherEvent) then,
  ) = _$WeatherEventCopyWithImpl<$Res, WeatherEvent>;
  @useResult
  $Res call({
    String name,
    String? description,
    WeatherEventType? type,
    WeatherEventSeverity? severity,
    int? frequency,
    int? durationDays,
    List<String> effects,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$WeatherEventCopyWithImpl<$Res, $Val extends WeatherEvent>
    implements $WeatherEventCopyWith<$Res> {
  _$WeatherEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WeatherEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? type = freezed,
    Object? severity = freezed,
    Object? frequency = freezed,
    Object? durationDays = freezed,
    Object? effects = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as WeatherEventType?,
            severity: freezed == severity
                ? _value.severity
                : severity // ignore: cast_nullable_to_non_nullable
                      as WeatherEventSeverity?,
            frequency: freezed == frequency
                ? _value.frequency
                : frequency // ignore: cast_nullable_to_non_nullable
                      as int?,
            durationDays: freezed == durationDays
                ? _value.durationDays
                : durationDays // ignore: cast_nullable_to_non_nullable
                      as int?,
            effects: null == effects
                ? _value.effects
                : effects // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$WeatherEventImplCopyWith<$Res>
    implements $WeatherEventCopyWith<$Res> {
  factory _$$WeatherEventImplCopyWith(
    _$WeatherEventImpl value,
    $Res Function(_$WeatherEventImpl) then,
  ) = __$$WeatherEventImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    String? description,
    WeatherEventType? type,
    WeatherEventSeverity? severity,
    int? frequency,
    int? durationDays,
    List<String> effects,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$WeatherEventImplCopyWithImpl<$Res>
    extends _$WeatherEventCopyWithImpl<$Res, _$WeatherEventImpl>
    implements _$$WeatherEventImplCopyWith<$Res> {
  __$$WeatherEventImplCopyWithImpl(
    _$WeatherEventImpl _value,
    $Res Function(_$WeatherEventImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of WeatherEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? type = freezed,
    Object? severity = freezed,
    Object? frequency = freezed,
    Object? durationDays = freezed,
    Object? effects = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$WeatherEventImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as WeatherEventType?,
        severity: freezed == severity
            ? _value.severity
            : severity // ignore: cast_nullable_to_non_nullable
                  as WeatherEventSeverity?,
        frequency: freezed == frequency
            ? _value.frequency
            : frequency // ignore: cast_nullable_to_non_nullable
                  as int?,
        durationDays: freezed == durationDays
            ? _value.durationDays
            : durationDays // ignore: cast_nullable_to_non_nullable
                  as int?,
        effects: null == effects
            ? _value._effects
            : effects // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$WeatherEventImpl implements _WeatherEvent {
  const _$WeatherEventImpl({
    required this.name,
    this.description,
    this.type,
    this.severity,
    this.frequency,
    this.durationDays,
    final List<String> effects = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _effects = effects,
       _customAttributes = customAttributes;

  factory _$WeatherEventImpl.fromJson(Map<String, dynamic> json) =>
      _$$WeatherEventImplFromJson(json);

  @override
  final String name;
  @override
  final String? description;
  @override
  final WeatherEventType? type;
  @override
  final WeatherEventSeverity? severity;
  @override
  final int? frequency;
  // 发生频率 (每年次数)
  @override
  final int? durationDays;
  // 持续天数
  final List<String> _effects;
  // 持续天数
  @override
  @JsonKey()
  List<String> get effects {
    if (_effects is EqualUnmodifiableListView) return _effects;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_effects);
  }

  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'WeatherEvent(name: $name, description: $description, type: $type, severity: $severity, frequency: $frequency, durationDays: $durationDays, effects: $effects, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WeatherEventImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.severity, severity) ||
                other.severity == severity) &&
            (identical(other.frequency, frequency) ||
                other.frequency == frequency) &&
            (identical(other.durationDays, durationDays) ||
                other.durationDays == durationDays) &&
            const DeepCollectionEquality().equals(other._effects, _effects) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    description,
    type,
    severity,
    frequency,
    durationDays,
    const DeepCollectionEquality().hash(_effects),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of WeatherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WeatherEventImplCopyWith<_$WeatherEventImpl> get copyWith =>
      __$$WeatherEventImplCopyWithImpl<_$WeatherEventImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WeatherEventImplToJson(this);
  }
}

abstract class _WeatherEvent implements WeatherEvent {
  const factory _WeatherEvent({
    required final String name,
    final String? description,
    final WeatherEventType? type,
    final WeatherEventSeverity? severity,
    final int? frequency,
    final int? durationDays,
    final List<String> effects,
    final Map<String, dynamic> customAttributes,
  }) = _$WeatherEventImpl;

  factory _WeatherEvent.fromJson(Map<String, dynamic> json) =
      _$WeatherEventImpl.fromJson;

  @override
  String get name;
  @override
  String? get description;
  @override
  WeatherEventType? get type;
  @override
  WeatherEventSeverity? get severity;
  @override
  int? get frequency; // 发生频率 (每年次数)
  @override
  int? get durationDays; // 持续天数
  @override
  List<String> get effects;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of WeatherEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WeatherEventImplCopyWith<_$WeatherEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LocationCulture _$LocationCultureFromJson(Map<String, dynamic> json) {
  return _LocationCulture.fromJson(json);
}

/// @nodoc
mixin _$LocationCulture {
  List<String> get ethnicGroups => throw _privateConstructorUsedError; // 民族群体
  List<String> get languages => throw _privateConstructorUsedError; // 语言
  List<String> get religions => throw _privateConstructorUsedError; // 宗教
  List<CulturalTradition> get traditions =>
      throw _privateConstructorUsedError; // 传统
  List<String> get festivals => throw _privateConstructorUsedError; // 节日
  List<String> get customs => throw _privateConstructorUsedError; // 习俗
  String? get architecture => throw _privateConstructorUsedError; // 建筑风格
  String? get artStyle => throw _privateConstructorUsedError; // 艺术风格
  List<String> get cuisine => throw _privateConstructorUsedError; // 美食
  List<String> get clothing => throw _privateConstructorUsedError; // 服装
  Map<String, dynamic> get customCulture => throw _privateConstructorUsedError;

  /// Serializes this LocationCulture to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LocationCulture
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LocationCultureCopyWith<LocationCulture> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationCultureCopyWith<$Res> {
  factory $LocationCultureCopyWith(
    LocationCulture value,
    $Res Function(LocationCulture) then,
  ) = _$LocationCultureCopyWithImpl<$Res, LocationCulture>;
  @useResult
  $Res call({
    List<String> ethnicGroups,
    List<String> languages,
    List<String> religions,
    List<CulturalTradition> traditions,
    List<String> festivals,
    List<String> customs,
    String? architecture,
    String? artStyle,
    List<String> cuisine,
    List<String> clothing,
    Map<String, dynamic> customCulture,
  });
}

/// @nodoc
class _$LocationCultureCopyWithImpl<$Res, $Val extends LocationCulture>
    implements $LocationCultureCopyWith<$Res> {
  _$LocationCultureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationCulture
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ethnicGroups = null,
    Object? languages = null,
    Object? religions = null,
    Object? traditions = null,
    Object? festivals = null,
    Object? customs = null,
    Object? architecture = freezed,
    Object? artStyle = freezed,
    Object? cuisine = null,
    Object? clothing = null,
    Object? customCulture = null,
  }) {
    return _then(
      _value.copyWith(
            ethnicGroups: null == ethnicGroups
                ? _value.ethnicGroups
                : ethnicGroups // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            languages: null == languages
                ? _value.languages
                : languages // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            religions: null == religions
                ? _value.religions
                : religions // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            traditions: null == traditions
                ? _value.traditions
                : traditions // ignore: cast_nullable_to_non_nullable
                      as List<CulturalTradition>,
            festivals: null == festivals
                ? _value.festivals
                : festivals // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customs: null == customs
                ? _value.customs
                : customs // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            architecture: freezed == architecture
                ? _value.architecture
                : architecture // ignore: cast_nullable_to_non_nullable
                      as String?,
            artStyle: freezed == artStyle
                ? _value.artStyle
                : artStyle // ignore: cast_nullable_to_non_nullable
                      as String?,
            cuisine: null == cuisine
                ? _value.cuisine
                : cuisine // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            clothing: null == clothing
                ? _value.clothing
                : clothing // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customCulture: null == customCulture
                ? _value.customCulture
                : customCulture // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$LocationCultureImplCopyWith<$Res>
    implements $LocationCultureCopyWith<$Res> {
  factory _$$LocationCultureImplCopyWith(
    _$LocationCultureImpl value,
    $Res Function(_$LocationCultureImpl) then,
  ) = __$$LocationCultureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    List<String> ethnicGroups,
    List<String> languages,
    List<String> religions,
    List<CulturalTradition> traditions,
    List<String> festivals,
    List<String> customs,
    String? architecture,
    String? artStyle,
    List<String> cuisine,
    List<String> clothing,
    Map<String, dynamic> customCulture,
  });
}

/// @nodoc
class __$$LocationCultureImplCopyWithImpl<$Res>
    extends _$LocationCultureCopyWithImpl<$Res, _$LocationCultureImpl>
    implements _$$LocationCultureImplCopyWith<$Res> {
  __$$LocationCultureImplCopyWithImpl(
    _$LocationCultureImpl _value,
    $Res Function(_$LocationCultureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of LocationCulture
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ethnicGroups = null,
    Object? languages = null,
    Object? religions = null,
    Object? traditions = null,
    Object? festivals = null,
    Object? customs = null,
    Object? architecture = freezed,
    Object? artStyle = freezed,
    Object? cuisine = null,
    Object? clothing = null,
    Object? customCulture = null,
  }) {
    return _then(
      _$LocationCultureImpl(
        ethnicGroups: null == ethnicGroups
            ? _value._ethnicGroups
            : ethnicGroups // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        languages: null == languages
            ? _value._languages
            : languages // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        religions: null == religions
            ? _value._religions
            : religions // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        traditions: null == traditions
            ? _value._traditions
            : traditions // ignore: cast_nullable_to_non_nullable
                  as List<CulturalTradition>,
        festivals: null == festivals
            ? _value._festivals
            : festivals // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customs: null == customs
            ? _value._customs
            : customs // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        architecture: freezed == architecture
            ? _value.architecture
            : architecture // ignore: cast_nullable_to_non_nullable
                  as String?,
        artStyle: freezed == artStyle
            ? _value.artStyle
            : artStyle // ignore: cast_nullable_to_non_nullable
                  as String?,
        cuisine: null == cuisine
            ? _value._cuisine
            : cuisine // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        clothing: null == clothing
            ? _value._clothing
            : clothing // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customCulture: null == customCulture
            ? _value._customCulture
            : customCulture // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$LocationCultureImpl implements _LocationCulture {
  const _$LocationCultureImpl({
    final List<String> ethnicGroups = const [],
    final List<String> languages = const [],
    final List<String> religions = const [],
    final List<CulturalTradition> traditions = const [],
    final List<String> festivals = const [],
    final List<String> customs = const [],
    this.architecture,
    this.artStyle,
    final List<String> cuisine = const [],
    final List<String> clothing = const [],
    final Map<String, dynamic> customCulture = const {},
  }) : _ethnicGroups = ethnicGroups,
       _languages = languages,
       _religions = religions,
       _traditions = traditions,
       _festivals = festivals,
       _customs = customs,
       _cuisine = cuisine,
       _clothing = clothing,
       _customCulture = customCulture;

  factory _$LocationCultureImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocationCultureImplFromJson(json);

  final List<String> _ethnicGroups;
  @override
  @JsonKey()
  List<String> get ethnicGroups {
    if (_ethnicGroups is EqualUnmodifiableListView) return _ethnicGroups;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_ethnicGroups);
  }

  // 民族群体
  final List<String> _languages;
  // 民族群体
  @override
  @JsonKey()
  List<String> get languages {
    if (_languages is EqualUnmodifiableListView) return _languages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_languages);
  }

  // 语言
  final List<String> _religions;
  // 语言
  @override
  @JsonKey()
  List<String> get religions {
    if (_religions is EqualUnmodifiableListView) return _religions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_religions);
  }

  // 宗教
  final List<CulturalTradition> _traditions;
  // 宗教
  @override
  @JsonKey()
  List<CulturalTradition> get traditions {
    if (_traditions is EqualUnmodifiableListView) return _traditions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_traditions);
  }

  // 传统
  final List<String> _festivals;
  // 传统
  @override
  @JsonKey()
  List<String> get festivals {
    if (_festivals is EqualUnmodifiableListView) return _festivals;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_festivals);
  }

  // 节日
  final List<String> _customs;
  // 节日
  @override
  @JsonKey()
  List<String> get customs {
    if (_customs is EqualUnmodifiableListView) return _customs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_customs);
  }

  // 习俗
  @override
  final String? architecture;
  // 建筑风格
  @override
  final String? artStyle;
  // 艺术风格
  final List<String> _cuisine;
  // 艺术风格
  @override
  @JsonKey()
  List<String> get cuisine {
    if (_cuisine is EqualUnmodifiableListView) return _cuisine;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_cuisine);
  }

  // 美食
  final List<String> _clothing;
  // 美食
  @override
  @JsonKey()
  List<String> get clothing {
    if (_clothing is EqualUnmodifiableListView) return _clothing;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_clothing);
  }

  // 服装
  final Map<String, dynamic> _customCulture;
  // 服装
  @override
  @JsonKey()
  Map<String, dynamic> get customCulture {
    if (_customCulture is EqualUnmodifiableMapView) return _customCulture;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customCulture);
  }

  @override
  String toString() {
    return 'LocationCulture(ethnicGroups: $ethnicGroups, languages: $languages, religions: $religions, traditions: $traditions, festivals: $festivals, customs: $customs, architecture: $architecture, artStyle: $artStyle, cuisine: $cuisine, clothing: $clothing, customCulture: $customCulture)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationCultureImpl &&
            const DeepCollectionEquality().equals(
              other._ethnicGroups,
              _ethnicGroups,
            ) &&
            const DeepCollectionEquality().equals(
              other._languages,
              _languages,
            ) &&
            const DeepCollectionEquality().equals(
              other._religions,
              _religions,
            ) &&
            const DeepCollectionEquality().equals(
              other._traditions,
              _traditions,
            ) &&
            const DeepCollectionEquality().equals(
              other._festivals,
              _festivals,
            ) &&
            const DeepCollectionEquality().equals(other._customs, _customs) &&
            (identical(other.architecture, architecture) ||
                other.architecture == architecture) &&
            (identical(other.artStyle, artStyle) ||
                other.artStyle == artStyle) &&
            const DeepCollectionEquality().equals(other._cuisine, _cuisine) &&
            const DeepCollectionEquality().equals(other._clothing, _clothing) &&
            const DeepCollectionEquality().equals(
              other._customCulture,
              _customCulture,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_ethnicGroups),
    const DeepCollectionEquality().hash(_languages),
    const DeepCollectionEquality().hash(_religions),
    const DeepCollectionEquality().hash(_traditions),
    const DeepCollectionEquality().hash(_festivals),
    const DeepCollectionEquality().hash(_customs),
    architecture,
    artStyle,
    const DeepCollectionEquality().hash(_cuisine),
    const DeepCollectionEquality().hash(_clothing),
    const DeepCollectionEquality().hash(_customCulture),
  );

  /// Create a copy of LocationCulture
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationCultureImplCopyWith<_$LocationCultureImpl> get copyWith =>
      __$$LocationCultureImplCopyWithImpl<_$LocationCultureImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$LocationCultureImplToJson(this);
  }
}

abstract class _LocationCulture implements LocationCulture {
  const factory _LocationCulture({
    final List<String> ethnicGroups,
    final List<String> languages,
    final List<String> religions,
    final List<CulturalTradition> traditions,
    final List<String> festivals,
    final List<String> customs,
    final String? architecture,
    final String? artStyle,
    final List<String> cuisine,
    final List<String> clothing,
    final Map<String, dynamic> customCulture,
  }) = _$LocationCultureImpl;

  factory _LocationCulture.fromJson(Map<String, dynamic> json) =
      _$LocationCultureImpl.fromJson;

  @override
  List<String> get ethnicGroups; // 民族群体
  @override
  List<String> get languages; // 语言
  @override
  List<String> get religions; // 宗教
  @override
  List<CulturalTradition> get traditions; // 传统
  @override
  List<String> get festivals; // 节日
  @override
  List<String> get customs; // 习俗
  @override
  String? get architecture; // 建筑风格
  @override
  String? get artStyle; // 艺术风格
  @override
  List<String> get cuisine; // 美食
  @override
  List<String> get clothing; // 服装
  @override
  Map<String, dynamic> get customCulture;

  /// Create a copy of LocationCulture
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationCultureImplCopyWith<_$LocationCultureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CulturalTradition _$CulturalTraditionFromJson(Map<String, dynamic> json) {
  return _CulturalTradition.fromJson(json);
}

/// @nodoc
mixin _$CulturalTradition {
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get origin => throw _privateConstructorUsedError;
  TraditionType? get type => throw _privateConstructorUsedError;
  List<String> get practices => throw _privateConstructorUsedError;
  String? get significance => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this CulturalTradition to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CulturalTradition
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CulturalTraditionCopyWith<CulturalTradition> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CulturalTraditionCopyWith<$Res> {
  factory $CulturalTraditionCopyWith(
    CulturalTradition value,
    $Res Function(CulturalTradition) then,
  ) = _$CulturalTraditionCopyWithImpl<$Res, CulturalTradition>;
  @useResult
  $Res call({
    String name,
    String? description,
    String? origin,
    TraditionType? type,
    List<String> practices,
    String? significance,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$CulturalTraditionCopyWithImpl<$Res, $Val extends CulturalTradition>
    implements $CulturalTraditionCopyWith<$Res> {
  _$CulturalTraditionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CulturalTradition
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? origin = freezed,
    Object? type = freezed,
    Object? practices = null,
    Object? significance = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            origin: freezed == origin
                ? _value.origin
                : origin // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as TraditionType?,
            practices: null == practices
                ? _value.practices
                : practices // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            significance: freezed == significance
                ? _value.significance
                : significance // ignore: cast_nullable_to_non_nullable
                      as String?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CulturalTraditionImplCopyWith<$Res>
    implements $CulturalTraditionCopyWith<$Res> {
  factory _$$CulturalTraditionImplCopyWith(
    _$CulturalTraditionImpl value,
    $Res Function(_$CulturalTraditionImpl) then,
  ) = __$$CulturalTraditionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    String? description,
    String? origin,
    TraditionType? type,
    List<String> practices,
    String? significance,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$CulturalTraditionImplCopyWithImpl<$Res>
    extends _$CulturalTraditionCopyWithImpl<$Res, _$CulturalTraditionImpl>
    implements _$$CulturalTraditionImplCopyWith<$Res> {
  __$$CulturalTraditionImplCopyWithImpl(
    _$CulturalTraditionImpl _value,
    $Res Function(_$CulturalTraditionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CulturalTradition
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? origin = freezed,
    Object? type = freezed,
    Object? practices = null,
    Object? significance = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _$CulturalTraditionImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        origin: freezed == origin
            ? _value.origin
            : origin // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as TraditionType?,
        practices: null == practices
            ? _value._practices
            : practices // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        significance: freezed == significance
            ? _value.significance
            : significance // ignore: cast_nullable_to_non_nullable
                  as String?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CulturalTraditionImpl implements _CulturalTradition {
  const _$CulturalTraditionImpl({
    required this.name,
    this.description,
    this.origin,
    this.type,
    final List<String> practices = const [],
    this.significance,
    final Map<String, dynamic> customAttributes = const {},
  }) : _practices = practices,
       _customAttributes = customAttributes;

  factory _$CulturalTraditionImpl.fromJson(Map<String, dynamic> json) =>
      _$$CulturalTraditionImplFromJson(json);

  @override
  final String name;
  @override
  final String? description;
  @override
  final String? origin;
  @override
  final TraditionType? type;
  final List<String> _practices;
  @override
  @JsonKey()
  List<String> get practices {
    if (_practices is EqualUnmodifiableListView) return _practices;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_practices);
  }

  @override
  final String? significance;
  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'CulturalTradition(name: $name, description: $description, origin: $origin, type: $type, practices: $practices, significance: $significance, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CulturalTraditionImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.origin, origin) || other.origin == origin) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(
              other._practices,
              _practices,
            ) &&
            (identical(other.significance, significance) ||
                other.significance == significance) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    description,
    origin,
    type,
    const DeepCollectionEquality().hash(_practices),
    significance,
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of CulturalTradition
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CulturalTraditionImplCopyWith<_$CulturalTraditionImpl> get copyWith =>
      __$$CulturalTraditionImplCopyWithImpl<_$CulturalTraditionImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CulturalTraditionImplToJson(this);
  }
}

abstract class _CulturalTradition implements CulturalTradition {
  const factory _CulturalTradition({
    required final String name,
    final String? description,
    final String? origin,
    final TraditionType? type,
    final List<String> practices,
    final String? significance,
    final Map<String, dynamic> customAttributes,
  }) = _$CulturalTraditionImpl;

  factory _CulturalTradition.fromJson(Map<String, dynamic> json) =
      _$CulturalTraditionImpl.fromJson;

  @override
  String get name;
  @override
  String? get description;
  @override
  String? get origin;
  @override
  TraditionType? get type;
  @override
  List<String> get practices;
  @override
  String? get significance;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of CulturalTradition
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CulturalTraditionImplCopyWith<_$CulturalTraditionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LocationEconomy _$LocationEconomyFromJson(Map<String, dynamic> json) {
  return _LocationEconomy.fromJson(json);
}

/// @nodoc
mixin _$LocationEconomy {
  String? get economicSystem => throw _privateConstructorUsedError; // 经济制度
  List<String> get primaryIndustries =>
      throw _privateConstructorUsedError; // 主要产业
  List<String> get exports => throw _privateConstructorUsedError; // 出口商品
  List<String> get imports => throw _privateConstructorUsedError; // 进口商品
  String? get currency => throw _privateConstructorUsedError; // 货币
  EconomicStatus? get economicStatus => throw _privateConstructorUsedError;
  List<TradeRoute> get tradeRoutes =>
      throw _privateConstructorUsedError; // 贸易路线
  List<String> get marketplaces => throw _privateConstructorUsedError; // 市场
  Map<String, dynamic> get customEconomy => throw _privateConstructorUsedError;

  /// Serializes this LocationEconomy to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LocationEconomy
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LocationEconomyCopyWith<LocationEconomy> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationEconomyCopyWith<$Res> {
  factory $LocationEconomyCopyWith(
    LocationEconomy value,
    $Res Function(LocationEconomy) then,
  ) = _$LocationEconomyCopyWithImpl<$Res, LocationEconomy>;
  @useResult
  $Res call({
    String? economicSystem,
    List<String> primaryIndustries,
    List<String> exports,
    List<String> imports,
    String? currency,
    EconomicStatus? economicStatus,
    List<TradeRoute> tradeRoutes,
    List<String> marketplaces,
    Map<String, dynamic> customEconomy,
  });
}

/// @nodoc
class _$LocationEconomyCopyWithImpl<$Res, $Val extends LocationEconomy>
    implements $LocationEconomyCopyWith<$Res> {
  _$LocationEconomyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationEconomy
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? economicSystem = freezed,
    Object? primaryIndustries = null,
    Object? exports = null,
    Object? imports = null,
    Object? currency = freezed,
    Object? economicStatus = freezed,
    Object? tradeRoutes = null,
    Object? marketplaces = null,
    Object? customEconomy = null,
  }) {
    return _then(
      _value.copyWith(
            economicSystem: freezed == economicSystem
                ? _value.economicSystem
                : economicSystem // ignore: cast_nullable_to_non_nullable
                      as String?,
            primaryIndustries: null == primaryIndustries
                ? _value.primaryIndustries
                : primaryIndustries // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            exports: null == exports
                ? _value.exports
                : exports // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            imports: null == imports
                ? _value.imports
                : imports // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            currency: freezed == currency
                ? _value.currency
                : currency // ignore: cast_nullable_to_non_nullable
                      as String?,
            economicStatus: freezed == economicStatus
                ? _value.economicStatus
                : economicStatus // ignore: cast_nullable_to_non_nullable
                      as EconomicStatus?,
            tradeRoutes: null == tradeRoutes
                ? _value.tradeRoutes
                : tradeRoutes // ignore: cast_nullable_to_non_nullable
                      as List<TradeRoute>,
            marketplaces: null == marketplaces
                ? _value.marketplaces
                : marketplaces // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customEconomy: null == customEconomy
                ? _value.customEconomy
                : customEconomy // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$LocationEconomyImplCopyWith<$Res>
    implements $LocationEconomyCopyWith<$Res> {
  factory _$$LocationEconomyImplCopyWith(
    _$LocationEconomyImpl value,
    $Res Function(_$LocationEconomyImpl) then,
  ) = __$$LocationEconomyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String? economicSystem,
    List<String> primaryIndustries,
    List<String> exports,
    List<String> imports,
    String? currency,
    EconomicStatus? economicStatus,
    List<TradeRoute> tradeRoutes,
    List<String> marketplaces,
    Map<String, dynamic> customEconomy,
  });
}

/// @nodoc
class __$$LocationEconomyImplCopyWithImpl<$Res>
    extends _$LocationEconomyCopyWithImpl<$Res, _$LocationEconomyImpl>
    implements _$$LocationEconomyImplCopyWith<$Res> {
  __$$LocationEconomyImplCopyWithImpl(
    _$LocationEconomyImpl _value,
    $Res Function(_$LocationEconomyImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of LocationEconomy
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? economicSystem = freezed,
    Object? primaryIndustries = null,
    Object? exports = null,
    Object? imports = null,
    Object? currency = freezed,
    Object? economicStatus = freezed,
    Object? tradeRoutes = null,
    Object? marketplaces = null,
    Object? customEconomy = null,
  }) {
    return _then(
      _$LocationEconomyImpl(
        economicSystem: freezed == economicSystem
            ? _value.economicSystem
            : economicSystem // ignore: cast_nullable_to_non_nullable
                  as String?,
        primaryIndustries: null == primaryIndustries
            ? _value._primaryIndustries
            : primaryIndustries // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        exports: null == exports
            ? _value._exports
            : exports // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        imports: null == imports
            ? _value._imports
            : imports // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        currency: freezed == currency
            ? _value.currency
            : currency // ignore: cast_nullable_to_non_nullable
                  as String?,
        economicStatus: freezed == economicStatus
            ? _value.economicStatus
            : economicStatus // ignore: cast_nullable_to_non_nullable
                  as EconomicStatus?,
        tradeRoutes: null == tradeRoutes
            ? _value._tradeRoutes
            : tradeRoutes // ignore: cast_nullable_to_non_nullable
                  as List<TradeRoute>,
        marketplaces: null == marketplaces
            ? _value._marketplaces
            : marketplaces // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customEconomy: null == customEconomy
            ? _value._customEconomy
            : customEconomy // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$LocationEconomyImpl implements _LocationEconomy {
  const _$LocationEconomyImpl({
    this.economicSystem,
    final List<String> primaryIndustries = const [],
    final List<String> exports = const [],
    final List<String> imports = const [],
    this.currency,
    this.economicStatus,
    final List<TradeRoute> tradeRoutes = const [],
    final List<String> marketplaces = const [],
    final Map<String, dynamic> customEconomy = const {},
  }) : _primaryIndustries = primaryIndustries,
       _exports = exports,
       _imports = imports,
       _tradeRoutes = tradeRoutes,
       _marketplaces = marketplaces,
       _customEconomy = customEconomy;

  factory _$LocationEconomyImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocationEconomyImplFromJson(json);

  @override
  final String? economicSystem;
  // 经济制度
  final List<String> _primaryIndustries;
  // 经济制度
  @override
  @JsonKey()
  List<String> get primaryIndustries {
    if (_primaryIndustries is EqualUnmodifiableListView)
      return _primaryIndustries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_primaryIndustries);
  }

  // 主要产业
  final List<String> _exports;
  // 主要产业
  @override
  @JsonKey()
  List<String> get exports {
    if (_exports is EqualUnmodifiableListView) return _exports;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_exports);
  }

  // 出口商品
  final List<String> _imports;
  // 出口商品
  @override
  @JsonKey()
  List<String> get imports {
    if (_imports is EqualUnmodifiableListView) return _imports;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_imports);
  }

  // 进口商品
  @override
  final String? currency;
  // 货币
  @override
  final EconomicStatus? economicStatus;
  final List<TradeRoute> _tradeRoutes;
  @override
  @JsonKey()
  List<TradeRoute> get tradeRoutes {
    if (_tradeRoutes is EqualUnmodifiableListView) return _tradeRoutes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tradeRoutes);
  }

  // 贸易路线
  final List<String> _marketplaces;
  // 贸易路线
  @override
  @JsonKey()
  List<String> get marketplaces {
    if (_marketplaces is EqualUnmodifiableListView) return _marketplaces;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_marketplaces);
  }

  // 市场
  final Map<String, dynamic> _customEconomy;
  // 市场
  @override
  @JsonKey()
  Map<String, dynamic> get customEconomy {
    if (_customEconomy is EqualUnmodifiableMapView) return _customEconomy;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customEconomy);
  }

  @override
  String toString() {
    return 'LocationEconomy(economicSystem: $economicSystem, primaryIndustries: $primaryIndustries, exports: $exports, imports: $imports, currency: $currency, economicStatus: $economicStatus, tradeRoutes: $tradeRoutes, marketplaces: $marketplaces, customEconomy: $customEconomy)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationEconomyImpl &&
            (identical(other.economicSystem, economicSystem) ||
                other.economicSystem == economicSystem) &&
            const DeepCollectionEquality().equals(
              other._primaryIndustries,
              _primaryIndustries,
            ) &&
            const DeepCollectionEquality().equals(other._exports, _exports) &&
            const DeepCollectionEquality().equals(other._imports, _imports) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.economicStatus, economicStatus) ||
                other.economicStatus == economicStatus) &&
            const DeepCollectionEquality().equals(
              other._tradeRoutes,
              _tradeRoutes,
            ) &&
            const DeepCollectionEquality().equals(
              other._marketplaces,
              _marketplaces,
            ) &&
            const DeepCollectionEquality().equals(
              other._customEconomy,
              _customEconomy,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    economicSystem,
    const DeepCollectionEquality().hash(_primaryIndustries),
    const DeepCollectionEquality().hash(_exports),
    const DeepCollectionEquality().hash(_imports),
    currency,
    economicStatus,
    const DeepCollectionEquality().hash(_tradeRoutes),
    const DeepCollectionEquality().hash(_marketplaces),
    const DeepCollectionEquality().hash(_customEconomy),
  );

  /// Create a copy of LocationEconomy
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationEconomyImplCopyWith<_$LocationEconomyImpl> get copyWith =>
      __$$LocationEconomyImplCopyWithImpl<_$LocationEconomyImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$LocationEconomyImplToJson(this);
  }
}

abstract class _LocationEconomy implements LocationEconomy {
  const factory _LocationEconomy({
    final String? economicSystem,
    final List<String> primaryIndustries,
    final List<String> exports,
    final List<String> imports,
    final String? currency,
    final EconomicStatus? economicStatus,
    final List<TradeRoute> tradeRoutes,
    final List<String> marketplaces,
    final Map<String, dynamic> customEconomy,
  }) = _$LocationEconomyImpl;

  factory _LocationEconomy.fromJson(Map<String, dynamic> json) =
      _$LocationEconomyImpl.fromJson;

  @override
  String? get economicSystem; // 经济制度
  @override
  List<String> get primaryIndustries; // 主要产业
  @override
  List<String> get exports; // 出口商品
  @override
  List<String> get imports; // 进口商品
  @override
  String? get currency; // 货币
  @override
  EconomicStatus? get economicStatus;
  @override
  List<TradeRoute> get tradeRoutes; // 贸易路线
  @override
  List<String> get marketplaces; // 市场
  @override
  Map<String, dynamic> get customEconomy;

  /// Create a copy of LocationEconomy
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationEconomyImplCopyWith<_$LocationEconomyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TradeRoute _$TradeRouteFromJson(Map<String, dynamic> json) {
  return _TradeRoute.fromJson(json);
}

/// @nodoc
mixin _$TradeRoute {
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  List<String> get connectedLocationIds => throw _privateConstructorUsedError;
  List<String> get tradedGoods => throw _privateConstructorUsedError;
  TradeRouteStatus? get status => throw _privateConstructorUsedError;
  List<String> get dangers => throw _privateConstructorUsedError; // 危险
  int? get travelTimeDays => throw _privateConstructorUsedError; // 旅行时间
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this TradeRoute to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TradeRoute
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TradeRouteCopyWith<TradeRoute> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TradeRouteCopyWith<$Res> {
  factory $TradeRouteCopyWith(
    TradeRoute value,
    $Res Function(TradeRoute) then,
  ) = _$TradeRouteCopyWithImpl<$Res, TradeRoute>;
  @useResult
  $Res call({
    String name,
    String? description,
    List<String> connectedLocationIds,
    List<String> tradedGoods,
    TradeRouteStatus? status,
    List<String> dangers,
    int? travelTimeDays,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$TradeRouteCopyWithImpl<$Res, $Val extends TradeRoute>
    implements $TradeRouteCopyWith<$Res> {
  _$TradeRouteCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TradeRoute
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? connectedLocationIds = null,
    Object? tradedGoods = null,
    Object? status = freezed,
    Object? dangers = null,
    Object? travelTimeDays = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            connectedLocationIds: null == connectedLocationIds
                ? _value.connectedLocationIds
                : connectedLocationIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            tradedGoods: null == tradedGoods
                ? _value.tradedGoods
                : tradedGoods // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            status: freezed == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as TradeRouteStatus?,
            dangers: null == dangers
                ? _value.dangers
                : dangers // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            travelTimeDays: freezed == travelTimeDays
                ? _value.travelTimeDays
                : travelTimeDays // ignore: cast_nullable_to_non_nullable
                      as int?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$TradeRouteImplCopyWith<$Res>
    implements $TradeRouteCopyWith<$Res> {
  factory _$$TradeRouteImplCopyWith(
    _$TradeRouteImpl value,
    $Res Function(_$TradeRouteImpl) then,
  ) = __$$TradeRouteImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    String? description,
    List<String> connectedLocationIds,
    List<String> tradedGoods,
    TradeRouteStatus? status,
    List<String> dangers,
    int? travelTimeDays,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$TradeRouteImplCopyWithImpl<$Res>
    extends _$TradeRouteCopyWithImpl<$Res, _$TradeRouteImpl>
    implements _$$TradeRouteImplCopyWith<$Res> {
  __$$TradeRouteImplCopyWithImpl(
    _$TradeRouteImpl _value,
    $Res Function(_$TradeRouteImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TradeRoute
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? connectedLocationIds = null,
    Object? tradedGoods = null,
    Object? status = freezed,
    Object? dangers = null,
    Object? travelTimeDays = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _$TradeRouteImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        connectedLocationIds: null == connectedLocationIds
            ? _value._connectedLocationIds
            : connectedLocationIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        tradedGoods: null == tradedGoods
            ? _value._tradedGoods
            : tradedGoods // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        status: freezed == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as TradeRouteStatus?,
        dangers: null == dangers
            ? _value._dangers
            : dangers // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        travelTimeDays: freezed == travelTimeDays
            ? _value.travelTimeDays
            : travelTimeDays // ignore: cast_nullable_to_non_nullable
                  as int?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$TradeRouteImpl implements _TradeRoute {
  const _$TradeRouteImpl({
    required this.name,
    this.description,
    final List<String> connectedLocationIds = const [],
    final List<String> tradedGoods = const [],
    this.status,
    final List<String> dangers = const [],
    this.travelTimeDays,
    final Map<String, dynamic> customAttributes = const {},
  }) : _connectedLocationIds = connectedLocationIds,
       _tradedGoods = tradedGoods,
       _dangers = dangers,
       _customAttributes = customAttributes;

  factory _$TradeRouteImpl.fromJson(Map<String, dynamic> json) =>
      _$$TradeRouteImplFromJson(json);

  @override
  final String name;
  @override
  final String? description;
  final List<String> _connectedLocationIds;
  @override
  @JsonKey()
  List<String> get connectedLocationIds {
    if (_connectedLocationIds is EqualUnmodifiableListView)
      return _connectedLocationIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_connectedLocationIds);
  }

  final List<String> _tradedGoods;
  @override
  @JsonKey()
  List<String> get tradedGoods {
    if (_tradedGoods is EqualUnmodifiableListView) return _tradedGoods;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tradedGoods);
  }

  @override
  final TradeRouteStatus? status;
  final List<String> _dangers;
  @override
  @JsonKey()
  List<String> get dangers {
    if (_dangers is EqualUnmodifiableListView) return _dangers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dangers);
  }

  // 危险
  @override
  final int? travelTimeDays;
  // 旅行时间
  final Map<String, dynamic> _customAttributes;
  // 旅行时间
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'TradeRoute(name: $name, description: $description, connectedLocationIds: $connectedLocationIds, tradedGoods: $tradedGoods, status: $status, dangers: $dangers, travelTimeDays: $travelTimeDays, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TradeRouteImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(
              other._connectedLocationIds,
              _connectedLocationIds,
            ) &&
            const DeepCollectionEquality().equals(
              other._tradedGoods,
              _tradedGoods,
            ) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(other._dangers, _dangers) &&
            (identical(other.travelTimeDays, travelTimeDays) ||
                other.travelTimeDays == travelTimeDays) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    description,
    const DeepCollectionEquality().hash(_connectedLocationIds),
    const DeepCollectionEquality().hash(_tradedGoods),
    status,
    const DeepCollectionEquality().hash(_dangers),
    travelTimeDays,
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of TradeRoute
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TradeRouteImplCopyWith<_$TradeRouteImpl> get copyWith =>
      __$$TradeRouteImplCopyWithImpl<_$TradeRouteImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TradeRouteImplToJson(this);
  }
}

abstract class _TradeRoute implements TradeRoute {
  const factory _TradeRoute({
    required final String name,
    final String? description,
    final List<String> connectedLocationIds,
    final List<String> tradedGoods,
    final TradeRouteStatus? status,
    final List<String> dangers,
    final int? travelTimeDays,
    final Map<String, dynamic> customAttributes,
  }) = _$TradeRouteImpl;

  factory _TradeRoute.fromJson(Map<String, dynamic> json) =
      _$TradeRouteImpl.fromJson;

  @override
  String get name;
  @override
  String? get description;
  @override
  List<String> get connectedLocationIds;
  @override
  List<String> get tradedGoods;
  @override
  TradeRouteStatus? get status;
  @override
  List<String> get dangers; // 危险
  @override
  int? get travelTimeDays; // 旅行时间
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of TradeRoute
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TradeRouteImplCopyWith<_$TradeRouteImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LocationPolitics _$LocationPoliticsFromJson(Map<String, dynamic> json) {
  return _LocationPolitics.fromJson(json);
}

/// @nodoc
mixin _$LocationPolitics {
  String? get governmentType => throw _privateConstructorUsedError; // 政府类型
  List<PoliticalFigure> get leaders =>
      throw _privateConstructorUsedError; // 领导者
  List<PoliticalFaction> get factions =>
      throw _privateConstructorUsedError; // 派系
  List<String> get laws => throw _privateConstructorUsedError; // 法律
  List<String> get conflicts => throw _privateConstructorUsedError; // 冲突
  List<String> get alliances => throw _privateConstructorUsedError; // 联盟
  String? get militaryStrength => throw _privateConstructorUsedError; // 军事力量
  Map<String, dynamic> get customPolitics => throw _privateConstructorUsedError;

  /// Serializes this LocationPolitics to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LocationPolitics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LocationPoliticsCopyWith<LocationPolitics> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationPoliticsCopyWith<$Res> {
  factory $LocationPoliticsCopyWith(
    LocationPolitics value,
    $Res Function(LocationPolitics) then,
  ) = _$LocationPoliticsCopyWithImpl<$Res, LocationPolitics>;
  @useResult
  $Res call({
    String? governmentType,
    List<PoliticalFigure> leaders,
    List<PoliticalFaction> factions,
    List<String> laws,
    List<String> conflicts,
    List<String> alliances,
    String? militaryStrength,
    Map<String, dynamic> customPolitics,
  });
}

/// @nodoc
class _$LocationPoliticsCopyWithImpl<$Res, $Val extends LocationPolitics>
    implements $LocationPoliticsCopyWith<$Res> {
  _$LocationPoliticsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationPolitics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? governmentType = freezed,
    Object? leaders = null,
    Object? factions = null,
    Object? laws = null,
    Object? conflicts = null,
    Object? alliances = null,
    Object? militaryStrength = freezed,
    Object? customPolitics = null,
  }) {
    return _then(
      _value.copyWith(
            governmentType: freezed == governmentType
                ? _value.governmentType
                : governmentType // ignore: cast_nullable_to_non_nullable
                      as String?,
            leaders: null == leaders
                ? _value.leaders
                : leaders // ignore: cast_nullable_to_non_nullable
                      as List<PoliticalFigure>,
            factions: null == factions
                ? _value.factions
                : factions // ignore: cast_nullable_to_non_nullable
                      as List<PoliticalFaction>,
            laws: null == laws
                ? _value.laws
                : laws // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            conflicts: null == conflicts
                ? _value.conflicts
                : conflicts // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            alliances: null == alliances
                ? _value.alliances
                : alliances // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            militaryStrength: freezed == militaryStrength
                ? _value.militaryStrength
                : militaryStrength // ignore: cast_nullable_to_non_nullable
                      as String?,
            customPolitics: null == customPolitics
                ? _value.customPolitics
                : customPolitics // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$LocationPoliticsImplCopyWith<$Res>
    implements $LocationPoliticsCopyWith<$Res> {
  factory _$$LocationPoliticsImplCopyWith(
    _$LocationPoliticsImpl value,
    $Res Function(_$LocationPoliticsImpl) then,
  ) = __$$LocationPoliticsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String? governmentType,
    List<PoliticalFigure> leaders,
    List<PoliticalFaction> factions,
    List<String> laws,
    List<String> conflicts,
    List<String> alliances,
    String? militaryStrength,
    Map<String, dynamic> customPolitics,
  });
}

/// @nodoc
class __$$LocationPoliticsImplCopyWithImpl<$Res>
    extends _$LocationPoliticsCopyWithImpl<$Res, _$LocationPoliticsImpl>
    implements _$$LocationPoliticsImplCopyWith<$Res> {
  __$$LocationPoliticsImplCopyWithImpl(
    _$LocationPoliticsImpl _value,
    $Res Function(_$LocationPoliticsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of LocationPolitics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? governmentType = freezed,
    Object? leaders = null,
    Object? factions = null,
    Object? laws = null,
    Object? conflicts = null,
    Object? alliances = null,
    Object? militaryStrength = freezed,
    Object? customPolitics = null,
  }) {
    return _then(
      _$LocationPoliticsImpl(
        governmentType: freezed == governmentType
            ? _value.governmentType
            : governmentType // ignore: cast_nullable_to_non_nullable
                  as String?,
        leaders: null == leaders
            ? _value._leaders
            : leaders // ignore: cast_nullable_to_non_nullable
                  as List<PoliticalFigure>,
        factions: null == factions
            ? _value._factions
            : factions // ignore: cast_nullable_to_non_nullable
                  as List<PoliticalFaction>,
        laws: null == laws
            ? _value._laws
            : laws // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        conflicts: null == conflicts
            ? _value._conflicts
            : conflicts // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        alliances: null == alliances
            ? _value._alliances
            : alliances // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        militaryStrength: freezed == militaryStrength
            ? _value.militaryStrength
            : militaryStrength // ignore: cast_nullable_to_non_nullable
                  as String?,
        customPolitics: null == customPolitics
            ? _value._customPolitics
            : customPolitics // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$LocationPoliticsImpl implements _LocationPolitics {
  const _$LocationPoliticsImpl({
    this.governmentType,
    final List<PoliticalFigure> leaders = const [],
    final List<PoliticalFaction> factions = const [],
    final List<String> laws = const [],
    final List<String> conflicts = const [],
    final List<String> alliances = const [],
    this.militaryStrength,
    final Map<String, dynamic> customPolitics = const {},
  }) : _leaders = leaders,
       _factions = factions,
       _laws = laws,
       _conflicts = conflicts,
       _alliances = alliances,
       _customPolitics = customPolitics;

  factory _$LocationPoliticsImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocationPoliticsImplFromJson(json);

  @override
  final String? governmentType;
  // 政府类型
  final List<PoliticalFigure> _leaders;
  // 政府类型
  @override
  @JsonKey()
  List<PoliticalFigure> get leaders {
    if (_leaders is EqualUnmodifiableListView) return _leaders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_leaders);
  }

  // 领导者
  final List<PoliticalFaction> _factions;
  // 领导者
  @override
  @JsonKey()
  List<PoliticalFaction> get factions {
    if (_factions is EqualUnmodifiableListView) return _factions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_factions);
  }

  // 派系
  final List<String> _laws;
  // 派系
  @override
  @JsonKey()
  List<String> get laws {
    if (_laws is EqualUnmodifiableListView) return _laws;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_laws);
  }

  // 法律
  final List<String> _conflicts;
  // 法律
  @override
  @JsonKey()
  List<String> get conflicts {
    if (_conflicts is EqualUnmodifiableListView) return _conflicts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_conflicts);
  }

  // 冲突
  final List<String> _alliances;
  // 冲突
  @override
  @JsonKey()
  List<String> get alliances {
    if (_alliances is EqualUnmodifiableListView) return _alliances;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_alliances);
  }

  // 联盟
  @override
  final String? militaryStrength;
  // 军事力量
  final Map<String, dynamic> _customPolitics;
  // 军事力量
  @override
  @JsonKey()
  Map<String, dynamic> get customPolitics {
    if (_customPolitics is EqualUnmodifiableMapView) return _customPolitics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customPolitics);
  }

  @override
  String toString() {
    return 'LocationPolitics(governmentType: $governmentType, leaders: $leaders, factions: $factions, laws: $laws, conflicts: $conflicts, alliances: $alliances, militaryStrength: $militaryStrength, customPolitics: $customPolitics)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationPoliticsImpl &&
            (identical(other.governmentType, governmentType) ||
                other.governmentType == governmentType) &&
            const DeepCollectionEquality().equals(other._leaders, _leaders) &&
            const DeepCollectionEquality().equals(other._factions, _factions) &&
            const DeepCollectionEquality().equals(other._laws, _laws) &&
            const DeepCollectionEquality().equals(
              other._conflicts,
              _conflicts,
            ) &&
            const DeepCollectionEquality().equals(
              other._alliances,
              _alliances,
            ) &&
            (identical(other.militaryStrength, militaryStrength) ||
                other.militaryStrength == militaryStrength) &&
            const DeepCollectionEquality().equals(
              other._customPolitics,
              _customPolitics,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    governmentType,
    const DeepCollectionEquality().hash(_leaders),
    const DeepCollectionEquality().hash(_factions),
    const DeepCollectionEquality().hash(_laws),
    const DeepCollectionEquality().hash(_conflicts),
    const DeepCollectionEquality().hash(_alliances),
    militaryStrength,
    const DeepCollectionEquality().hash(_customPolitics),
  );

  /// Create a copy of LocationPolitics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationPoliticsImplCopyWith<_$LocationPoliticsImpl> get copyWith =>
      __$$LocationPoliticsImplCopyWithImpl<_$LocationPoliticsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$LocationPoliticsImplToJson(this);
  }
}

abstract class _LocationPolitics implements LocationPolitics {
  const factory _LocationPolitics({
    final String? governmentType,
    final List<PoliticalFigure> leaders,
    final List<PoliticalFaction> factions,
    final List<String> laws,
    final List<String> conflicts,
    final List<String> alliances,
    final String? militaryStrength,
    final Map<String, dynamic> customPolitics,
  }) = _$LocationPoliticsImpl;

  factory _LocationPolitics.fromJson(Map<String, dynamic> json) =
      _$LocationPoliticsImpl.fromJson;

  @override
  String? get governmentType; // 政府类型
  @override
  List<PoliticalFigure> get leaders; // 领导者
  @override
  List<PoliticalFaction> get factions; // 派系
  @override
  List<String> get laws; // 法律
  @override
  List<String> get conflicts; // 冲突
  @override
  List<String> get alliances; // 联盟
  @override
  String? get militaryStrength; // 军事力量
  @override
  Map<String, dynamic> get customPolitics;

  /// Create a copy of LocationPolitics
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationPoliticsImplCopyWith<_$LocationPoliticsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PoliticalFigure _$PoliticalFigureFromJson(Map<String, dynamic> json) {
  return _PoliticalFigure.fromJson(json);
}

/// @nodoc
mixin _$PoliticalFigure {
  String get characterId => throw _privateConstructorUsedError;
  String get position => throw _privateConstructorUsedError; // 职位
  String? get title => throw _privateConstructorUsedError; // 头衔
  int? get influence => throw _privateConstructorUsedError; // 影响力 1-10
  List<String> get responsibilities => throw _privateConstructorUsedError; // 职责
  DateTime? get termStart => throw _privateConstructorUsedError;
  DateTime? get termEnd => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this PoliticalFigure to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PoliticalFigure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PoliticalFigureCopyWith<PoliticalFigure> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PoliticalFigureCopyWith<$Res> {
  factory $PoliticalFigureCopyWith(
    PoliticalFigure value,
    $Res Function(PoliticalFigure) then,
  ) = _$PoliticalFigureCopyWithImpl<$Res, PoliticalFigure>;
  @useResult
  $Res call({
    String characterId,
    String position,
    String? title,
    int? influence,
    List<String> responsibilities,
    DateTime? termStart,
    DateTime? termEnd,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$PoliticalFigureCopyWithImpl<$Res, $Val extends PoliticalFigure>
    implements $PoliticalFigureCopyWith<$Res> {
  _$PoliticalFigureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PoliticalFigure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? characterId = null,
    Object? position = null,
    Object? title = freezed,
    Object? influence = freezed,
    Object? responsibilities = null,
    Object? termStart = freezed,
    Object? termEnd = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            characterId: null == characterId
                ? _value.characterId
                : characterId // ignore: cast_nullable_to_non_nullable
                      as String,
            position: null == position
                ? _value.position
                : position // ignore: cast_nullable_to_non_nullable
                      as String,
            title: freezed == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String?,
            influence: freezed == influence
                ? _value.influence
                : influence // ignore: cast_nullable_to_non_nullable
                      as int?,
            responsibilities: null == responsibilities
                ? _value.responsibilities
                : responsibilities // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            termStart: freezed == termStart
                ? _value.termStart
                : termStart // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            termEnd: freezed == termEnd
                ? _value.termEnd
                : termEnd // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PoliticalFigureImplCopyWith<$Res>
    implements $PoliticalFigureCopyWith<$Res> {
  factory _$$PoliticalFigureImplCopyWith(
    _$PoliticalFigureImpl value,
    $Res Function(_$PoliticalFigureImpl) then,
  ) = __$$PoliticalFigureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String characterId,
    String position,
    String? title,
    int? influence,
    List<String> responsibilities,
    DateTime? termStart,
    DateTime? termEnd,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$PoliticalFigureImplCopyWithImpl<$Res>
    extends _$PoliticalFigureCopyWithImpl<$Res, _$PoliticalFigureImpl>
    implements _$$PoliticalFigureImplCopyWith<$Res> {
  __$$PoliticalFigureImplCopyWithImpl(
    _$PoliticalFigureImpl _value,
    $Res Function(_$PoliticalFigureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PoliticalFigure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? characterId = null,
    Object? position = null,
    Object? title = freezed,
    Object? influence = freezed,
    Object? responsibilities = null,
    Object? termStart = freezed,
    Object? termEnd = freezed,
    Object? customAttributes = null,
  }) {
    return _then(
      _$PoliticalFigureImpl(
        characterId: null == characterId
            ? _value.characterId
            : characterId // ignore: cast_nullable_to_non_nullable
                  as String,
        position: null == position
            ? _value.position
            : position // ignore: cast_nullable_to_non_nullable
                  as String,
        title: freezed == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String?,
        influence: freezed == influence
            ? _value.influence
            : influence // ignore: cast_nullable_to_non_nullable
                  as int?,
        responsibilities: null == responsibilities
            ? _value._responsibilities
            : responsibilities // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        termStart: freezed == termStart
            ? _value.termStart
            : termStart // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        termEnd: freezed == termEnd
            ? _value.termEnd
            : termEnd // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PoliticalFigureImpl implements _PoliticalFigure {
  const _$PoliticalFigureImpl({
    required this.characterId,
    required this.position,
    this.title,
    this.influence,
    final List<String> responsibilities = const [],
    this.termStart,
    this.termEnd,
    final Map<String, dynamic> customAttributes = const {},
  }) : _responsibilities = responsibilities,
       _customAttributes = customAttributes;

  factory _$PoliticalFigureImpl.fromJson(Map<String, dynamic> json) =>
      _$$PoliticalFigureImplFromJson(json);

  @override
  final String characterId;
  @override
  final String position;
  // 职位
  @override
  final String? title;
  // 头衔
  @override
  final int? influence;
  // 影响力 1-10
  final List<String> _responsibilities;
  // 影响力 1-10
  @override
  @JsonKey()
  List<String> get responsibilities {
    if (_responsibilities is EqualUnmodifiableListView)
      return _responsibilities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_responsibilities);
  }

  // 职责
  @override
  final DateTime? termStart;
  @override
  final DateTime? termEnd;
  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'PoliticalFigure(characterId: $characterId, position: $position, title: $title, influence: $influence, responsibilities: $responsibilities, termStart: $termStart, termEnd: $termEnd, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PoliticalFigureImpl &&
            (identical(other.characterId, characterId) ||
                other.characterId == characterId) &&
            (identical(other.position, position) ||
                other.position == position) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.influence, influence) ||
                other.influence == influence) &&
            const DeepCollectionEquality().equals(
              other._responsibilities,
              _responsibilities,
            ) &&
            (identical(other.termStart, termStart) ||
                other.termStart == termStart) &&
            (identical(other.termEnd, termEnd) || other.termEnd == termEnd) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    characterId,
    position,
    title,
    influence,
    const DeepCollectionEquality().hash(_responsibilities),
    termStart,
    termEnd,
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of PoliticalFigure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PoliticalFigureImplCopyWith<_$PoliticalFigureImpl> get copyWith =>
      __$$PoliticalFigureImplCopyWithImpl<_$PoliticalFigureImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$PoliticalFigureImplToJson(this);
  }
}

abstract class _PoliticalFigure implements PoliticalFigure {
  const factory _PoliticalFigure({
    required final String characterId,
    required final String position,
    final String? title,
    final int? influence,
    final List<String> responsibilities,
    final DateTime? termStart,
    final DateTime? termEnd,
    final Map<String, dynamic> customAttributes,
  }) = _$PoliticalFigureImpl;

  factory _PoliticalFigure.fromJson(Map<String, dynamic> json) =
      _$PoliticalFigureImpl.fromJson;

  @override
  String get characterId;
  @override
  String get position; // 职位
  @override
  String? get title; // 头衔
  @override
  int? get influence; // 影响力 1-10
  @override
  List<String> get responsibilities; // 职责
  @override
  DateTime? get termStart;
  @override
  DateTime? get termEnd;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of PoliticalFigure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PoliticalFigureImplCopyWith<_$PoliticalFigureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PoliticalFaction _$PoliticalFactionFromJson(Map<String, dynamic> json) {
  return _PoliticalFaction.fromJson(json);
}

/// @nodoc
mixin _$PoliticalFaction {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  List<String> get memberCharacterIds => throw _privateConstructorUsedError;
  String? get ideology => throw _privateConstructorUsedError; // 意识形态
  int? get power => throw _privateConstructorUsedError; // 权力 1-10
  List<String> get goals => throw _privateConstructorUsedError;
  List<String> get enemies => throw _privateConstructorUsedError;
  List<String> get allies => throw _privateConstructorUsedError;
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this PoliticalFaction to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PoliticalFaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PoliticalFactionCopyWith<PoliticalFaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PoliticalFactionCopyWith<$Res> {
  factory $PoliticalFactionCopyWith(
    PoliticalFaction value,
    $Res Function(PoliticalFaction) then,
  ) = _$PoliticalFactionCopyWithImpl<$Res, PoliticalFaction>;
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    List<String> memberCharacterIds,
    String? ideology,
    int? power,
    List<String> goals,
    List<String> enemies,
    List<String> allies,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$PoliticalFactionCopyWithImpl<$Res, $Val extends PoliticalFaction>
    implements $PoliticalFactionCopyWith<$Res> {
  _$PoliticalFactionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PoliticalFaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? memberCharacterIds = null,
    Object? ideology = freezed,
    Object? power = freezed,
    Object? goals = null,
    Object? enemies = null,
    Object? allies = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            memberCharacterIds: null == memberCharacterIds
                ? _value.memberCharacterIds
                : memberCharacterIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            ideology: freezed == ideology
                ? _value.ideology
                : ideology // ignore: cast_nullable_to_non_nullable
                      as String?,
            power: freezed == power
                ? _value.power
                : power // ignore: cast_nullable_to_non_nullable
                      as int?,
            goals: null == goals
                ? _value.goals
                : goals // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            enemies: null == enemies
                ? _value.enemies
                : enemies // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            allies: null == allies
                ? _value.allies
                : allies // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PoliticalFactionImplCopyWith<$Res>
    implements $PoliticalFactionCopyWith<$Res> {
  factory _$$PoliticalFactionImplCopyWith(
    _$PoliticalFactionImpl value,
    $Res Function(_$PoliticalFactionImpl) then,
  ) = __$$PoliticalFactionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String? description,
    List<String> memberCharacterIds,
    String? ideology,
    int? power,
    List<String> goals,
    List<String> enemies,
    List<String> allies,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$PoliticalFactionImplCopyWithImpl<$Res>
    extends _$PoliticalFactionCopyWithImpl<$Res, _$PoliticalFactionImpl>
    implements _$$PoliticalFactionImplCopyWith<$Res> {
  __$$PoliticalFactionImplCopyWithImpl(
    _$PoliticalFactionImpl _value,
    $Res Function(_$PoliticalFactionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PoliticalFaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? memberCharacterIds = null,
    Object? ideology = freezed,
    Object? power = freezed,
    Object? goals = null,
    Object? enemies = null,
    Object? allies = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$PoliticalFactionImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        memberCharacterIds: null == memberCharacterIds
            ? _value._memberCharacterIds
            : memberCharacterIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        ideology: freezed == ideology
            ? _value.ideology
            : ideology // ignore: cast_nullable_to_non_nullable
                  as String?,
        power: freezed == power
            ? _value.power
            : power // ignore: cast_nullable_to_non_nullable
                  as int?,
        goals: null == goals
            ? _value._goals
            : goals // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        enemies: null == enemies
            ? _value._enemies
            : enemies // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        allies: null == allies
            ? _value._allies
            : allies // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PoliticalFactionImpl implements _PoliticalFaction {
  const _$PoliticalFactionImpl({
    required this.id,
    required this.name,
    this.description,
    final List<String> memberCharacterIds = const [],
    this.ideology,
    this.power,
    final List<String> goals = const [],
    final List<String> enemies = const [],
    final List<String> allies = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _memberCharacterIds = memberCharacterIds,
       _goals = goals,
       _enemies = enemies,
       _allies = allies,
       _customAttributes = customAttributes;

  factory _$PoliticalFactionImpl.fromJson(Map<String, dynamic> json) =>
      _$$PoliticalFactionImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  final List<String> _memberCharacterIds;
  @override
  @JsonKey()
  List<String> get memberCharacterIds {
    if (_memberCharacterIds is EqualUnmodifiableListView)
      return _memberCharacterIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_memberCharacterIds);
  }

  @override
  final String? ideology;
  // 意识形态
  @override
  final int? power;
  // 权力 1-10
  final List<String> _goals;
  // 权力 1-10
  @override
  @JsonKey()
  List<String> get goals {
    if (_goals is EqualUnmodifiableListView) return _goals;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_goals);
  }

  final List<String> _enemies;
  @override
  @JsonKey()
  List<String> get enemies {
    if (_enemies is EqualUnmodifiableListView) return _enemies;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_enemies);
  }

  final List<String> _allies;
  @override
  @JsonKey()
  List<String> get allies {
    if (_allies is EqualUnmodifiableListView) return _allies;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_allies);
  }

  final Map<String, dynamic> _customAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'PoliticalFaction(id: $id, name: $name, description: $description, memberCharacterIds: $memberCharacterIds, ideology: $ideology, power: $power, goals: $goals, enemies: $enemies, allies: $allies, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PoliticalFactionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(
              other._memberCharacterIds,
              _memberCharacterIds,
            ) &&
            (identical(other.ideology, ideology) ||
                other.ideology == ideology) &&
            (identical(other.power, power) || other.power == power) &&
            const DeepCollectionEquality().equals(other._goals, _goals) &&
            const DeepCollectionEquality().equals(other._enemies, _enemies) &&
            const DeepCollectionEquality().equals(other._allies, _allies) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    const DeepCollectionEquality().hash(_memberCharacterIds),
    ideology,
    power,
    const DeepCollectionEquality().hash(_goals),
    const DeepCollectionEquality().hash(_enemies),
    const DeepCollectionEquality().hash(_allies),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of PoliticalFaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PoliticalFactionImplCopyWith<_$PoliticalFactionImpl> get copyWith =>
      __$$PoliticalFactionImplCopyWithImpl<_$PoliticalFactionImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$PoliticalFactionImplToJson(this);
  }
}

abstract class _PoliticalFaction implements PoliticalFaction {
  const factory _PoliticalFaction({
    required final String id,
    required final String name,
    final String? description,
    final List<String> memberCharacterIds,
    final String? ideology,
    final int? power,
    final List<String> goals,
    final List<String> enemies,
    final List<String> allies,
    final Map<String, dynamic> customAttributes,
  }) = _$PoliticalFactionImpl;

  factory _PoliticalFaction.fromJson(Map<String, dynamic> json) =
      _$PoliticalFactionImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  List<String> get memberCharacterIds;
  @override
  String? get ideology; // 意识形态
  @override
  int? get power; // 权力 1-10
  @override
  List<String> get goals;
  @override
  List<String> get enemies;
  @override
  List<String> get allies;
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of PoliticalFaction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PoliticalFactionImplCopyWith<_$PoliticalFactionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LocationFeature _$LocationFeatureFromJson(Map<String, dynamic> json) {
  return _LocationFeature.fromJson(json);
}

/// @nodoc
mixin _$LocationFeature {
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  FeatureType get type => throw _privateConstructorUsedError;
  FeatureImportance? get importance => throw _privateConstructorUsedError;
  List<String> get effects => throw _privateConstructorUsedError; // 影响
  List<String> get requirements => throw _privateConstructorUsedError; // 访问要求
  Map<String, dynamic> get customAttributes =>
      throw _privateConstructorUsedError;

  /// Serializes this LocationFeature to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LocationFeature
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LocationFeatureCopyWith<LocationFeature> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationFeatureCopyWith<$Res> {
  factory $LocationFeatureCopyWith(
    LocationFeature value,
    $Res Function(LocationFeature) then,
  ) = _$LocationFeatureCopyWithImpl<$Res, LocationFeature>;
  @useResult
  $Res call({
    String name,
    String? description,
    FeatureType type,
    FeatureImportance? importance,
    List<String> effects,
    List<String> requirements,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class _$LocationFeatureCopyWithImpl<$Res, $Val extends LocationFeature>
    implements $LocationFeatureCopyWith<$Res> {
  _$LocationFeatureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationFeature
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? type = null,
    Object? importance = freezed,
    Object? effects = null,
    Object? requirements = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _value.copyWith(
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as FeatureType,
            importance: freezed == importance
                ? _value.importance
                : importance // ignore: cast_nullable_to_non_nullable
                      as FeatureImportance?,
            effects: null == effects
                ? _value.effects
                : effects // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            requirements: null == requirements
                ? _value.requirements
                : requirements // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            customAttributes: null == customAttributes
                ? _value.customAttributes
                : customAttributes // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$LocationFeatureImplCopyWith<$Res>
    implements $LocationFeatureCopyWith<$Res> {
  factory _$$LocationFeatureImplCopyWith(
    _$LocationFeatureImpl value,
    $Res Function(_$LocationFeatureImpl) then,
  ) = __$$LocationFeatureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String name,
    String? description,
    FeatureType type,
    FeatureImportance? importance,
    List<String> effects,
    List<String> requirements,
    Map<String, dynamic> customAttributes,
  });
}

/// @nodoc
class __$$LocationFeatureImplCopyWithImpl<$Res>
    extends _$LocationFeatureCopyWithImpl<$Res, _$LocationFeatureImpl>
    implements _$$LocationFeatureImplCopyWith<$Res> {
  __$$LocationFeatureImplCopyWithImpl(
    _$LocationFeatureImpl _value,
    $Res Function(_$LocationFeatureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of LocationFeature
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? description = freezed,
    Object? type = null,
    Object? importance = freezed,
    Object? effects = null,
    Object? requirements = null,
    Object? customAttributes = null,
  }) {
    return _then(
      _$LocationFeatureImpl(
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as FeatureType,
        importance: freezed == importance
            ? _value.importance
            : importance // ignore: cast_nullable_to_non_nullable
                  as FeatureImportance?,
        effects: null == effects
            ? _value._effects
            : effects // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        requirements: null == requirements
            ? _value._requirements
            : requirements // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        customAttributes: null == customAttributes
            ? _value._customAttributes
            : customAttributes // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$LocationFeatureImpl implements _LocationFeature {
  const _$LocationFeatureImpl({
    required this.name,
    this.description,
    required this.type,
    this.importance,
    final List<String> effects = const [],
    final List<String> requirements = const [],
    final Map<String, dynamic> customAttributes = const {},
  }) : _effects = effects,
       _requirements = requirements,
       _customAttributes = customAttributes;

  factory _$LocationFeatureImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocationFeatureImplFromJson(json);

  @override
  final String name;
  @override
  final String? description;
  @override
  final FeatureType type;
  @override
  final FeatureImportance? importance;
  final List<String> _effects;
  @override
  @JsonKey()
  List<String> get effects {
    if (_effects is EqualUnmodifiableListView) return _effects;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_effects);
  }

  // 影响
  final List<String> _requirements;
  // 影响
  @override
  @JsonKey()
  List<String> get requirements {
    if (_requirements is EqualUnmodifiableListView) return _requirements;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_requirements);
  }

  // 访问要求
  final Map<String, dynamic> _customAttributes;
  // 访问要求
  @override
  @JsonKey()
  Map<String, dynamic> get customAttributes {
    if (_customAttributes is EqualUnmodifiableMapView) return _customAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customAttributes);
  }

  @override
  String toString() {
    return 'LocationFeature(name: $name, description: $description, type: $type, importance: $importance, effects: $effects, requirements: $requirements, customAttributes: $customAttributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationFeatureImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.importance, importance) ||
                other.importance == importance) &&
            const DeepCollectionEquality().equals(other._effects, _effects) &&
            const DeepCollectionEquality().equals(
              other._requirements,
              _requirements,
            ) &&
            const DeepCollectionEquality().equals(
              other._customAttributes,
              _customAttributes,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    description,
    type,
    importance,
    const DeepCollectionEquality().hash(_effects),
    const DeepCollectionEquality().hash(_requirements),
    const DeepCollectionEquality().hash(_customAttributes),
  );

  /// Create a copy of LocationFeature
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationFeatureImplCopyWith<_$LocationFeatureImpl> get copyWith =>
      __$$LocationFeatureImplCopyWithImpl<_$LocationFeatureImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$LocationFeatureImplToJson(this);
  }
}

abstract class _LocationFeature implements LocationFeature {
  const factory _LocationFeature({
    required final String name,
    final String? description,
    required final FeatureType type,
    final FeatureImportance? importance,
    final List<String> effects,
    final List<String> requirements,
    final Map<String, dynamic> customAttributes,
  }) = _$LocationFeatureImpl;

  factory _LocationFeature.fromJson(Map<String, dynamic> json) =
      _$LocationFeatureImpl.fromJson;

  @override
  String get name;
  @override
  String? get description;
  @override
  FeatureType get type;
  @override
  FeatureImportance? get importance;
  @override
  List<String> get effects; // 影响
  @override
  List<String> get requirements; // 访问要求
  @override
  Map<String, dynamic> get customAttributes;

  /// Create a copy of LocationFeature
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationFeatureImplCopyWith<_$LocationFeatureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LocationEvent _$LocationEventFromJson(Map<String, dynamic> json) {
  return _LocationEvent.fromJson(json);
}

/// @nodoc
mixin _$LocationEvent {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  DateTime get eventDate => throw _privateConstructorUsedError;
  LocationEventType? get type => throw _privateConstructorUsedError;
  List<String> get involvedCharacterIds => throw _privateConstructorUsedError;
  List<String> get consequences => throw _privateConstructorUsedError; // 后果
  String? get chapterId => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this LocationEvent to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LocationEventCopyWith<LocationEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationEventCopyWith<$Res> {
  factory $LocationEventCopyWith(
    LocationEvent value,
    $Res Function(LocationEvent) then,
  ) = _$LocationEventCopyWithImpl<$Res, LocationEvent>;
  @useResult
  $Res call({
    String id,
    String title,
    String? description,
    DateTime eventDate,
    LocationEventType? type,
    List<String> involvedCharacterIds,
    List<String> consequences,
    String? chapterId,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$LocationEventCopyWithImpl<$Res, $Val extends LocationEvent>
    implements $LocationEventCopyWith<$Res> {
  _$LocationEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = freezed,
    Object? eventDate = null,
    Object? type = freezed,
    Object? involvedCharacterIds = null,
    Object? consequences = null,
    Object? chapterId = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            eventDate: null == eventDate
                ? _value.eventDate
                : eventDate // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as LocationEventType?,
            involvedCharacterIds: null == involvedCharacterIds
                ? _value.involvedCharacterIds
                : involvedCharacterIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            consequences: null == consequences
                ? _value.consequences
                : consequences // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            chapterId: freezed == chapterId
                ? _value.chapterId
                : chapterId // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$LocationEventImplCopyWith<$Res>
    implements $LocationEventCopyWith<$Res> {
  factory _$$LocationEventImplCopyWith(
    _$LocationEventImpl value,
    $Res Function(_$LocationEventImpl) then,
  ) = __$$LocationEventImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String? description,
    DateTime eventDate,
    LocationEventType? type,
    List<String> involvedCharacterIds,
    List<String> consequences,
    String? chapterId,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$LocationEventImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$LocationEventImpl>
    implements _$$LocationEventImplCopyWith<$Res> {
  __$$LocationEventImplCopyWithImpl(
    _$LocationEventImpl _value,
    $Res Function(_$LocationEventImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = freezed,
    Object? eventDate = null,
    Object? type = freezed,
    Object? involvedCharacterIds = null,
    Object? consequences = null,
    Object? chapterId = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$LocationEventImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        eventDate: null == eventDate
            ? _value.eventDate
            : eventDate // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as LocationEventType?,
        involvedCharacterIds: null == involvedCharacterIds
            ? _value._involvedCharacterIds
            : involvedCharacterIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        consequences: null == consequences
            ? _value._consequences
            : consequences // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        chapterId: freezed == chapterId
            ? _value.chapterId
            : chapterId // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$LocationEventImpl implements _LocationEvent {
  const _$LocationEventImpl({
    required this.id,
    required this.title,
    this.description,
    required this.eventDate,
    this.type,
    final List<String> involvedCharacterIds = const [],
    final List<String> consequences = const [],
    this.chapterId,
    final Map<String, dynamic> metadata = const {},
    this.createdAt,
    this.updatedAt,
  }) : _involvedCharacterIds = involvedCharacterIds,
       _consequences = consequences,
       _metadata = metadata;

  factory _$LocationEventImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocationEventImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String? description;
  @override
  final DateTime eventDate;
  @override
  final LocationEventType? type;
  final List<String> _involvedCharacterIds;
  @override
  @JsonKey()
  List<String> get involvedCharacterIds {
    if (_involvedCharacterIds is EqualUnmodifiableListView)
      return _involvedCharacterIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_involvedCharacterIds);
  }

  final List<String> _consequences;
  @override
  @JsonKey()
  List<String> get consequences {
    if (_consequences is EqualUnmodifiableListView) return _consequences;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_consequences);
  }

  // 后果
  @override
  final String? chapterId;
  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'LocationEvent(id: $id, title: $title, description: $description, eventDate: $eventDate, type: $type, involvedCharacterIds: $involvedCharacterIds, consequences: $consequences, chapterId: $chapterId, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationEventImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.eventDate, eventDate) ||
                other.eventDate == eventDate) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(
              other._involvedCharacterIds,
              _involvedCharacterIds,
            ) &&
            const DeepCollectionEquality().equals(
              other._consequences,
              _consequences,
            ) &&
            (identical(other.chapterId, chapterId) ||
                other.chapterId == chapterId) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    title,
    description,
    eventDate,
    type,
    const DeepCollectionEquality().hash(_involvedCharacterIds),
    const DeepCollectionEquality().hash(_consequences),
    chapterId,
    const DeepCollectionEquality().hash(_metadata),
    createdAt,
    updatedAt,
  );

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationEventImplCopyWith<_$LocationEventImpl> get copyWith =>
      __$$LocationEventImplCopyWithImpl<_$LocationEventImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LocationEventImplToJson(this);
  }
}

abstract class _LocationEvent implements LocationEvent {
  const factory _LocationEvent({
    required final String id,
    required final String title,
    final String? description,
    required final DateTime eventDate,
    final LocationEventType? type,
    final List<String> involvedCharacterIds,
    final List<String> consequences,
    final String? chapterId,
    final Map<String, dynamic> metadata,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$LocationEventImpl;

  factory _LocationEvent.fromJson(Map<String, dynamic> json) =
      _$LocationEventImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String? get description;
  @override
  DateTime get eventDate;
  @override
  LocationEventType? get type;
  @override
  List<String> get involvedCharacterIds;
  @override
  List<String> get consequences; // 后果
  @override
  String? get chapterId;
  @override
  Map<String, dynamic> get metadata;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationEventImplCopyWith<_$LocationEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LocationSecret _$LocationSecretFromJson(Map<String, dynamic> json) {
  return _LocationSecret.fromJson(json);
}

/// @nodoc
mixin _$LocationSecret {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  SecretType get type => throw _privateConstructorUsedError;
  SecretSeverity get severity => throw _privateConstructorUsedError;
  List<String> get knownByCharacterIds => throw _privateConstructorUsedError;
  String? get discoveryCondition => throw _privateConstructorUsedError; // 发现条件
  String? get consequence => throw _privateConstructorUsedError; // 发现后果
  bool? get isDiscovered => throw _privateConstructorUsedError;
  DateTime? get discoveredAt => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this LocationSecret to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LocationSecret
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LocationSecretCopyWith<LocationSecret> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationSecretCopyWith<$Res> {
  factory $LocationSecretCopyWith(
    LocationSecret value,
    $Res Function(LocationSecret) then,
  ) = _$LocationSecretCopyWithImpl<$Res, LocationSecret>;
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    SecretType type,
    SecretSeverity severity,
    List<String> knownByCharacterIds,
    String? discoveryCondition,
    String? consequence,
    bool? isDiscovered,
    DateTime? discoveredAt,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$LocationSecretCopyWithImpl<$Res, $Val extends LocationSecret>
    implements $LocationSecretCopyWith<$Res> {
  _$LocationSecretCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationSecret
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? type = null,
    Object? severity = null,
    Object? knownByCharacterIds = null,
    Object? discoveryCondition = freezed,
    Object? consequence = freezed,
    Object? isDiscovered = freezed,
    Object? discoveredAt = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as SecretType,
            severity: null == severity
                ? _value.severity
                : severity // ignore: cast_nullable_to_non_nullable
                      as SecretSeverity,
            knownByCharacterIds: null == knownByCharacterIds
                ? _value.knownByCharacterIds
                : knownByCharacterIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            discoveryCondition: freezed == discoveryCondition
                ? _value.discoveryCondition
                : discoveryCondition // ignore: cast_nullable_to_non_nullable
                      as String?,
            consequence: freezed == consequence
                ? _value.consequence
                : consequence // ignore: cast_nullable_to_non_nullable
                      as String?,
            isDiscovered: freezed == isDiscovered
                ? _value.isDiscovered
                : isDiscovered // ignore: cast_nullable_to_non_nullable
                      as bool?,
            discoveredAt: freezed == discoveredAt
                ? _value.discoveredAt
                : discoveredAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$LocationSecretImplCopyWith<$Res>
    implements $LocationSecretCopyWith<$Res> {
  factory _$$LocationSecretImplCopyWith(
    _$LocationSecretImpl value,
    $Res Function(_$LocationSecretImpl) then,
  ) = __$$LocationSecretImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    SecretType type,
    SecretSeverity severity,
    List<String> knownByCharacterIds,
    String? discoveryCondition,
    String? consequence,
    bool? isDiscovered,
    DateTime? discoveredAt,
    Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$LocationSecretImplCopyWithImpl<$Res>
    extends _$LocationSecretCopyWithImpl<$Res, _$LocationSecretImpl>
    implements _$$LocationSecretImplCopyWith<$Res> {
  __$$LocationSecretImplCopyWithImpl(
    _$LocationSecretImpl _value,
    $Res Function(_$LocationSecretImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of LocationSecret
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? type = null,
    Object? severity = null,
    Object? knownByCharacterIds = null,
    Object? discoveryCondition = freezed,
    Object? consequence = freezed,
    Object? isDiscovered = freezed,
    Object? discoveredAt = freezed,
    Object? metadata = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$LocationSecretImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as SecretType,
        severity: null == severity
            ? _value.severity
            : severity // ignore: cast_nullable_to_non_nullable
                  as SecretSeverity,
        knownByCharacterIds: null == knownByCharacterIds
            ? _value._knownByCharacterIds
            : knownByCharacterIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        discoveryCondition: freezed == discoveryCondition
            ? _value.discoveryCondition
            : discoveryCondition // ignore: cast_nullable_to_non_nullable
                  as String?,
        consequence: freezed == consequence
            ? _value.consequence
            : consequence // ignore: cast_nullable_to_non_nullable
                  as String?,
        isDiscovered: freezed == isDiscovered
            ? _value.isDiscovered
            : isDiscovered // ignore: cast_nullable_to_non_nullable
                  as bool?,
        discoveredAt: freezed == discoveredAt
            ? _value.discoveredAt
            : discoveredAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$LocationSecretImpl implements _LocationSecret {
  const _$LocationSecretImpl({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.severity,
    final List<String> knownByCharacterIds = const [],
    this.discoveryCondition,
    this.consequence,
    this.isDiscovered,
    this.discoveredAt,
    final Map<String, dynamic> metadata = const {},
    this.createdAt,
    this.updatedAt,
  }) : _knownByCharacterIds = knownByCharacterIds,
       _metadata = metadata;

  factory _$LocationSecretImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocationSecretImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  @override
  final SecretType type;
  @override
  final SecretSeverity severity;
  final List<String> _knownByCharacterIds;
  @override
  @JsonKey()
  List<String> get knownByCharacterIds {
    if (_knownByCharacterIds is EqualUnmodifiableListView)
      return _knownByCharacterIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_knownByCharacterIds);
  }

  @override
  final String? discoveryCondition;
  // 发现条件
  @override
  final String? consequence;
  // 发现后果
  @override
  final bool? isDiscovered;
  @override
  final DateTime? discoveredAt;
  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'LocationSecret(id: $id, title: $title, description: $description, type: $type, severity: $severity, knownByCharacterIds: $knownByCharacterIds, discoveryCondition: $discoveryCondition, consequence: $consequence, isDiscovered: $isDiscovered, discoveredAt: $discoveredAt, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationSecretImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.severity, severity) ||
                other.severity == severity) &&
            const DeepCollectionEquality().equals(
              other._knownByCharacterIds,
              _knownByCharacterIds,
            ) &&
            (identical(other.discoveryCondition, discoveryCondition) ||
                other.discoveryCondition == discoveryCondition) &&
            (identical(other.consequence, consequence) ||
                other.consequence == consequence) &&
            (identical(other.isDiscovered, isDiscovered) ||
                other.isDiscovered == isDiscovered) &&
            (identical(other.discoveredAt, discoveredAt) ||
                other.discoveredAt == discoveredAt) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    title,
    description,
    type,
    severity,
    const DeepCollectionEquality().hash(_knownByCharacterIds),
    discoveryCondition,
    consequence,
    isDiscovered,
    discoveredAt,
    const DeepCollectionEquality().hash(_metadata),
    createdAt,
    updatedAt,
  );

  /// Create a copy of LocationSecret
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationSecretImplCopyWith<_$LocationSecretImpl> get copyWith =>
      __$$LocationSecretImplCopyWithImpl<_$LocationSecretImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$LocationSecretImplToJson(this);
  }
}

abstract class _LocationSecret implements LocationSecret {
  const factory _LocationSecret({
    required final String id,
    required final String title,
    required final String description,
    required final SecretType type,
    required final SecretSeverity severity,
    final List<String> knownByCharacterIds,
    final String? discoveryCondition,
    final String? consequence,
    final bool? isDiscovered,
    final DateTime? discoveredAt,
    final Map<String, dynamic> metadata,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$LocationSecretImpl;

  factory _LocationSecret.fromJson(Map<String, dynamic> json) =
      _$LocationSecretImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String get description;
  @override
  SecretType get type;
  @override
  SecretSeverity get severity;
  @override
  List<String> get knownByCharacterIds;
  @override
  String? get discoveryCondition; // 发现条件
  @override
  String? get consequence; // 发现后果
  @override
  bool? get isDiscovered;
  @override
  DateTime? get discoveredAt;
  @override
  Map<String, dynamic> get metadata;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of LocationSecret
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationSecretImplCopyWith<_$LocationSecretImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LocationStats _$LocationStatsFromJson(Map<String, dynamic> json) {
  return _LocationStats.fromJson(json);
}

/// @nodoc
mixin _$LocationStats {
  String get locationId => throw _privateConstructorUsedError;
  int? get visitCount => throw _privateConstructorUsedError; // 访问次数
  int? get eventCount => throw _privateConstructorUsedError; // 事件次数
  List<String> get frequentVisitors => throw _privateConstructorUsedError; // 常客
  List<String> get significantEvents =>
      throw _privateConstructorUsedError; // 重要事件
  DateTime? get lastVisit => throw _privateConstructorUsedError;
  Map<String, int> get characterVisitCounts =>
      throw _privateConstructorUsedError;
  Map<String, dynamic> get customStats => throw _privateConstructorUsedError;

  /// Serializes this LocationStats to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LocationStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LocationStatsCopyWith<LocationStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationStatsCopyWith<$Res> {
  factory $LocationStatsCopyWith(
    LocationStats value,
    $Res Function(LocationStats) then,
  ) = _$LocationStatsCopyWithImpl<$Res, LocationStats>;
  @useResult
  $Res call({
    String locationId,
    int? visitCount,
    int? eventCount,
    List<String> frequentVisitors,
    List<String> significantEvents,
    DateTime? lastVisit,
    Map<String, int> characterVisitCounts,
    Map<String, dynamic> customStats,
  });
}

/// @nodoc
class _$LocationStatsCopyWithImpl<$Res, $Val extends LocationStats>
    implements $LocationStatsCopyWith<$Res> {
  _$LocationStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? locationId = null,
    Object? visitCount = freezed,
    Object? eventCount = freezed,
    Object? frequentVisitors = null,
    Object? significantEvents = null,
    Object? lastVisit = freezed,
    Object? characterVisitCounts = null,
    Object? customStats = null,
  }) {
    return _then(
      _value.copyWith(
            locationId: null == locationId
                ? _value.locationId
                : locationId // ignore: cast_nullable_to_non_nullable
                      as String,
            visitCount: freezed == visitCount
                ? _value.visitCount
                : visitCount // ignore: cast_nullable_to_non_nullable
                      as int?,
            eventCount: freezed == eventCount
                ? _value.eventCount
                : eventCount // ignore: cast_nullable_to_non_nullable
                      as int?,
            frequentVisitors: null == frequentVisitors
                ? _value.frequentVisitors
                : frequentVisitors // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            significantEvents: null == significantEvents
                ? _value.significantEvents
                : significantEvents // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            lastVisit: freezed == lastVisit
                ? _value.lastVisit
                : lastVisit // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            characterVisitCounts: null == characterVisitCounts
                ? _value.characterVisitCounts
                : characterVisitCounts // ignore: cast_nullable_to_non_nullable
                      as Map<String, int>,
            customStats: null == customStats
                ? _value.customStats
                : customStats // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$LocationStatsImplCopyWith<$Res>
    implements $LocationStatsCopyWith<$Res> {
  factory _$$LocationStatsImplCopyWith(
    _$LocationStatsImpl value,
    $Res Function(_$LocationStatsImpl) then,
  ) = __$$LocationStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String locationId,
    int? visitCount,
    int? eventCount,
    List<String> frequentVisitors,
    List<String> significantEvents,
    DateTime? lastVisit,
    Map<String, int> characterVisitCounts,
    Map<String, dynamic> customStats,
  });
}

/// @nodoc
class __$$LocationStatsImplCopyWithImpl<$Res>
    extends _$LocationStatsCopyWithImpl<$Res, _$LocationStatsImpl>
    implements _$$LocationStatsImplCopyWith<$Res> {
  __$$LocationStatsImplCopyWithImpl(
    _$LocationStatsImpl _value,
    $Res Function(_$LocationStatsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of LocationStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? locationId = null,
    Object? visitCount = freezed,
    Object? eventCount = freezed,
    Object? frequentVisitors = null,
    Object? significantEvents = null,
    Object? lastVisit = freezed,
    Object? characterVisitCounts = null,
    Object? customStats = null,
  }) {
    return _then(
      _$LocationStatsImpl(
        locationId: null == locationId
            ? _value.locationId
            : locationId // ignore: cast_nullable_to_non_nullable
                  as String,
        visitCount: freezed == visitCount
            ? _value.visitCount
            : visitCount // ignore: cast_nullable_to_non_nullable
                  as int?,
        eventCount: freezed == eventCount
            ? _value.eventCount
            : eventCount // ignore: cast_nullable_to_non_nullable
                  as int?,
        frequentVisitors: null == frequentVisitors
            ? _value._frequentVisitors
            : frequentVisitors // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        significantEvents: null == significantEvents
            ? _value._significantEvents
            : significantEvents // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        lastVisit: freezed == lastVisit
            ? _value.lastVisit
            : lastVisit // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        characterVisitCounts: null == characterVisitCounts
            ? _value._characterVisitCounts
            : characterVisitCounts // ignore: cast_nullable_to_non_nullable
                  as Map<String, int>,
        customStats: null == customStats
            ? _value._customStats
            : customStats // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$LocationStatsImpl implements _LocationStats {
  const _$LocationStatsImpl({
    required this.locationId,
    this.visitCount,
    this.eventCount,
    final List<String> frequentVisitors = const [],
    final List<String> significantEvents = const [],
    this.lastVisit,
    final Map<String, int> characterVisitCounts = const {},
    final Map<String, dynamic> customStats = const {},
  }) : _frequentVisitors = frequentVisitors,
       _significantEvents = significantEvents,
       _characterVisitCounts = characterVisitCounts,
       _customStats = customStats;

  factory _$LocationStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocationStatsImplFromJson(json);

  @override
  final String locationId;
  @override
  final int? visitCount;
  // 访问次数
  @override
  final int? eventCount;
  // 事件次数
  final List<String> _frequentVisitors;
  // 事件次数
  @override
  @JsonKey()
  List<String> get frequentVisitors {
    if (_frequentVisitors is EqualUnmodifiableListView)
      return _frequentVisitors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_frequentVisitors);
  }

  // 常客
  final List<String> _significantEvents;
  // 常客
  @override
  @JsonKey()
  List<String> get significantEvents {
    if (_significantEvents is EqualUnmodifiableListView)
      return _significantEvents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_significantEvents);
  }

  // 重要事件
  @override
  final DateTime? lastVisit;
  final Map<String, int> _characterVisitCounts;
  @override
  @JsonKey()
  Map<String, int> get characterVisitCounts {
    if (_characterVisitCounts is EqualUnmodifiableMapView)
      return _characterVisitCounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_characterVisitCounts);
  }

  final Map<String, dynamic> _customStats;
  @override
  @JsonKey()
  Map<String, dynamic> get customStats {
    if (_customStats is EqualUnmodifiableMapView) return _customStats;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_customStats);
  }

  @override
  String toString() {
    return 'LocationStats(locationId: $locationId, visitCount: $visitCount, eventCount: $eventCount, frequentVisitors: $frequentVisitors, significantEvents: $significantEvents, lastVisit: $lastVisit, characterVisitCounts: $characterVisitCounts, customStats: $customStats)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationStatsImpl &&
            (identical(other.locationId, locationId) ||
                other.locationId == locationId) &&
            (identical(other.visitCount, visitCount) ||
                other.visitCount == visitCount) &&
            (identical(other.eventCount, eventCount) ||
                other.eventCount == eventCount) &&
            const DeepCollectionEquality().equals(
              other._frequentVisitors,
              _frequentVisitors,
            ) &&
            const DeepCollectionEquality().equals(
              other._significantEvents,
              _significantEvents,
            ) &&
            (identical(other.lastVisit, lastVisit) ||
                other.lastVisit == lastVisit) &&
            const DeepCollectionEquality().equals(
              other._characterVisitCounts,
              _characterVisitCounts,
            ) &&
            const DeepCollectionEquality().equals(
              other._customStats,
              _customStats,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    locationId,
    visitCount,
    eventCount,
    const DeepCollectionEquality().hash(_frequentVisitors),
    const DeepCollectionEquality().hash(_significantEvents),
    lastVisit,
    const DeepCollectionEquality().hash(_characterVisitCounts),
    const DeepCollectionEquality().hash(_customStats),
  );

  /// Create a copy of LocationStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationStatsImplCopyWith<_$LocationStatsImpl> get copyWith =>
      __$$LocationStatsImplCopyWithImpl<_$LocationStatsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LocationStatsImplToJson(this);
  }
}

abstract class _LocationStats implements LocationStats {
  const factory _LocationStats({
    required final String locationId,
    final int? visitCount,
    final int? eventCount,
    final List<String> frequentVisitors,
    final List<String> significantEvents,
    final DateTime? lastVisit,
    final Map<String, int> characterVisitCounts,
    final Map<String, dynamic> customStats,
  }) = _$LocationStatsImpl;

  factory _LocationStats.fromJson(Map<String, dynamic> json) =
      _$LocationStatsImpl.fromJson;

  @override
  String get locationId;
  @override
  int? get visitCount; // 访问次数
  @override
  int? get eventCount; // 事件次数
  @override
  List<String> get frequentVisitors; // 常客
  @override
  List<String> get significantEvents; // 重要事件
  @override
  DateTime? get lastVisit;
  @override
  Map<String, int> get characterVisitCounts;
  @override
  Map<String, dynamic> get customStats;

  /// Create a copy of LocationStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationStatsImplCopyWith<_$LocationStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
