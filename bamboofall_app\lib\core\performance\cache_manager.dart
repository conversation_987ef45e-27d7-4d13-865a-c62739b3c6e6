import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'dart:developer' as developer;

/// 缓存管理器
class CacheManager {
  static final CacheManager _instance = CacheManager._internal();
  factory CacheManager() => _instance;
  CacheManager._internal();

  final Map<String, CacheStore> _stores = {};
  Timer? _cleanupTimer;
  
  // 默认配置
  static const Duration _defaultTTL = Duration(hours: 1);
  static const int _defaultMaxSize = 100;
  static const Duration _cleanupInterval = Duration(minutes: 5);

  /// 初始化缓存管理器
  void initialize() {
    // 创建默认缓存存储
    _stores['default'] = CacheStore('default');
    _stores['images'] = CacheStore('images', maxSize: 50, ttl: const Duration(hours: 24));
    _stores['ai_responses'] = CacheStore('ai_responses', maxSize: 200, ttl: const Duration(hours: 2));
    _stores['documents'] = CacheStore('documents', maxSize: 30, ttl: const Duration(days: 1));
    _stores['templates'] = CacheStore('templates', maxSize: 100, ttl: const Duration(days: 7));
    
    // 启动定期清理
    _startPeriodicCleanup();
    
    developer.log('Cache manager initialized', name: 'CacheManager');
  }

  /// 获取缓存存储
  CacheStore getStore(String name) {
    return _stores[name] ?? _stores['default']!;
  }

  /// 创建自定义缓存存储
  CacheStore createStore(String name, {
    int maxSize = _defaultMaxSize,
    Duration ttl = _defaultTTL,
  }) {
    final store = CacheStore(name, maxSize: maxSize, ttl: ttl);
    _stores[name] = store;
    return store;
  }

  /// 获取缓存统计信息
  CacheStatistics getStatistics() {
    int totalEntries = 0;
    int totalHits = 0;
    int totalMisses = 0;
    int totalSize = 0;

    for (final store in _stores.values) {
      final stats = store.getStatistics();
      totalEntries += stats.entryCount;
      totalHits += stats.hitCount;
      totalMisses += stats.missCount;
      totalSize += stats.estimatedSizeBytes;
    }

    return CacheStatistics(
      storeCount: _stores.length,
      totalEntries: totalEntries,
      totalHits: totalHits,
      totalMisses: totalMisses,
      hitRate: totalHits + totalMisses > 0 ? totalHits / (totalHits + totalMisses) : 0,
      estimatedTotalSizeBytes: totalSize,
    );
  }

  /// 清理所有过期缓存
  void cleanupExpired() {
    for (final store in _stores.values) {
      store.cleanupExpired();
    }
  }

  /// 清理所有缓存
  void clearAll() {
    for (final store in _stores.values) {
      store.clear();
    }
    developer.log('All caches cleared', name: 'CacheManager');
  }

  /// 启动定期清理
  void _startPeriodicCleanup() {
    _cleanupTimer = Timer.periodic(_cleanupInterval, (_) {
      cleanupExpired();
    });
  }

  /// 停止定期清理
  void _stopPeriodicCleanup() {
    _cleanupTimer?.cancel();
    _cleanupTimer = null;
  }

  /// 释放资源
  void dispose() {
    _stopPeriodicCleanup();
    _stores.clear();
  }
}

/// 缓存存储
class CacheStore {
  final String name;
  final int maxSize;
  final Duration ttl;
  
  final LinkedHashMap<String, CacheEntry> _cache = LinkedHashMap();
  int _hitCount = 0;
  int _missCount = 0;

  CacheStore(this.name, {
    this.maxSize = 100,
    this.ttl = const Duration(hours: 1),
  });

  /// 获取缓存项
  T? get<T>(String key) {
    final entry = _cache[key];
    
    if (entry == null) {
      _missCount++;
      return null;
    }
    
    if (entry.isExpired) {
      _cache.remove(key);
      _missCount++;
      return null;
    }
    
    // 更新访问时间和访问次数
    entry._lastAccessTime = DateTime.now();
    entry._accessCount++;
    
    // 移动到末尾（LRU策略）
    _cache.remove(key);
    _cache[key] = entry;
    
    _hitCount++;
    return entry.value as T?;
  }

  /// 设置缓存项
  void set<T>(String key, T value, {Duration? customTTL}) {
    final effectiveTTL = customTTL ?? ttl;
    final entry = CacheEntry(
      key: key,
      value: value,
      createdTime: DateTime.now(),
      ttl: effectiveTTL,
    );

    _cache[key] = entry;

    // 检查大小限制
    if (_cache.length > maxSize) {
      _evictLRU();
    }
  }

  /// 删除缓存项
  bool remove(String key) {
    return _cache.remove(key) != null;
  }

  /// 检查是否包含键
  bool containsKey(String key) {
    final entry = _cache[key];
    if (entry == null) return false;
    
    if (entry.isExpired) {
      _cache.remove(key);
      return false;
    }
    
    return true;
  }

  /// 获取所有键
  List<String> get keys => _cache.keys.toList();

  /// 获取缓存大小
  int get size => _cache.length;

  /// 清空缓存
  void clear() {
    _cache.clear();
    _hitCount = 0;
    _missCount = 0;
  }

  /// 清理过期项
  void cleanupExpired() {
    final expiredKeys = <String>[];
    
    for (final entry in _cache.entries) {
      if (entry.value.isExpired) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _cache.remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      developer.log('Cleaned up ${expiredKeys.length} expired entries from $name cache', 
          name: 'CacheStore');
    }
  }

  /// 驱逐最少使用的项（LRU）
  void _evictLRU() {
    if (_cache.isEmpty) return;
    
    final firstKey = _cache.keys.first;
    _cache.remove(firstKey);
    
    developer.log('Evicted LRU entry: $firstKey from $name cache', name: 'CacheStore');
  }

  /// 获取统计信息
  CacheStoreStatistics getStatistics() {
    int estimatedSize = 0;
    
    for (final entry in _cache.values) {
      estimatedSize += entry.estimatedSizeBytes;
    }

    return CacheStoreStatistics(
      name: name,
      entryCount: _cache.length,
      maxSize: maxSize,
      hitCount: _hitCount,
      missCount: _missCount,
      hitRate: _hitCount + _missCount > 0 ? _hitCount / (_hitCount + _missCount) : 0,
      estimatedSizeBytes: estimatedSize,
      ttl: ttl,
    );
  }

  /// 获取缓存项详情
  List<CacheEntryInfo> getEntryDetails() {
    return _cache.values.map((entry) => CacheEntryInfo(
      key: entry.key,
      createdTime: entry.createdTime,
      lastAccessTime: entry._lastAccessTime,
      accessCount: entry._accessCount,
      isExpired: entry.isExpired,
      estimatedSizeBytes: entry.estimatedSizeBytes,
      ttl: entry.ttl,
    )).toList();
  }
}

/// 缓存项
class CacheEntry {
  final String key;
  final dynamic value;
  final DateTime createdTime;
  final Duration ttl;
  
  DateTime _lastAccessTime;
  int _accessCount = 1;

  CacheEntry({
    required this.key,
    required this.value,
    required this.createdTime,
    required this.ttl,
  }) : _lastAccessTime = createdTime;

  /// 是否过期
  bool get isExpired => DateTime.now().isAfter(createdTime.add(ttl));

  /// 剩余生存时间
  Duration get remainingTTL {
    final expireTime = createdTime.add(ttl);
    final now = DateTime.now();
    return expireTime.isAfter(now) ? expireTime.difference(now) : Duration.zero;
  }

  /// 估算大小（字节）
  int get estimatedSizeBytes {
    if (value == null) return 0;
    
    if (value is String) {
      return (value as String).length * 2; // UTF-16编码
    } else if (value is List<int>) {
      return (value as List<int>).length;
    } else if (value is Map) {
      return jsonEncode(value).length * 2;
    } else {
      return value.toString().length * 2;
    }
  }
}

/// 缓存统计信息
class CacheStatistics {
  final int storeCount;
  final int totalEntries;
  final int totalHits;
  final int totalMisses;
  final double hitRate;
  final int estimatedTotalSizeBytes;

  const CacheStatistics({
    required this.storeCount,
    required this.totalEntries,
    required this.totalHits,
    required this.totalMisses,
    required this.hitRate,
    required this.estimatedTotalSizeBytes,
  });

  double get estimatedTotalSizeMB => estimatedTotalSizeBytes / (1024 * 1024);
}

/// 缓存存储统计信息
class CacheStoreStatistics {
  final String name;
  final int entryCount;
  final int maxSize;
  final int hitCount;
  final int missCount;
  final double hitRate;
  final int estimatedSizeBytes;
  final Duration ttl;

  const CacheStoreStatistics({
    required this.name,
    required this.entryCount,
    required this.maxSize,
    required this.hitCount,
    required this.missCount,
    required this.hitRate,
    required this.estimatedSizeBytes,
    required this.ttl,
  });

  double get utilizationRate => entryCount / maxSize;
  double get estimatedSizeMB => estimatedSizeBytes / (1024 * 1024);
}

/// 缓存项信息
class CacheEntryInfo {
  final String key;
  final DateTime createdTime;
  final DateTime lastAccessTime;
  final int accessCount;
  final bool isExpired;
  final int estimatedSizeBytes;
  final Duration ttl;

  const CacheEntryInfo({
    required this.key,
    required this.createdTime,
    required this.lastAccessTime,
    required this.accessCount,
    required this.isExpired,
    required this.estimatedSizeBytes,
    required this.ttl,
  });

  Duration get age => DateTime.now().difference(createdTime);
  Duration get timeSinceLastAccess => DateTime.now().difference(lastAccessTime);
}

/// 智能缓存策略
class SmartCacheStrategy {
  final CacheManager _cacheManager;

  SmartCacheStrategy(this._cacheManager);

  /// 根据使用模式调整缓存配置
  void optimizeCacheConfiguration() {
    final statistics = _cacheManager.getStatistics();
    
    // 如果命中率低，可能需要增加缓存大小或调整TTL
    if (statistics.hitRate < 0.5) {
      developer.log('Low cache hit rate: ${statistics.hitRate.toStringAsFixed(2)}', 
          name: 'SmartCacheStrategy');
      _suggestCacheOptimizations();
    }
    
    // 如果内存使用过高，建议清理
    if (statistics.estimatedTotalSizeMB > 100) {
      developer.log('High cache memory usage: ${statistics.estimatedTotalSizeMB.toStringAsFixed(1)}MB', 
          name: 'SmartCacheStrategy');
      _performIntelligentCleanup();
    }
  }

  /// 建议缓存优化
  void _suggestCacheOptimizations() {
    // 分析各个存储的性能
    for (final storeName in ['images', 'ai_responses', 'documents', 'templates']) {
      final store = _cacheManager.getStore(storeName);
      final stats = store.getStatistics();
      
      if (stats.hitRate < 0.3) {
        developer.log('Consider increasing TTL for $storeName cache (hit rate: ${stats.hitRate.toStringAsFixed(2)})', 
            name: 'SmartCacheStrategy');
      }
      
      if (stats.utilizationRate > 0.9) {
        developer.log('Consider increasing max size for $storeName cache (utilization: ${(stats.utilizationRate * 100).toStringAsFixed(1)}%)', 
            name: 'SmartCacheStrategy');
      }
    }
  }

  /// 执行智能清理
  void _performIntelligentCleanup() {
    // 优先清理访问频率低的大文件
    for (final storeName in ['images', 'documents']) {
      final store = _cacheManager.getStore(storeName);
      final entries = store.getEntryDetails();
      
      // 按访问频率和大小排序，清理低频大文件
      entries.sort((a, b) {
        final aScore = a.accessCount / a.estimatedSizeBytes;
        final bScore = b.accessCount / b.estimatedSizeBytes;
        return aScore.compareTo(bScore);
      });
      
      // 清理前20%的低效缓存项
      final cleanupCount = (entries.length * 0.2).ceil();
      for (int i = 0; i < cleanupCount && i < entries.length; i++) {
        store.remove(entries[i].key);
      }
      
      if (cleanupCount > 0) {
        developer.log('Cleaned up $cleanupCount low-efficiency entries from $storeName cache', 
            name: 'SmartCacheStrategy');
      }
    }
  }
}

/// 缓存预热器
class CachePrewarmer {
  final CacheManager _cacheManager;

  CachePrewarmer(this._cacheManager);

  /// 预热常用模板
  Future<void> prewarmTemplates(List<String> templateIds) async {
    final store = _cacheManager.getStore('templates');
    
    for (final templateId in templateIds) {
      if (!store.containsKey(templateId)) {
        // 这里应该从数据源加载模板
        // 暂时使用占位符
        store.set(templateId, 'template_content_$templateId');
      }
    }
    
    developer.log('Prewarmed ${templateIds.length} templates', name: 'CachePrewarmer');
  }

  /// 预热用户文档
  Future<void> prewarmUserDocuments(List<String> documentIds) async {
    final store = _cacheManager.getStore('documents');
    
    for (final documentId in documentIds) {
      if (!store.containsKey(documentId)) {
        // 这里应该从数据源加载文档
        store.set(documentId, 'document_content_$documentId');
      }
    }
    
    developer.log('Prewarmed ${documentIds.length} documents', name: 'CachePrewarmer');
  }
}

/// 缓存监控器
class CacheMonitor {
  final CacheManager _cacheManager;
  Timer? _monitoringTimer;
  final List<CacheStatistics> _statisticsHistory = [];

  CacheMonitor(this._cacheManager);

  /// 开始监控
  void startMonitoring({Duration interval = const Duration(minutes: 1)}) {
    _monitoringTimer = Timer.periodic(interval, (_) {
      _collectStatistics();
    });
    
    developer.log('Cache monitoring started', name: 'CacheMonitor');
  }

  /// 停止监控
  void stopMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    developer.log('Cache monitoring stopped', name: 'CacheMonitor');
  }

  /// 收集统计信息
  void _collectStatistics() {
    final stats = _cacheManager.getStatistics();
    _statisticsHistory.add(stats);
    
    // 保持最近100个统计记录
    if (_statisticsHistory.length > 100) {
      _statisticsHistory.removeAt(0);
    }
    
    // 检查异常情况
    _checkForAnomalies(stats);
  }

  /// 检查异常情况
  void _checkForAnomalies(CacheStatistics stats) {
    // 检查命中率是否异常低
    if (stats.hitRate < 0.2) {
      developer.log('ANOMALY: Very low cache hit rate: ${stats.hitRate.toStringAsFixed(2)}', 
          name: 'CacheMonitor', level: 900);
    }
    
    // 检查内存使用是否过高
    if (stats.estimatedTotalSizeMB > 200) {
      developer.log('ANOMALY: High cache memory usage: ${stats.estimatedTotalSizeMB.toStringAsFixed(1)}MB', 
          name: 'CacheMonitor', level: 900);
    }
  }

  /// 获取统计历史
  List<CacheStatistics> get statisticsHistory => List.unmodifiable(_statisticsHistory);

  /// 生成监控报告
  String generateReport() {
    if (_statisticsHistory.isEmpty) {
      return 'No monitoring data available';
    }

    final latest = _statisticsHistory.last;
    final buffer = StringBuffer();
    
    buffer.writeln('=== 缓存监控报告 ===');
    buffer.writeln('生成时间: ${DateTime.now()}');
    buffer.writeln('监控记录数: ${_statisticsHistory.length}');
    buffer.writeln('');
    buffer.writeln('当前状态:');
    buffer.writeln('  缓存存储数: ${latest.storeCount}');
    buffer.writeln('  总缓存项: ${latest.totalEntries}');
    buffer.writeln('  命中率: ${(latest.hitRate * 100).toStringAsFixed(1)}%');
    buffer.writeln('  内存使用: ${latest.estimatedTotalSizeMB.toStringAsFixed(1)} MB');
    
    return buffer.toString();
  }
}