import '../entities/project.dart';

/// 项目仓库接口
/// 定义项目数据访问的抽象接口
abstract class ProjectRepository {
  /// 获取所有项目
  Future<List<Project>> getAllProjects();

  /// 根据ID获取项目
  Future<Project?> getProjectById(String projectId);

  /// 根据状态获取项目
  Future<List<Project>> getProjectsByStatus(ProjectStatus status);

  /// 获取收藏的项目
  Future<List<Project>> getFavoriteProjects();

  /// 根据标签获取项目
  Future<List<Project>> getProjectsByTag(String tag);

  /// 搜索项目
  Future<List<Project>> searchProjects(String query);

  /// 创建项目
  Future<Project> createProject(Project project);

  /// 更新项目
  Future<Project> updateProject(Project project);

  /// 删除项目
  Future<void> deleteProject(String projectId);

  /// 归档项目
  Future<Project> archiveProject(String projectId, String reason);

  /// 恢复项目
  Future<Project> restoreProject(String projectId);

  /// 获取项目统计信息
  Future<Map<ProjectStatus, int>> getProjectStatistics();

  /// 获取最近访问的项目
  Future<List<Project>> getRecentProjects({int limit = 10});

  /// 更新项目访问时间
  Future<void> updateProjectAccessTime(String projectId);

  /// 清理已删除的项目
  Future<void> cleanupDeletedProjects({Duration olderThan = const Duration(days: 30)});

  /// 导出项目数据
  Future<Map<String, dynamic>> exportProject(String projectId);

  /// 导入项目数据
  Future<Project> importProject(Map<String, dynamic> projectData);

  /// 复制项目
  Future<Project> duplicateProject(String projectId, String newName);

  /// 获取项目模板
  Future<List<ProjectTemplate>> getProjectTemplates();

  /// 创建项目模板
  Future<ProjectTemplate> createProjectTemplate(ProjectTemplate template);

  /// 删除项目模板
  Future<void> deleteProjectTemplate(String templateId);

  /// 基于模板创建项目
  Future<Project> createProjectFromTemplate(String templateId, String projectName, String createdBy);
}

/// 项目模板
class ProjectTemplate {
  final String id;
  final String name;
  final String description;
  final ProjectType type;
  final ProjectConfig config;
  final List<String> tags;
  final bool isBuiltIn;
  final DateTime createdAt;

  const ProjectTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.config,
    required this.tags,
    required this.isBuiltIn,
    required this.createdAt,
  });
}