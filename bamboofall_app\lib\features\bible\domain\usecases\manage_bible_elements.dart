import 'package:logger/logger.dart';

import '../entities/story_bible.dart';
import '../entities/character.dart';
import '../entities/location.dart';
import '../entities/world_settings.dart';
import '../repositories/bible_repository.dart';

/// 圣经元素管理用例
/// 提供圣经系统的核心业务功能
class ManageBibleElementsUseCase {
  final BibleRepository _repository;
  final Logger _logger;
  
  ManageBibleElementsUseCase({
    required BibleRepository repository,
    Logger? logger,
  }) : _repository = repository,
       _logger = logger ?? Logger();
  
  // ==================== StoryBible 管理 ====================
  
  /// 创建故事圣经
  Future<BibleOperationResult<StoryBible>> createStoryBible({
    required String projectId,
    required String title,
    String? description,
    required WorldSettings worldSettings,
  }) async {
    try {
      // 检查项目是否已有圣经
      final existing = await _repository.getStoryBibleByProjectId(projectId);
      if (existing != null) {
        return BibleOperationResult.failure(
          'Project already has a story bible',
          BibleOperationErrorType.alreadyExists,
        );
      }
      
      // 创建新的故事圣经
      final storyBible = StoryBible(
        id: _generateId('bible'),
        projectId: projectId,
        title: title,
        description: description,
        worldSettings: worldSettings,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      // 保存到仓库
      await _repository.saveStoryBible(storyBible);
      
      _logger.d('Created story bible: ${storyBible.id}');
      return BibleOperationResult.success(storyBible);
    } catch (e) {
      _logger.e('Failed to create story bible: $e');
      return BibleOperationResult.failure(
        'Failed to create story bible: $e',
        BibleOperationErrorType.unknown,
      );
    }
  }
  
  /// 更新故事圣经
  Future<BibleOperationResult<StoryBible>> updateStoryBible(
    StoryBible storyBible,
  ) async {
    try {
      // 检查圣经是否存在
      final existing = await _repository.getStoryBible(storyBible.id);
      if (existing == null) {
        return BibleOperationResult.failure(
          'Story bible not found',
          BibleOperationErrorType.notFound,
        );
      }
      
      // 更新时间戳
      final updatedBible = storyBible.copyWith(
        updatedAt: DateTime.now(),
        version: existing.version + 1,
      );
      
      // 保存更新
      await _repository.updateStoryBible(updatedBible);
      
      _logger.d('Updated story bible: ${updatedBible.id}');
      return BibleOperationResult.success(updatedBible);
    } catch (e) {
      _logger.e('Failed to update story bible: $e');
      return BibleOperationResult.failure(
        'Failed to update story bible: $e',
        BibleOperationErrorType.unknown,
      );
    }
  }
  
  /// 删除故事圣经
  Future<BibleOperationResult<void>> deleteStoryBible(String id) async {
    try {
      // 检查圣经是否存在
      final existing = await _repository.getStoryBible(id);
      if (existing == null) {
        return BibleOperationResult.failure(
          'Story bible not found',
          BibleOperationErrorType.notFound,
        );
      }
      
      // 删除圣经
      await _repository.deleteStoryBible(id);
      
      _logger.d('Deleted story bible: $id');
      return BibleOperationResult.success(null);
    } catch (e) {
      _logger.e('Failed to delete story bible: $e');
      return BibleOperationResult.failure(
        'Failed to delete story bible: $e',
        BibleOperationErrorType.unknown,
      );
    }
  }
  
  // ==================== Character 管理 ====================
  
  /// 创建角色
  Future<BibleOperationResult<Character>> createCharacter({
    required String name,
    required CharacterType type,
    String? description,
    CharacterAppearance? appearance,
    CharacterPersonality? personality,
    CharacterBackground? background,
  }) async {
    try {
      // 验证角色名称唯一性
      final existing = await _repository.searchCharacters(name);
      if (existing.any((c) => c.name.toLowerCase() == name.toLowerCase())) {
        return BibleOperationResult.failure(
          'Character with this name already exists',
          BibleOperationErrorType.alreadyExists,
        );
      }
      
      // 创建新角色
      final character = Character(
        id: _generateId('char'),
        name: name,
        type: type,
        status: CharacterStatus.alive,
        description: description,
        appearance: appearance,
        personality: personality,
        background: background,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      // 验证角色约束
      final isValid = await _repository.validateCharacterConstraints(character);
      if (!isValid) {
        return BibleOperationResult.failure(
          'Character constraints validation failed',
          BibleOperationErrorType.validationFailed,
        );
      }
      
      // 保存角色
      await _repository.saveCharacter(character);
      
      _logger.d('Created character: ${character.id}');
      return BibleOperationResult.success(character);
    } catch (e) {
      _logger.e('Failed to create character: $e');
      return BibleOperationResult.failure(
        'Failed to create character: $e',
        BibleOperationErrorType.unknown,
      );
    }
  }
  
  /// 更新角色
  Future<BibleOperationResult<Character>> updateCharacter(
    Character character,
  ) async {
    try {
      // 检查角色是否存在
      final existing = await _repository.getCharacter(character.id);
      if (existing == null) {
        return BibleOperationResult.failure(
          'Character not found',
          BibleOperationErrorType.notFound,
        );
      }
      
      // 更新时间戳
      final updatedCharacter = character.copyWith(
        updatedAt: DateTime.now(),
      );
      
      // 验证角色约束
      final isValid = await _repository.validateCharacterConstraints(updatedCharacter);
      if (!isValid) {
        return BibleOperationResult.failure(
          'Character constraints validation failed',
          BibleOperationErrorType.validationFailed,
        );
      }
      
      // 保存更新
      await _repository.updateCharacter(updatedCharacter);
      
      _logger.d('Updated character: ${updatedCharacter.id}');
      return BibleOperationResult.success(updatedCharacter);
    } catch (e) {
      _logger.e('Failed to update character: $e');
      return BibleOperationResult.failure(
        'Failed to update character: $e',
        BibleOperationErrorType.unknown,
      );
    }
  }
  
  /// 删除角色
  Future<BibleOperationResult<void>> deleteCharacter(String id) async {
    try {
      // 检查角色是否存在
      final existing = await _repository.getCharacter(id);
      if (existing == null) {
        return BibleOperationResult.failure(
          'Character not found',
          BibleOperationErrorType.notFound,
        );
      }
      
      // 检查角色是否被引用
      final conflicts = await _checkCharacterReferences(id);
      if (conflicts.isNotEmpty) {
        return BibleOperationResult.failure(
          'Character is referenced by other elements: ${conflicts.join(', ')}',
          BibleOperationErrorType.hasReferences,
        );
      }
      
      // 删除角色
      await _repository.deleteCharacter(id);
      
      _logger.d('Deleted character: $id');
      return BibleOperationResult.success(null);
    } catch (e) {
      _logger.e('Failed to delete character: $e');
      return BibleOperationResult.failure(
        'Failed to delete character: $e',
        BibleOperationErrorType.unknown,
      );
    }
  }
  
  /// 批量创建角色
  Future<BibleOperationResult<List<Character>>> createCharactersBatch(
    List<CharacterCreationData> charactersData,
  ) async {
    try {
      final results = <Character>[];
      final errors = <String>[];
      
      for (final data in charactersData) {
        final result = await createCharacter(
          name: data.name,
          type: data.type,
          description: data.description,
          appearance: data.appearance,
          personality: data.personality,
          background: data.background,
        );
        
        if (result.isSuccess) {
          results.add(result.data!);
        } else {
          errors.add('${data.name}: ${result.error}');
        }
      }
      
      if (errors.isNotEmpty) {
        _logger.w('Batch character creation had errors: ${errors.join('; ')}');
      }
      
      _logger.d('Created ${results.length} characters in batch');
      return BibleOperationResult.success(results);
    } catch (e) {
      _logger.e('Failed to create characters batch: $e');
      return BibleOperationResult.failure(
        'Failed to create characters batch: $e',
        BibleOperationErrorType.unknown,
      );
    }
  }
  
  // ==================== Location 管理 ====================
  
  /// 创建地点
  Future<BibleOperationResult<Location>> createLocation({
    required String name,
    required LocationType type,
    String? description,
    List<String>? parentLocationIds,
    LocationGeography? geography,
    LocationClimate? climate,
    LocationCulture? culture,
  }) async {
    try {
      // 创建新地点
      final location = Location(
        id: _generateId('loc'),
        name: name,
        type: type,
        status: LocationStatus.active,
        description: description,
        parentLocationIds: parentLocationIds ?? [],
        geography: geography,
        climate: climate,
        culture: culture,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      // 验证地点约束
      final isValid = await _repository.validateLocationConstraints(location);
      if (!isValid) {
        return BibleOperationResult.failure(
          'Location constraints validation failed',
          BibleOperationErrorType.validationFailed,
        );
      }
      
      // 保存地点
      await _repository.saveLocation(location);
      
      // 更新父地点的子地点列表
      if (parentLocationIds != null) {
        await _updateParentLocationChildren(location.id, parentLocationIds);
      }
      
      _logger.d('Created location: ${location.id}');
      return BibleOperationResult.success(location);
    } catch (e) {
      _logger.e('Failed to create location: $e');
      return BibleOperationResult.failure(
        'Failed to create location: $e',
        BibleOperationErrorType.unknown,
      );
    }
  }
  
  /// 更新地点
  Future<BibleOperationResult<Location>> updateLocation(
    Location location,
  ) async {
    try {
      // 检查地点是否存在
      final existing = await _repository.getLocation(location.id);
      if (existing == null) {
        return BibleOperationResult.failure(
          'Location not found',
          BibleOperationErrorType.notFound,
        );
      }
      
      // 更新时间戳
      final updatedLocation = location.copyWith(
        updatedAt: DateTime.now(),
      );
      
      // 验证地点约束
      final isValid = await _repository.validateLocationConstraints(updatedLocation);
      if (!isValid) {
        return BibleOperationResult.failure(
          'Location constraints validation failed',
          BibleOperationErrorType.validationFailed,
        );
      }
      
      // 保存更新
      await _repository.updateLocation(updatedLocation);
      
      _logger.d('Updated location: ${updatedLocation.id}');
      return BibleOperationResult.success(updatedLocation);
    } catch (e) {
      _logger.e('Failed to update location: $e');
      return BibleOperationResult.failure(
        'Failed to update location: $e',
        BibleOperationErrorType.unknown,
      );
    }
  }
  
  /// 删除地点
  Future<BibleOperationResult<void>> deleteLocation(String id) async {
    try {
      // 检查地点是否存在
      final existing = await _repository.getLocation(id);
      if (existing == null) {
        return BibleOperationResult.failure(
          'Location not found',
          BibleOperationErrorType.notFound,
        );
      }
      
      // 检查是否有子地点
      final children = await _repository.getChildLocations(id);
      if (children.isNotEmpty) {
        return BibleOperationResult.failure(
          'Location has child locations: ${children.map((l) => l.name).join(', ')}',
          BibleOperationErrorType.hasReferences,
        );
      }
      
      // 检查地点是否被角色引用
      final conflicts = await _checkLocationReferences(id);
      if (conflicts.isNotEmpty) {
        return BibleOperationResult.failure(
          'Location is referenced by other elements: ${conflicts.join(', ')}',
          BibleOperationErrorType.hasReferences,
        );
      }
      
      // 删除地点
      await _repository.deleteLocation(id);
      
      _logger.d('Deleted location: $id');
      return BibleOperationResult.success(null);
    } catch (e) {
      _logger.e('Failed to delete location: $e');
      return BibleOperationResult.failure(
        'Failed to delete location: $e',
        BibleOperationErrorType.unknown,
      );
    }
  }
  
  // ==================== WorldSettings 管理 ====================
  
  /// 创建世界设定
  Future<BibleOperationResult<WorldSettings>> createWorldSettings({
    required String name,
    required WorldType type,
    required TechnologyLevel technologyLevel,
    required MagicLevel magicLevel,
    String? description,
    WorldPhysics? physics,
    WorldHistory? history,
  }) async {
    try {
      // 创建新世界设定
      final worldSettings = WorldSettings(
        id: _generateId('world'),
        name: name,
        type: type,
        technologyLevel: technologyLevel,
        magicLevel: magicLevel,
        description: description,
        physics: physics,
        history: history,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      // 保存世界设定
      await _repository.saveWorldSettings(worldSettings);
      
      _logger.d('Created world settings: ${worldSettings.id}');
      return BibleOperationResult.success(worldSettings);
    } catch (e) {
      _logger.e('Failed to create world settings: $e');
      return BibleOperationResult.failure(
        'Failed to create world settings: $e',
        BibleOperationErrorType.unknown,
      );
    }
  }
  
  /// 更新世界设定
  Future<BibleOperationResult<WorldSettings>> updateWorldSettings(
    WorldSettings worldSettings,
  ) async {
    try {
      // 检查世界设定是否存在
      final existing = await _repository.getWorldSettings(worldSettings.id);
      if (existing == null) {
        return BibleOperationResult.failure(
          'World settings not found',
          BibleOperationErrorType.notFound,
        );
      }
      
      // 更新时间戳
      final updatedSettings = worldSettings.copyWith(
        updatedAt: DateTime.now(),
      );
      
      // 保存更新
      await _repository.updateWorldSettings(updatedSettings);
      
      _logger.d('Updated world settings: ${updatedSettings.id}');
      return BibleOperationResult.success(updatedSettings);
    } catch (e) {
      _logger.e('Failed to update world settings: $e');
      return BibleOperationResult.failure(
        'Failed to update world settings: $e',
        BibleOperationErrorType.unknown,
      );
    }
  }
  
  // ==================== 查询和搜索 ====================
  
  /// 搜索角色
  Future<BibleOperationResult<List<Character>>> searchCharacters({
    String? query,
    CharacterType? type,
    CharacterStatus? status,
    int? limit,
  }) async {
    try {
      List<Character> results = [];
      
      if (query != null && query.isNotEmpty) {
        results = await _repository.searchCharacters(query);
      } else {
        // 获取所有角色然后过滤
        results = await _repository.searchCharacters('');
      }
      
      // 按类型过滤
      if (type != null) {
        results = results.where((c) => c.type == type).toList();
      }
      
      // 按状态过滤
      if (status != null) {
        results = results.where((c) => c.status == status).toList();
      }
      
      // 限制结果数量
      if (limit != null && results.length > limit) {
        results = results.take(limit).toList();
      }
      
      _logger.d('Found ${results.length} characters');
      return BibleOperationResult.success(results);
    } catch (e) {
      _logger.e('Failed to search characters: $e');
      return BibleOperationResult.failure(
        'Failed to search characters: $e',
        BibleOperationErrorType.unknown,
      );
    }
  }
  
  /// 获取地点层级
  Future<BibleOperationResult<List<Location>>> getLocationHierarchy(
    String rootLocationId,
  ) async {
    try {
      final hierarchy = <Location>[];
      await _buildLocationHierarchy(rootLocationId, hierarchy);
      
      _logger.d('Built location hierarchy with ${hierarchy.length} locations');
      return BibleOperationResult.success(hierarchy);
    } catch (e) {
      _logger.e('Failed to get location hierarchy: $e');
      return BibleOperationResult.failure(
        'Failed to get location hierarchy: $e',
        BibleOperationErrorType.unknown,
      );
    }
  }
  
  // ==================== 私有辅助方法 ====================
  
  /// 生成唯一ID
  String _generateId(String prefix) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return '${prefix}_${timestamp}_$random';
  }
  
  /// 检查角色引用
  Future<List<String>> _checkCharacterReferences(String characterId) async {
    final conflicts = <String>[];
    
    // 检查地点中的角色引用
    // 这里可以添加更多的引用检查逻辑
    
    return conflicts;
  }
  
  /// 检查地点引用
  Future<List<String>> _checkLocationReferences(String locationId) async {
    final conflicts = <String>[];
    
    // 检查角色的当前位置
    final characters = await _repository.searchCharacters('');
    for (final character in characters) {
      if (character.currentLocationId == locationId) {
        conflicts.add('Character: ${character.name}');
      }
    }
    
    return conflicts;
  }
  
  /// 更新父地点的子地点列表
  Future<void> _updateParentLocationChildren(
    String childId,
    List<String> parentIds,
  ) async {
    for (final parentId in parentIds) {
      final parent = await _repository.getLocation(parentId);
      if (parent != null) {
        final updatedParent = parent.copyWith(
          childLocationIds: [...parent.childLocationIds, childId],
          updatedAt: DateTime.now(),
        );
        await _repository.updateLocation(updatedParent);
      }
    }
  }
  
  /// 构建地点层级
  Future<void> _buildLocationHierarchy(
    String locationId,
    List<Location> hierarchy,
  ) async {
    final location = await _repository.getLocation(locationId);
    if (location != null) {
      hierarchy.add(location);
      
      // 递归添加子地点
      for (final childId in location.childLocationIds) {
        await _buildLocationHierarchy(childId, hierarchy);
      }
    }
  }
}

/// 圣经操作结果
class BibleOperationResult<T> {
  final bool isSuccess;
  final T? data;
  final String? error;
  final BibleOperationErrorType? errorType;
  
  const BibleOperationResult._({
    required this.isSuccess,
    this.data,
    this.error,
    this.errorType,
  });
  
  factory BibleOperationResult.success(T data) {
    return BibleOperationResult._(
      isSuccess: true,
      data: data,
    );
  }
  
  factory BibleOperationResult.failure(
    String error,
    BibleOperationErrorType errorType,
  ) {
    return BibleOperationResult._(
      isSuccess: false,
      error: error,
      errorType: errorType,
    );
  }
}

/// 圣经操作错误类型
enum BibleOperationErrorType {
  notFound,
  alreadyExists,
  validationFailed,
  hasReferences,
  unknown,
}

/// 角色创建数据
class CharacterCreationData {
  final String name;
  final CharacterType type;
  final String? description;
  final CharacterAppearance? appearance;
  final CharacterPersonality? personality;
  final CharacterBackground? background;
  
  const CharacterCreationData({
    required this.name,
    required this.type,
    this.description,
    this.appearance,
    this.personality,
    this.background,
  });
}