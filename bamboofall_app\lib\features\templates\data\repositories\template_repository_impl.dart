
import 'package:logger/logger.dart';

import '../../domain/entities/template.dart';
import '../../domain/repositories/template_repository.dart';
import '../models/template_model.dart';
import '../datasources/template_local_datasource.dart';

/// 模板仓库实现
class TemplateRepositoryImpl implements TemplateRepository {
  final TemplateLocalDataSource _localDataSource;
  static final Logger _logger = Logger();

  TemplateRepositoryImpl(this._localDataSource);

  @override
  Future<List<PromptTemplate>> getAllTemplates() async {
    final models = await _localDataSource.getAllTemplates();
    return models.map((model) => model.toEntity()).toList();
  }

  @override
  Future<List<PromptTemplate>> getTemplatesByCategory(TemplateCategory category) async {
    final models = await _localDataSource.getTemplatesByCategory(category);
    return models.map((model) => model.toEntity()).toList();
  }

  @override
  Future<PromptTemplate?> getTemplateById(String id) async {
    final model = await _localDataSource.getTemplateById(id);
    return model?.toEntity();
  }

  @override
  Future<List<PromptTemplate>> searchTemplates(String query) async {
    final models = await _localDataSource.searchTemplates(query);
    return models.map((model) => model.toEntity()).toList();
  }

  @override
  Future<List<PromptTemplate>> getTemplatesByTags(List<String> tags) async {
    final models = await _localDataSource.getTemplatesByTags(tags);
    return models.map((model) => model.toEntity()).toList();
  }

  @override
  Future<void> createTemplate(PromptTemplate template) async {
    final model = TemplateModel.fromEntity(template);
    await _localDataSource.createTemplate(model);
  }

  @override
  Future<void> updateTemplate(PromptTemplate template) async {
    final model = TemplateModel.fromEntity(template);
    await _localDataSource.updateTemplate(model);
  }

  @override
  Future<void> deleteTemplate(String id) async {
    await _localDataSource.deleteTemplate(id);
  }

  @override
  Future<PromptTemplate> duplicateTemplate(String id, String newName) async {
    final originalTemplate = await getTemplateById(id);
    if (originalTemplate == null) {
      throw Exception('Template not found: $id');
    }

    final duplicatedTemplate = originalTemplate.copyWith(
      id: _generateUniqueId(),
      name: newName,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      usageCount: 0,
      isBuiltIn: false,
    );

    await createTemplate(duplicatedTemplate);
    return duplicatedTemplate;
  }

  @override
  Future<List<PromptTemplate>> getBuiltInTemplates() async {
    final models = await _localDataSource.getBuiltInTemplates();
    return models.map((model) => model.toEntity()).toList();
  }

  @override
  Future<List<PromptTemplate>> getUserTemplates() async {
    final models = await _localDataSource.getUserTemplates();
    return models.map((model) => model.toEntity()).toList();
  }

  @override
  Future<void> incrementUsageCount(String id) async {
    await _localDataSource.incrementUsageCount(id);
  }

  @override
  Future<List<PromptTemplate>> getMostUsedTemplates({int limit = 10}) async {
    final models = await _localDataSource.getPopularTemplates(limit: limit);
    return models.map((model) => model.toEntity()).toList();
  }

  @override
  Future<List<PromptTemplate>> getRecentlyUsedTemplates({int limit = 10}) async {
    final models = await _localDataSource.getRecentTemplates(limit: limit);
    return models.map((model) => model.toEntity()).toList();
  }

  @override
  Future<String> exportTemplate(String id) async {
    final template = await getTemplateById(id);
    if (template == null) {
      throw Exception('Template not found: $id');
    }

    // 简化的导出格式（实际项目中应使用JSON）
    return _templateToExportString(template);
  }

  @override
  Future<PromptTemplate> importTemplate(String templateData) async {
    final template = _templateFromExportString(templateData);
    
    // 确保ID唯一
    final newTemplate = template.copyWith(
      id: _generateUniqueId(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isBuiltIn: false,
    );

    await createTemplate(newTemplate);
    return newTemplate;
  }

  @override
  Future<List<PromptTemplate>> importTemplates(List<String> templatesData) async {
    final templates = <PromptTemplate>[];
    
    for (final templateData in templatesData) {
      try {
        final template = await importTemplate(templateData);
        templates.add(template);
      } catch (e) {
        // 记录错误但继续处理其他模板
        _logger.e('Failed to import template: $e');
      }
    }
    
    return templates;
  }

  @override
  Future<bool> validateTemplate(PromptTemplate template) async {
    // 基本验证
    if (template.name.trim().isEmpty) {
      return false;
    }
    
    if (template.content.trim().isEmpty) {
      return false;
    }

    // 验证模板内容与变量的一致性
    if (!template.isValid()) {
      return false;
    }

    // 检查是否存在重复的变量名
    final variableNames = template.variables.map((v) => v.name).toList();
    final uniqueNames = variableNames.toSet();
    if (variableNames.length != uniqueNames.length) {
      return false;
    }

    return true;
  }

  @override
  Future<TemplateStatistics> getTemplateStatistics() async {
    final stats = await _localDataSource.getTemplateStatistics();
    return TemplateStatistics(
      totalTemplates: stats['totalTemplates'] as int,
      userTemplates: stats['userTemplates'] as int,
      systemTemplates: stats['systemTemplates'] as int,
      totalUsage: stats['totalUsage'] as int,
      categoryCount: stats['categoryCount'] as Map<TemplateCategory, int>,
      mostUsedTemplates: (stats['recentTemplates'] as List).map((e) => (e as TemplateModel).toEntity()).toList(),
      recentTemplates: (stats['recentTemplates'] as List).map((e) => (e as TemplateModel).toEntity()).toList(),
    );
  }

  @override
  Future<int> cleanupUnusedTemplates({int daysThreshold = 30}) async {
    await _localDataSource.cleanupExpiredData();
    return 0; // TODO: 实现清理功能并返回清理的数量
  }

  @override
  Future<String> backupAllTemplates() async {
    final templates = await getAllTemplates();
    final exportData = templates.map(_templateToExportString).toList();
    
    // 简化的备份格式
    return exportData.join('\n---TEMPLATE_SEPARATOR---\n');
  }

  @override
  Future<void> restoreTemplatesFromBackup(String backupData) async {
    final templatesData = backupData.split('\n---TEMPLATE_SEPARATOR---\n');
    await importTemplates(templatesData);
  }

  /// 生成唯一ID
  String _generateUniqueId() {
    return 'template_${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecond}';
  }

  /// 将模板转换为导出字符串
  String _templateToExportString(PromptTemplate template) {
    // 简化的导出格式，实际项目中应使用JSON
    final buffer = StringBuffer();
    buffer.writeln('TEMPLATE_EXPORT_V1');
    buffer.writeln('ID:${template.id}');
    buffer.writeln('NAME:${template.name}');
    buffer.writeln('DESCRIPTION:${template.description}');
    buffer.writeln('CATEGORY:${template.category.name}');
    buffer.writeln('VERSION:${template.version}');
    buffer.writeln('TAGS:${template.tags.join(',')}');
    buffer.writeln('CONTENT_START');
    buffer.writeln(template.content);
    buffer.writeln('CONTENT_END');
    buffer.writeln('VARIABLES_START');
    
    for (final variable in template.variables) {
      buffer.writeln('VAR:${variable.name}|${variable.displayName}|${variable.description}|${variable.type.name}|${variable.isRequired}|${variable.defaultValue ?? ''}|${variable.placeholder ?? ''}');
    }
    
    buffer.writeln('VARIABLES_END');
    return buffer.toString();
  }

  /// 从导出字符串解析模板
  PromptTemplate _templateFromExportString(String data) {
    final lines = data.split('\n');
    
    if (lines.isEmpty || lines[0] != 'TEMPLATE_EXPORT_V1') {
      throw Exception('Invalid template export format');
    }

    String id = '';
    String name = '';
    String description = '';
    TemplateCategory category = TemplateCategory.custom;
    String version = '1.0.0';
    List<String> tags = [];
    String content = '';
    List<TemplateVariable> variables = [];

    bool inContent = false;
    bool inVariables = false;

    for (final line in lines) {
      if (line.startsWith('ID:')) {
        id = line.substring(3);
      } else if (line.startsWith('NAME:')) {
        name = line.substring(5);
      } else if (line.startsWith('DESCRIPTION:')) {
        description = line.substring(12);
      } else if (line.startsWith('CATEGORY:')) {
        final categoryName = line.substring(9);
        category = TemplateCategory.values.firstWhere(
          (c) => c.name == categoryName,
          orElse: () => TemplateCategory.custom,
        );
      } else if (line.startsWith('VERSION:')) {
        version = line.substring(8);
      } else if (line.startsWith('TAGS:')) {
        final tagsString = line.substring(5);
        tags = tagsString.isEmpty ? [] : tagsString.split(',');
      } else if (line == 'CONTENT_START') {
        inContent = true;
      } else if (line == 'CONTENT_END') {
        inContent = false;
      } else if (line == 'VARIABLES_START') {
        inVariables = true;
      } else if (line == 'VARIABLES_END') {
        inVariables = false;
      } else if (inContent) {
        if (content.isNotEmpty) content += '\n';
        content += line;
      } else if (inVariables && line.startsWith('VAR:')) {
        final parts = line.substring(4).split('|');
        if (parts.length >= 5) {
          variables.add(TemplateVariable(
            name: parts[0],
            displayName: parts[1],
            description: parts[2],
            type: VariableType.values.firstWhere(
              (t) => t.name == parts[3],
              orElse: () => VariableType.text,
            ),
            isRequired: parts[4] == 'true',
            defaultValue: parts.length > 5 && parts[5].isNotEmpty ? parts[5] : null,
            placeholder: parts.length > 6 && parts[6].isNotEmpty ? parts[6] : null,
          ));
        }
      }
    }

    return PromptTemplate(
      id: id,
      name: name,
      description: description,
      category: category,
      content: content,
      variables: variables,
      version: version,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      tags: tags,
    );
  }
}