import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/template.dart' hide TemplateValidationResult;
import '../../domain/repositories/template_repository.dart';
import '../../domain/usecases/manage_templates.dart';
import '../../data/repositories/template_repository_impl.dart';
import '../../data/datasources/template_local_datasource.dart';
import '../pages/template_manager_page.dart';

// 数据源提供者
final templateLocalDataSourceProvider = Provider<TemplateLocalDataSource>((ref) {
  // 这里应该从全局的Isar实例获取
  // 暂时返回一个占位符，实际使用时需要注入真实的Isar实例
  throw UnimplementedError('Isar instance not provided');
});

// 仓库提供者
final templateRepositoryProvider = Provider<TemplateRepository>((ref) {
  final dataSource = ref.watch(templateLocalDataSourceProvider);
  return TemplateRepositoryImpl(dataSource);
});

// 用例提供者
final manageTemplatesUseCaseProvider = Provider<ManageTemplatesUseCase>((ref) {
  final repository = ref.watch(templateRepositoryProvider);
  return ManageTemplatesUseCase(repository);
});

final validateTemplateUseCaseProvider = Provider<ValidateTemplateUseCase>((ref) {
  return ValidateTemplateUseCase();
});

// 模板管理状态提供者
final templateManagerProvider = StateNotifierProvider<TemplateManagerNotifier, TemplateManagerState>((ref) {
  final useCase = ref.watch(manageTemplatesUseCaseProvider);
  return TemplateManagerNotifier(useCase);
});

// 所有模板提供者
final allTemplatesProvider = FutureProvider<List<PromptTemplate>>((ref) async {
  final useCase = ref.watch(manageTemplatesUseCaseProvider);
  return await useCase.getAllTemplates();
});

// 内置模板提供者
final builtInTemplatesProvider = FutureProvider<List<PromptTemplate>>((ref) async {
  final repository = ref.watch(templateRepositoryProvider);
  return await repository.getBuiltInTemplates();
});

// 用户模板提供者
final userTemplatesProvider = FutureProvider<List<PromptTemplate>>((ref) async {
  final repository = ref.watch(templateRepositoryProvider);
  return await repository.getUserTemplates();
});

// 最常用模板提供者
final mostUsedTemplatesProvider = FutureProvider.family<List<PromptTemplate>, int>((ref, limit) async {
  final repository = ref.watch(templateRepositoryProvider);
  return await repository.getMostUsedTemplates(limit: limit);
});

// 最近使用模板提供者
final recentlyUsedTemplatesProvider = FutureProvider.family<List<PromptTemplate>, int>((ref, limit) async {
  final repository = ref.watch(templateRepositoryProvider);
  return await repository.getRecentlyUsedTemplates(limit: limit);
});

// 分类模板提供者
final categoryTemplatesProvider = FutureProvider.family<List<PromptTemplate>, TemplateCategory>((ref, category) async {
  final useCase = ref.watch(manageTemplatesUseCaseProvider);
  return await useCase.getTemplatesByCategory(category);
});

// 搜索模板提供者
final searchTemplatesProvider = FutureProvider.family<List<PromptTemplate>, String>((ref, query) async {
  final useCase = ref.watch(manageTemplatesUseCaseProvider);
  return await useCase.searchTemplates(query);
});

// 过滤模板提供者
final filteredTemplatesProvider = FutureProvider.family<List<PromptTemplate>, TemplateFilter>((ref, filter) async {
  final useCase = ref.watch(manageTemplatesUseCaseProvider);
  
  List<PromptTemplate> templates;
  
  if (filter.query.isNotEmpty) {
    templates = await useCase.searchTemplates(filter.query);
  } else if (filter.category != null) {
    templates = await useCase.getTemplatesByCategory(filter.category!);
  } else {
    templates = await useCase.getAllTemplates();
  }
  
  // 应用用户过滤器
  if (filter.userOnly) {
    templates = templates.where((template) => !template.isBuiltIn).toList();
  }
  
  return templates;
});

// 模板统计提供者
final templateStatisticsProvider = FutureProvider<TemplateStatistics>((ref) async {
  final useCase = ref.watch(manageTemplatesUseCaseProvider);
  return await useCase.getTemplateStatistics();
});

// 推荐模板提供者
final recommendedTemplatesProvider = FutureProvider.family<List<PromptTemplate>, RecommendationParams>((ref, params) async {
  final useCase = ref.watch(manageTemplatesUseCaseProvider);
  return await useCase.getRecommendedTemplates(
    category: params.category,
    tags: params.tags,
    limit: params.limit,
  );
});

// 单个模板提供者
final templateProvider = FutureProvider.family<PromptTemplate?, String>((ref, id) async {
  final repository = ref.watch(templateRepositoryProvider);
  return await repository.getTemplateById(id);
});

/// 模板管理状态
class TemplateManagerState {
  final bool isLoading;
  final String? error;
  final List<PromptTemplate> templates;
  final TemplateStatistics? statistics;

  const TemplateManagerState({
    this.isLoading = false,
    this.error,
    this.templates = const [],
    this.statistics,
  });

  TemplateManagerState copyWith({
    bool? isLoading,
    String? error,
    List<PromptTemplate>? templates,
    TemplateStatistics? statistics,
  }) {
    return TemplateManagerState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      templates: templates ?? this.templates,
      statistics: statistics ?? this.statistics,
    );
  }
}

/// 模板管理状态通知器
class TemplateManagerNotifier extends StateNotifier<TemplateManagerState> {
  final ManageTemplatesUseCase _useCase;

  TemplateManagerNotifier(this._useCase) : super(const TemplateManagerState());

  /// 加载所有模板
  Future<void> loadAllTemplates() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final templates = await _useCase.getAllTemplates();
      state = state.copyWith(
        isLoading: false,
        templates: templates,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 创建模板
  Future<void> createTemplate({
    required String name,
    required String description,
    required TemplateCategory category,
    required String content,
    required List<TemplateVariable> variables,
    List<String> tags = const [],
  }) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _useCase.createTemplate(
        name: name,
        description: description,
        category: category,
        content: content,
        variables: variables,
        tags: tags,
      );
      
      // 重新加载模板列表
      await loadAllTemplates();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// 更新模板
  Future<void> updateTemplate({
    required String id,
    String? name,
    String? description,
    TemplateCategory? category,
    String? content,
    List<TemplateVariable>? variables,
    List<String>? tags,
  }) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _useCase.updateTemplate(
        id: id,
        name: name,
        description: description,
        category: category,
        content: content,
        variables: variables,
        tags: tags,
      );
      
      // 重新加载模板列表
      await loadAllTemplates();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// 删除模板
  Future<void> deleteTemplate(String id) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _useCase.deleteTemplate(id);
      
      // 重新加载模板列表
      await loadAllTemplates();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// 复制模板
  Future<PromptTemplate> duplicateTemplate(String id, String newName) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final duplicatedTemplate = await _useCase.duplicateTemplate(id, newName);
      
      // 重新加载模板列表
      await loadAllTemplates();
      
      return duplicatedTemplate;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// 使用模板
  Future<String> useTemplate(String id, Map<String, dynamic> variableValues) async {
    try {
      return await _useCase.useTemplate(id, variableValues);
    } catch (e) {
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }

  /// 导出模板
  Future<String> exportTemplate(String id) async {
    try {
      return await _useCase.exportTemplate(id);
    } catch (e) {
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }

  /// 导入模板
  Future<PromptTemplate> importTemplate(String templateData) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final template = await _useCase.importTemplate(templateData);
      
      // 重新加载模板列表
      await loadAllTemplates();
      
      return template;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// 批量导入模板
  Future<List<PromptTemplate>> importTemplates(List<String> templatesData) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final templates = await _useCase.importTemplates(templatesData);
      
      // 重新加载模板列表
      await loadAllTemplates();
      
      return templates;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// 备份所有模板
  Future<String> backupAllTemplates() async {
    try {
      return 'TODO: 实现备份功能'; // TODO: 实现备份功能
    } catch (e) {
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }

  /// 清理未使用的模板
  Future<int> cleanupUnusedTemplates({int daysThreshold = 30}) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final count = await _useCase.cleanupUnusedTemplates(daysThreshold: daysThreshold);
      
      // 重新加载模板列表
      await loadAllTemplates();
      
      return count;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// 加载统计信息
  Future<void> loadStatistics() async {
    try {
      final statistics = await _useCase.getTemplateStatistics();
      state = state.copyWith(statistics: statistics);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// 推荐参数
class RecommendationParams {
  final TemplateCategory? category;
  final List<String>? tags;
  final int limit;

  const RecommendationParams({
    this.category,
    this.tags,
    this.limit = 5,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RecommendationParams &&
          runtimeType == other.runtimeType &&
          category == other.category &&
          _listEquals(tags, other.tags) &&
          limit == other.limit;

  @override
  int get hashCode => category.hashCode ^ tags.hashCode ^ limit.hashCode;

  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }
}

/// 模板验证提供者
final templateValidationProvider = FutureProvider.family<TemplateValidationResult, PromptTemplate>((ref, template) async {
  final useCase = ref.watch(validateTemplateUseCaseProvider);
  return await useCase.validateTemplate(template);
});

/// 变量值验证提供者
final variableValidationProvider = Provider.family<TemplateVariableValidationResult, VariableValidationParams>((ref, params) {
  final useCase = ref.watch(validateTemplateUseCaseProvider);
  return useCase.validateVariableValues(params.template, params.values);
});

/// 变量验证参数
class VariableValidationParams {
  final PromptTemplate template;
  final Map<String, dynamic> values;

  const VariableValidationParams({
    required this.template,
    required this.values,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VariableValidationParams &&
          runtimeType == other.runtimeType &&
          template == other.template &&
          _mapEquals(values, other.values);

  @override
  int get hashCode => template.hashCode ^ values.hashCode;

  bool _mapEquals<K, V>(Map<K, V>? a, Map<K, V>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (final key in a.keys) {
      if (!b.containsKey(key) || a[key] != b[key]) return false;
    }
    return true;
  }
}