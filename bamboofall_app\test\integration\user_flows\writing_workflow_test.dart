import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:bamboofall_app/features/project/domain/entities/project.dart';
import 'package:bamboofall_app/features/project/domain/repositories/project_repository.dart';
import 'package:bamboofall_app/features/writing/domain/entities/chapter.dart';
import 'package:bamboofall_app/features/writing/domain/repositories/chapter_repository.dart';
import 'package:bamboofall_app/features/project/presentation/pages/project_list_page.dart';
import 'package:bamboofall_app/features/writing/presentation/pages/writing_workspace.dart';

import 'writing_workflow_test.mocks.dart';

@GenerateMocks([ProjectRepository, ChapterRepository])
void main() {
  group('Writing Workflow Integration Tests', () {
    late MockProjectRepository mockProjectRepository;
    late MockChapterRepository mockChapterRepository;
    late ProviderContainer container;

    setUp(() {
      mockProjectRepository = MockProjectRepository();
      mockChapterRepository = MockChapterRepository();
      
      container = ProviderContainer(
        overrides: [
          // Override providers with mocks
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    testWidgets('Complete writing workflow: create project -> add chapters -> edit content', (WidgetTester tester) async {
      // Arrange - 准备测试数据
      final testProject = Project(
        id: 'test-project-1',
        name: '测试小说项目',
        description: '这是一个测试项目',
        type: ProjectType.novel,
        status: ProjectStatus.active,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: 'test-user',
        config: const ProjectConfig(),
        statistics: const ProjectStatistics(),
      );

      final testChapter = Chapter(
        id: 'test-chapter-1',
        projectId: 'test-project-1',
        title: '第一章：开始',
        content: '这是第一章的内容...',
        order: 1,
        status: ChapterStatus.draft,
        wordCount: 100,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Mock repository responses
      when(mockProjectRepository.getProjects()).thenAnswer((_) async => []);
      when(mockProjectRepository.createProject(any)).thenAnswer((_) async => testProject);
      when(mockProjectRepository.getProject('test-project-1')).thenAnswer((_) async => testProject);
      
      when(mockChapterRepository.getChaptersByProject('test-project-1')).thenAnswer((_) async => []);
      when(mockChapterRepository.createChapter(any)).thenAnswer((_) async => testChapter);
      when(mockChapterRepository.updateChapter(any)).thenAnswer((_) async => testChapter.copyWith(content: '更新后的内容'));

      // Act & Assert - 执行测试流程
      
      // 1. 启动应用，显示项目列表页面
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: MaterialApp(
            home: const ProjectListPage(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // 验证项目列表页面加载
      expect(find.text('项目管理'), findsOneWidget);
      expect(find.text('新建项目'), findsOneWidget);

      // 2. 点击新建项目按钮
      await tester.tap(find.text('新建项目'));
      await tester.pumpAndSettle();

      // 验证创建项目对话框出现
      expect(find.text('创建新项目'), findsOneWidget);

      // 3. 填写项目信息并创建
      await tester.enterText(find.byKey(const Key('project_title_field')), '测试小说项目');
      await tester.enterText(find.byKey(const Key('project_description_field')), '这是一个测试项目');
      
      await tester.tap(find.text('创建'));
      await tester.pumpAndSettle();

      // 验证项目创建成功
      verify(mockProjectRepository.createProject(any)).called(1);

      // 4. 进入写作工作台
      await tester.tap(find.text('测试小说项目'));
      await tester.pumpAndSettle();

      // 验证写作工作台页面加载
      expect(find.byType(WritingWorkspace), findsOneWidget);
      expect(find.text('章节大纲'), findsOneWidget);

      // 5. 创建新章节
      await tester.tap(find.byKey(const Key('add_chapter_button')));
      await tester.pumpAndSettle();

      // 填写章节信息
      await tester.enterText(find.byKey(const Key('chapter_title_field')), '第一章：开始');
      await tester.tap(find.text('创建章节'));
      await tester.pumpAndSettle();

      // 验证章节创建成功
      verify(mockChapterRepository.createChapter(any)).called(1);
      expect(find.text('第一章：开始'), findsOneWidget);

      // 6. 选择章节进行编辑
      await tester.tap(find.text('第一章：开始'));
      await tester.pumpAndSettle();

      // 验证编辑器加载
      expect(find.byKey(const Key('markdown_editor')), findsOneWidget);

      // 7. 编辑章节内容
      await tester.enterText(find.byKey(const Key('markdown_editor')), '这是第一章的内容...');
      await tester.pumpAndSettle();

      // 8. 保存内容
      await tester.tap(find.byKey(const Key('save_button')));
      await tester.pumpAndSettle();

      // 验证内容保存成功
      verify(mockChapterRepository.updateChapter(any)).called(1);

      // 9. 验证字数统计更新
      expect(find.textContaining('字数'), findsOneWidget);
      expect(find.textContaining('100'), findsOneWidget);
    });

    testWidgets('Chapter management workflow: create -> reorder -> delete', (WidgetTester tester) async {
      // Arrange
      final testProject = Project(
        id: 'test-project-2',
        name: '章节管理测试项目',
        description: '测试章节管理功能',
        type: ProjectType.novel,
        status: ProjectStatus.active,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: 'test-user',
        config: const ProjectConfig(),
        statistics: const ProjectStatistics(),
      );

      final chapters = [
        Chapter(
          id: 'chapter-1',
          projectId: 'test-project-2',
          title: '第一章',
          content: '第一章内容',
          order: 1,
          status: ChapterStatus.draft,
          wordCount: 50,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Chapter(
          id: 'chapter-2',
          projectId: 'test-project-2',
          title: '第二章',
          content: '第二章内容',
          order: 2,
          status: ChapterStatus.draft,
          wordCount: 75,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      // Mock responses
      when(mockProjectRepository.getProject('test-project-2')).thenAnswer((_) async => testProject);
      when(mockChapterRepository.getChaptersByProject('test-project-2')).thenAnswer((_) async => chapters);
      when(mockChapterRepository.reorderChapters(any, any)).thenAnswer((_) async => {});
      when(mockChapterRepository.deleteChapter('chapter-2')).thenAnswer((_) async => {});

      // Act & Assert
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: const MaterialApp(
            home: WritingWorkspace(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // 验证章节列表显示
      expect(find.text('第一章'), findsOneWidget);
      expect(find.text('第二章'), findsOneWidget);

      // 测试章节重排序
      await tester.longPress(find.text('第二章'));
      await tester.pumpAndSettle();

      // 拖拽到第一章之前
      await tester.drag(find.text('第二章'), const Offset(0, -100));
      await tester.pumpAndSettle();

      // 验证重排序调用
      verify(mockChapterRepository.reorderChapters('test-project-2', any)).called(1);

      // 测试章节删除
      await tester.longPress(find.text('第二章'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('删除'));
      await tester.pumpAndSettle();

      // 确认删除
      await tester.tap(find.text('确认'));
      await tester.pumpAndSettle();

      // 验证删除调用
      verify(mockChapterRepository.deleteChapter('chapter-2')).called(1);
    });

    testWidgets('Auto-save functionality during editing', (WidgetTester tester) async {
      // Arrange
      final testProject = Project(
        id: 'test-project-3',
        name: '自动保存测试项目',
        description: '测试自动保存功能',
        type: ProjectType.novel,
        status: ProjectStatus.active,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: 'test-user',
        config: const ProjectConfig(autoSaveInterval: 5), // 5秒自动保存
        statistics: const ProjectStatistics(),
      );

      final testChapter = Chapter(
        id: 'chapter-auto-save',
        projectId: 'test-project-3',
        title: '自动保存测试章节',
        content: '初始内容',
        order: 1,
        status: ChapterStatus.draft,
        wordCount: 10,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Mock responses
      when(mockProjectRepository.getProject('test-project-3')).thenAnswer((_) async => testProject);
      when(mockChapterRepository.getChaptersByProject('test-project-3')).thenAnswer((_) async => [testChapter]);
      when(mockChapterRepository.updateChapter(any)).thenAnswer((_) async => testChapter.copyWith(content: '更新的内容'));

      // Act & Assert
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: const MaterialApp(
            home: WritingWorkspace(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // 选择章节
      await tester.tap(find.text('自动保存测试章节'));
      await tester.pumpAndSettle();

      // 开始编辑
      await tester.enterText(find.byKey(const Key('markdown_editor')), '这是新的内容，应该会自动保存');
      await tester.pumpAndSettle();

      // 等待自动保存触发（模拟6秒后）
      await tester.pump(const Duration(seconds: 6));

      // 验证自动保存被调用
      verify(mockChapterRepository.updateChapter(any)).called(1);

      // 验证保存状态指示器
      expect(find.text('已保存'), findsOneWidget);
    });

    testWidgets('Error handling during chapter operations', (WidgetTester tester) async {
      // Arrange
      final testProject = Project(
        id: 'test-project-error',
        title: '错误处理测试项目',
        description: '测试错误处理',
        genre: ProjectGenre.mystery,
        status: ProjectStatus.active,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        settings: const ProjectSettings(),
      );

      // Mock error responses
      when(mockProjectRepository.getProject('test-project-error')).thenAnswer((_) async => testProject);
      when(mockChapterRepository.getChaptersByProject('test-project-error')).thenAnswer((_) async => []);
      when(mockChapterRepository.createChapter(any)).thenThrow(Exception('网络连接失败'));

      // Act & Assert
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: const MaterialApp(
            home: WritingWorkspace(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // 尝试创建章节
      await tester.tap(find.byKey(const Key('add_chapter_button')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('chapter_title_field')), '错误测试章节');
      await tester.tap(find.text('创建章节'));
      await tester.pumpAndSettle();

      // 验证错误消息显示
      expect(find.text('创建章节失败'), findsOneWidget);
      expect(find.text('网络连接失败'), findsOneWidget);

      // 验证错误处理后的状态
      expect(find.text('错误测试章节'), findsNothing); // 章节不应该被创建
    });
  });
}