import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:bamboofall_app/features/bible/domain/entities/character.dart';
import 'package:bamboofall_app/features/bible/domain/entities/location.dart';
import 'package:bamboofall_app/features/bible/domain/entities/story_bible.dart';
import 'package:bamboofall_app/features/bible/domain/entities/constraint_violation.dart';
import 'package:bamboofall_app/features/bible/domain/repositories/bible_repository.dart';
import 'package:bamboofall_app/features/bible/presentation/pages/bible_manager_page.dart';

import 'bible_management_workflow_test.mocks.dart';

@GenerateMocks([BibleRepository])
void main() {
  group('Bible Management Workflow Tests', () {
    late MockBibleRepository mockBibleRepository;
    late ProviderContainer container;

    setUp(() {
      mockBibleRepository = MockBibleRepository();
      
      container = ProviderContainer(
        overrides: [
          // Override providers with mocks
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    testWidgets('Complete character bible creation and management workflow', (WidgetTester tester) async {
      // Arrange - 准备测试数据
      final testCharacter = Character(
        id: 'char-1',
        projectId: 'project-1',
        name: '李明',
        type: CharacterType.protagonist,
        status: CharacterStatus.active,
        appearance: CharacterAppearance(
          age: 25,
          gender: 'male',
          height: '175cm',
          build: 'medium',
          hairColor: 'black',
          eyeColor: 'brown',
          distinctiveFeatures: ['戴眼镜'],
          clothing: '穿着简洁',
        ),
        personality: CharacterPersonality(
          traits: ['内向', '聪明', '善良'],
          motivations: ['成为技术专家', '找到真爱'],
          fears: ['失败', '孤独'],
          values: ['诚实', '努力'],
          flaws: ['过于完美主义'],
          strengths: ['逻辑思维强', '学习能力强'],
        ),
        background: '出生在小城市，大学毕业后来到大城市工作',
        occupation: '软件工程师',
        skills: ['编程', '数学', '逻辑思维'],
        relationships: {},
        notes: '主要角色，故事的核心人物',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      );

      // Mock repository responses
      when(mockBibleRepository.getCharacters('project-1')).thenAnswer((_) async => []);
      when(mockBibleRepository.createCharacter(any)).thenAnswer((_) async => testCharacter);
      when(mockBibleRepository.updateCharacter(any)).thenAnswer((_) async => testCharacter.copyWith(
        appearance: testCharacter.appearance.copyWith(age: 26),
      ));
      when(mockBibleRepository.validateConstraints('project-1')).thenAnswer((_) async => []);

      // Act & Assert - 执行测试流程
      
      // 1. 启动圣经管理页面
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: MaterialApp(
            home: BibleManagerPage(projectId: 'project-1'),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // 验证页面加载
      expect(find.text('圣经管理'), findsOneWidget);
      expect(find.text('角色'), findsOneWidget);
      expect(find.text('地点'), findsOneWidget);
      expect(find.text('故事'), findsOneWidget);

      // 2. 切换到角色标签页
      await tester.tap(find.text('角色'));
      await tester.pumpAndSettle();

      // 验证角色列表为空
      expect(find.text('暂无角色'), findsOneWidget);

      // 3. 创建新角色
      await tester.tap(find.byKey(const Key('add_character_button')));
      await tester.pumpAndSettle();

      // 验证角色创建对话框
      expect(find.text('创建角色'), findsOneWidget);

      // 填写基本信息
      await tester.enterText(find.byKey(const Key('character_name_field')), '李明');
      await tester.enterText(find.byKey(const Key('character_age_field')), '25');
      
      // 选择性别
      await tester.tap(find.byKey(const Key('gender_male_radio')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('character_occupation_field')), '软件工程师');

      // 4. 填写详细信息
      await tester.tap(find.text('详细信息'));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('character_personality_field')), '内向,聪明,善良');
      await tester.enterText(find.byKey(const Key('character_appearance_field')), '中等身材，戴眼镜，穿着简洁');
      await tester.enterText(find.byKey(const Key('character_background_field')), '出生在小城市，大学毕业后来到大城市工作');

      // 5. 添加技能和目标
      await tester.tap(find.text('技能目标'));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('character_skills_field')), '编程,数学,逻辑思维');
      await tester.enterText(find.byKey(const Key('character_goals_field')), '成为技术专家,找到真爱');
      await tester.enterText(find.byKey(const Key('character_fears_field')), '失败,孤独');

      // 6. 创建角色
      await tester.tap(find.text('创建'));
      await tester.pumpAndSettle();

      // 验证角色创建成功
      verify(mockBibleRepository.createCharacterBible(any)).called(1);
      expect(find.text('李明'), findsOneWidget);
      expect(find.text('25岁'), findsOneWidget);
      expect(find.text('软件工程师'), findsOneWidget);

      // 7. 编辑角色信息
      await tester.tap(find.byKey(const Key('edit_character_button')));
      await tester.pumpAndSettle();

      // 修改年龄
      await tester.enterText(find.byKey(const Key('character_age_field')), '26');
      
      await tester.tap(find.text('保存'));
      await tester.pumpAndSettle();

      // 验证更新成功
      verify(mockBibleRepository.updateCharacter(any)).called(1);
      expect(find.text('26岁'), findsOneWidget);
    });

    testWidgets('Location bible creation and relationship management', (WidgetTester tester) async {
      // Arrange
      final testLocation = Location(
        id: 'loc-1',
        projectId: 'project-1',
        name: '星光咖啡厅',
        type: LocationType.commercial,
        status: LocationStatus.active,
        description: '一家温馨的小咖啡厅，位于市中心的安静街道上',
        geography: LocationGeography(
          region: '市中心',
          coordinates: '39.9042, 116.4074',
          terrain: '平地',
          climate: '四季分明，春秋最为舒适',
          size: '二层小楼',
          layout: '一楼是咖啡厅，二楼是阅读区',
        ),
        culture: '文艺青年聚集地，经常有读书会和小型音乐会',
        history: '开业三年，由一对年轻夫妇经营',
        significance: '男女主角初次相遇的地方',
        connectedLocations: {},
        residents: [],
        visitingCharacters: [],
        events: [],
        atmosphere: '温馨、文艺、安静',
        notes: '重要场景，多个关键情节发生地',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      when(mockBibleRepository.getLocations('project-1')).thenAnswer((_) async => []);
      when(mockBibleRepository.createLocation(any)).thenAnswer((_) async => testLocation);
      when(mockBibleRepository.getCharacters('project-1')).thenAnswer((_) async => [
        Character(
          id: 'char-1',
          projectId: 'project-1',
          name: '李明',
          type: CharacterType.protagonist,
          status: CharacterStatus.active,
          appearance: CharacterAppearance(
            age: 25,
            gender: 'male',
            height: '175cm',
            build: 'medium',
            hairColor: 'black',
            eyeColor: 'brown',
            distinctiveFeatures: [],
            clothing: '',
          ),
          personality: CharacterPersonality(
            traits: [],
            motivations: [],
            fears: [],
            values: [],
            flaws: [],
            strengths: [],
          ),
          background: '',
          occupation: '软件工程师',
          skills: [],
          relationships: {},
          notes: '',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ]);

      // Act & Assert
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: MaterialApp(
            home: BibleManagerPage(projectId: 'project-1'),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // 切换到地点标签页
      await tester.tap(find.text('地点'));
      await tester.pumpAndSettle();

      // 创建新地点
      await tester.tap(find.byKey(const Key('add_location_button')));
      await tester.pumpAndSettle();

      // 填写地点信息
      await tester.enterText(find.byKey(const Key('location_name_field')), '星光咖啡厅');
      
      // 选择地点类型
      await tester.tap(find.byKey(const Key('location_type_dropdown')));
      await tester.pumpAndSettle();
      await tester.tap(find.text('商业场所'));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('location_description_field')), '一家温馨的小咖啡厅，位于市中心的安静街道上');

      // 设置地点关系
      await tester.tap(find.text('关系设置'));
      await tester.pumpAndSettle();

      // 添加访问角色
      await tester.tap(find.byKey(const Key('add_visiting_character_button')));
      await tester.pumpAndSettle();

      await tester.tap(find.text('李明'));
      await tester.pumpAndSettle();

      // 创建地点
      await tester.tap(find.text('创建'));
      await tester.pumpAndSettle();

      // 验证地点创建成功
      verify(mockBibleRepository.createLocationBible(any)).called(1);
      expect(find.text('星光咖啡厅'), findsOneWidget);
      expect(find.text('商业场所'), findsOneWidget);
    });

    testWidgets('Story bible creation and timeline management', (WidgetTester tester) async {
      // Arrange
      final testStoryBible = StoryBible(
        id: 'story-1',
        projectId: 'project-1',
        title: '相遇的开始',
        summary: '男女主角在咖啡厅的初次相遇',
        theme: '爱情的萌芽',
        genre: StoryGenre.romance,
        timeline: [
          TimelineEvent(
            id: 'event-1',
            title: '李明进入咖啡厅',
            description: '下雨天，李明躲进咖啡厅',
            timestamp: DateTime.now(),
            location: '星光咖啡厅',
            characters: ['李明'],
            significance: '故事开始',
          ),
        ],
        plotLines: [
          PlotLine(
            id: 'plot-line-1',
            title: '主线剧情',
            description: '男女主角的爱情故事',
            type: PlotLineType.main,
            status: PlotLineStatus.active,
            plotPoints: [
              PlotPoint(
                id: 'plot-1',
                title: '初次相遇',
                description: '李明和女主角的第一次见面',
                type: PlotPointType.incitingIncident,
                status: PlotPointStatus.planned,
                chapterReference: 'chapter-1',
                significance: '推动故事发展的关键事件',
                order: 1,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
            ],
            order: 1,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ],
        conflicts: [],
        resolutions: [],
        themes: ['爱情', '成长', '勇气'],
        symbolism: {},
        foreshadowing: [],
        notes: '整个故事的核心情节线',
        tags: ['主线', '爱情', '开端'],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      when(mockBibleRepository.getStoryBibles('project-1')).thenAnswer((_) async => []);
      when(mockBibleRepository.createStoryBible(any)).thenAnswer((_) async => testStoryBible);

      // Act & Assert
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: MaterialApp(
            home: BibleManagerPage(projectId: 'project-1'),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // 切换到故事标签页
      await tester.tap(find.text('故事'));
      await tester.pumpAndSettle();

      // 创建新故事线
      await tester.tap(find.byKey(const Key('add_story_button')));
      await tester.pumpAndSettle();

      // 填写故事基本信息
      await tester.enterText(find.byKey(const Key('story_title_field')), '相遇的开始');
      await tester.enterText(find.byKey(const Key('story_summary_field')), '男女主角在咖啡厅的初次相遇');
      await tester.enterText(find.byKey(const Key('story_theme_field')), '爱情的萌芽');

      // 选择故事类型
      await tester.tap(find.byKey(const Key('story_genre_dropdown')));
      await tester.pumpAndSettle();
      await tester.tap(find.text('爱情'));
      await tester.pumpAndSettle();

      // 添加时间线事件
      await tester.tap(find.text('时间线'));
      await tester.pumpAndSettle();

      await tester.tap(find.byKey(const Key('add_timeline_event_button')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('event_title_field')), '李明进入咖啡厅');
      await tester.enterText(find.byKey(const Key('event_description_field')), '下雨天，李明躲进咖啡厅');

      await tester.tap(find.text('添加事件'));
      await tester.pumpAndSettle();

      // 添加情节点
      await tester.tap(find.text('情节点'));
      await tester.pumpAndSettle();

      await tester.tap(find.byKey(const Key('add_plot_point_button')));
      await tester.pumpAndSettle();

      await tester.enterText(find.byKey(const Key('plot_title_field')), '初次相遇');
      await tester.enterText(find.byKey(const Key('plot_description_field')), '李明和女主角的第一次见面');

      // 选择情节点类型
      await tester.tap(find.byKey(const Key('plot_type_dropdown')));
      await tester.pumpAndSettle();
      await tester.tap(find.text('起始事件'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('添加情节点'));
      await tester.pumpAndSettle();

      // 创建故事线
      await tester.tap(find.text('创建'));
      await tester.pumpAndSettle();

      // 验证故事线创建成功
      verify(mockBibleRepository.createStoryBible(any)).called(1);
      expect(find.text('相遇的开始'), findsOneWidget);
      expect(find.text('爱情'), findsOneWidget);
    });

    testWidgets('Constraint validation workflow', (WidgetTester tester) async {
      // Arrange
      final constraintViolations = [
        ConstraintViolation(
          id: 'violation-1',
          type: ViolationType.duplicateName,
          severity: ViolationSeverity.error,
          message: '发现重复的角色名称: 李明',
          entityType: 'character',
          entityId: 'char-2',
          conflictingEntityId: 'char-1',
          suggestions: ['将其中一个角色重命名', '合并重复角色'],
          createdAt: DateTime.now(),
        ),
        ConstraintViolation(
          id: 'violation-2',
          type: ViolationType.invalidReference,
          severity: ViolationSeverity.warning,
          message: '角色引用了不存在的地点: 神秘森林',
          entityType: 'character',
          entityId: 'char-1',
          conflictingEntityId: null,
          suggestions: ['创建对应的地点', '移除无效引用'],
          createdAt: DateTime.now(),
        ),
      ];

      when(mockBibleRepository.validateConstraints('project-1')).thenAnswer((_) async => constraintViolations);
      when(mockBibleRepository.getCharacterBibles('project-1')).thenAnswer((_) async => []);

      // Act & Assert
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: MaterialApp(
            home: BibleManagerPage(projectId: 'project-1'),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // 点击约束验证按钮
      await tester.tap(find.byKey(const Key('validate_constraints_button')));
      await tester.pumpAndSettle();

      // 验证约束检查执行
      verify(mockBibleRepository.validateConstraints('project-1')).called(1);

      // 验证违规显示
      expect(find.text('约束验证结果'), findsOneWidget);
      expect(find.text('发现 2 个问题'), findsOneWidget);

      // 验证错误级别违规
      expect(find.text('发现重复的角色名称: 李明'), findsOneWidget);
      expect(find.byIcon(Icons.error), findsOneWidget);

      // 验证警告级别违规
      expect(find.text('角色引用了不存在的地点: 神秘森林'), findsOneWidget);
      expect(find.byIcon(Icons.warning), findsOneWidget);

      // 查看建议
      await tester.tap(find.text('查看建议'));
      await tester.pumpAndSettle();

      expect(find.text('将其中一个角色重命名'), findsOneWidget);
      expect(find.text('合并重复角色'), findsOneWidget);

      // 应用建议
      await tester.tap(find.text('应用建议'));
      await tester.pumpAndSettle();

      // 验证建议应用确认
      expect(find.text('确认应用建议?'), findsOneWidget);
      
      await tester.tap(find.text('确认'));
      await tester.pumpAndSettle();
    });

    testWidgets('Bible search and filtering workflow', (WidgetTester tester) async {
      // Arrange
      final characters = [
        CharacterBible(
          id: 'char-1',
          projectId: 'project-1',
          name: '李明',
          age: 25,
          gender: Gender.male,
          occupation: '软件工程师',
          personality: ['内向'],
          appearance: '',
          background: '',
          relationships: {},
          skills: [],
          goals: [],
          fears: [],
          secrets: [],
          characterArc: '',
          notes: '',
          tags: ['主角', '程序员'],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        CharacterBible(
          id: 'char-2',
          projectId: 'project-1',
          name: '王小美',
          age: 23,
          gender: Gender.female,
          occupation: '设计师',
          personality: ['外向'],
          appearance: '',
          background: '',
          relationships: {},
          skills: [],
          goals: [],
          fears: [],
          secrets: [],
          characterArc: '',
          notes: '',
          tags: ['女主角', '设计师'],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      when(mockBibleRepository.getCharacterBibles('project-1')).thenAnswer((_) async => characters);
      when(mockBibleRepository.searchCharacters('project-1', '李明')).thenAnswer((_) async => [characters[0]]);

      // Act & Assert
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: MaterialApp(
            home: BibleManagerPage(projectId: 'project-1'),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // 切换到角色标签页
      await tester.tap(find.text('角色'));
      await tester.pumpAndSettle();

      // 验证所有角色显示
      expect(find.text('李明'), findsOneWidget);
      expect(find.text('王小美'), findsOneWidget);

      // 使用搜索功能
      await tester.enterText(find.byKey(const Key('search_field')), '李明');
      await tester.pumpAndSettle();

      // 验证搜索结果
      verify(mockBibleRepository.searchCharacters('project-1', '李明')).called(1);
      expect(find.text('李明'), findsOneWidget);
      expect(find.text('王小美'), findsNothing);

      // 清除搜索
      await tester.tap(find.byKey(const Key('clear_search_button')));
      await tester.pumpAndSettle();

      // 验证所有角色重新显示
      expect(find.text('李明'), findsOneWidget);
      expect(find.text('王小美'), findsOneWidget);

      // 使用标签过滤
      await tester.tap(find.byKey(const Key('filter_button')));
      await tester.pumpAndSettle();

      await tester.tap(find.text('主角'));
      await tester.pumpAndSettle();

      // 验证过滤结果
      expect(find.text('李明'), findsOneWidget);
      expect(find.text('王小美'), findsNothing);
    });
  });
}