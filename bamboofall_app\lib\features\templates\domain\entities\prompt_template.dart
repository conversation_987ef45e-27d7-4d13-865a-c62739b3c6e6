import 'package:equatable/equatable.dart';
// import 'package:json_annotation/json_annotation.dart';

// part 'prompt_template.g.dart';

/// 提示词模板实体
class PromptTemplate extends Equatable {
  /// 模板ID
  final String id;
  
  /// 模板名称
  final String name;
  
  /// 模板描述
  final String description;
  
  /// 模板分类
  final TemplateCategory category;
  
  /// 模板内容（包含变量占位符）
  final String content;
  
  /// 模板变量定义
  final List<TemplateVariable> variables;
  
  /// 模板版本
  final String version;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 更新时间
  final DateTime updatedAt;
  
  /// 是否为系统内置模板
  final bool isBuiltIn;
  
  /// 使用次数
  final int usageCount;
  
  /// 模板标签
  final List<String> tags;

  const PromptTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.content,
    required this.variables,
    required this.version,
    required this.createdAt,
    required this.updatedAt,
    this.isBuiltIn = false,
    this.usageCount = 0,
    this.tags = const [],
  });

  /// 从JSON创建PromptTemplate
  factory PromptTemplate.fromJson(Map<String, dynamic> json) {
    return PromptTemplate(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      category: TemplateCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => TemplateCategory.custom,
      ),
      content: json['content'] as String,
      variables: (json['variables'] as List<dynamic>?)
          ?.map((v) => TemplateVariable(
                name: v['name'],
                displayName: v['displayName'],
                description: v['description'],
                type: VariableType.values.firstWhere(
                  (e) => e.name == v['type'],
                  orElse: () => VariableType.text,
                ),
                isRequired: v['isRequired'] ?? false,
                defaultValue: v['defaultValue'],
              ))
          .toList() ?? [],
      version: json['version'] as String? ?? '1.0',
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isBuiltIn: json['isBuiltIn'] as bool? ?? false,
      usageCount: json['usageCount'] as int? ?? 0,
      tags: (json['tags'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category.name,
      'content': content,
      'variables': variables.map((v) => {
        'name': v.name,
        'displayName': v.displayName,
        'description': v.description,
        'type': v.type.name,
        'isRequired': v.isRequired,
        'defaultValue': v.defaultValue,
      }).toList(),
      'version': version,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isBuiltIn': isBuiltIn,
      'usageCount': usageCount,
      'tags': tags,
    };
  }

  /// 复制并更新模板
  PromptTemplate copyWith({
    String? id,
    String? name,
    String? description,
    TemplateCategory? category,
    String? content,
    List<TemplateVariable>? variables,
    String? version,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isBuiltIn,
    int? usageCount,
    List<String>? tags,
  }) {
    return PromptTemplate(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      content: content ?? this.content,
      variables: variables ?? this.variables,
      version: version ?? this.version,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isBuiltIn: isBuiltIn ?? this.isBuiltIn,
      usageCount: usageCount ?? this.usageCount,
      tags: tags ?? this.tags,
    );
  }

  /// 渲染模板内容（替换变量）
  String render(Map<String, dynamic> variableValues) {
    String result = content;
    
    for (final variable in variables) {
      final value = variableValues[variable.name];
      if (value != null) {
        final placeholder = '{{${variable.name}}}';
        result = result.replaceAll(placeholder, value.toString());
      }
    }
    
    return result;
  }

  /// 验证模板内容
  bool isValid() {
    // 检查必需变量是否都有占位符
    for (final variable in variables) {
      if (variable.isRequired) {
        final placeholder = '{{${variable.name}}}';
        if (!content.contains(placeholder)) {
          return false;
        }
      }
    }
    return true;
  }

  /// 获取模板中使用的变量名列表
  List<String> getUsedVariables() {
    final regex = RegExp(r'\{\{(\w+)\}\}');
    final matches = regex.allMatches(content);
    return matches.map((match) => match.group(1)!).toList();
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        category,
        content,
        variables,
        version,
        createdAt,
        updatedAt,
        isBuiltIn,
        usageCount,
        tags,
      ];
}

/// 模板分类枚举
enum TemplateCategory {
  /// 角色设定
  character,
  /// 情节发展
  plot,
  /// 对话生成
  dialogue,
  /// 场景描述
  scene,
  /// 世界观构建
  worldBuilding,
  /// 文本润色
  polish,
  /// 创意激发
  inspiration,
  /// 写作通用
  writing,
  /// 自定义
  custom,
}

/// 模板分类扩展
extension TemplateCategoryExtension on TemplateCategory {
  String get displayName {
    switch (this) {
      case TemplateCategory.character:
        return '角色设定';
      case TemplateCategory.plot:
        return '情节发展';
      case TemplateCategory.dialogue:
        return '对话生成';
      case TemplateCategory.scene:
        return '场景描述';
      case TemplateCategory.worldBuilding:
        return '世界观构建';
      case TemplateCategory.polish:
        return '文本润色';
      case TemplateCategory.inspiration:
        return '创意激发';
      case TemplateCategory.writing:
        return '写作通用';
      case TemplateCategory.custom:
        return '自定义';
    }
  }

  String get description {
    switch (this) {
      case TemplateCategory.character:
        return '用于创建和发展角色的模板';
      case TemplateCategory.plot:
        return '用于推进故事情节的模板';
      case TemplateCategory.dialogue:
        return '用于生成角色对话的模板';
      case TemplateCategory.scene:
        return '用于描述场景和环境的模板';
      case TemplateCategory.worldBuilding:
        return '用于构建世界观设定的模板';
      case TemplateCategory.polish:
        return '用于润色和改进文本的模板';
      case TemplateCategory.inspiration:
        return '用于激发创作灵感的模板';
      case TemplateCategory.writing:
        return '用于通用写作的模板';
      case TemplateCategory.custom:
        return '用户自定义的模板';
    }
  }
}

/// 模板变量定义
// @JsonSerializable()
class TemplateVariable extends Equatable {
  /// 变量名
  final String name;
  
  /// 变量显示名称
  final String displayName;
  
  /// 变量描述
  final String description;
  
  /// 变量类型
  final VariableType type;
  
  /// 是否必需
  final bool isRequired;
  
  /// 默认值
  final String? defaultValue;
  
  /// 可选值列表（用于选择类型）
  final List<String>? options;
  
  /// 最小长度（用于文本类型）
  final int? minLength;
  
  /// 最大长度（用于文本类型）
  final int? maxLength;
  
  /// 占位符文本
  final String? placeholder;

  const TemplateVariable({
    required this.name,
    required this.displayName,
    required this.description,
    required this.type,
    this.isRequired = false,
    this.defaultValue,
    this.options,
    this.minLength,
    this.maxLength,
    this.placeholder,
  });

  // factory TemplateVariable.fromJson(Map<String, dynamic> json) => _$TemplateVariableFromJson(json);
  // Map<String, dynamic> toJson() => _$TemplateVariableToJson(this);

  @override
  List<Object?> get props => [
        name,
        displayName,
        description,
        type,
        isRequired,
        defaultValue,
        options,
        minLength,
        maxLength,
        placeholder,
      ];
}

/// 变量类型枚举
enum VariableType {
  /// 短文本
  text,
  /// 长文本
  longText,
  /// 数字
  number,
  /// 选择
  select,
  /// 多选
  multiSelect,
  /// 布尔值
  boolean,
  /// 日期
  date,
}

/// 变量类型扩展
extension VariableTypeExtension on VariableType {
  String get displayName {
    switch (this) {
      case VariableType.text:
        return '短文本';
      case VariableType.longText:
        return '长文本';
      case VariableType.number:
        return '数字';
      case VariableType.select:
        return '单选';
      case VariableType.multiSelect:
        return '多选';
      case VariableType.boolean:
        return '是/否';
      case VariableType.date:
        return '日期';
    }
  }
}