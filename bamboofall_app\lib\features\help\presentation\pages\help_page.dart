import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

/// 帮助页面 - 提供应用内帮助和用户指南
class HelpPage extends ConsumerWidget {
  const HelpPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return fluent.ScaffoldPage(
      header: const fluent.PageHeader(
        title: Text('帮助与支持'),
      ),
      content: const _HelpContent(),
    );
  }
}

class _HelpContent extends StatefulWidget {
  const _HelpContent();

  @override
  State<_HelpContent> createState() => _HelpContentState();
}

class _HelpContentState extends State<_HelpContent> {
  int selectedIndex = 0;

  final List<HelpSection> helpSections = [
    HelpSection(
      title: '快速开始',
      icon: fluent.FluentIcons.rocket,
      content: _QuickStartContent(),
    ),
    HelpSection(
      title: 'AI功能',
      icon: fluent.FluentIcons.robot,
      content: _AIFeaturesContent(),
    ),
    HelpSection(
      title: '项目管理',
      icon: fluent.FluentIcons.folder_open,
      content: _ProjectManagementContent(),
    ),
    HelpSection(
      title: '设置配置',
      icon: fluent.FluentIcons.settings,
      content: _SettingsContent(),
    ),
    HelpSection(
      title: '常见问题',
      icon: fluent.FluentIcons.help,
      content: _FAQContent(),
    ),
    HelpSection(
      title: '关于应用',
      icon: fluent.FluentIcons.info,
      content: _AboutContent(),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // 左侧导航
        SizedBox(
          width: 250,
          child: fluent.NavigationView(
            pane: fluent.NavigationPane(
              selected: selectedIndex,
              onChanged: (index) => setState(() => selectedIndex = index),
              displayMode: fluent.PaneDisplayMode.open,
              items: helpSections.asMap().entries.map<fluent.NavigationPaneItem>((entry) {
                final index = entry.key;
                final section = entry.value;
                return fluent.PaneItem(
                  key: ValueKey(index),
                  icon: Icon(section.icon),
                  title: Text(section.title),
                  body: const SizedBox.shrink(),
                );
              }).toList(),
            ),
          ),
        ),
        
        // 右侧内容
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: helpSections[selectedIndex].content,
          ),
        ),
      ],
    );
  }
}

class HelpSection {
  final String title;
  final IconData icon;
  final Widget content;

  HelpSection({
    required this.title,
    required this.icon,
    required this.content,
  });
}

// 快速开始内容
class _QuickStartContent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '欢迎使用笔落（BambooFall）',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          const Text(
            '笔落是一款AI辅助小说创作应用，帮助您更高效地进行创作。',
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 24),
          
          _buildStepCard(
            '1. 创建项目',
            '点击"新建项目"按钮，输入项目名称和简介，开始您的创作之旅。',
            fluent.FluentIcons.add_to,
          ),
          
          _buildStepCard(
            '2. 配置AI',
            '在设置中配置AI模型API密钥，解锁强大的AI辅助功能。',
            fluent.FluentIcons.robot,
          ),
          
          _buildStepCard(
            '3. 开始创作',
            '在编辑器中输入文字，使用AI功能获得创作灵感和建议。',
            fluent.FluentIcons.edit,
          ),
          
          _buildStepCard(
            '4. 管理内容',
            '使用章节管理功能组织您的作品，跟踪创作进度。',
            fluent.FluentIcons.folder_open,
          ),
        ],
      ),
    );
  }

  Widget _buildStepCard(String title, String description, IconData icon) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, size: 32, color: Colors.blue),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// AI功能内容
class _AIFeaturesContent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'AI功能指南',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          _buildFeatureSection(
            '智能续写',
            '选中文本后，AI会根据上下文为您续写故事情节。',
            [
              '选择要续写的位置',
              '点击AI续写按钮',
              '选择合适的续写内容',
              '应用到您的作品中',
            ],
          ),
          
          _buildFeatureSection(
            '角色对话',
            'AI可以为不同角色生成符合性格的对话内容。',
            [
              '描述角色性格特点',
              '设置对话场景',
              '生成角色对话',
              '调整和完善对话',
            ],
          ),
          
          _buildFeatureSection(
            '情节建议',
            '获得创意情节和转折点建议，丰富您的故事。',
            [
              '输入当前情节概要',
              '选择情节类型',
              '获取AI建议',
              '融入到故事中',
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureSection(String title, String description, List<String> steps) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 12),
          ...steps.asMap().entries.map((entry) {
            final index = entry.key + 1;
            final step = entry.value;
            return Padding(
              padding: const EdgeInsets.only(left: 16, bottom: 4),
              child: Text('$index. $step'),
            );
          }),
        ],
      ),
    );
  }
}

// 项目管理内容
class _ProjectManagementContent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '项目管理',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          _buildManagementSection(
            '创建项目',
            '如何创建和设置新的小说项目',
            [
              '点击主页的"新建项目"按钮',
              '输入项目名称和简介',
              '选择项目类型和分类',
              '设置项目目标（字数、章节数等）',
              '确认创建项目',
            ],
          ),
          
          _buildManagementSection(
            '章节管理',
            '组织和管理您的小说章节结构',
            [
              '在项目中创建新章节',
              '设置章节标题和概要',
              '调整章节顺序',
              '设置章节状态（草稿、完成等）',
              '查看章节统计信息',
            ],
          ),
          
          _buildManagementSection(
            '进度跟踪',
            '监控您的创作进度和统计数据',
            [
              '查看总字数和目标进度',
              '统计每日创作字数',
              '查看创作时间记录',
              '设置创作提醒',
              '导出进度报告',
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildManagementSection(String title, String description, List<String> steps) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 12),
          ...steps.asMap().entries.map((entry) {
            final index = entry.key + 1;
            final step = entry.value;
            return Padding(
              padding: const EdgeInsets.only(left: 16, bottom: 4),
              child: Text('$index. $step'),
            );
          }),
        ],
      ),
    );
  }
}

// 设置配置内容
class _SettingsContent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '设置配置',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          _buildSettingSection(
            'AI配置',
            '配置AI模型和API设置',
            [
              '选择要使用的AI模型',
              '输入API密钥',
              '调整AI参数（创造性、长度等）',
              '测试AI连接',
              '保存配置',
            ],
          ),
          
          _buildSettingSection(
            '界面设置',
            '个性化您的应用界面',
            [
              '选择主题（亮色/暗色）',
              '调整字体大小',
              '设置界面语言',
              '配置快捷键',
              '自定义工具栏',
            ],
          ),
          
          _buildSettingSection(
            '数据管理',
            '管理您的创作数据和备份',
            [
              '设置自动保存间隔',
              '配置备份策略',
              '导入/导出数据',
              '清理缓存文件',
              '重置应用设置',
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSettingSection(String title, String description, List<String> steps) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 12),
          ...steps.asMap().entries.map((entry) {
            final index = entry.key + 1;
            final step = entry.value;
            return Padding(
              padding: const EdgeInsets.only(left: 16, bottom: 4),
              child: Text('$index. $step'),
            );
          }),
        ],
      ),
    );
  }
}

// 常见问题内容
class _FAQContent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '常见问题',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          _buildFAQItem(
            '应用无法启动怎么办？',
            '请检查系统是否满足最低要求，确保已安装必要的运行时库。Windows用户可能需要安装Visual C++ Redistributable。',
          ),
          
          _buildFAQItem(
            'AI功能不可用？',
            '请检查网络连接和API密钥配置。确保API密钥有效且有足够的使用额度。',
          ),
          
          _buildFAQItem(
            '数据会丢失吗？',
            '应用采用本地存储，数据完全保存在您的设备上。建议定期备份重要创作内容。',
          ),
          
          _buildFAQItem(
            '支持哪些文件格式？',
            '目前支持导入/导出TXT、DOCX、PDF等常见格式，更多格式支持正在开发中。',
          ),
          
          _buildFAQItem(
            '如何提高AI生成质量？',
            '提供更详细的上下文信息，调整AI参数设置，选择合适的AI模型。',
          ),
          
          _buildFAQItem(
            '应用占用内存过多？',
            '关闭不必要的功能，清理缓存文件，重启应用。如果问题持续，请联系技术支持。',
          ),
        ],
      ),
    );
  }

  Widget _buildFAQItem(String question, String answer) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: fluent.Expander(
        header: Text(
          question,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        content: Padding(
          padding: const EdgeInsets.all(12),
          child: Text(
            answer,
            style: const TextStyle(fontSize: 14),
          ),
        ),
      ),
    );
  }
}

// 关于应用内容
class _AboutContent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '关于笔落',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '笔落（BambooFall）',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                const Text('版本: 1.0.0'),
                const Text('构建: 2025-09-27'),
                const SizedBox(height: 16),
                const Text(
                  '笔落是一款专为小说创作者设计的AI辅助写作应用。它集成了多种先进的AI模型，为作者提供灵感启发、情节构思、角色塑造等全方位的创作支持。',
                ),
                const SizedBox(height: 16),
                const Text(
                  '技术栈:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const Text('• Flutter 3.24+'),
                const Text('• Dart 3.0+'),
                const Text('• Riverpod 2.0+'),
                const Text('• Isar Database'),
                const SizedBox(height: 16),
                const Text(
                  '开源许可:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const Text('本项目基于多个开源技术构建，遵循相应的开源许可证。'),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          const Text(
            '联系我们',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text('如果您有任何问题或建议，欢迎联系我们：'),
          const SizedBox(height: 8),
          const Text('• 问题反馈：请详细描述问题现象'),
          const Text('• 功能建议：欢迎提出改进建议'),
          const Text('• 技术交流：与其他用户交流心得'),
          
          const SizedBox(height: 24),
          
          const Text(
            '版权声明',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text('© 2025 BambooFall Development Team. All rights reserved.'),
          const SizedBox(height: 8),
          const Text(
            '本软件按"现状"提供，不提供任何明示或暗示的保证。使用本软件的风险由用户自行承担。',
            style: TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }
}