// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ai_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

AIModel _$AIModelFromJson(Map<String, dynamic> json) {
  return _AIModel.fromJson(json);
}

/// @nodoc
mixin _$AIModel {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get displayName => throw _privateConstructorUsedError;
  AIProvider get provider => throw _privateConstructorUsedError;
  AIModelType get type => throw _privateConstructorUsedError;
  AIModelStatus get status => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get version => throw _privateConstructorUsedError;
  int get maxTokens => throw _privateConstructorUsedError;
  double get defaultTemperature => throw _privateConstructorUsedError;
  double get defaultTopP => throw _privateConstructorUsedError;
  double get defaultFrequencyPenalty => throw _privateConstructorUsedError;
  double get defaultPresencePenalty => throw _privateConstructorUsedError;
  List<String> get supportedFeatures => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this AIModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AIModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AIModelCopyWith<AIModel> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AIModelCopyWith<$Res> {
  factory $AIModelCopyWith(AIModel value, $Res Function(AIModel) then) =
      _$AIModelCopyWithImpl<$Res, AIModel>;
  @useResult
  $Res call({
    String id,
    String name,
    String displayName,
    AIProvider provider,
    AIModelType type,
    AIModelStatus status,
    String? description,
    String? version,
    int maxTokens,
    double defaultTemperature,
    double defaultTopP,
    double defaultFrequencyPenalty,
    double defaultPresencePenalty,
    List<String> supportedFeatures,
    Map<String, dynamic>? metadata,
    bool isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$AIModelCopyWithImpl<$Res, $Val extends AIModel>
    implements $AIModelCopyWith<$Res> {
  _$AIModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AIModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? displayName = null,
    Object? provider = null,
    Object? type = null,
    Object? status = null,
    Object? description = freezed,
    Object? version = freezed,
    Object? maxTokens = null,
    Object? defaultTemperature = null,
    Object? defaultTopP = null,
    Object? defaultFrequencyPenalty = null,
    Object? defaultPresencePenalty = null,
    Object? supportedFeatures = null,
    Object? metadata = freezed,
    Object? isActive = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            displayName: null == displayName
                ? _value.displayName
                : displayName // ignore: cast_nullable_to_non_nullable
                      as String,
            provider: null == provider
                ? _value.provider
                : provider // ignore: cast_nullable_to_non_nullable
                      as AIProvider,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as AIModelType,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as AIModelStatus,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
            version: freezed == version
                ? _value.version
                : version // ignore: cast_nullable_to_non_nullable
                      as String?,
            maxTokens: null == maxTokens
                ? _value.maxTokens
                : maxTokens // ignore: cast_nullable_to_non_nullable
                      as int,
            defaultTemperature: null == defaultTemperature
                ? _value.defaultTemperature
                : defaultTemperature // ignore: cast_nullable_to_non_nullable
                      as double,
            defaultTopP: null == defaultTopP
                ? _value.defaultTopP
                : defaultTopP // ignore: cast_nullable_to_non_nullable
                      as double,
            defaultFrequencyPenalty: null == defaultFrequencyPenalty
                ? _value.defaultFrequencyPenalty
                : defaultFrequencyPenalty // ignore: cast_nullable_to_non_nullable
                      as double,
            defaultPresencePenalty: null == defaultPresencePenalty
                ? _value.defaultPresencePenalty
                : defaultPresencePenalty // ignore: cast_nullable_to_non_nullable
                      as double,
            supportedFeatures: null == supportedFeatures
                ? _value.supportedFeatures
                : supportedFeatures // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            isActive: null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                      as bool,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AIModelImplCopyWith<$Res> implements $AIModelCopyWith<$Res> {
  factory _$$AIModelImplCopyWith(
    _$AIModelImpl value,
    $Res Function(_$AIModelImpl) then,
  ) = __$$AIModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String displayName,
    AIProvider provider,
    AIModelType type,
    AIModelStatus status,
    String? description,
    String? version,
    int maxTokens,
    double defaultTemperature,
    double defaultTopP,
    double defaultFrequencyPenalty,
    double defaultPresencePenalty,
    List<String> supportedFeatures,
    Map<String, dynamic>? metadata,
    bool isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$AIModelImplCopyWithImpl<$Res>
    extends _$AIModelCopyWithImpl<$Res, _$AIModelImpl>
    implements _$$AIModelImplCopyWith<$Res> {
  __$$AIModelImplCopyWithImpl(
    _$AIModelImpl _value,
    $Res Function(_$AIModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AIModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? displayName = null,
    Object? provider = null,
    Object? type = null,
    Object? status = null,
    Object? description = freezed,
    Object? version = freezed,
    Object? maxTokens = null,
    Object? defaultTemperature = null,
    Object? defaultTopP = null,
    Object? defaultFrequencyPenalty = null,
    Object? defaultPresencePenalty = null,
    Object? supportedFeatures = null,
    Object? metadata = freezed,
    Object? isActive = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$AIModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        displayName: null == displayName
            ? _value.displayName
            : displayName // ignore: cast_nullable_to_non_nullable
                  as String,
        provider: null == provider
            ? _value.provider
            : provider // ignore: cast_nullable_to_non_nullable
                  as AIProvider,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as AIModelType,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as AIModelStatus,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
        version: freezed == version
            ? _value.version
            : version // ignore: cast_nullable_to_non_nullable
                  as String?,
        maxTokens: null == maxTokens
            ? _value.maxTokens
            : maxTokens // ignore: cast_nullable_to_non_nullable
                  as int,
        defaultTemperature: null == defaultTemperature
            ? _value.defaultTemperature
            : defaultTemperature // ignore: cast_nullable_to_non_nullable
                  as double,
        defaultTopP: null == defaultTopP
            ? _value.defaultTopP
            : defaultTopP // ignore: cast_nullable_to_non_nullable
                  as double,
        defaultFrequencyPenalty: null == defaultFrequencyPenalty
            ? _value.defaultFrequencyPenalty
            : defaultFrequencyPenalty // ignore: cast_nullable_to_non_nullable
                  as double,
        defaultPresencePenalty: null == defaultPresencePenalty
            ? _value.defaultPresencePenalty
            : defaultPresencePenalty // ignore: cast_nullable_to_non_nullable
                  as double,
        supportedFeatures: null == supportedFeatures
            ? _value._supportedFeatures
            : supportedFeatures // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        isActive: null == isActive
            ? _value.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AIModelImpl implements _AIModel {
  const _$AIModelImpl({
    required this.id,
    required this.name,
    required this.displayName,
    required this.provider,
    required this.type,
    required this.status,
    this.description,
    this.version,
    this.maxTokens = 4096,
    this.defaultTemperature = 0.7,
    this.defaultTopP = 1.0,
    this.defaultFrequencyPenalty = 1.0,
    this.defaultPresencePenalty = 1.0,
    final List<String> supportedFeatures = const [],
    final Map<String, dynamic>? metadata,
    this.isActive = false,
    this.createdAt,
    this.updatedAt,
  }) : _supportedFeatures = supportedFeatures,
       _metadata = metadata;

  factory _$AIModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AIModelImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String displayName;
  @override
  final AIProvider provider;
  @override
  final AIModelType type;
  @override
  final AIModelStatus status;
  @override
  final String? description;
  @override
  final String? version;
  @override
  @JsonKey()
  final int maxTokens;
  @override
  @JsonKey()
  final double defaultTemperature;
  @override
  @JsonKey()
  final double defaultTopP;
  @override
  @JsonKey()
  final double defaultFrequencyPenalty;
  @override
  @JsonKey()
  final double defaultPresencePenalty;
  final List<String> _supportedFeatures;
  @override
  @JsonKey()
  List<String> get supportedFeatures {
    if (_supportedFeatures is EqualUnmodifiableListView)
      return _supportedFeatures;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_supportedFeatures);
  }

  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  @JsonKey()
  final bool isActive;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'AIModel(id: $id, name: $name, displayName: $displayName, provider: $provider, type: $type, status: $status, description: $description, version: $version, maxTokens: $maxTokens, defaultTemperature: $defaultTemperature, defaultTopP: $defaultTopP, defaultFrequencyPenalty: $defaultFrequencyPenalty, defaultPresencePenalty: $defaultPresencePenalty, supportedFeatures: $supportedFeatures, metadata: $metadata, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AIModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.provider, provider) ||
                other.provider == provider) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.maxTokens, maxTokens) ||
                other.maxTokens == maxTokens) &&
            (identical(other.defaultTemperature, defaultTemperature) ||
                other.defaultTemperature == defaultTemperature) &&
            (identical(other.defaultTopP, defaultTopP) ||
                other.defaultTopP == defaultTopP) &&
            (identical(
                  other.defaultFrequencyPenalty,
                  defaultFrequencyPenalty,
                ) ||
                other.defaultFrequencyPenalty == defaultFrequencyPenalty) &&
            (identical(other.defaultPresencePenalty, defaultPresencePenalty) ||
                other.defaultPresencePenalty == defaultPresencePenalty) &&
            const DeepCollectionEquality().equals(
              other._supportedFeatures,
              _supportedFeatures,
            ) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    displayName,
    provider,
    type,
    status,
    description,
    version,
    maxTokens,
    defaultTemperature,
    defaultTopP,
    defaultFrequencyPenalty,
    defaultPresencePenalty,
    const DeepCollectionEquality().hash(_supportedFeatures),
    const DeepCollectionEquality().hash(_metadata),
    isActive,
    createdAt,
    updatedAt,
  );

  /// Create a copy of AIModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AIModelImplCopyWith<_$AIModelImpl> get copyWith =>
      __$$AIModelImplCopyWithImpl<_$AIModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AIModelImplToJson(this);
  }
}

abstract class _AIModel implements AIModel {
  const factory _AIModel({
    required final String id,
    required final String name,
    required final String displayName,
    required final AIProvider provider,
    required final AIModelType type,
    required final AIModelStatus status,
    final String? description,
    final String? version,
    final int maxTokens,
    final double defaultTemperature,
    final double defaultTopP,
    final double defaultFrequencyPenalty,
    final double defaultPresencePenalty,
    final List<String> supportedFeatures,
    final Map<String, dynamic>? metadata,
    final bool isActive,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$AIModelImpl;

  factory _AIModel.fromJson(Map<String, dynamic> json) = _$AIModelImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get displayName;
  @override
  AIProvider get provider;
  @override
  AIModelType get type;
  @override
  AIModelStatus get status;
  @override
  String? get description;
  @override
  String? get version;
  @override
  int get maxTokens;
  @override
  double get defaultTemperature;
  @override
  double get defaultTopP;
  @override
  double get defaultFrequencyPenalty;
  @override
  double get defaultPresencePenalty;
  @override
  List<String> get supportedFeatures;
  @override
  Map<String, dynamic>? get metadata;
  @override
  bool get isActive;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of AIModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AIModelImplCopyWith<_$AIModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AIModelConfig _$AIModelConfigFromJson(Map<String, dynamic> json) {
  return _AIModelConfig.fromJson(json);
}

/// @nodoc
mixin _$AIModelConfig {
  String get modelId => throw _privateConstructorUsedError;
  String get apiKey => throw _privateConstructorUsedError;
  String? get baseUrl => throw _privateConstructorUsedError;
  String? get organizationId => throw _privateConstructorUsedError;
  double get temperature => throw _privateConstructorUsedError;
  double get topP => throw _privateConstructorUsedError;
  double get frequencyPenalty => throw _privateConstructorUsedError;
  double get presencePenalty => throw _privateConstructorUsedError;
  int get maxTokens => throw _privateConstructorUsedError;
  int get maxRetries => throw _privateConstructorUsedError;
  int get timeoutMs => throw _privateConstructorUsedError;
  Map<String, String>? get customHeaders => throw _privateConstructorUsedError;
  Map<String, dynamic>? get additionalParams =>
      throw _privateConstructorUsedError;
  bool get isEnabled => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this AIModelConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AIModelConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AIModelConfigCopyWith<AIModelConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AIModelConfigCopyWith<$Res> {
  factory $AIModelConfigCopyWith(
    AIModelConfig value,
    $Res Function(AIModelConfig) then,
  ) = _$AIModelConfigCopyWithImpl<$Res, AIModelConfig>;
  @useResult
  $Res call({
    String modelId,
    String apiKey,
    String? baseUrl,
    String? organizationId,
    double temperature,
    double topP,
    double frequencyPenalty,
    double presencePenalty,
    int maxTokens,
    int maxRetries,
    int timeoutMs,
    Map<String, String>? customHeaders,
    Map<String, dynamic>? additionalParams,
    bool isEnabled,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$AIModelConfigCopyWithImpl<$Res, $Val extends AIModelConfig>
    implements $AIModelConfigCopyWith<$Res> {
  _$AIModelConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AIModelConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? modelId = null,
    Object? apiKey = null,
    Object? baseUrl = freezed,
    Object? organizationId = freezed,
    Object? temperature = null,
    Object? topP = null,
    Object? frequencyPenalty = null,
    Object? presencePenalty = null,
    Object? maxTokens = null,
    Object? maxRetries = null,
    Object? timeoutMs = null,
    Object? customHeaders = freezed,
    Object? additionalParams = freezed,
    Object? isEnabled = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            modelId: null == modelId
                ? _value.modelId
                : modelId // ignore: cast_nullable_to_non_nullable
                      as String,
            apiKey: null == apiKey
                ? _value.apiKey
                : apiKey // ignore: cast_nullable_to_non_nullable
                      as String,
            baseUrl: freezed == baseUrl
                ? _value.baseUrl
                : baseUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            organizationId: freezed == organizationId
                ? _value.organizationId
                : organizationId // ignore: cast_nullable_to_non_nullable
                      as String?,
            temperature: null == temperature
                ? _value.temperature
                : temperature // ignore: cast_nullable_to_non_nullable
                      as double,
            topP: null == topP
                ? _value.topP
                : topP // ignore: cast_nullable_to_non_nullable
                      as double,
            frequencyPenalty: null == frequencyPenalty
                ? _value.frequencyPenalty
                : frequencyPenalty // ignore: cast_nullable_to_non_nullable
                      as double,
            presencePenalty: null == presencePenalty
                ? _value.presencePenalty
                : presencePenalty // ignore: cast_nullable_to_non_nullable
                      as double,
            maxTokens: null == maxTokens
                ? _value.maxTokens
                : maxTokens // ignore: cast_nullable_to_non_nullable
                      as int,
            maxRetries: null == maxRetries
                ? _value.maxRetries
                : maxRetries // ignore: cast_nullable_to_non_nullable
                      as int,
            timeoutMs: null == timeoutMs
                ? _value.timeoutMs
                : timeoutMs // ignore: cast_nullable_to_non_nullable
                      as int,
            customHeaders: freezed == customHeaders
                ? _value.customHeaders
                : customHeaders // ignore: cast_nullable_to_non_nullable
                      as Map<String, String>?,
            additionalParams: freezed == additionalParams
                ? _value.additionalParams
                : additionalParams // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            isEnabled: null == isEnabled
                ? _value.isEnabled
                : isEnabled // ignore: cast_nullable_to_non_nullable
                      as bool,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AIModelConfigImplCopyWith<$Res>
    implements $AIModelConfigCopyWith<$Res> {
  factory _$$AIModelConfigImplCopyWith(
    _$AIModelConfigImpl value,
    $Res Function(_$AIModelConfigImpl) then,
  ) = __$$AIModelConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String modelId,
    String apiKey,
    String? baseUrl,
    String? organizationId,
    double temperature,
    double topP,
    double frequencyPenalty,
    double presencePenalty,
    int maxTokens,
    int maxRetries,
    int timeoutMs,
    Map<String, String>? customHeaders,
    Map<String, dynamic>? additionalParams,
    bool isEnabled,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$AIModelConfigImplCopyWithImpl<$Res>
    extends _$AIModelConfigCopyWithImpl<$Res, _$AIModelConfigImpl>
    implements _$$AIModelConfigImplCopyWith<$Res> {
  __$$AIModelConfigImplCopyWithImpl(
    _$AIModelConfigImpl _value,
    $Res Function(_$AIModelConfigImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AIModelConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? modelId = null,
    Object? apiKey = null,
    Object? baseUrl = freezed,
    Object? organizationId = freezed,
    Object? temperature = null,
    Object? topP = null,
    Object? frequencyPenalty = null,
    Object? presencePenalty = null,
    Object? maxTokens = null,
    Object? maxRetries = null,
    Object? timeoutMs = null,
    Object? customHeaders = freezed,
    Object? additionalParams = freezed,
    Object? isEnabled = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$AIModelConfigImpl(
        modelId: null == modelId
            ? _value.modelId
            : modelId // ignore: cast_nullable_to_non_nullable
                  as String,
        apiKey: null == apiKey
            ? _value.apiKey
            : apiKey // ignore: cast_nullable_to_non_nullable
                  as String,
        baseUrl: freezed == baseUrl
            ? _value.baseUrl
            : baseUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        organizationId: freezed == organizationId
            ? _value.organizationId
            : organizationId // ignore: cast_nullable_to_non_nullable
                  as String?,
        temperature: null == temperature
            ? _value.temperature
            : temperature // ignore: cast_nullable_to_non_nullable
                  as double,
        topP: null == topP
            ? _value.topP
            : topP // ignore: cast_nullable_to_non_nullable
                  as double,
        frequencyPenalty: null == frequencyPenalty
            ? _value.frequencyPenalty
            : frequencyPenalty // ignore: cast_nullable_to_non_nullable
                  as double,
        presencePenalty: null == presencePenalty
            ? _value.presencePenalty
            : presencePenalty // ignore: cast_nullable_to_non_nullable
                  as double,
        maxTokens: null == maxTokens
            ? _value.maxTokens
            : maxTokens // ignore: cast_nullable_to_non_nullable
                  as int,
        maxRetries: null == maxRetries
            ? _value.maxRetries
            : maxRetries // ignore: cast_nullable_to_non_nullable
                  as int,
        timeoutMs: null == timeoutMs
            ? _value.timeoutMs
            : timeoutMs // ignore: cast_nullable_to_non_nullable
                  as int,
        customHeaders: freezed == customHeaders
            ? _value._customHeaders
            : customHeaders // ignore: cast_nullable_to_non_nullable
                  as Map<String, String>?,
        additionalParams: freezed == additionalParams
            ? _value._additionalParams
            : additionalParams // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        isEnabled: null == isEnabled
            ? _value.isEnabled
            : isEnabled // ignore: cast_nullable_to_non_nullable
                  as bool,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AIModelConfigImpl implements _AIModelConfig {
  const _$AIModelConfigImpl({
    required this.modelId,
    required this.apiKey,
    this.baseUrl,
    this.organizationId,
    this.temperature = 0.7,
    this.topP = 1.0,
    this.frequencyPenalty = 1.0,
    this.presencePenalty = 1.0,
    this.maxTokens = 4096,
    this.maxRetries = 1,
    this.timeoutMs = 30000,
    final Map<String, String>? customHeaders,
    final Map<String, dynamic>? additionalParams,
    this.isEnabled = true,
    this.createdAt,
    this.updatedAt,
  }) : _customHeaders = customHeaders,
       _additionalParams = additionalParams;

  factory _$AIModelConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$AIModelConfigImplFromJson(json);

  @override
  final String modelId;
  @override
  final String apiKey;
  @override
  final String? baseUrl;
  @override
  final String? organizationId;
  @override
  @JsonKey()
  final double temperature;
  @override
  @JsonKey()
  final double topP;
  @override
  @JsonKey()
  final double frequencyPenalty;
  @override
  @JsonKey()
  final double presencePenalty;
  @override
  @JsonKey()
  final int maxTokens;
  @override
  @JsonKey()
  final int maxRetries;
  @override
  @JsonKey()
  final int timeoutMs;
  final Map<String, String>? _customHeaders;
  @override
  Map<String, String>? get customHeaders {
    final value = _customHeaders;
    if (value == null) return null;
    if (_customHeaders is EqualUnmodifiableMapView) return _customHeaders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final Map<String, dynamic>? _additionalParams;
  @override
  Map<String, dynamic>? get additionalParams {
    final value = _additionalParams;
    if (value == null) return null;
    if (_additionalParams is EqualUnmodifiableMapView) return _additionalParams;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  @JsonKey()
  final bool isEnabled;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'AIModelConfig(modelId: $modelId, apiKey: $apiKey, baseUrl: $baseUrl, organizationId: $organizationId, temperature: $temperature, topP: $topP, frequencyPenalty: $frequencyPenalty, presencePenalty: $presencePenalty, maxTokens: $maxTokens, maxRetries: $maxRetries, timeoutMs: $timeoutMs, customHeaders: $customHeaders, additionalParams: $additionalParams, isEnabled: $isEnabled, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AIModelConfigImpl &&
            (identical(other.modelId, modelId) || other.modelId == modelId) &&
            (identical(other.apiKey, apiKey) || other.apiKey == apiKey) &&
            (identical(other.baseUrl, baseUrl) || other.baseUrl == baseUrl) &&
            (identical(other.organizationId, organizationId) ||
                other.organizationId == organizationId) &&
            (identical(other.temperature, temperature) ||
                other.temperature == temperature) &&
            (identical(other.topP, topP) || other.topP == topP) &&
            (identical(other.frequencyPenalty, frequencyPenalty) ||
                other.frequencyPenalty == frequencyPenalty) &&
            (identical(other.presencePenalty, presencePenalty) ||
                other.presencePenalty == presencePenalty) &&
            (identical(other.maxTokens, maxTokens) ||
                other.maxTokens == maxTokens) &&
            (identical(other.maxRetries, maxRetries) ||
                other.maxRetries == maxRetries) &&
            (identical(other.timeoutMs, timeoutMs) ||
                other.timeoutMs == timeoutMs) &&
            const DeepCollectionEquality().equals(
              other._customHeaders,
              _customHeaders,
            ) &&
            const DeepCollectionEquality().equals(
              other._additionalParams,
              _additionalParams,
            ) &&
            (identical(other.isEnabled, isEnabled) ||
                other.isEnabled == isEnabled) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    modelId,
    apiKey,
    baseUrl,
    organizationId,
    temperature,
    topP,
    frequencyPenalty,
    presencePenalty,
    maxTokens,
    maxRetries,
    timeoutMs,
    const DeepCollectionEquality().hash(_customHeaders),
    const DeepCollectionEquality().hash(_additionalParams),
    isEnabled,
    createdAt,
    updatedAt,
  );

  /// Create a copy of AIModelConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AIModelConfigImplCopyWith<_$AIModelConfigImpl> get copyWith =>
      __$$AIModelConfigImplCopyWithImpl<_$AIModelConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AIModelConfigImplToJson(this);
  }
}

abstract class _AIModelConfig implements AIModelConfig {
  const factory _AIModelConfig({
    required final String modelId,
    required final String apiKey,
    final String? baseUrl,
    final String? organizationId,
    final double temperature,
    final double topP,
    final double frequencyPenalty,
    final double presencePenalty,
    final int maxTokens,
    final int maxRetries,
    final int timeoutMs,
    final Map<String, String>? customHeaders,
    final Map<String, dynamic>? additionalParams,
    final bool isEnabled,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$AIModelConfigImpl;

  factory _AIModelConfig.fromJson(Map<String, dynamic> json) =
      _$AIModelConfigImpl.fromJson;

  @override
  String get modelId;
  @override
  String get apiKey;
  @override
  String? get baseUrl;
  @override
  String? get organizationId;
  @override
  double get temperature;
  @override
  double get topP;
  @override
  double get frequencyPenalty;
  @override
  double get presencePenalty;
  @override
  int get maxTokens;
  @override
  int get maxRetries;
  @override
  int get timeoutMs;
  @override
  Map<String, String>? get customHeaders;
  @override
  Map<String, dynamic>? get additionalParams;
  @override
  bool get isEnabled;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of AIModelConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AIModelConfigImplCopyWith<_$AIModelConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AIRequest _$AIRequestFromJson(Map<String, dynamic> json) {
  return _AIRequest.fromJson(json);
}

/// @nodoc
mixin _$AIRequest {
  String get modelId => throw _privateConstructorUsedError;
  String get prompt => throw _privateConstructorUsedError;
  List<AIMessage>? get messages => throw _privateConstructorUsedError;
  double get temperature => throw _privateConstructorUsedError;
  double get topP => throw _privateConstructorUsedError;
  double get frequencyPenalty => throw _privateConstructorUsedError;
  double get presencePenalty => throw _privateConstructorUsedError;
  int get maxTokens => throw _privateConstructorUsedError;
  List<String>? get stop => throw _privateConstructorUsedError;
  String? get systemPrompt => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;
  String? get requestId => throw _privateConstructorUsedError;

  /// Serializes this AIRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AIRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AIRequestCopyWith<AIRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AIRequestCopyWith<$Res> {
  factory $AIRequestCopyWith(AIRequest value, $Res Function(AIRequest) then) =
      _$AIRequestCopyWithImpl<$Res, AIRequest>;
  @useResult
  $Res call({
    String modelId,
    String prompt,
    List<AIMessage>? messages,
    double temperature,
    double topP,
    double frequencyPenalty,
    double presencePenalty,
    int maxTokens,
    List<String>? stop,
    String? systemPrompt,
    Map<String, dynamic>? metadata,
    String? requestId,
  });
}

/// @nodoc
class _$AIRequestCopyWithImpl<$Res, $Val extends AIRequest>
    implements $AIRequestCopyWith<$Res> {
  _$AIRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AIRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? modelId = null,
    Object? prompt = null,
    Object? messages = freezed,
    Object? temperature = null,
    Object? topP = null,
    Object? frequencyPenalty = null,
    Object? presencePenalty = null,
    Object? maxTokens = null,
    Object? stop = freezed,
    Object? systemPrompt = freezed,
    Object? metadata = freezed,
    Object? requestId = freezed,
  }) {
    return _then(
      _value.copyWith(
            modelId: null == modelId
                ? _value.modelId
                : modelId // ignore: cast_nullable_to_non_nullable
                      as String,
            prompt: null == prompt
                ? _value.prompt
                : prompt // ignore: cast_nullable_to_non_nullable
                      as String,
            messages: freezed == messages
                ? _value.messages
                : messages // ignore: cast_nullable_to_non_nullable
                      as List<AIMessage>?,
            temperature: null == temperature
                ? _value.temperature
                : temperature // ignore: cast_nullable_to_non_nullable
                      as double,
            topP: null == topP
                ? _value.topP
                : topP // ignore: cast_nullable_to_non_nullable
                      as double,
            frequencyPenalty: null == frequencyPenalty
                ? _value.frequencyPenalty
                : frequencyPenalty // ignore: cast_nullable_to_non_nullable
                      as double,
            presencePenalty: null == presencePenalty
                ? _value.presencePenalty
                : presencePenalty // ignore: cast_nullable_to_non_nullable
                      as double,
            maxTokens: null == maxTokens
                ? _value.maxTokens
                : maxTokens // ignore: cast_nullable_to_non_nullable
                      as int,
            stop: freezed == stop
                ? _value.stop
                : stop // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
            systemPrompt: freezed == systemPrompt
                ? _value.systemPrompt
                : systemPrompt // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            requestId: freezed == requestId
                ? _value.requestId
                : requestId // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AIRequestImplCopyWith<$Res>
    implements $AIRequestCopyWith<$Res> {
  factory _$$AIRequestImplCopyWith(
    _$AIRequestImpl value,
    $Res Function(_$AIRequestImpl) then,
  ) = __$$AIRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String modelId,
    String prompt,
    List<AIMessage>? messages,
    double temperature,
    double topP,
    double frequencyPenalty,
    double presencePenalty,
    int maxTokens,
    List<String>? stop,
    String? systemPrompt,
    Map<String, dynamic>? metadata,
    String? requestId,
  });
}

/// @nodoc
class __$$AIRequestImplCopyWithImpl<$Res>
    extends _$AIRequestCopyWithImpl<$Res, _$AIRequestImpl>
    implements _$$AIRequestImplCopyWith<$Res> {
  __$$AIRequestImplCopyWithImpl(
    _$AIRequestImpl _value,
    $Res Function(_$AIRequestImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AIRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? modelId = null,
    Object? prompt = null,
    Object? messages = freezed,
    Object? temperature = null,
    Object? topP = null,
    Object? frequencyPenalty = null,
    Object? presencePenalty = null,
    Object? maxTokens = null,
    Object? stop = freezed,
    Object? systemPrompt = freezed,
    Object? metadata = freezed,
    Object? requestId = freezed,
  }) {
    return _then(
      _$AIRequestImpl(
        modelId: null == modelId
            ? _value.modelId
            : modelId // ignore: cast_nullable_to_non_nullable
                  as String,
        prompt: null == prompt
            ? _value.prompt
            : prompt // ignore: cast_nullable_to_non_nullable
                  as String,
        messages: freezed == messages
            ? _value._messages
            : messages // ignore: cast_nullable_to_non_nullable
                  as List<AIMessage>?,
        temperature: null == temperature
            ? _value.temperature
            : temperature // ignore: cast_nullable_to_non_nullable
                  as double,
        topP: null == topP
            ? _value.topP
            : topP // ignore: cast_nullable_to_non_nullable
                  as double,
        frequencyPenalty: null == frequencyPenalty
            ? _value.frequencyPenalty
            : frequencyPenalty // ignore: cast_nullable_to_non_nullable
                  as double,
        presencePenalty: null == presencePenalty
            ? _value.presencePenalty
            : presencePenalty // ignore: cast_nullable_to_non_nullable
                  as double,
        maxTokens: null == maxTokens
            ? _value.maxTokens
            : maxTokens // ignore: cast_nullable_to_non_nullable
                  as int,
        stop: freezed == stop
            ? _value._stop
            : stop // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        systemPrompt: freezed == systemPrompt
            ? _value.systemPrompt
            : systemPrompt // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        requestId: freezed == requestId
            ? _value.requestId
            : requestId // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AIRequestImpl implements _AIRequest {
  const _$AIRequestImpl({
    required this.modelId,
    required this.prompt,
    final List<AIMessage>? messages,
    this.temperature = 0.7,
    this.topP = 1.0,
    this.frequencyPenalty = 1.0,
    this.presencePenalty = 1.0,
    this.maxTokens = 4096,
    final List<String>? stop,
    this.systemPrompt,
    final Map<String, dynamic>? metadata,
    this.requestId,
  }) : _messages = messages,
       _stop = stop,
       _metadata = metadata;

  factory _$AIRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$AIRequestImplFromJson(json);

  @override
  final String modelId;
  @override
  final String prompt;
  final List<AIMessage>? _messages;
  @override
  List<AIMessage>? get messages {
    final value = _messages;
    if (value == null) return null;
    if (_messages is EqualUnmodifiableListView) return _messages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey()
  final double temperature;
  @override
  @JsonKey()
  final double topP;
  @override
  @JsonKey()
  final double frequencyPenalty;
  @override
  @JsonKey()
  final double presencePenalty;
  @override
  @JsonKey()
  final int maxTokens;
  final List<String>? _stop;
  @override
  List<String>? get stop {
    final value = _stop;
    if (value == null) return null;
    if (_stop is EqualUnmodifiableListView) return _stop;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? systemPrompt;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final String? requestId;

  @override
  String toString() {
    return 'AIRequest(modelId: $modelId, prompt: $prompt, messages: $messages, temperature: $temperature, topP: $topP, frequencyPenalty: $frequencyPenalty, presencePenalty: $presencePenalty, maxTokens: $maxTokens, stop: $stop, systemPrompt: $systemPrompt, metadata: $metadata, requestId: $requestId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AIRequestImpl &&
            (identical(other.modelId, modelId) || other.modelId == modelId) &&
            (identical(other.prompt, prompt) || other.prompt == prompt) &&
            const DeepCollectionEquality().equals(other._messages, _messages) &&
            (identical(other.temperature, temperature) ||
                other.temperature == temperature) &&
            (identical(other.topP, topP) || other.topP == topP) &&
            (identical(other.frequencyPenalty, frequencyPenalty) ||
                other.frequencyPenalty == frequencyPenalty) &&
            (identical(other.presencePenalty, presencePenalty) ||
                other.presencePenalty == presencePenalty) &&
            (identical(other.maxTokens, maxTokens) ||
                other.maxTokens == maxTokens) &&
            const DeepCollectionEquality().equals(other._stop, _stop) &&
            (identical(other.systemPrompt, systemPrompt) ||
                other.systemPrompt == systemPrompt) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.requestId, requestId) ||
                other.requestId == requestId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    modelId,
    prompt,
    const DeepCollectionEquality().hash(_messages),
    temperature,
    topP,
    frequencyPenalty,
    presencePenalty,
    maxTokens,
    const DeepCollectionEquality().hash(_stop),
    systemPrompt,
    const DeepCollectionEquality().hash(_metadata),
    requestId,
  );

  /// Create a copy of AIRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AIRequestImplCopyWith<_$AIRequestImpl> get copyWith =>
      __$$AIRequestImplCopyWithImpl<_$AIRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AIRequestImplToJson(this);
  }
}

abstract class _AIRequest implements AIRequest {
  const factory _AIRequest({
    required final String modelId,
    required final String prompt,
    final List<AIMessage>? messages,
    final double temperature,
    final double topP,
    final double frequencyPenalty,
    final double presencePenalty,
    final int maxTokens,
    final List<String>? stop,
    final String? systemPrompt,
    final Map<String, dynamic>? metadata,
    final String? requestId,
  }) = _$AIRequestImpl;

  factory _AIRequest.fromJson(Map<String, dynamic> json) =
      _$AIRequestImpl.fromJson;

  @override
  String get modelId;
  @override
  String get prompt;
  @override
  List<AIMessage>? get messages;
  @override
  double get temperature;
  @override
  double get topP;
  @override
  double get frequencyPenalty;
  @override
  double get presencePenalty;
  @override
  int get maxTokens;
  @override
  List<String>? get stop;
  @override
  String? get systemPrompt;
  @override
  Map<String, dynamic>? get metadata;
  @override
  String? get requestId;

  /// Create a copy of AIRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AIRequestImplCopyWith<_$AIRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AIMessage _$AIMessageFromJson(Map<String, dynamic> json) {
  return _AIMessage.fromJson(json);
}

/// @nodoc
mixin _$AIMessage {
  String get role => throw _privateConstructorUsedError;
  String get content => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;
  DateTime? get timestamp => throw _privateConstructorUsedError;

  /// Serializes this AIMessage to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AIMessage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AIMessageCopyWith<AIMessage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AIMessageCopyWith<$Res> {
  factory $AIMessageCopyWith(AIMessage value, $Res Function(AIMessage) then) =
      _$AIMessageCopyWithImpl<$Res, AIMessage>;
  @useResult
  $Res call({
    String role,
    String content,
    String? name,
    Map<String, dynamic>? metadata,
    DateTime? timestamp,
  });
}

/// @nodoc
class _$AIMessageCopyWithImpl<$Res, $Val extends AIMessage>
    implements $AIMessageCopyWith<$Res> {
  _$AIMessageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AIMessage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? role = null,
    Object? content = null,
    Object? name = freezed,
    Object? metadata = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(
      _value.copyWith(
            role: null == role
                ? _value.role
                : role // ignore: cast_nullable_to_non_nullable
                      as String,
            content: null == content
                ? _value.content
                : content // ignore: cast_nullable_to_non_nullable
                      as String,
            name: freezed == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String?,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            timestamp: freezed == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AIMessageImplCopyWith<$Res>
    implements $AIMessageCopyWith<$Res> {
  factory _$$AIMessageImplCopyWith(
    _$AIMessageImpl value,
    $Res Function(_$AIMessageImpl) then,
  ) = __$$AIMessageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String role,
    String content,
    String? name,
    Map<String, dynamic>? metadata,
    DateTime? timestamp,
  });
}

/// @nodoc
class __$$AIMessageImplCopyWithImpl<$Res>
    extends _$AIMessageCopyWithImpl<$Res, _$AIMessageImpl>
    implements _$$AIMessageImplCopyWith<$Res> {
  __$$AIMessageImplCopyWithImpl(
    _$AIMessageImpl _value,
    $Res Function(_$AIMessageImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AIMessage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? role = null,
    Object? content = null,
    Object? name = freezed,
    Object? metadata = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(
      _$AIMessageImpl(
        role: null == role
            ? _value.role
            : role // ignore: cast_nullable_to_non_nullable
                  as String,
        content: null == content
            ? _value.content
            : content // ignore: cast_nullable_to_non_nullable
                  as String,
        name: freezed == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String?,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        timestamp: freezed == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AIMessageImpl implements _AIMessage {
  const _$AIMessageImpl({
    required this.role,
    required this.content,
    this.name,
    final Map<String, dynamic>? metadata,
    this.timestamp,
  }) : _metadata = metadata;

  factory _$AIMessageImpl.fromJson(Map<String, dynamic> json) =>
      _$$AIMessageImplFromJson(json);

  @override
  final String role;
  @override
  final String content;
  @override
  final String? name;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final DateTime? timestamp;

  @override
  String toString() {
    return 'AIMessage(role: $role, content: $content, name: $name, metadata: $metadata, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AIMessageImpl &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    role,
    content,
    name,
    const DeepCollectionEquality().hash(_metadata),
    timestamp,
  );

  /// Create a copy of AIMessage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AIMessageImplCopyWith<_$AIMessageImpl> get copyWith =>
      __$$AIMessageImplCopyWithImpl<_$AIMessageImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AIMessageImplToJson(this);
  }
}

abstract class _AIMessage implements AIMessage {
  const factory _AIMessage({
    required final String role,
    required final String content,
    final String? name,
    final Map<String, dynamic>? metadata,
    final DateTime? timestamp,
  }) = _$AIMessageImpl;

  factory _AIMessage.fromJson(Map<String, dynamic> json) =
      _$AIMessageImpl.fromJson;

  @override
  String get role;
  @override
  String get content;
  @override
  String? get name;
  @override
  Map<String, dynamic>? get metadata;
  @override
  DateTime? get timestamp;

  /// Create a copy of AIMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AIMessageImplCopyWith<_$AIMessageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AIResponse _$AIResponseFromJson(Map<String, dynamic> json) {
  return _AIResponse.fromJson(json);
}

/// @nodoc
mixin _$AIResponse {
  String get id => throw _privateConstructorUsedError;
  String get modelId => throw _privateConstructorUsedError;
  String get content => throw _privateConstructorUsedError;
  AIUsage get usage => throw _privateConstructorUsedError;
  String? get finishReason => throw _privateConstructorUsedError;
  List<AIChoice>? get choices => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  int? get responseTimeMs => throw _privateConstructorUsedError;

  /// Serializes this AIResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AIResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AIResponseCopyWith<AIResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AIResponseCopyWith<$Res> {
  factory $AIResponseCopyWith(
    AIResponse value,
    $Res Function(AIResponse) then,
  ) = _$AIResponseCopyWithImpl<$Res, AIResponse>;
  @useResult
  $Res call({
    String id,
    String modelId,
    String content,
    AIUsage usage,
    String? finishReason,
    List<AIChoice>? choices,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    int? responseTimeMs,
  });

  $AIUsageCopyWith<$Res> get usage;
}

/// @nodoc
class _$AIResponseCopyWithImpl<$Res, $Val extends AIResponse>
    implements $AIResponseCopyWith<$Res> {
  _$AIResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AIResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? modelId = null,
    Object? content = null,
    Object? usage = null,
    Object? finishReason = freezed,
    Object? choices = freezed,
    Object? metadata = freezed,
    Object? createdAt = freezed,
    Object? responseTimeMs = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            modelId: null == modelId
                ? _value.modelId
                : modelId // ignore: cast_nullable_to_non_nullable
                      as String,
            content: null == content
                ? _value.content
                : content // ignore: cast_nullable_to_non_nullable
                      as String,
            usage: null == usage
                ? _value.usage
                : usage // ignore: cast_nullable_to_non_nullable
                      as AIUsage,
            finishReason: freezed == finishReason
                ? _value.finishReason
                : finishReason // ignore: cast_nullable_to_non_nullable
                      as String?,
            choices: freezed == choices
                ? _value.choices
                : choices // ignore: cast_nullable_to_non_nullable
                      as List<AIChoice>?,
            metadata: freezed == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            responseTimeMs: freezed == responseTimeMs
                ? _value.responseTimeMs
                : responseTimeMs // ignore: cast_nullable_to_non_nullable
                      as int?,
          )
          as $Val,
    );
  }

  /// Create a copy of AIResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AIUsageCopyWith<$Res> get usage {
    return $AIUsageCopyWith<$Res>(_value.usage, (value) {
      return _then(_value.copyWith(usage: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AIResponseImplCopyWith<$Res>
    implements $AIResponseCopyWith<$Res> {
  factory _$$AIResponseImplCopyWith(
    _$AIResponseImpl value,
    $Res Function(_$AIResponseImpl) then,
  ) = __$$AIResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String modelId,
    String content,
    AIUsage usage,
    String? finishReason,
    List<AIChoice>? choices,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    int? responseTimeMs,
  });

  @override
  $AIUsageCopyWith<$Res> get usage;
}

/// @nodoc
class __$$AIResponseImplCopyWithImpl<$Res>
    extends _$AIResponseCopyWithImpl<$Res, _$AIResponseImpl>
    implements _$$AIResponseImplCopyWith<$Res> {
  __$$AIResponseImplCopyWithImpl(
    _$AIResponseImpl _value,
    $Res Function(_$AIResponseImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AIResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? modelId = null,
    Object? content = null,
    Object? usage = null,
    Object? finishReason = freezed,
    Object? choices = freezed,
    Object? metadata = freezed,
    Object? createdAt = freezed,
    Object? responseTimeMs = freezed,
  }) {
    return _then(
      _$AIResponseImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        modelId: null == modelId
            ? _value.modelId
            : modelId // ignore: cast_nullable_to_non_nullable
                  as String,
        content: null == content
            ? _value.content
            : content // ignore: cast_nullable_to_non_nullable
                  as String,
        usage: null == usage
            ? _value.usage
            : usage // ignore: cast_nullable_to_non_nullable
                  as AIUsage,
        finishReason: freezed == finishReason
            ? _value.finishReason
            : finishReason // ignore: cast_nullable_to_non_nullable
                  as String?,
        choices: freezed == choices
            ? _value._choices
            : choices // ignore: cast_nullable_to_non_nullable
                  as List<AIChoice>?,
        metadata: freezed == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        responseTimeMs: freezed == responseTimeMs
            ? _value.responseTimeMs
            : responseTimeMs // ignore: cast_nullable_to_non_nullable
                  as int?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AIResponseImpl implements _AIResponse {
  const _$AIResponseImpl({
    required this.id,
    required this.modelId,
    required this.content,
    required this.usage,
    this.finishReason,
    final List<AIChoice>? choices,
    final Map<String, dynamic>? metadata,
    this.createdAt,
    this.responseTimeMs,
  }) : _choices = choices,
       _metadata = metadata;

  factory _$AIResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$AIResponseImplFromJson(json);

  @override
  final String id;
  @override
  final String modelId;
  @override
  final String content;
  @override
  final AIUsage usage;
  @override
  final String? finishReason;
  final List<AIChoice>? _choices;
  @override
  List<AIChoice>? get choices {
    final value = _choices;
    if (value == null) return null;
    if (_choices is EqualUnmodifiableListView) return _choices;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final DateTime? createdAt;
  @override
  final int? responseTimeMs;

  @override
  String toString() {
    return 'AIResponse(id: $id, modelId: $modelId, content: $content, usage: $usage, finishReason: $finishReason, choices: $choices, metadata: $metadata, createdAt: $createdAt, responseTimeMs: $responseTimeMs)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AIResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.modelId, modelId) || other.modelId == modelId) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.usage, usage) || other.usage == usage) &&
            (identical(other.finishReason, finishReason) ||
                other.finishReason == finishReason) &&
            const DeepCollectionEquality().equals(other._choices, _choices) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.responseTimeMs, responseTimeMs) ||
                other.responseTimeMs == responseTimeMs));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    modelId,
    content,
    usage,
    finishReason,
    const DeepCollectionEquality().hash(_choices),
    const DeepCollectionEquality().hash(_metadata),
    createdAt,
    responseTimeMs,
  );

  /// Create a copy of AIResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AIResponseImplCopyWith<_$AIResponseImpl> get copyWith =>
      __$$AIResponseImplCopyWithImpl<_$AIResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AIResponseImplToJson(this);
  }
}

abstract class _AIResponse implements AIResponse {
  const factory _AIResponse({
    required final String id,
    required final String modelId,
    required final String content,
    required final AIUsage usage,
    final String? finishReason,
    final List<AIChoice>? choices,
    final Map<String, dynamic>? metadata,
    final DateTime? createdAt,
    final int? responseTimeMs,
  }) = _$AIResponseImpl;

  factory _AIResponse.fromJson(Map<String, dynamic> json) =
      _$AIResponseImpl.fromJson;

  @override
  String get id;
  @override
  String get modelId;
  @override
  String get content;
  @override
  AIUsage get usage;
  @override
  String? get finishReason;
  @override
  List<AIChoice>? get choices;
  @override
  Map<String, dynamic>? get metadata;
  @override
  DateTime? get createdAt;
  @override
  int? get responseTimeMs;

  /// Create a copy of AIResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AIResponseImplCopyWith<_$AIResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AIChoice _$AIChoiceFromJson(Map<String, dynamic> json) {
  return _AIChoice.fromJson(json);
}

/// @nodoc
mixin _$AIChoice {
  int get index => throw _privateConstructorUsedError;
  AIMessage get message => throw _privateConstructorUsedError;
  String? get finishReason => throw _privateConstructorUsedError;
  double? get logprobs => throw _privateConstructorUsedError;

  /// Serializes this AIChoice to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AIChoice
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AIChoiceCopyWith<AIChoice> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AIChoiceCopyWith<$Res> {
  factory $AIChoiceCopyWith(AIChoice value, $Res Function(AIChoice) then) =
      _$AIChoiceCopyWithImpl<$Res, AIChoice>;
  @useResult
  $Res call({
    int index,
    AIMessage message,
    String? finishReason,
    double? logprobs,
  });

  $AIMessageCopyWith<$Res> get message;
}

/// @nodoc
class _$AIChoiceCopyWithImpl<$Res, $Val extends AIChoice>
    implements $AIChoiceCopyWith<$Res> {
  _$AIChoiceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AIChoice
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? index = null,
    Object? message = null,
    Object? finishReason = freezed,
    Object? logprobs = freezed,
  }) {
    return _then(
      _value.copyWith(
            index: null == index
                ? _value.index
                : index // ignore: cast_nullable_to_non_nullable
                      as int,
            message: null == message
                ? _value.message
                : message // ignore: cast_nullable_to_non_nullable
                      as AIMessage,
            finishReason: freezed == finishReason
                ? _value.finishReason
                : finishReason // ignore: cast_nullable_to_non_nullable
                      as String?,
            logprobs: freezed == logprobs
                ? _value.logprobs
                : logprobs // ignore: cast_nullable_to_non_nullable
                      as double?,
          )
          as $Val,
    );
  }

  /// Create a copy of AIChoice
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AIMessageCopyWith<$Res> get message {
    return $AIMessageCopyWith<$Res>(_value.message, (value) {
      return _then(_value.copyWith(message: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AIChoiceImplCopyWith<$Res>
    implements $AIChoiceCopyWith<$Res> {
  factory _$$AIChoiceImplCopyWith(
    _$AIChoiceImpl value,
    $Res Function(_$AIChoiceImpl) then,
  ) = __$$AIChoiceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int index,
    AIMessage message,
    String? finishReason,
    double? logprobs,
  });

  @override
  $AIMessageCopyWith<$Res> get message;
}

/// @nodoc
class __$$AIChoiceImplCopyWithImpl<$Res>
    extends _$AIChoiceCopyWithImpl<$Res, _$AIChoiceImpl>
    implements _$$AIChoiceImplCopyWith<$Res> {
  __$$AIChoiceImplCopyWithImpl(
    _$AIChoiceImpl _value,
    $Res Function(_$AIChoiceImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AIChoice
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? index = null,
    Object? message = null,
    Object? finishReason = freezed,
    Object? logprobs = freezed,
  }) {
    return _then(
      _$AIChoiceImpl(
        index: null == index
            ? _value.index
            : index // ignore: cast_nullable_to_non_nullable
                  as int,
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as AIMessage,
        finishReason: freezed == finishReason
            ? _value.finishReason
            : finishReason // ignore: cast_nullable_to_non_nullable
                  as String?,
        logprobs: freezed == logprobs
            ? _value.logprobs
            : logprobs // ignore: cast_nullable_to_non_nullable
                  as double?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AIChoiceImpl implements _AIChoice {
  const _$AIChoiceImpl({
    required this.index,
    required this.message,
    this.finishReason,
    this.logprobs,
  });

  factory _$AIChoiceImpl.fromJson(Map<String, dynamic> json) =>
      _$$AIChoiceImplFromJson(json);

  @override
  final int index;
  @override
  final AIMessage message;
  @override
  final String? finishReason;
  @override
  final double? logprobs;

  @override
  String toString() {
    return 'AIChoice(index: $index, message: $message, finishReason: $finishReason, logprobs: $logprobs)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AIChoiceImpl &&
            (identical(other.index, index) || other.index == index) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.finishReason, finishReason) ||
                other.finishReason == finishReason) &&
            (identical(other.logprobs, logprobs) ||
                other.logprobs == logprobs));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, index, message, finishReason, logprobs);

  /// Create a copy of AIChoice
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AIChoiceImplCopyWith<_$AIChoiceImpl> get copyWith =>
      __$$AIChoiceImplCopyWithImpl<_$AIChoiceImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AIChoiceImplToJson(this);
  }
}

abstract class _AIChoice implements AIChoice {
  const factory _AIChoice({
    required final int index,
    required final AIMessage message,
    final String? finishReason,
    final double? logprobs,
  }) = _$AIChoiceImpl;

  factory _AIChoice.fromJson(Map<String, dynamic> json) =
      _$AIChoiceImpl.fromJson;

  @override
  int get index;
  @override
  AIMessage get message;
  @override
  String? get finishReason;
  @override
  double? get logprobs;

  /// Create a copy of AIChoice
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AIChoiceImplCopyWith<_$AIChoiceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AIUsage _$AIUsageFromJson(Map<String, dynamic> json) {
  return _AIUsage.fromJson(json);
}

/// @nodoc
mixin _$AIUsage {
  int get promptTokens => throw _privateConstructorUsedError;
  int get completionTokens => throw _privateConstructorUsedError;
  int get totalTokens => throw _privateConstructorUsedError;
  double? get cost => throw _privateConstructorUsedError;
  String? get currency => throw _privateConstructorUsedError;

  /// Serializes this AIUsage to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AIUsage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AIUsageCopyWith<AIUsage> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AIUsageCopyWith<$Res> {
  factory $AIUsageCopyWith(AIUsage value, $Res Function(AIUsage) then) =
      _$AIUsageCopyWithImpl<$Res, AIUsage>;
  @useResult
  $Res call({
    int promptTokens,
    int completionTokens,
    int totalTokens,
    double? cost,
    String? currency,
  });
}

/// @nodoc
class _$AIUsageCopyWithImpl<$Res, $Val extends AIUsage>
    implements $AIUsageCopyWith<$Res> {
  _$AIUsageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AIUsage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? promptTokens = null,
    Object? completionTokens = null,
    Object? totalTokens = null,
    Object? cost = freezed,
    Object? currency = freezed,
  }) {
    return _then(
      _value.copyWith(
            promptTokens: null == promptTokens
                ? _value.promptTokens
                : promptTokens // ignore: cast_nullable_to_non_nullable
                      as int,
            completionTokens: null == completionTokens
                ? _value.completionTokens
                : completionTokens // ignore: cast_nullable_to_non_nullable
                      as int,
            totalTokens: null == totalTokens
                ? _value.totalTokens
                : totalTokens // ignore: cast_nullable_to_non_nullable
                      as int,
            cost: freezed == cost
                ? _value.cost
                : cost // ignore: cast_nullable_to_non_nullable
                      as double?,
            currency: freezed == currency
                ? _value.currency
                : currency // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AIUsageImplCopyWith<$Res> implements $AIUsageCopyWith<$Res> {
  factory _$$AIUsageImplCopyWith(
    _$AIUsageImpl value,
    $Res Function(_$AIUsageImpl) then,
  ) = __$$AIUsageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int promptTokens,
    int completionTokens,
    int totalTokens,
    double? cost,
    String? currency,
  });
}

/// @nodoc
class __$$AIUsageImplCopyWithImpl<$Res>
    extends _$AIUsageCopyWithImpl<$Res, _$AIUsageImpl>
    implements _$$AIUsageImplCopyWith<$Res> {
  __$$AIUsageImplCopyWithImpl(
    _$AIUsageImpl _value,
    $Res Function(_$AIUsageImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AIUsage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? promptTokens = null,
    Object? completionTokens = null,
    Object? totalTokens = null,
    Object? cost = freezed,
    Object? currency = freezed,
  }) {
    return _then(
      _$AIUsageImpl(
        promptTokens: null == promptTokens
            ? _value.promptTokens
            : promptTokens // ignore: cast_nullable_to_non_nullable
                  as int,
        completionTokens: null == completionTokens
            ? _value.completionTokens
            : completionTokens // ignore: cast_nullable_to_non_nullable
                  as int,
        totalTokens: null == totalTokens
            ? _value.totalTokens
            : totalTokens // ignore: cast_nullable_to_non_nullable
                  as int,
        cost: freezed == cost
            ? _value.cost
            : cost // ignore: cast_nullable_to_non_nullable
                  as double?,
        currency: freezed == currency
            ? _value.currency
            : currency // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AIUsageImpl implements _AIUsage {
  const _$AIUsageImpl({
    required this.promptTokens,
    required this.completionTokens,
    required this.totalTokens,
    this.cost,
    this.currency,
  });

  factory _$AIUsageImpl.fromJson(Map<String, dynamic> json) =>
      _$$AIUsageImplFromJson(json);

  @override
  final int promptTokens;
  @override
  final int completionTokens;
  @override
  final int totalTokens;
  @override
  final double? cost;
  @override
  final String? currency;

  @override
  String toString() {
    return 'AIUsage(promptTokens: $promptTokens, completionTokens: $completionTokens, totalTokens: $totalTokens, cost: $cost, currency: $currency)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AIUsageImpl &&
            (identical(other.promptTokens, promptTokens) ||
                other.promptTokens == promptTokens) &&
            (identical(other.completionTokens, completionTokens) ||
                other.completionTokens == completionTokens) &&
            (identical(other.totalTokens, totalTokens) ||
                other.totalTokens == totalTokens) &&
            (identical(other.cost, cost) || other.cost == cost) &&
            (identical(other.currency, currency) ||
                other.currency == currency));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    promptTokens,
    completionTokens,
    totalTokens,
    cost,
    currency,
  );

  /// Create a copy of AIUsage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AIUsageImplCopyWith<_$AIUsageImpl> get copyWith =>
      __$$AIUsageImplCopyWithImpl<_$AIUsageImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AIUsageImplToJson(this);
  }
}

abstract class _AIUsage implements AIUsage {
  const factory _AIUsage({
    required final int promptTokens,
    required final int completionTokens,
    required final int totalTokens,
    final double? cost,
    final String? currency,
  }) = _$AIUsageImpl;

  factory _AIUsage.fromJson(Map<String, dynamic> json) = _$AIUsageImpl.fromJson;

  @override
  int get promptTokens;
  @override
  int get completionTokens;
  @override
  int get totalTokens;
  @override
  double? get cost;
  @override
  String? get currency;

  /// Create a copy of AIUsage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AIUsageImplCopyWith<_$AIUsageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AIError _$AIErrorFromJson(Map<String, dynamic> json) {
  return _AIError.fromJson(json);
}

/// @nodoc
mixin _$AIError {
  String get code => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  String? get type => throw _privateConstructorUsedError;
  String? get param => throw _privateConstructorUsedError;
  Map<String, dynamic>? get details => throw _privateConstructorUsedError;
  DateTime? get timestamp => throw _privateConstructorUsedError;

  /// Serializes this AIError to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AIError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AIErrorCopyWith<AIError> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AIErrorCopyWith<$Res> {
  factory $AIErrorCopyWith(AIError value, $Res Function(AIError) then) =
      _$AIErrorCopyWithImpl<$Res, AIError>;
  @useResult
  $Res call({
    String code,
    String message,
    String? type,
    String? param,
    Map<String, dynamic>? details,
    DateTime? timestamp,
  });
}

/// @nodoc
class _$AIErrorCopyWithImpl<$Res, $Val extends AIError>
    implements $AIErrorCopyWith<$Res> {
  _$AIErrorCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AIError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
    Object? message = null,
    Object? type = freezed,
    Object? param = freezed,
    Object? details = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(
      _value.copyWith(
            code: null == code
                ? _value.code
                : code // ignore: cast_nullable_to_non_nullable
                      as String,
            message: null == message
                ? _value.message
                : message // ignore: cast_nullable_to_non_nullable
                      as String,
            type: freezed == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as String?,
            param: freezed == param
                ? _value.param
                : param // ignore: cast_nullable_to_non_nullable
                      as String?,
            details: freezed == details
                ? _value.details
                : details // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>?,
            timestamp: freezed == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AIErrorImplCopyWith<$Res> implements $AIErrorCopyWith<$Res> {
  factory _$$AIErrorImplCopyWith(
    _$AIErrorImpl value,
    $Res Function(_$AIErrorImpl) then,
  ) = __$$AIErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String code,
    String message,
    String? type,
    String? param,
    Map<String, dynamic>? details,
    DateTime? timestamp,
  });
}

/// @nodoc
class __$$AIErrorImplCopyWithImpl<$Res>
    extends _$AIErrorCopyWithImpl<$Res, _$AIErrorImpl>
    implements _$$AIErrorImplCopyWith<$Res> {
  __$$AIErrorImplCopyWithImpl(
    _$AIErrorImpl _value,
    $Res Function(_$AIErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AIError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
    Object? message = null,
    Object? type = freezed,
    Object? param = freezed,
    Object? details = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(
      _$AIErrorImpl(
        code: null == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String,
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        type: freezed == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as String?,
        param: freezed == param
            ? _value.param
            : param // ignore: cast_nullable_to_non_nullable
                  as String?,
        details: freezed == details
            ? _value._details
            : details // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>?,
        timestamp: freezed == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AIErrorImpl implements _AIError {
  const _$AIErrorImpl({
    required this.code,
    required this.message,
    this.type,
    this.param,
    final Map<String, dynamic>? details,
    this.timestamp,
  }) : _details = details;

  factory _$AIErrorImpl.fromJson(Map<String, dynamic> json) =>
      _$$AIErrorImplFromJson(json);

  @override
  final String code;
  @override
  final String message;
  @override
  final String? type;
  @override
  final String? param;
  final Map<String, dynamic>? _details;
  @override
  Map<String, dynamic>? get details {
    final value = _details;
    if (value == null) return null;
    if (_details is EqualUnmodifiableMapView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final DateTime? timestamp;

  @override
  String toString() {
    return 'AIError(code: $code, message: $message, type: $type, param: $param, details: $details, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AIErrorImpl &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.param, param) || other.param == param) &&
            const DeepCollectionEquality().equals(other._details, _details) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    code,
    message,
    type,
    param,
    const DeepCollectionEquality().hash(_details),
    timestamp,
  );

  /// Create a copy of AIError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AIErrorImplCopyWith<_$AIErrorImpl> get copyWith =>
      __$$AIErrorImplCopyWithImpl<_$AIErrorImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AIErrorImplToJson(this);
  }
}

abstract class _AIError implements AIError {
  const factory _AIError({
    required final String code,
    required final String message,
    final String? type,
    final String? param,
    final Map<String, dynamic>? details,
    final DateTime? timestamp,
  }) = _$AIErrorImpl;

  factory _AIError.fromJson(Map<String, dynamic> json) = _$AIErrorImpl.fromJson;

  @override
  String get code;
  @override
  String get message;
  @override
  String? get type;
  @override
  String? get param;
  @override
  Map<String, dynamic>? get details;
  @override
  DateTime? get timestamp;

  /// Create a copy of AIError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AIErrorImplCopyWith<_$AIErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AICapabilities _$AICapabilitiesFromJson(Map<String, dynamic> json) {
  return _AICapabilities.fromJson(json);
}

/// @nodoc
mixin _$AICapabilities {
  bool get supportsStreaming => throw _privateConstructorUsedError;
  bool get supportsSystemPrompt => throw _privateConstructorUsedError;
  bool get supportsFunctionCalling => throw _privateConstructorUsedError;
  bool get supportsImageInput => throw _privateConstructorUsedError;
  bool get supportsImageOutput => throw _privateConstructorUsedError;
  bool get supportsAudioInput => throw _privateConstructorUsedError;
  bool get supportsAudioOutput => throw _privateConstructorUsedError;
  bool get supportsCodeExecution => throw _privateConstructorUsedError;
  bool get supportsWebSearch => throw _privateConstructorUsedError;
  List<String> get supportedLanguages => throw _privateConstructorUsedError;
  List<String> get supportedFormats => throw _privateConstructorUsedError;

  /// Serializes this AICapabilities to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AICapabilities
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AICapabilitiesCopyWith<AICapabilities> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AICapabilitiesCopyWith<$Res> {
  factory $AICapabilitiesCopyWith(
    AICapabilities value,
    $Res Function(AICapabilities) then,
  ) = _$AICapabilitiesCopyWithImpl<$Res, AICapabilities>;
  @useResult
  $Res call({
    bool supportsStreaming,
    bool supportsSystemPrompt,
    bool supportsFunctionCalling,
    bool supportsImageInput,
    bool supportsImageOutput,
    bool supportsAudioInput,
    bool supportsAudioOutput,
    bool supportsCodeExecution,
    bool supportsWebSearch,
    List<String> supportedLanguages,
    List<String> supportedFormats,
  });
}

/// @nodoc
class _$AICapabilitiesCopyWithImpl<$Res, $Val extends AICapabilities>
    implements $AICapabilitiesCopyWith<$Res> {
  _$AICapabilitiesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AICapabilities
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? supportsStreaming = null,
    Object? supportsSystemPrompt = null,
    Object? supportsFunctionCalling = null,
    Object? supportsImageInput = null,
    Object? supportsImageOutput = null,
    Object? supportsAudioInput = null,
    Object? supportsAudioOutput = null,
    Object? supportsCodeExecution = null,
    Object? supportsWebSearch = null,
    Object? supportedLanguages = null,
    Object? supportedFormats = null,
  }) {
    return _then(
      _value.copyWith(
            supportsStreaming: null == supportsStreaming
                ? _value.supportsStreaming
                : supportsStreaming // ignore: cast_nullable_to_non_nullable
                      as bool,
            supportsSystemPrompt: null == supportsSystemPrompt
                ? _value.supportsSystemPrompt
                : supportsSystemPrompt // ignore: cast_nullable_to_non_nullable
                      as bool,
            supportsFunctionCalling: null == supportsFunctionCalling
                ? _value.supportsFunctionCalling
                : supportsFunctionCalling // ignore: cast_nullable_to_non_nullable
                      as bool,
            supportsImageInput: null == supportsImageInput
                ? _value.supportsImageInput
                : supportsImageInput // ignore: cast_nullable_to_non_nullable
                      as bool,
            supportsImageOutput: null == supportsImageOutput
                ? _value.supportsImageOutput
                : supportsImageOutput // ignore: cast_nullable_to_non_nullable
                      as bool,
            supportsAudioInput: null == supportsAudioInput
                ? _value.supportsAudioInput
                : supportsAudioInput // ignore: cast_nullable_to_non_nullable
                      as bool,
            supportsAudioOutput: null == supportsAudioOutput
                ? _value.supportsAudioOutput
                : supportsAudioOutput // ignore: cast_nullable_to_non_nullable
                      as bool,
            supportsCodeExecution: null == supportsCodeExecution
                ? _value.supportsCodeExecution
                : supportsCodeExecution // ignore: cast_nullable_to_non_nullable
                      as bool,
            supportsWebSearch: null == supportsWebSearch
                ? _value.supportsWebSearch
                : supportsWebSearch // ignore: cast_nullable_to_non_nullable
                      as bool,
            supportedLanguages: null == supportedLanguages
                ? _value.supportedLanguages
                : supportedLanguages // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            supportedFormats: null == supportedFormats
                ? _value.supportedFormats
                : supportedFormats // ignore: cast_nullable_to_non_nullable
                      as List<String>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AICapabilitiesImplCopyWith<$Res>
    implements $AICapabilitiesCopyWith<$Res> {
  factory _$$AICapabilitiesImplCopyWith(
    _$AICapabilitiesImpl value,
    $Res Function(_$AICapabilitiesImpl) then,
  ) = __$$AICapabilitiesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    bool supportsStreaming,
    bool supportsSystemPrompt,
    bool supportsFunctionCalling,
    bool supportsImageInput,
    bool supportsImageOutput,
    bool supportsAudioInput,
    bool supportsAudioOutput,
    bool supportsCodeExecution,
    bool supportsWebSearch,
    List<String> supportedLanguages,
    List<String> supportedFormats,
  });
}

/// @nodoc
class __$$AICapabilitiesImplCopyWithImpl<$Res>
    extends _$AICapabilitiesCopyWithImpl<$Res, _$AICapabilitiesImpl>
    implements _$$AICapabilitiesImplCopyWith<$Res> {
  __$$AICapabilitiesImplCopyWithImpl(
    _$AICapabilitiesImpl _value,
    $Res Function(_$AICapabilitiesImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AICapabilities
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? supportsStreaming = null,
    Object? supportsSystemPrompt = null,
    Object? supportsFunctionCalling = null,
    Object? supportsImageInput = null,
    Object? supportsImageOutput = null,
    Object? supportsAudioInput = null,
    Object? supportsAudioOutput = null,
    Object? supportsCodeExecution = null,
    Object? supportsWebSearch = null,
    Object? supportedLanguages = null,
    Object? supportedFormats = null,
  }) {
    return _then(
      _$AICapabilitiesImpl(
        supportsStreaming: null == supportsStreaming
            ? _value.supportsStreaming
            : supportsStreaming // ignore: cast_nullable_to_non_nullable
                  as bool,
        supportsSystemPrompt: null == supportsSystemPrompt
            ? _value.supportsSystemPrompt
            : supportsSystemPrompt // ignore: cast_nullable_to_non_nullable
                  as bool,
        supportsFunctionCalling: null == supportsFunctionCalling
            ? _value.supportsFunctionCalling
            : supportsFunctionCalling // ignore: cast_nullable_to_non_nullable
                  as bool,
        supportsImageInput: null == supportsImageInput
            ? _value.supportsImageInput
            : supportsImageInput // ignore: cast_nullable_to_non_nullable
                  as bool,
        supportsImageOutput: null == supportsImageOutput
            ? _value.supportsImageOutput
            : supportsImageOutput // ignore: cast_nullable_to_non_nullable
                  as bool,
        supportsAudioInput: null == supportsAudioInput
            ? _value.supportsAudioInput
            : supportsAudioInput // ignore: cast_nullable_to_non_nullable
                  as bool,
        supportsAudioOutput: null == supportsAudioOutput
            ? _value.supportsAudioOutput
            : supportsAudioOutput // ignore: cast_nullable_to_non_nullable
                  as bool,
        supportsCodeExecution: null == supportsCodeExecution
            ? _value.supportsCodeExecution
            : supportsCodeExecution // ignore: cast_nullable_to_non_nullable
                  as bool,
        supportsWebSearch: null == supportsWebSearch
            ? _value.supportsWebSearch
            : supportsWebSearch // ignore: cast_nullable_to_non_nullable
                  as bool,
        supportedLanguages: null == supportedLanguages
            ? _value._supportedLanguages
            : supportedLanguages // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        supportedFormats: null == supportedFormats
            ? _value._supportedFormats
            : supportedFormats // ignore: cast_nullable_to_non_nullable
                  as List<String>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AICapabilitiesImpl implements _AICapabilities {
  const _$AICapabilitiesImpl({
    this.supportsStreaming = false,
    this.supportsSystemPrompt = false,
    this.supportsFunctionCalling = false,
    this.supportsImageInput = false,
    this.supportsImageOutput = false,
    this.supportsAudioInput = false,
    this.supportsAudioOutput = false,
    this.supportsCodeExecution = false,
    this.supportsWebSearch = false,
    final List<String> supportedLanguages = const [],
    final List<String> supportedFormats = const [],
  }) : _supportedLanguages = supportedLanguages,
       _supportedFormats = supportedFormats;

  factory _$AICapabilitiesImpl.fromJson(Map<String, dynamic> json) =>
      _$$AICapabilitiesImplFromJson(json);

  @override
  @JsonKey()
  final bool supportsStreaming;
  @override
  @JsonKey()
  final bool supportsSystemPrompt;
  @override
  @JsonKey()
  final bool supportsFunctionCalling;
  @override
  @JsonKey()
  final bool supportsImageInput;
  @override
  @JsonKey()
  final bool supportsImageOutput;
  @override
  @JsonKey()
  final bool supportsAudioInput;
  @override
  @JsonKey()
  final bool supportsAudioOutput;
  @override
  @JsonKey()
  final bool supportsCodeExecution;
  @override
  @JsonKey()
  final bool supportsWebSearch;
  final List<String> _supportedLanguages;
  @override
  @JsonKey()
  List<String> get supportedLanguages {
    if (_supportedLanguages is EqualUnmodifiableListView)
      return _supportedLanguages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_supportedLanguages);
  }

  final List<String> _supportedFormats;
  @override
  @JsonKey()
  List<String> get supportedFormats {
    if (_supportedFormats is EqualUnmodifiableListView)
      return _supportedFormats;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_supportedFormats);
  }

  @override
  String toString() {
    return 'AICapabilities(supportsStreaming: $supportsStreaming, supportsSystemPrompt: $supportsSystemPrompt, supportsFunctionCalling: $supportsFunctionCalling, supportsImageInput: $supportsImageInput, supportsImageOutput: $supportsImageOutput, supportsAudioInput: $supportsAudioInput, supportsAudioOutput: $supportsAudioOutput, supportsCodeExecution: $supportsCodeExecution, supportsWebSearch: $supportsWebSearch, supportedLanguages: $supportedLanguages, supportedFormats: $supportedFormats)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AICapabilitiesImpl &&
            (identical(other.supportsStreaming, supportsStreaming) ||
                other.supportsStreaming == supportsStreaming) &&
            (identical(other.supportsSystemPrompt, supportsSystemPrompt) ||
                other.supportsSystemPrompt == supportsSystemPrompt) &&
            (identical(
                  other.supportsFunctionCalling,
                  supportsFunctionCalling,
                ) ||
                other.supportsFunctionCalling == supportsFunctionCalling) &&
            (identical(other.supportsImageInput, supportsImageInput) ||
                other.supportsImageInput == supportsImageInput) &&
            (identical(other.supportsImageOutput, supportsImageOutput) ||
                other.supportsImageOutput == supportsImageOutput) &&
            (identical(other.supportsAudioInput, supportsAudioInput) ||
                other.supportsAudioInput == supportsAudioInput) &&
            (identical(other.supportsAudioOutput, supportsAudioOutput) ||
                other.supportsAudioOutput == supportsAudioOutput) &&
            (identical(other.supportsCodeExecution, supportsCodeExecution) ||
                other.supportsCodeExecution == supportsCodeExecution) &&
            (identical(other.supportsWebSearch, supportsWebSearch) ||
                other.supportsWebSearch == supportsWebSearch) &&
            const DeepCollectionEquality().equals(
              other._supportedLanguages,
              _supportedLanguages,
            ) &&
            const DeepCollectionEquality().equals(
              other._supportedFormats,
              _supportedFormats,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    supportsStreaming,
    supportsSystemPrompt,
    supportsFunctionCalling,
    supportsImageInput,
    supportsImageOutput,
    supportsAudioInput,
    supportsAudioOutput,
    supportsCodeExecution,
    supportsWebSearch,
    const DeepCollectionEquality().hash(_supportedLanguages),
    const DeepCollectionEquality().hash(_supportedFormats),
  );

  /// Create a copy of AICapabilities
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AICapabilitiesImplCopyWith<_$AICapabilitiesImpl> get copyWith =>
      __$$AICapabilitiesImplCopyWithImpl<_$AICapabilitiesImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$AICapabilitiesImplToJson(this);
  }
}

abstract class _AICapabilities implements AICapabilities {
  const factory _AICapabilities({
    final bool supportsStreaming,
    final bool supportsSystemPrompt,
    final bool supportsFunctionCalling,
    final bool supportsImageInput,
    final bool supportsImageOutput,
    final bool supportsAudioInput,
    final bool supportsAudioOutput,
    final bool supportsCodeExecution,
    final bool supportsWebSearch,
    final List<String> supportedLanguages,
    final List<String> supportedFormats,
  }) = _$AICapabilitiesImpl;

  factory _AICapabilities.fromJson(Map<String, dynamic> json) =
      _$AICapabilitiesImpl.fromJson;

  @override
  bool get supportsStreaming;
  @override
  bool get supportsSystemPrompt;
  @override
  bool get supportsFunctionCalling;
  @override
  bool get supportsImageInput;
  @override
  bool get supportsImageOutput;
  @override
  bool get supportsAudioInput;
  @override
  bool get supportsAudioOutput;
  @override
  bool get supportsCodeExecution;
  @override
  bool get supportsWebSearch;
  @override
  List<String> get supportedLanguages;
  @override
  List<String> get supportedFormats;

  /// Create a copy of AICapabilities
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AICapabilitiesImplCopyWith<_$AICapabilitiesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
