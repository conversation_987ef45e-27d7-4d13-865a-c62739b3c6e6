import 'package:flutter/material.dart';
import '../../domain/entities/prompt_template.dart';

/// 模板列表项
class TemplateListItem extends StatelessWidget {
  final PromptTemplate template;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDuplicate;
  final VoidCallback? onDelete;
  final VoidCallback? onUse;

  const TemplateListItem({
    super.key,
    required this.template,
    this.onTap,
    this.onEdit,
    this.onDuplicate,
    this.onDelete,
    this.onUse,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context),
              const SizedBox(height: 8),
              _buildDescription(),
              const SizedBox(height: 8),
              _buildMetadata(context),
              if (template.tags.isNotEmpty) ...[
                const SizedBox(height: 8),
                _buildTags(),
              ],
              const SizedBox(height: 12),
              _buildActions(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                template.name,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  _buildCategoryChip(),
                  const SizedBox(width: 8),
                  if (template.isBuiltIn)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade100,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '内置',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.blue.shade700,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
        _buildUsageInfo(context),
      ],
    );
  }

  /// 构建分类标签
  Widget _buildCategoryChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getCategoryColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getCategoryColor().withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        template.category.displayName,
        style: TextStyle(
          fontSize: 12,
          color: _getCategoryColor(),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 获取分类颜色
  Color _getCategoryColor() {
    switch (template.category) {
      case TemplateCategory.character:
        return Colors.purple;
      case TemplateCategory.plot:
        return Colors.orange;
      case TemplateCategory.dialogue:
        return Colors.green;
      case TemplateCategory.scene:
        return Colors.blue;
      case TemplateCategory.worldBuilding:
        return Colors.red;
      case TemplateCategory.polish:
        return Colors.teal;
      case TemplateCategory.inspiration:
        return Colors.pink;
      case TemplateCategory.writing:
        return Colors.indigo;
      case TemplateCategory.custom:
        return Colors.grey;
    }
  }

  /// 构建使用信息
  Widget _buildUsageInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.play_arrow, size: 16, color: Colors.grey),
            const SizedBox(width: 4),
            Text(
              template.usageCount.toString(),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          _formatDate(template.updatedAt),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey.shade500,
          ),
        ),
      ],
    );
  }

  /// 构建描述
  Widget _buildDescription() {
    if (template.description.isEmpty) {
      return const SizedBox.shrink();
    }

    return Text(
      template.description,
      style: const TextStyle(
        color: Colors.grey,
        fontSize: 14,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// 构建元数据
  Widget _buildMetadata(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.tune,
          size: 16,
          color: Colors.grey.shade600,
        ),
        const SizedBox(width: 4),
        Text(
          '${template.variables.length} 个变量',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey.shade600,
          ),
        ),
        const SizedBox(width: 16),
        Icon(
          Icons.text_fields,
          size: 16,
          color: Colors.grey.shade600,
        ),
        const SizedBox(width: 4),
        Text(
          '${template.content.length} 字符',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  /// 构建标签
  Widget _buildTags() {
    return Wrap(
      spacing: 4,
      runSpacing: 4,
      children: template.tags.take(3).map((tag) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            tag,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade700,
            ),
          ),
        );
      }).toList(),
    );
  }

  /// 构建操作按钮
  Widget _buildActions(BuildContext context) {
    return Row(
      children: [
        if (onUse != null)
          TextButton.icon(
            onPressed: onUse,
            icon: const Icon(Icons.play_arrow, size: 16),
            label: const Text('使用'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.green,
              padding: const EdgeInsets.symmetric(horizontal: 8),
            ),
          ),
        if (onEdit != null && !template.isBuiltIn)
          TextButton.icon(
            onPressed: onEdit,
            icon: const Icon(Icons.edit, size: 16),
            label: const Text('编辑'),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 8),
            ),
          ),
        if (onDuplicate != null)
          TextButton.icon(
            onPressed: onDuplicate,
            icon: const Icon(Icons.copy, size: 16),
            label: const Text('复制'),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 8),
            ),
          ),
        const Spacer(),
        PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(value, context),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'details',
              child: ListTile(
                leading: Icon(Icons.info),
                title: Text('详情'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'export',
              child: ListTile(
                leading: Icon(Icons.file_download),
                title: Text('导出'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            if (onDelete != null)
              const PopupMenuItem(
                value: 'delete',
                child: ListTile(
                  leading: Icon(Icons.delete, color: Colors.red),
                  title: Text('删除', style: TextStyle(color: Colors.red)),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
          ],
          child: const Icon(Icons.more_vert, size: 20),
        ),
      ],
    );
  }

  /// 处理菜单操作
  void _handleMenuAction(String action, BuildContext context) {
    switch (action) {
      case 'details':
        onTap?.call();
        break;
      case 'export':
        _exportTemplate(context);
        break;
      case 'delete':
        onDelete?.call();
        break;
    }
  }

  /// 导出模板
  void _exportTemplate(BuildContext context) {
    // 这里应该实现导出功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('导出功能开发中...')),
    );
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        if (difference.inMinutes == 0) {
          return '刚刚';
        }
        return '${difference.inMinutes}分钟前';
      }
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else if (difference.inDays < 30) {
      return '${(difference.inDays / 7).floor()}周前';
    } else if (difference.inDays < 365) {
      return '${(difference.inDays / 30).floor()}个月前';
    } else {
      return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    }
  }
}