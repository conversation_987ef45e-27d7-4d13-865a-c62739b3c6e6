import 'package:flutter/material.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import 'logger.dart';

/// 日志查看器页面
class LogViewerPage extends ConsumerStatefulWidget {
  const LogViewerPage({super.key});

  @override
  ConsumerState<LogViewerPage> createState() => _LogViewerPageState();
}

class _LogViewerPageState extends ConsumerState<LogViewerPage>
    with TickerProviderStateMixin {
  final AppLogger _logger = AppLogger();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  StreamSubscription<LogEntry>? _logSubscription;
  List<LogEntry> _filteredLogs = [];
  LogLevel? _selectedLevel;
  bool _autoScroll = true;
  bool _showDetails = false;
  LogEntry? _selectedLogEntry;

  @override
  void initState() {
    super.initState();
    _loadLogs();
    _subscribeToLogs();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _logSubscription?.cancel();
    super.dispose();
  }

  void _loadLogs() {
    setState(() {
      _filteredLogs = _logger.logBuffer;
    });
  }

  void _subscribeToLogs() {
    _logSubscription = _logger.logStream.listen((logEntry) {
      if (mounted) {
        setState(() {
          _filteredLogs = _logger.searchLogs(
            query: _searchController.text,
            level: _selectedLevel,
          );
        });
        
        if (_autoScroll && _scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      }
    });
  }

  void _filterLogs() {
    setState(() {
      _filteredLogs = _logger.searchLogs(
        query: _searchController.text,
        level: _selectedLevel,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return fluent.ScaffoldPage(
      header: fluent.PageHeader(
        title: const Text('日志查看器'),
        commandBar: fluent.CommandBar(
          primaryItems: [
            fluent.CommandBarButton(
              icon: const Icon(fluent.FluentIcons.refresh),
              label: const Text('刷新'),
              onPressed: _loadLogs,
            ),
            fluent.CommandBarButton(
              icon: const Icon(fluent.FluentIcons.clear),
              label: const Text('清空'),
              onPressed: _clearLogs,
            ),
            fluent.CommandBarButton(
              icon: Icon(_autoScroll ? fluent.FluentIcons.pause : fluent.FluentIcons.play),
              label: Text(_autoScroll ? '停止滚动' : '自动滚动'),
              onPressed: () {
                setState(() {
                  _autoScroll = !_autoScroll;
                });
              },
            ),
          ],
          secondaryItems: [
            fluent.CommandBarButton(
              icon: const Icon(fluent.FluentIcons.download),
              label: const Text('导出'),
              onPressed: _exportLogs,
            ),
            fluent.CommandBarButton(
              icon: const Icon(fluent.FluentIcons.settings),
              label: const Text('设置'),
              onPressed: _showSettings,
            ),
          ],
        ),
      ),
      content: Column(
        children: [
          // 过滤器栏
          _buildFilterBar(),
          const SizedBox(height: 8),
          
          // 统计信息
          _buildStatisticsBar(),
          const SizedBox(height: 8),
          
          // 日志内容
          Expanded(
            child: Row(
              children: [
                // 日志列表
                Expanded(
                  flex: _showDetails ? 2 : 1,
                  child: _buildLogList(),
                ),
                
                // 详情面板
                if (_showDetails) ...[
                  const VerticalDivider(width: 1),
                  Expanded(
                    flex: 1,
                    child: _buildDetailsPanel(),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建过滤器栏
  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 搜索框
          Expanded(
            flex: 2,
            child: fluent.TextBox(
              controller: _searchController,
              placeholder: '搜索日志内容...',
              prefix: const Icon(fluent.FluentIcons.search),
              suffix: _searchController.text.isNotEmpty
                  ? fluent.IconButton(
                      icon: const Icon(fluent.FluentIcons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _filterLogs();
                      },
                    )
                  : null,
              onChanged: (_) => _filterLogs(),
            ),
          ),
          const SizedBox(width: 16),
          
          // 级别过滤器
          SizedBox(
            width: 150,
            child: fluent.ComboBox<LogLevel?>(
              value: _selectedLevel,
              placeholder: const Text('所有级别'),
              items: [
                const fluent.ComboBoxItem<LogLevel?>(
                  value: null,
                  child: Text('所有级别'),
                ),
                ...LogLevel.values.map((level) => fluent.ComboBoxItem<LogLevel?>(
                  value: level,
                  child: Text(level.displayName),
                )),
              ],
              onChanged: (level) {
                setState(() {
                  _selectedLevel = level;
                });
                _filterLogs();
              },
            ),
          ),
          const SizedBox(width: 16),
          
          // 详情切换
          fluent.ToggleButton(
            checked: _showDetails,
            onChanged: (checked) {
              setState(() {
                _showDetails = checked;
              });
            },
            child: const Text('详情'),
          ),
        ],
      ),
    );
  }

  /// 构建统计信息栏
  Widget _buildStatisticsBar() {
    final statistics = _logger.getStatistics();
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          _buildStatChip('总计', statistics.totalLogs.toString(), Colors.blue),
          const SizedBox(width: 8),
          _buildStatChip('24小时', statistics.logs24Hours.toString(), Colors.green),
          const SizedBox(width: 8),
          _buildStatChip('当前显示', _filteredLogs.length.toString(), Colors.orange),
          const Spacer(),
          
          // 级别统计
          ...statistics.logsByLevel.entries.map((entry) => 
            Padding(
              padding: const EdgeInsets.only(left: 8),
              child: _buildStatChip(
                entry.key.displayName,
                entry.value.toString(),
                entry.key.color,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建统计标签
  Widget _buildStatChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建日志列表
  Widget _buildLogList() {
    if (_filteredLogs.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(fluent.FluentIcons.info, size: 48, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              '没有找到匹配的日志',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      itemCount: _filteredLogs.length,
      itemBuilder: (context, index) {
        final logEntry = _filteredLogs[index];
        return _buildLogItem(logEntry, index);
      },
    );
  }

  /// 构建日志项
  Widget _buildLogItem(LogEntry logEntry, int index) {
    final isSelected = _selectedLogEntry == logEntry;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: isSelected ? logEntry.level.color.withValues(alpha: 0.1) : null,
        borderRadius: BorderRadius.circular(4),
        border: isSelected ? Border.all(color: logEntry.level.color.withValues(alpha: 0.3)) : null,
      ),
      child: fluent.ListTile(
        leading: Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: logEntry.level.color,
            shape: BoxShape.circle,
          ),
        ),
        title: Text(
          logEntry.message,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        subtitle: Row(
          children: [
            Text(
              _formatTimestamp(logEntry.timestamp),
              style: const TextStyle(fontSize: 11, color: Colors.grey),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: logEntry.level.color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                logEntry.level.displayName,
                style: TextStyle(
                  fontSize: 10,
                  color: logEntry.level.color,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            if (logEntry.error != null) ...[
              const SizedBox(width: 8),
              const Icon(
                fluent.FluentIcons.error,
                size: 12,
                color: Colors.red,
              ),
            ],
          ],
        ),
        onPressed: () {
          setState(() {
            _selectedLogEntry = logEntry;
            _showDetails = true;
          });
        },
      ),
    );
  }

  /// 构建详情面板
  Widget _buildDetailsPanel() {
    if (_selectedLogEntry == null) {
      return const Center(
        child: Text(
          '选择一个日志条目查看详情',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    final logEntry = _selectedLogEntry!;
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: logEntry.level.color,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '日志详情',
                style: fluent.FluentTheme.of(context).typography.subtitle,
              ),
              const Spacer(),
              fluent.IconButton(
                icon: const Icon(fluent.FluentIcons.copy),
                onPressed: () => _copyLogEntry(logEntry),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // 详情内容
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDetailRow('时间', _formatFullTimestamp(logEntry.timestamp)),
                  _buildDetailRow('级别', logEntry.level.displayName),
                  _buildDetailRow('消息', logEntry.message),
                  
                  if (logEntry.error != null) ...[
                    const SizedBox(height: 16),
                    const Text(
                      '错误信息',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                      ),
                      child: Text(
                        logEntry.error.toString(),
                        style: const TextStyle(fontFamily: 'monospace'),
                      ),
                    ),
                  ],
                  
                  if (logEntry.stackTrace != null) ...[
                    const SizedBox(height: 16),
                    const Text(
                      '堆栈跟踪',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                      ),
                      child: Text(
                        logEntry.stackTrace.toString(),
                        style: const TextStyle(
                          fontFamily: 'monospace',
                          fontSize: 11,
                        ),
                      ),
                    ),
                  ],
                  
                  if (logEntry.data != null && logEntry.data!.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    const Text(
                      '附加数据',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: logEntry.data!.entries.map((entry) =>
                          Padding(
                            padding: const EdgeInsets.only(bottom: 4),
                            child: Text(
                              '${entry.key}: ${entry.value}',
                              style: const TextStyle(fontFamily: 'monospace'),
                            ),
                          ),
                        ).toList(),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建详情行
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 60,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontFamily: 'monospace'),
            ),
          ),
        ],
      ),
    );
  }

  /// 格式化时间戳
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 格式化完整时间戳
  String _formatFullTimestamp(DateTime timestamp) {
    return '${timestamp.year}-${timestamp.month.toString().padLeft(2, '0')}-'
        '${timestamp.day.toString().padLeft(2, '0')} '
        '${timestamp.hour.toString().padLeft(2, '0')}:'
        '${timestamp.minute.toString().padLeft(2, '0')}:'
        '${timestamp.second.toString().padLeft(2, '0')}';
  }

  /// 清空日志
  void _clearLogs() {
    showDialog(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: const Text('清空日志'),
        content: const Text('确定要清空所有日志吗？此操作无法撤销。'),
        actions: [
          fluent.Button(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          fluent.FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              _logger.clearLogs();
              _loadLogs();
            },
            child: const Text('清空'),
          ),
        ],
      ),
    );
  }

  /// 导出日志
  void _exportLogs() async {
    try {
      await _logger.exportLogs();

      // 这里应该保存到文件
      if (mounted) {
        fluent.displayInfoBar(
          context,
          builder: (context, close) => fluent.InfoBar(
            title: const Text('导出成功'),
            content: const Text('日志已导出'),
            severity: fluent.InfoBarSeverity.success,
            action: fluent.IconButton(
              icon: const Icon(fluent.FluentIcons.clear),
              onPressed: close,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        fluent.displayInfoBar(
          context,
          builder: (context, close) => fluent.InfoBar(
            title: const Text('导出失败'),
            content: Text(e.toString()),
            severity: fluent.InfoBarSeverity.error,
            action: fluent.IconButton(
              icon: const Icon(fluent.FluentIcons.clear),
              onPressed: close,
            ),
          ),
        );
      }
    }
  }

  /// 复制日志条目
  void _copyLogEntry(LogEntry logEntry) {
    // 这里应该复制到剪贴板
    fluent.displayInfoBar(
      context,
      builder: (context, close) => fluent.InfoBar(
        title: const Text('已复制'),
        content: const Text('日志内容已复制到剪贴板'),
        severity: fluent.InfoBarSeverity.info,
        action: fluent.IconButton(
          icon: const Icon(fluent.FluentIcons.clear),
          onPressed: close,
        ),
      ),
    );
  }

  /// 显示设置
  void _showSettings() {
    showDialog(
      context: context,
      builder: (context) => const LogSettingsDialog(),
    );
  }
}

/// 日志设置对话框
class LogSettingsDialog extends StatefulWidget {
  const LogSettingsDialog({super.key});

  @override
  State<LogSettingsDialog> createState() => _LogSettingsDialogState();
}

class _LogSettingsDialogState extends State<LogSettingsDialog> {
  final AppLogger _logger = AppLogger();
  LogLevel _selectedMinLevel = LogLevel.info;

  @override
  Widget build(BuildContext context) {
    return fluent.ContentDialog(
      title: const Text('日志设置'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('最小日志级别'),
          const SizedBox(height: 8),
          fluent.ComboBox<LogLevel>(
            value: _selectedMinLevel,
            items: LogLevel.values.map((level) => fluent.ComboBoxItem<LogLevel>(
              value: level,
              child: Text(level.displayName),
            )).toList(),
            onChanged: (level) {
              if (level != null) {
                setState(() {
                  _selectedMinLevel = level;
                });
              }
            },
          ),
          const SizedBox(height: 16),
          
          fluent.Button(
            onPressed: _cleanupOldLogs,
            child: const Text('清理旧日志文件'),
          ),
        ],
      ),
      actions: [
        fluent.Button(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        fluent.FilledButton(
          onPressed: () {
            _logger.setMinLogLevel(_selectedMinLevel);
            Navigator.of(context).pop();
          },
          child: const Text('保存'),
        ),
      ],
    );
  }

  void _cleanupOldLogs() async {
    try {
      await _logger.cleanupOldLogFiles();
      if (mounted) {
        fluent.displayInfoBar(
          context,
          builder: (context, close) => fluent.InfoBar(
            title: const Text('清理完成'),
            content: const Text('旧日志文件已清理'),
            severity: fluent.InfoBarSeverity.success,
            action: fluent.IconButton(
              icon: const Icon(fluent.FluentIcons.clear),
              onPressed: close,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        fluent.displayInfoBar(
          context,
          builder: (context, close) => fluent.InfoBar(
            title: const Text('清理失败'),
            content: Text(e.toString()),
            severity: fluent.InfoBarSeverity.error,
            action: fluent.IconButton(
              icon: const Icon(fluent.FluentIcons.clear),
              onPressed: close,
            ),
          ),
        );
      }
    }
  }
}