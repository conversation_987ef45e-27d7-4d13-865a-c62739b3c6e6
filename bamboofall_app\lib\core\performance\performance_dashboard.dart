import 'package:flutter/material.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import 'performance_monitor.dart';
import 'memory_manager.dart';
import 'cache_manager.dart';

/// 性能仪表板页面
class PerformanceDashboard extends ConsumerStatefulWidget {
  const PerformanceDashboard({super.key});

  @override
  ConsumerState<PerformanceDashboard> createState() => _PerformanceDashboardState();
}

class _PerformanceDashboardState extends ConsumerState<PerformanceDashboard>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final PerformanceMonitor _performanceMonitor = PerformanceMonitor();
  final MemoryManager _memoryManager = MemoryManager();
  final CacheManager _cacheManager = CacheManager();
  
  StreamSubscription<PerformanceMetrics>? _metricsSubscription;
  StreamSubscription<MemoryInfo>? _memorySubscription;
  
  PerformanceMetrics? _latestMetrics;
  MemoryInfo? _latestMemoryInfo;
  bool _isMonitoring = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeMonitoring();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _metricsSubscription?.cancel();
    _memorySubscription?.cancel();
    super.dispose();
  }

  void _initializeMonitoring() {
    _performanceMonitor.initialize();
    
    _metricsSubscription = _performanceMonitor.metricsStream.listen((metrics) {
      if (mounted) {
        setState(() {
          _latestMetrics = metrics;
        });
      }
    });
    
    _memorySubscription = _memoryManager.memoryInfoStream.listen((memoryInfo) {
      if (mounted) {
        setState(() {
          _latestMemoryInfo = memoryInfo;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return fluent.ScaffoldPage(
      header: fluent.PageHeader(
        title: const Text('性能监控'),
        commandBar: fluent.CommandBar(
          primaryItems: [
            fluent.CommandBarButton(
              icon: Icon(_isMonitoring ? fluent.FluentIcons.pause : fluent.FluentIcons.play),
              label: Text(_isMonitoring ? '停止监控' : '开始监控'),
              onPressed: _toggleMonitoring,
            ),
            fluent.CommandBarButton(
              icon: const Icon(fluent.FluentIcons.refresh),
              label: const Text('刷新'),
              onPressed: _refreshData,
            ),
            fluent.CommandBarButton(
              icon: const Icon(fluent.FluentIcons.clear_formatting),
              label: const Text('优化'),
              onPressed: _performOptimization,
            ),
          ],
          secondaryItems: [
            fluent.CommandBarButton(
              icon: const Icon(fluent.FluentIcons.download),
              label: const Text('导出报告'),
              onPressed: _exportReport,
            ),
          ],
        ),
      ),
      content: Column(
        children: [
          // 状态概览
          _buildStatusOverview(),
          const SizedBox(height: 16),
          
          // 标签页
          Expanded(
            child: fluent.TabView(
              currentIndex: 0,
              tabs: [
                fluent.Tab(
                  text: const Text('实时监控'),
                  body: _buildRealtimeTab(),
                ),
                fluent.Tab(
                  text: const Text('内存分析'),
                  body: _buildMemoryTab(),
                ),
                fluent.Tab(
                  text: const Text('缓存状态'),
                  body: _buildCacheTab(),
                ),
                fluent.Tab(
                  text: const Text('历史统计'),
                  body: _buildHistoryTab(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建状态概览
  Widget _buildStatusOverview() {
    final healthStatus = _performanceMonitor.getHealthStatus();
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 健康状态指示器
          _buildHealthIndicator(healthStatus),
          const SizedBox(width: 24),
          
          // 关键指标
          Expanded(
            child: Row(
              children: [
                _buildMetricCard(
                  '内存使用',
                  '${_latestMemoryInfo?.processMemoryMB.toStringAsFixed(1) ?? '0'} MB',
                  fluent.FluentIcons.hard_drive,
                  _getMemoryColor(),
                ),
                const SizedBox(width: 16),
                _buildMetricCard(
                  '帧率',
                  '${_latestMetrics?.averageFPS.toStringAsFixed(1) ?? '0'} FPS',
                  fluent.FluentIcons.speed_high,
                  _getFPSColor(),
                ),
                const SizedBox(width: 16),
                _buildMetricCard(
                  'CPU使用',
                  '${_latestMetrics?.cpuUsagePercent.toStringAsFixed(1) ?? '0'}%',
                  fluent.FluentIcons.processing,
                  _getCPUColor(),
                ),
                const SizedBox(width: 16),
                _buildMetricCard(
                  '缓存命中率',
                  '${(_latestMetrics?.cacheHitRate ?? 0 * 100).toStringAsFixed(1)}%',
                  fluent.FluentIcons.database_source,
                  _getCacheColor(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建健康指示器
  Widget _buildHealthIndicator(PerformanceHealthStatus status) {
    Color color;
    IconData icon;
    
    switch (status) {
      case PerformanceHealthStatus.healthy:
        color = Colors.green;
        icon = fluent.FluentIcons.completed_solid;
        break;
      case PerformanceHealthStatus.warning:
        color = Colors.orange;
        icon = fluent.FluentIcons.warning;
        break;
      case PerformanceHealthStatus.critical:
        color = Colors.red;
        icon = fluent.FluentIcons.error_badge;
        break;
      default:
        color = Colors.grey;
        icon = fluent.FluentIcons.unknown_solid;
    }
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            status.displayName,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建指标卡片
  Widget _buildMetricCard(String title, String value, IconData icon, Color color) {
    return Expanded(
      child: fluent.Card(
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(icon, color: color, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建实时监控标签页
  Widget _buildRealtimeTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 实时图表区域
          Expanded(
            flex: 2,
            child: Row(
              children: [
                Expanded(
                  child: _buildRealtimeChart('内存使用 (MB)', _getMemoryData()),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildRealtimeChart('帧率 (FPS)', _getFPSData()),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          
          // 详细信息
          Expanded(
            flex: 1,
            child: Row(
              children: [
                Expanded(
                  child: _buildDetailCard('性能详情', _buildPerformanceDetails()),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDetailCard('系统信息', _buildSystemInfo()),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建内存分析标签页
  Widget _buildMemoryTab() {
    final memoryStats = _memoryManager.getMemoryStatistics();
    
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 内存统计卡片
          Row(
            children: [
              _buildStatCard('当前内存', '${memoryStats.currentMemoryMB.toStringAsFixed(1)} MB'),
              const SizedBox(width: 16),
              _buildStatCard('平均内存', '${memoryStats.averageMemoryMB.toStringAsFixed(1)} MB'),
              const SizedBox(width: 16),
              _buildStatCard('最大内存', '${memoryStats.maxMemoryMB.toStringAsFixed(1)} MB'),
              const SizedBox(width: 16),
              _buildStatCard('监控时长', _formatDuration(memoryStats.monitoringDuration)),
            ],
          ),
          const SizedBox(height: 16),
          
          // 内存趋势图
          Expanded(
            child: fluent.Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '内存使用趋势',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                    ),
                    const SizedBox(height: 16),
                    Expanded(
                      child: _buildMemoryTrendChart(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建缓存状态标签页
  Widget _buildCacheTab() {
    final cacheStats = _cacheManager.getStatistics();
    
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 缓存统计
          Row(
            children: [
              _buildStatCard('缓存存储', '${cacheStats.storeCount}'),
              const SizedBox(width: 16),
              _buildStatCard('总缓存项', '${cacheStats.totalEntries}'),
              const SizedBox(width: 16),
              _buildStatCard('命中率', '${(cacheStats.hitRate * 100).toStringAsFixed(1)}%'),
              const SizedBox(width: 16),
              _buildStatCard('缓存大小', '${cacheStats.estimatedTotalSizeMB.toStringAsFixed(1)} MB'),
            ],
          ),
          const SizedBox(height: 16),
          
          // 缓存详情
          Expanded(
            child: _buildCacheDetails(),
          ),
        ],
      ),
    );
  }

  /// 构建历史统计标签页
  Widget _buildHistoryTab() {
    final perfStats = _performanceMonitor.getStatistics();
    
    return Padding(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '性能统计报告',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),
            
            _buildStatisticsSection('监控概览', [
              '监控时长: ${_formatDuration(perfStats.monitoringDuration)}',
              '数据点数: ${perfStats.snapshotCount}',
            ]),
            
            _buildStatisticsSection('内存统计', [
              '平均内存: ${perfStats.averageMemoryMB.toStringAsFixed(1)} MB',
              '最大内存: ${perfStats.maxMemoryMB.toStringAsFixed(1)} MB',
            ]),
            
            _buildStatisticsSection('渲染性能', [
              '平均帧率: ${perfStats.averageFPS.toStringAsFixed(1)} FPS',
              '最低帧率: ${perfStats.minFPS.toStringAsFixed(1)} FPS',
              '平均帧时间: ${perfStats.averageFrameTimeMs.toStringAsFixed(1)} ms',
              '最大帧时间: ${perfStats.maxFrameTimeMs.toStringAsFixed(1)} ms',
              '卡顿帧数: ${perfStats.totalJankFrames}',
            ]),
            
            _buildStatisticsSection('CPU统计', [
              '平均CPU: ${perfStats.averageCPUPercent.toStringAsFixed(1)}%',
              '最大CPU: ${perfStats.maxCPUPercent.toStringAsFixed(1)}%',
            ]),
          ],
        ),
      ),
    );
  }

  /// 构建实时图表
  Widget _buildRealtimeChart(String title, List<double> data) {
    return fluent.Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Center(
                  child: Text(
                    '图表区域\n(需要图表库支持)',
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建详情卡片
  Widget _buildDetailCard(String title, Widget content) {
    return fluent.Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            Expanded(child: content),
          ],
        ),
      ),
    );
  }

  /// 构建性能详情
  Widget _buildPerformanceDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailRow('内存使用', '${_latestMemoryInfo?.processMemoryMB.toStringAsFixed(1) ?? '0'} MB'),
        _buildDetailRow('VM堆内存', '${_latestMemoryInfo?.dartVMMemoryMB.toStringAsFixed(1) ?? '0'} MB'),
        _buildDetailRow('外部内存', '${_latestMemoryInfo?.externalMemoryMB.toStringAsFixed(1) ?? '0'} MB'),
        _buildDetailRow('帧率', '${_latestMetrics?.averageFPS.toStringAsFixed(1) ?? '0'} FPS'),
        _buildDetailRow('帧时间', '${_latestMetrics?.frameTimeMs.toStringAsFixed(1) ?? '0'} ms'),
        _buildDetailRow('卡顿帧', '${_latestMetrics?.jankFrameCount ?? 0}'),
      ],
    );
  }

  /// 构建系统信息
  Widget _buildSystemInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailRow('监控状态', _isMonitoring ? '运行中' : '已停止'),
        _buildDetailRow('监控时长', _formatDuration(_performanceMonitor.startTime != null 
            ? DateTime.now().difference(_performanceMonitor.startTime!) 
            : Duration.zero)),
        _buildDetailRow('数据点数', '${_performanceMonitor.snapshots.length}'),
        _buildDetailRow('健康状态', _performanceMonitor.getHealthStatus().displayName),
      ],
    );
  }

  /// 构建统计卡片
  Widget _buildStatCard(String title, String value) {
    return Expanded(
      child: fluent.Card(
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            children: [
              Text(
                title,
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建内存趋势图
  Widget _buildMemoryTrendChart() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Center(
        child: Text(
          '内存趋势图\n(需要图表库支持)',
          textAlign: TextAlign.center,
          style: TextStyle(color: Colors.grey),
        ),
      ),
    );
  }

  /// 构建缓存详情
  Widget _buildCacheDetails() {
    return fluent.Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '缓存存储详情',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView(
                children: [
                  _buildCacheStoreItem('默认缓存', 'default'),
                  _buildCacheStoreItem('图片缓存', 'images'),
                  _buildCacheStoreItem('AI响应缓存', 'ai_responses'),
                  _buildCacheStoreItem('文档缓存', 'documents'),
                  _buildCacheStoreItem('模板缓存', 'templates'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建缓存存储项
  Widget _buildCacheStoreItem(String displayName, String storeName) {
    final store = _cacheManager.getStore(storeName);
    final stats = store.getStatistics();
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  displayName,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 4),
                Text(
                  '${stats.entryCount}/${stats.maxSize} 项 | 命中率: ${(stats.hitRate * 100).toStringAsFixed(1)}%',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
          ),
          Text(
            '${stats.estimatedSizeMB.toStringAsFixed(1)} MB',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  /// 构建统计部分
  Widget _buildStatisticsSection(String title, List<String> items) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: fluent.Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 12),
              ...items.map((item) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Text(item),
              )),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建详情行
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(color: Colors.grey)),
          Text(value, style: const TextStyle(fontWeight: FontWeight.w500)),
        ],
      ),
    );
  }

  /// 获取内存数据
  List<double> _getMemoryData() {
    return _memoryManager.snapshots
        .map((s) => s.memoryInfo.processMemoryMB)
        .toList();
  }

  /// 获取帧率数据
  List<double> _getFPSData() {
    return _performanceMonitor.snapshots
        .map((s) => s.metrics.averageFPS)
        .toList();
  }

  /// 获取内存颜色
  Color _getMemoryColor() {
    final memory = _latestMemoryInfo?.processMemoryMB ?? 0;
    if (memory > 500) return Colors.red;
    if (memory > 400) return Colors.orange;
    return Colors.green;
  }

  /// 获取帧率颜色
  Color _getFPSColor() {
    final fps = _latestMetrics?.averageFPS ?? 60;
    if (fps < 30) return Colors.red;
    if (fps < 45) return Colors.orange;
    return Colors.green;
  }

  /// 获取CPU颜色
  Color _getCPUColor() {
    final cpu = _latestMetrics?.cpuUsagePercent ?? 0;
    if (cpu > 80) return Colors.red;
    if (cpu > 60) return Colors.orange;
    return Colors.green;
  }

  /// 获取缓存颜色
  Color _getCacheColor() {
    final hitRate = _latestMetrics?.cacheHitRate ?? 0;
    if (hitRate < 0.3) return Colors.red;
    if (hitRate < 0.6) return Colors.orange;
    return Colors.green;
  }

  /// 格式化时长
  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m ${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  /// 切换监控状态
  void _toggleMonitoring() {
    setState(() {
      if (_isMonitoring) {
        _performanceMonitor.stopMonitoring();
        _isMonitoring = false;
      } else {
        _performanceMonitor.startMonitoring();
        _isMonitoring = true;
      }
    });
  }

  /// 刷新数据
  void _refreshData() {
    setState(() {
      // 触发重新构建
    });
  }

  /// 执行优化
  void _performOptimization() {
    _performanceMonitor.performOptimization();
    
    fluent.displayInfoBar(
      context,
      builder: (context, close) => fluent.InfoBar(
        title: const Text('优化完成'),
        content: const Text('已执行性能优化操作'),
        severity: fluent.InfoBarSeverity.success,
        action: fluent.IconButton(
          icon: const Icon(fluent.FluentIcons.clear),
          onPressed: close,
        ),
      ),
    );
  }

  /// 导出报告
  void _exportReport() {
    _performanceMonitor.generatePerformanceReport();

    // 这里应该保存报告到文件
    fluent.displayInfoBar(
      context,
      builder: (context, close) => fluent.InfoBar(
        title: const Text('报告已生成'),
        content: const Text('性能报告已导出'),
        severity: fluent.InfoBarSeverity.info,
        action: fluent.IconButton(
          icon: const Icon(fluent.FluentIcons.clear),
          onPressed: close,
        ),
      ),
    );
  }
}