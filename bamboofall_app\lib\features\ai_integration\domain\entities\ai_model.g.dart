// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ai_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AIModelImpl _$$AIModelImplFromJson(Map<String, dynamic> json) =>
    _$AIModelImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      displayName: json['displayName'] as String,
      provider: $enumDecode(_$AIProviderEnumMap, json['provider']),
      type: $enumDecode(_$AIModelTypeEnumMap, json['type']),
      status: $enumDecode(_$AIModelStatusEnumMap, json['status']),
      description: json['description'] as String?,
      version: json['version'] as String?,
      maxTokens: (json['maxTokens'] as num?)?.toInt() ?? 4096,
      defaultTemperature:
          (json['defaultTemperature'] as num?)?.toDouble() ?? 0.7,
      defaultTopP: (json['defaultTopP'] as num?)?.toDouble() ?? 1.0,
      defaultFrequencyPenalty:
          (json['defaultFrequencyPenalty'] as num?)?.toDouble() ?? 1.0,
      defaultPresencePenalty:
          (json['defaultPresencePenalty'] as num?)?.toDouble() ?? 1.0,
      supportedFeatures:
          (json['supportedFeatures'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      metadata: json['metadata'] as Map<String, dynamic>?,
      isActive: json['isActive'] as bool? ?? false,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$AIModelImplToJson(_$AIModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'displayName': instance.displayName,
      'provider': _$AIProviderEnumMap[instance.provider]!,
      'type': _$AIModelTypeEnumMap[instance.type]!,
      'status': _$AIModelStatusEnumMap[instance.status]!,
      'description': instance.description,
      'version': instance.version,
      'maxTokens': instance.maxTokens,
      'defaultTemperature': instance.defaultTemperature,
      'defaultTopP': instance.defaultTopP,
      'defaultFrequencyPenalty': instance.defaultFrequencyPenalty,
      'defaultPresencePenalty': instance.defaultPresencePenalty,
      'supportedFeatures': instance.supportedFeatures,
      'metadata': instance.metadata,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$AIProviderEnumMap = {
  AIProvider.openai: 'openai',
  AIProvider.anthropic: 'anthropic',
  AIProvider.google: 'google',
  AIProvider.deepseek: 'deepseek',
  AIProvider.zhipu: 'zhipu',
  AIProvider.moonshot: 'moonshot',
  AIProvider.baidu: 'baidu',
  AIProvider.alibaba: 'alibaba',
  AIProvider.custom: 'custom',
};

const _$AIModelTypeEnumMap = {
  AIModelType.chat: 'chat',
  AIModelType.completion: 'completion',
  AIModelType.embedding: 'embedding',
};

const _$AIModelStatusEnumMap = {
  AIModelStatus.available: 'available',
  AIModelStatus.unavailable: 'unavailable',
  AIModelStatus.deprecated: 'deprecated',
  AIModelStatus.beta: 'beta',
};

_$AIModelConfigImpl _$$AIModelConfigImplFromJson(Map<String, dynamic> json) =>
    _$AIModelConfigImpl(
      modelId: json['modelId'] as String,
      apiKey: json['apiKey'] as String,
      baseUrl: json['baseUrl'] as String?,
      organizationId: json['organizationId'] as String?,
      temperature: (json['temperature'] as num?)?.toDouble() ?? 0.7,
      topP: (json['topP'] as num?)?.toDouble() ?? 1.0,
      frequencyPenalty: (json['frequencyPenalty'] as num?)?.toDouble() ?? 1.0,
      presencePenalty: (json['presencePenalty'] as num?)?.toDouble() ?? 1.0,
      maxTokens: (json['maxTokens'] as num?)?.toInt() ?? 4096,
      maxRetries: (json['maxRetries'] as num?)?.toInt() ?? 1,
      timeoutMs: (json['timeoutMs'] as num?)?.toInt() ?? 30000,
      customHeaders: (json['customHeaders'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String),
      ),
      additionalParams: json['additionalParams'] as Map<String, dynamic>?,
      isEnabled: json['isEnabled'] as bool? ?? true,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$AIModelConfigImplToJson(_$AIModelConfigImpl instance) =>
    <String, dynamic>{
      'modelId': instance.modelId,
      'apiKey': instance.apiKey,
      'baseUrl': instance.baseUrl,
      'organizationId': instance.organizationId,
      'temperature': instance.temperature,
      'topP': instance.topP,
      'frequencyPenalty': instance.frequencyPenalty,
      'presencePenalty': instance.presencePenalty,
      'maxTokens': instance.maxTokens,
      'maxRetries': instance.maxRetries,
      'timeoutMs': instance.timeoutMs,
      'customHeaders': instance.customHeaders,
      'additionalParams': instance.additionalParams,
      'isEnabled': instance.isEnabled,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

_$AIRequestImpl _$$AIRequestImplFromJson(Map<String, dynamic> json) =>
    _$AIRequestImpl(
      modelId: json['modelId'] as String,
      prompt: json['prompt'] as String,
      messages: (json['messages'] as List<dynamic>?)
          ?.map((e) => AIMessage.fromJson(e as Map<String, dynamic>))
          .toList(),
      temperature: (json['temperature'] as num?)?.toDouble() ?? 0.7,
      topP: (json['topP'] as num?)?.toDouble() ?? 1.0,
      frequencyPenalty: (json['frequencyPenalty'] as num?)?.toDouble() ?? 1.0,
      presencePenalty: (json['presencePenalty'] as num?)?.toDouble() ?? 1.0,
      maxTokens: (json['maxTokens'] as num?)?.toInt() ?? 4096,
      stop: (json['stop'] as List<dynamic>?)?.map((e) => e as String).toList(),
      systemPrompt: json['systemPrompt'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      requestId: json['requestId'] as String?,
    );

Map<String, dynamic> _$$AIRequestImplToJson(_$AIRequestImpl instance) =>
    <String, dynamic>{
      'modelId': instance.modelId,
      'prompt': instance.prompt,
      'messages': instance.messages,
      'temperature': instance.temperature,
      'topP': instance.topP,
      'frequencyPenalty': instance.frequencyPenalty,
      'presencePenalty': instance.presencePenalty,
      'maxTokens': instance.maxTokens,
      'stop': instance.stop,
      'systemPrompt': instance.systemPrompt,
      'metadata': instance.metadata,
      'requestId': instance.requestId,
    };

_$AIMessageImpl _$$AIMessageImplFromJson(Map<String, dynamic> json) =>
    _$AIMessageImpl(
      role: json['role'] as String,
      content: json['content'] as String,
      name: json['name'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      timestamp: json['timestamp'] == null
          ? null
          : DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$$AIMessageImplToJson(_$AIMessageImpl instance) =>
    <String, dynamic>{
      'role': instance.role,
      'content': instance.content,
      'name': instance.name,
      'metadata': instance.metadata,
      'timestamp': instance.timestamp?.toIso8601String(),
    };

_$AIResponseImpl _$$AIResponseImplFromJson(Map<String, dynamic> json) =>
    _$AIResponseImpl(
      id: json['id'] as String,
      modelId: json['modelId'] as String,
      content: json['content'] as String,
      usage: AIUsage.fromJson(json['usage'] as Map<String, dynamic>),
      finishReason: json['finishReason'] as String?,
      choices: (json['choices'] as List<dynamic>?)
          ?.map((e) => AIChoice.fromJson(e as Map<String, dynamic>))
          .toList(),
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      responseTimeMs: (json['responseTimeMs'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$AIResponseImplToJson(_$AIResponseImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'modelId': instance.modelId,
      'content': instance.content,
      'usage': instance.usage,
      'finishReason': instance.finishReason,
      'choices': instance.choices,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt?.toIso8601String(),
      'responseTimeMs': instance.responseTimeMs,
    };

_$AIChoiceImpl _$$AIChoiceImplFromJson(Map<String, dynamic> json) =>
    _$AIChoiceImpl(
      index: (json['index'] as num).toInt(),
      message: AIMessage.fromJson(json['message'] as Map<String, dynamic>),
      finishReason: json['finishReason'] as String?,
      logprobs: (json['logprobs'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$AIChoiceImplToJson(_$AIChoiceImpl instance) =>
    <String, dynamic>{
      'index': instance.index,
      'message': instance.message,
      'finishReason': instance.finishReason,
      'logprobs': instance.logprobs,
    };

_$AIUsageImpl _$$AIUsageImplFromJson(Map<String, dynamic> json) =>
    _$AIUsageImpl(
      promptTokens: (json['promptTokens'] as num).toInt(),
      completionTokens: (json['completionTokens'] as num).toInt(),
      totalTokens: (json['totalTokens'] as num).toInt(),
      cost: (json['cost'] as num?)?.toDouble(),
      currency: json['currency'] as String?,
    );

Map<String, dynamic> _$$AIUsageImplToJson(_$AIUsageImpl instance) =>
    <String, dynamic>{
      'promptTokens': instance.promptTokens,
      'completionTokens': instance.completionTokens,
      'totalTokens': instance.totalTokens,
      'cost': instance.cost,
      'currency': instance.currency,
    };

_$AIErrorImpl _$$AIErrorImplFromJson(Map<String, dynamic> json) =>
    _$AIErrorImpl(
      code: json['code'] as String,
      message: json['message'] as String,
      type: json['type'] as String?,
      param: json['param'] as String?,
      details: json['details'] as Map<String, dynamic>?,
      timestamp: json['timestamp'] == null
          ? null
          : DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$$AIErrorImplToJson(_$AIErrorImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'message': instance.message,
      'type': instance.type,
      'param': instance.param,
      'details': instance.details,
      'timestamp': instance.timestamp?.toIso8601String(),
    };

_$AICapabilitiesImpl _$$AICapabilitiesImplFromJson(Map<String, dynamic> json) =>
    _$AICapabilitiesImpl(
      supportsStreaming: json['supportsStreaming'] as bool? ?? false,
      supportsSystemPrompt: json['supportsSystemPrompt'] as bool? ?? false,
      supportsFunctionCalling:
          json['supportsFunctionCalling'] as bool? ?? false,
      supportsImageInput: json['supportsImageInput'] as bool? ?? false,
      supportsImageOutput: json['supportsImageOutput'] as bool? ?? false,
      supportsAudioInput: json['supportsAudioInput'] as bool? ?? false,
      supportsAudioOutput: json['supportsAudioOutput'] as bool? ?? false,
      supportsCodeExecution: json['supportsCodeExecution'] as bool? ?? false,
      supportsWebSearch: json['supportsWebSearch'] as bool? ?? false,
      supportedLanguages:
          (json['supportedLanguages'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      supportedFormats:
          (json['supportedFormats'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$AICapabilitiesImplToJson(
  _$AICapabilitiesImpl instance,
) => <String, dynamic>{
  'supportsStreaming': instance.supportsStreaming,
  'supportsSystemPrompt': instance.supportsSystemPrompt,
  'supportsFunctionCalling': instance.supportsFunctionCalling,
  'supportsImageInput': instance.supportsImageInput,
  'supportsImageOutput': instance.supportsImageOutput,
  'supportsAudioInput': instance.supportsAudioInput,
  'supportsAudioOutput': instance.supportsAudioOutput,
  'supportsCodeExecution': instance.supportsCodeExecution,
  'supportsWebSearch': instance.supportsWebSearch,
  'supportedLanguages': instance.supportedLanguages,
  'supportedFormats': instance.supportedFormats,
};
