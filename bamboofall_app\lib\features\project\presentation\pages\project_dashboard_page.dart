import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

import '../../domain/entities/project.dart';
import '../../domain/usecases/manage_projects.dart';
import '../../domain/usecases/track_progress.dart';
import '../widgets/progress_dashboard.dart';

/// 项目仪表板页面
/// 显示项目的详细信息和进度跟踪
class ProjectDashboardPage extends ConsumerStatefulWidget {
  final String projectId;

  const ProjectDashboardPage({
    super.key,
    required this.projectId,
  });

  @override
  ConsumerState<ProjectDashboardPage> createState() => _ProjectDashboardPageState();
}

class _ProjectDashboardPageState extends ConsumerState<ProjectDashboardPage> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Project?>(
      future: ref.read(manageProjectsUseCaseProvider).getProjectById(widget.projectId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(child: fluent.ProgressRing()),
          );
        }

        if (snapshot.hasError || snapshot.data == null) {
          return Scaffold(
            body: Center(
              child: fluent.InfoBar(
                title: const Text('加载失败'),
                content: Text('无法加载项目信息: ${snapshot.error}'),
                severity: fluent.InfoBarSeverity.error,
              ),
            ),
          );
        }

        final project = snapshot.data!;
        return fluent.NavigationView(
          appBar: fluent.NavigationAppBar(
            title: Text(project.name),
            actions: fluent.CommandBar(
              primaryItems: [
                fluent.CommandBarButton(
                  icon: const fluent.Icon(fluent.FluentIcons.edit),
                  label: const Text('编辑'),
                  onPressed: () => _editProject(project),
                ),
                fluent.CommandBarButton(
                  icon: const fluent.Icon(fluent.FluentIcons.play),
                  label: const Text('开始写作'),
                  onPressed: () => _startWriting(project),
                ),
              ],
              secondaryItems: [
                fluent.CommandBarButton(
                  icon: const fluent.Icon(fluent.FluentIcons.settings),
                  label: const Text('设置'),
                  onPressed: () => _showSettings(project),
                ),
                fluent.CommandBarButton(
                  icon: const fluent.Icon(fluent.FluentIcons.more),
                  label: const Text('更多'),
                  onPressed: () => _showMoreOptions(project),
                ),
              ],
            ),
          ),
          pane: fluent.NavigationPane(
            selected: _selectedIndex,
            onChanged: (index) => setState(() => _selectedIndex = index),
            displayMode: fluent.PaneDisplayMode.open,
            items: <fluent.NavigationPaneItem>[
              fluent.PaneItem(
                icon: const fluent.Icon(fluent.FluentIcons.analytics_view),
                title: const Text('进度概览'),
                body: _buildOverviewTab(project),
              ),
              fluent.PaneItem(
                icon: const fluent.Icon(fluent.FluentIcons.chart),
                title: const Text('详细统计'),
                body: _buildStatisticsTab(project),
              ),
              fluent.PaneItem(
                icon: const fluent.Icon(fluent.FluentIcons.timeline),
                title: const Text('写作历史'),
                body: _buildHistoryTab(project),
              ),
              fluent.PaneItem(
                icon: const fluent.Icon(fluent.FluentIcons.bullseye),
                title: const Text('目标管理'),
                body: _buildGoalsTab(project),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildOverviewTab(Project project) {
    return fluent.ScaffoldPage(
      content: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 项目基本信息
            _buildProjectInfo(project),
            
            const SizedBox(height: 24),
            
            // 进度仪表板
            Expanded(
              child: ProgressDashboard(projectId: project.id),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsTab(Project project) {
    return fluent.ScaffoldPage(
      content: Padding(
        padding: const EdgeInsets.all(24),
        child: FutureBuilder<WritingStatistics>(
          future: ref.read(trackProgressUseCaseProvider).getWritingStatistics(project.id),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: fluent.ProgressRing());
            }

            if (snapshot.hasError) {
              return fluent.InfoBar(
                title: const Text('加载失败'),
                content: Text('无法加载统计数据: ${snapshot.error}'),
                severity: fluent.InfoBarSeverity.error,
              );
            }

            final stats = snapshot.data!;
            return _buildDetailedStatistics(stats);
          },
        ),
      ),
    );
  }

  Widget _buildHistoryTab(Project project) {
    return fluent.ScaffoldPage(
      content: Padding(
        padding: const EdgeInsets.all(24),
        child: FutureBuilder<List<ProgressDataPoint>>(
          future: ref.read(trackProgressUseCaseProvider).getProgressHistory(
            project.id,
            period: const Duration(days: 90),
          ),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: fluent.ProgressRing());
            }

            if (snapshot.hasError) {
              return fluent.InfoBar(
                title: const Text('加载失败'),
                content: Text('无法加载历史数据: ${snapshot.error}'),
                severity: fluent.InfoBarSeverity.error,
              );
            }

            final history = snapshot.data!;
            return _buildWritingHistory(history);
          },
        ),
      ),
    );
  }

  Widget _buildGoalsTab(Project project) {
    return fluent.ScaffoldPage(
      content: Padding(
        padding: const EdgeInsets.all(24),
        child: FutureBuilder<GoalAchievement>(
          future: ref.read(trackProgressUseCaseProvider).getGoalAchievement(project.id),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: fluent.ProgressRing());
            }

            if (snapshot.hasError) {
              return fluent.InfoBar(
                title: const Text('加载失败'),
                content: Text('无法加载目标数据: ${snapshot.error}'),
                severity: fluent.InfoBarSeverity.error,
              );
            }

            final achievement = snapshot.data!;
            return _buildGoalManagement(project, achievement);
          },
        ),
      ),
    );
  }

  Widget _buildProjectInfo(Project project) {
    return fluent.Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // 项目图标和基本信息
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: _getProjectColor(project).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: _getProjectColor(project).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      project.type.icon,
                      style: const TextStyle(fontSize: 24),
                    ),
                  ),
                ),
                
                const SizedBox(width: 16),
                
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            project.name,
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (project.isFavorite)
                            const fluent.Icon(
                              fluent.FluentIcons.favorite_star_fill,
                              color: Colors.amber,
                              size: 16,
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        project.type.displayName,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                      if (project.description.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Text(
                          project.description,
                          style: TextStyle(
                            color: Colors.grey[700],
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                
                // 状态标签
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getProjectColor(project).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: _getProjectColor(project).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    project.status.displayName,
                    style: TextStyle(
                      color: _getProjectColor(project),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            
            if (project.tags.isNotEmpty) ...[
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: project.tags.map((tag) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      tag,
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey[700],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedStatistics(WritingStatistics stats) {
    return GridView.count(
      crossAxisCount: 3,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildStatCard('总字数', _formatNumber(stats.totalWords), fluent.FluentIcons.edit, Colors.blue),
        _buildStatCard('总字符数', _formatNumber(stats.totalCharacters), fluent.FluentIcons.text_document, Colors.green),
        _buildStatCard('章节数', stats.totalChapters.toString(), fluent.FluentIcons.document, Colors.orange),
        _buildStatCard('写作天数', stats.writingDays.toString(), fluent.FluentIcons.calendar, Colors.purple),
        _buildStatCard('平均日字数', stats.averageDailyWords.toString(), fluent.FluentIcons.chart, Colors.teal),
        _buildStatCard('最高日字数', stats.maxDailyWords.toString(), fluent.FluentIcons.trophy, Colors.amber),
        _buildStatCard('连续天数', stats.consecutiveDays.toString(), fluent.FluentIcons.streaming, Colors.red),
        _buildStatCard('写作时长', '${stats.writingTimeHours.toStringAsFixed(1)}h', fluent.FluentIcons.clock, Colors.indigo),
        _buildStatCard('写作效率', '${stats.wordsPerMinute.toStringAsFixed(1)} 字/分', fluent.FluentIcons.speed_high, Colors.pink),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return fluent.Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            fluent.Icon(
              icon,
              color: color,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w700,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWritingHistory(List<ProgressDataPoint> history) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '写作历史记录',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: ListView.builder(
            itemCount: history.length,
            itemBuilder: (context, index) {
              final point = history[index];
              return fluent.Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Center(
                          child: Text(
                            point.date.day.toString(),
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              color: Colors.blue,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${point.date.month}月${point.date.day}日',
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '新增 ${point.dailyWords} 字 • 写作 ${point.writingTime.inMinutes} 分钟',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Text(
                        _formatNumber(point.wordCount),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildGoalManagement(Project project, GoalAchievement achievement) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '目标管理',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        
        // 目标设置卡片
        fluent.Card(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '当前目标',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildGoalItem(
                        '总字数目标',
                        '${_formatNumber(project.statistics.totalWordCount)} / ${_formatNumber(project.config.targetWordCount)}',
                        achievement.wordGoalAchievement,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildGoalItem(
                        '每日目标',
                        '${project.statistics.averageDailyWords} / ${project.config.dailyWritingGoal}',
                        achievement.dailyGoalAchievement,
                      ),
                    ),
                  ],
                ),
                if (project.config.deadline != null) ...[
                  const SizedBox(height: 16),
                  _buildGoalItem(
                    '截止日期',
                    '${project.config.deadline!.year}-${project.config.deadline!.month.toString().padLeft(2, '0')}-${project.config.deadline!.day.toString().padLeft(2, '0')}',
                    achievement.timeGoalAchievement,
                  ),
                ],
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // 改进建议
        if (achievement.recommendations.isNotEmpty)
          fluent.Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '改进建议',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ...achievement.recommendations.map((rec) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const fluent.Icon(
                          fluent.FluentIcons.lightbulb,
                          size: 16,
                          color: Colors.orange,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            rec,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),
                      ],
                    ),
                  )),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildGoalItem(String title, String value, double? progress) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        if (progress != null) ...[
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: (progress / 100).clamp(0.0, 1.0),
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              progress >= 100 ? Colors.green :
              progress >= 75 ? Colors.blue :
              progress >= 50 ? Colors.orange : Colors.red,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${progress.toStringAsFixed(1)}%',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ],
    );
  }

  Color _getProjectColor(Project project) {
    if (project.colorTheme != null) {
      try {
        return Color(int.parse(project.colorTheme!.replaceFirst('#', '0xFF')));
      } catch (e) {
        // 如果颜色解析失败，使用默认颜色
      }
    }
    
    return project.status.color.isNotEmpty 
        ? Color(int.parse(project.status.color.replaceFirst('#', '0xFF')))
        : Colors.blue;
  }

  String _formatNumber(int number) {
    if (number >= 10000) {
      return '${(number / 10000).toStringAsFixed(1)}万';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}k';
    } else {
      return number.toString();
    }
  }

  void _editProject(Project project) {
    // TODO: 实现编辑项目功能
    fluent.showDialog(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: const Text('编辑项目'),
        content: const Text('编辑项目功能正在开发中...'),
        actions: [
          fluent.Button(
            child: const Text('确定'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  void _startWriting(Project project) {
    // TODO: 实现开始写作功能
    fluent.showDialog(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: const Text('开始写作'),
        content: const Text('写作界面正在开发中...'),
        actions: [
          fluent.Button(
            child: const Text('确定'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  void _showSettings(Project project) {
    // TODO: 实现项目设置功能
    fluent.showDialog(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: const Text('项目设置'),
        content: const Text('项目设置功能正在开发中...'),
        actions: [
          fluent.Button(
            child: const Text('确定'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  void _showMoreOptions(Project project) {
    // TODO: 实现更多选项功能
    fluent.showDialog(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: const Text('更多选项'),
        content: const Text('更多选项功能正在开发中...'),
        actions: [
          fluent.Button(
            child: const Text('确定'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }
}