import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'error_handler.dart';
import '../logging/logger.dart';

/// 错误报告服务
class ErrorReportingService {
  static final ErrorReportingService _instance = ErrorReportingService._internal();
  factory ErrorReportingService() => _instance;
  ErrorReportingService._internal();

  final AppLogger _logger = AppLogger();
  final List<ErrorReport> _pendingReports = [];
  Timer? _uploadTimer;
  
  bool _isEnabled = false;
  bool _isInitialized = false;
  String? _userId;
  String? _sessionId;
  Map<String, dynamic>? _deviceInfo;
  Map<String, dynamic>? _appInfo;

  // 配置
  static const Duration _uploadInterval = Duration(minutes: 5);
  static const int _maxPendingReports = 100;
  static const int _maxRetryAttempts = 3;

  /// 是否启用错误报告
  bool get isEnabled => _isEnabled;

  /// 待上传报告数量
  int get pendingReportsCount => _pendingReports.length;

  /// 初始化错误报告服务
  Future<void> initialize({
    bool enabled = true,
    String? userId,
  }) async {
    if (_isInitialized) return;

    _isEnabled = enabled;
    _userId = userId;
    _sessionId = _generateSessionId();

    if (_isEnabled) {
      await _collectDeviceInfo();
      await _collectAppInfo();
      _startPeriodicUpload();
      
      // 监听错误事件
      GlobalErrorHandler().errorStream.listen(_handleError);
    }

    _isInitialized = true;
    _logger.info('Error reporting service initialized (enabled: $enabled)');
  }

  /// 收集设备信息
  Future<void> _collectDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      
      if (Platform.isWindows) {
        final windowsInfo = await deviceInfo.windowsInfo;
        _deviceInfo = {
          'platform': 'windows',
          'computerName': windowsInfo.computerName,
          'numberOfCores': windowsInfo.numberOfCores,
          'systemMemoryInMegabytes': windowsInfo.systemMemoryInMegabytes,
          'majorVersion': windowsInfo.majorVersion,
          'minorVersion': windowsInfo.minorVersion,
          'buildNumber': windowsInfo.buildNumber,
        };
      } else if (Platform.isMacOS) {
        final macInfo = await deviceInfo.macOsInfo;
        _deviceInfo = {
          'platform': 'macos',
          'computerName': macInfo.computerName,
          'hostName': macInfo.hostName,
          'arch': macInfo.arch,
          'model': macInfo.model,
          'kernelVersion': macInfo.kernelVersion,
          'osRelease': macInfo.osRelease,
          'majorVersion': macInfo.majorVersion,
          'minorVersion': macInfo.minorVersion,
          'patchVersion': macInfo.patchVersion,
        };
      } else if (Platform.isLinux) {
        final linuxInfo = await deviceInfo.linuxInfo;
        _deviceInfo = {
          'platform': 'linux',
          'name': linuxInfo.name,
          'version': linuxInfo.version,
          'id': linuxInfo.id,
          'idLike': linuxInfo.idLike,
          'versionCodename': linuxInfo.versionCodename,
          'versionId': linuxInfo.versionId,
          'prettyName': linuxInfo.prettyName,
          'buildId': linuxInfo.buildId,
          'variant': linuxInfo.variant,
          'variantId': linuxInfo.variantId,
          'machineId': linuxInfo.machineId,
        };
      }
    } catch (e) {
      _logger.warning('Failed to collect device info', error: e);
      _deviceInfo = {'platform': Platform.operatingSystem};
    }
  }

  /// 收集应用信息
  Future<void> _collectAppInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      _appInfo = {
        'appName': packageInfo.appName,
        'packageName': packageInfo.packageName,
        'version': packageInfo.version,
        'buildNumber': packageInfo.buildNumber,
        'buildSignature': packageInfo.buildSignature,
        'installerStore': packageInfo.installerStore,
      };
    } catch (e) {
      _logger.warning('Failed to collect app info', error: e);
      _appInfo = {'appName': 'BambooFall'};
    }
  }

  /// 处理错误
  void _handleError(ErrorRecord errorRecord) {
    if (!_isEnabled) return;

    final errorReport = ErrorReport(
      id: _generateReportId(),
      timestamp: errorRecord.timestamp,
      error: errorRecord.error.toString(),
      stackTrace: errorRecord.stackTrace?.toString(),
      errorType: errorRecord.errorType.name,
      severity: errorRecord.severity.name,
      context: errorRecord.context,
      library: errorRecord.library,
      additionalData: errorRecord.additionalData,
      userId: _userId,
      sessionId: _sessionId,
      deviceInfo: _deviceInfo,
      appInfo: _appInfo,
    );

    _addPendingReport(errorReport);
  }

  /// 添加待处理报告
  void _addPendingReport(ErrorReport report) {
    _pendingReports.add(report);

    // 限制待处理报告数量
    if (_pendingReports.length > _maxPendingReports) {
      _pendingReports.removeAt(0);
    }

    _logger.debug('Added error report to pending queue (${_pendingReports.length} pending)');
  }

  /// 手动报告错误
  void reportError({
    required Object error,
    StackTrace? stackTrace,
    String? context,
    ErrorSeverity severity = ErrorSeverity.error,
    Map<String, dynamic>? additionalData,
  }) {
    if (!_isEnabled) return;

    final errorReport = ErrorReport(
      id: _generateReportId(),
      timestamp: DateTime.now(),
      error: error.toString(),
      stackTrace: stackTrace?.toString(),
      errorType: 'manual',
      severity: severity.name,
      context: context,
      additionalData: additionalData,
      userId: _userId,
      sessionId: _sessionId,
      deviceInfo: _deviceInfo,
      appInfo: _appInfo,
    );

    _addPendingReport(errorReport);
  }

  /// 开始定期上传
  void _startPeriodicUpload() {
    _uploadTimer = Timer.periodic(_uploadInterval, (_) {
      _uploadPendingReports();
    });
  }

  /// 上传待处理报告
  Future<void> _uploadPendingReports() async {
    if (_pendingReports.isEmpty) return;

    final reportsToUpload = List<ErrorReport>.from(_pendingReports);
    _pendingReports.clear();

    try {
      await _uploadReports(reportsToUpload);
      _logger.info('Successfully uploaded ${reportsToUpload.length} error reports');
    } catch (e) {
      _logger.error('Failed to upload error reports', error: e);
      
      // 重新添加到待处理队列（有重试限制）
      for (final report in reportsToUpload) {
        if (report.retryCount < _maxRetryAttempts) {
          _pendingReports.add(report.copyWith(retryCount: report.retryCount + 1));
        }
      }
    }
  }

  /// 上传报告到服务器
  Future<void> _uploadReports(List<ErrorReport> reports) async {
    // 在生产环境中，这里应该发送到实际的错误报告服务
    // 例如：Sentry, Crashlytics, 自定义服务器等
    
    if (kDebugMode) {
      // 调试模式下只记录日志
      for (final report in reports) {
        developer.log('Error Report: ${report.toJson()}', name: 'ErrorReporting');
      }
      return;
    }

    // 模拟网络请求
    await Future.delayed(const Duration(seconds: 2));
    
    // 这里应该实现实际的HTTP请求
    /*
    final client = HttpClient();
    try {
      final request = await client.postUrl(Uri.parse('https://your-error-service.com/reports'));
      request.headers.contentType = ContentType.json;
      
      final payload = {
        'reports': reports.map((r) => r.toJson()).toList(),
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      request.write(jsonEncode(payload));
      final response = await request.close();
      
      if (response.statusCode != 200) {
        throw Exception('Server returned ${response.statusCode}');
      }
    } finally {
      client.close();
    }
    */
  }

  /// 设置用户ID
  void setUserId(String? userId) {
    _userId = userId;
    _logger.info('Error reporting user ID updated');
  }

  /// 启用/禁用错误报告
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
    
    if (enabled && !_isInitialized) {
      initialize(enabled: true, userId: _userId);
    } else if (!enabled) {
      _uploadTimer?.cancel();
      _uploadTimer = null;
      _pendingReports.clear();
    }
    
    _logger.info('Error reporting ${enabled ? 'enabled' : 'disabled'}');
  }

  /// 立即上传所有待处理报告
  Future<void> flushReports() async {
    if (_pendingReports.isNotEmpty) {
      await _uploadPendingReports();
    }
  }

  /// 获取错误报告统计
  ErrorReportingStatistics getStatistics() {
    return ErrorReportingStatistics(
      isEnabled: _isEnabled,
      pendingReports: _pendingReports.length,
      sessionId: _sessionId,
      userId: _userId,
    );
  }

  /// 生成会话ID
  String _generateSessionId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return 'session_${timestamp}_$random';
  }

  /// 生成报告ID
  String _generateReportId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return 'report_${timestamp}_$random';
  }

  /// 释放资源
  void dispose() {
    _uploadTimer?.cancel();
    _uploadTimer = null;
    _pendingReports.clear();
  }
}

/// 错误报告
class ErrorReport {
  final String id;
  final DateTime timestamp;
  final String error;
  final String? stackTrace;
  final String errorType;
  final String severity;
  final String? context;
  final String? library;
  final Map<String, dynamic>? additionalData;
  final String? userId;
  final String? sessionId;
  final Map<String, dynamic>? deviceInfo;
  final Map<String, dynamic>? appInfo;
  final int retryCount;

  const ErrorReport({
    required this.id,
    required this.timestamp,
    required this.error,
    this.stackTrace,
    required this.errorType,
    required this.severity,
    this.context,
    this.library,
    this.additionalData,
    this.userId,
    this.sessionId,
    this.deviceInfo,
    this.appInfo,
    this.retryCount = 0,
  });

  /// 复制并更新报告
  ErrorReport copyWith({
    String? id,
    DateTime? timestamp,
    String? error,
    String? stackTrace,
    String? errorType,
    String? severity,
    String? context,
    String? library,
    Map<String, dynamic>? additionalData,
    String? userId,
    String? sessionId,
    Map<String, dynamic>? deviceInfo,
    Map<String, dynamic>? appInfo,
    int? retryCount,
  }) {
    return ErrorReport(
      id: id ?? this.id,
      timestamp: timestamp ?? this.timestamp,
      error: error ?? this.error,
      stackTrace: stackTrace ?? this.stackTrace,
      errorType: errorType ?? this.errorType,
      severity: severity ?? this.severity,
      context: context ?? this.context,
      library: library ?? this.library,
      additionalData: additionalData ?? this.additionalData,
      userId: userId ?? this.userId,
      sessionId: sessionId ?? this.sessionId,
      deviceInfo: deviceInfo ?? this.deviceInfo,
      appInfo: appInfo ?? this.appInfo,
      retryCount: retryCount ?? this.retryCount,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'error': error,
      if (stackTrace != null) 'stackTrace': stackTrace,
      'errorType': errorType,
      'severity': severity,
      if (context != null) 'context': context,
      if (library != null) 'library': library,
      if (additionalData != null) 'additionalData': additionalData,
      if (userId != null) 'userId': userId,
      if (sessionId != null) 'sessionId': sessionId,
      if (deviceInfo != null) 'deviceInfo': deviceInfo,
      if (appInfo != null) 'appInfo': appInfo,
      'retryCount': retryCount,
    };
  }

  /// 从JSON创建报告
  static ErrorReport fromJson(Map<String, dynamic> json) {
    return ErrorReport(
      id: json['id'],
      timestamp: DateTime.parse(json['timestamp']),
      error: json['error'],
      stackTrace: json['stackTrace'],
      errorType: json['errorType'],
      severity: json['severity'],
      context: json['context'],
      library: json['library'],
      additionalData: json['additionalData'] as Map<String, dynamic>?,
      userId: json['userId'],
      sessionId: json['sessionId'],
      deviceInfo: json['deviceInfo'] as Map<String, dynamic>?,
      appInfo: json['appInfo'] as Map<String, dynamic>?,
      retryCount: json['retryCount'] ?? 0,
    );
  }
}

/// 错误报告统计
class ErrorReportingStatistics {
  final bool isEnabled;
  final int pendingReports;
  final String? sessionId;
  final String? userId;

  const ErrorReportingStatistics({
    required this.isEnabled,
    required this.pendingReports,
    this.sessionId,
    this.userId,
  });
}

/// 错误报告配置
class ErrorReportingConfig {
  final bool enabled;
  final String? endpoint;
  final String? apiKey;
  final Duration uploadInterval;
  final int maxPendingReports;
  final int maxRetryAttempts;
  final bool includeDeviceInfo;
  final bool includeAppInfo;
  final List<String> excludeErrorTypes;

  const ErrorReportingConfig({
    this.enabled = true,
    this.endpoint,
    this.apiKey,
    this.uploadInterval = const Duration(minutes: 5),
    this.maxPendingReports = 100,
    this.maxRetryAttempts = 3,
    this.includeDeviceInfo = true,
    this.includeAppInfo = true,
    this.excludeErrorTypes = const [],
  });
}

/// 错误报告过滤器
class ErrorReportFilter {
  final List<String> excludeErrorTypes;
  final List<String> excludeMessages;
  final ErrorSeverity? minSeverity;
  final bool Function(ErrorRecord)? customFilter;

  const ErrorReportFilter({
    this.excludeErrorTypes = const [],
    this.excludeMessages = const [],
    this.minSeverity,
    this.customFilter,
  });

  /// 检查错误是否应该被报告
  bool shouldReport(ErrorRecord errorRecord) {
    // 检查错误类型排除列表
    if (excludeErrorTypes.contains(errorRecord.errorType.name)) {
      return false;
    }

    // 检查消息排除列表
    final errorMessage = errorRecord.error.toString().toLowerCase();
    for (final excludeMessage in excludeMessages) {
      if (errorMessage.contains(excludeMessage.toLowerCase())) {
        return false;
      }
    }

    // 检查最小严重程度
    if (minSeverity != null && errorRecord.severity.index < minSeverity!.index) {
      return false;
    }

    // 应用自定义过滤器
    if (customFilter != null && !customFilter!(errorRecord)) {
      return false;
    }

    return true;
  }
}