import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

import '../../domain/entities/chapter_version.dart';

/// 版本历史面板
/// 显示版本列表和提供版本操作功能
class VersionHistoryPanel extends ConsumerWidget {
  final List<ChapterVersion> versions;
  final ChapterVersion? selectedVersion;
  final ChapterVersion? compareVersion;
  final Function(ChapterVersion version)? onVersionSelected;
  final Function(ChapterVersion version)? onCompareVersionSelected;
  final Function(ChapterVersion version, String action)? onVersionAction;
  final bool showCompareMode;

  const VersionHistoryPanel({
    super.key,
    required this.versions,
    this.selectedVersion,
    this.compareVersion,
    this.onVersionSelected,
    this.onCompareVersionSelected,
    this.onVersionAction,
    this.showCompareMode = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        // 头部
        _buildHeader(context),
        
        // 版本列表
        Expanded(
          child: _buildVersionList(context),
        ),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: fluent.FluentTheme.of(context).resources.layerFillColorDefault,
        border: Border(
          bottom: BorderSide(
            color: fluent.FluentTheme.of(context).resources.dividerStrokeColorDefault,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            showCompareMode ? '版本对比' : '版本历史',
            style: fluent.FluentTheme.of(context).typography.bodyStrong,
          ),
          const Spacer(),
          Text(
            '${versions.length} 个版本',
            style: fluent.FluentTheme.of(context).typography.caption,
          ),
        ],
      ),
    );
  }

  Widget _buildVersionList(BuildContext context) {
    if (versions.isEmpty) {
      return const Center(
        child: Text('暂无版本'),
      );
    }

    return ListView.builder(
      itemCount: versions.length,
      itemBuilder: (context, index) {
        final version = versions[index];
        return _buildVersionItem(context, version);
      },
    );
  }

  Widget _buildVersionItem(BuildContext context, ChapterVersion version) {
    final isSelected = selectedVersion?.id == version.id;
    final isCompareSelected = compareVersion?.id == version.id;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: isSelected 
            ? fluent.FluentTheme.of(context).accentColor.withValues(alpha: 0.1)
            : isCompareSelected
                ? Colors.orange.withValues(alpha: 0.1)
                : Colors.transparent,
        borderRadius: BorderRadius.circular(4),
        border: isSelected 
            ? Border.all(color: fluent.FluentTheme.of(context).accentColor, width: 1)
            : isCompareSelected
                ? Border.all(color: Colors.orange, width: 1)
                : null,
      ),
      child: fluent.ListTile(
        leading: _buildVersionIcon(context, version),
        title: Text(
          version.versionLabel,
          style: fluent.FluentTheme.of(context).typography.bodyStrong?.copyWith(
                color: isSelected 
                    ? fluent.FluentTheme.of(context).accentColor
                    : isCompareSelected
                        ? Colors.orange
                        : null,
              ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              version.description.isEmpty ? '无描述' : version.description,
              style: fluent.FluentTheme.of(context).typography.caption,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Text(
                  version.createdBy,
                  style: fluent.FluentTheme.of(context).typography.caption?.copyWith(
                        color: fluent.FluentTheme.of(context).resources.textFillColorSecondary,
                      ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${version.wordCount}字',
                  style: fluent.FluentTheme.of(context).typography.caption?.copyWith(
                        color: fluent.FluentTheme.of(context).resources.textFillColorSecondary,
                      ),
                ),
                const SizedBox(width: 8),
                Text(
                  _formatDateTime(version.createdAt),
                  style: fluent.FluentTheme.of(context).typography.caption?.copyWith(
                        color: fluent.FluentTheme.of(context).resources.textFillColorSecondary,
                      ),
                ),
              ],
            ),
          ],
        ),
        trailing: _buildVersionActions(context, version),
        onPressed: () => onVersionSelected?.call(version),
      ),
    );
  }

  Widget _buildVersionIcon(BuildContext context, ChapterVersion version) {
    IconData iconData;
    Color iconColor;
    
    if (version.isMainVersion) {
      iconData = fluent.FluentIcons.crown;
      iconColor = Colors.amber;
    } else if (version.isAutoSave) {
      iconData = fluent.FluentIcons.save;
      iconColor = Colors.grey;
    } else {
      switch (version.status) {
        case VersionStatus.draft:
          iconData = fluent.FluentIcons.edit;
          iconColor = Colors.grey;
          break;
        case VersionStatus.pendingReview:
          iconData = fluent.FluentIcons.clock;
          iconColor = Colors.orange;
          break;
        case VersionStatus.inReview:
          iconData = fluent.FluentIcons.review_solid;
          iconColor = Colors.blue;
          break;
        case VersionStatus.approved:
          iconData = fluent.FluentIcons.check_mark;
          iconColor = Colors.green;
          break;
        case VersionStatus.rejected:
          iconData = fluent.FluentIcons.cancel;
          iconColor = Colors.red;
          break;
        case VersionStatus.published:
          iconData = fluent.FluentIcons.publish_content;
          iconColor = Colors.purple;
          break;
        case VersionStatus.archived:
          iconData = fluent.FluentIcons.archive;
          iconColor = Colors.grey;
          break;
        case VersionStatus.deleted:
          iconData = fluent.FluentIcons.delete;
          iconColor = Colors.red;
          break;
      }
    }
    
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Icon(
        iconData,
        size: 16,
        color: iconColor,
      ),
    );
  }

  Widget _buildVersionActions(BuildContext context, ChapterVersion version) {
    return fluent.DropDownButton(
      title: const fluent.Icon(fluent.FluentIcons.more, size: 16),
      items: _buildActionItems(context, version),
    );
  }

  List<fluent.MenuFlyoutItemBase> _buildActionItems(BuildContext context, ChapterVersion version) {
    final items = <fluent.MenuFlyoutItemBase>[];
    
    // 基本操作
    if (showCompareMode) {
      items.add(fluent.MenuFlyoutItem(
        text: const Text('设为对比版本'),
        onPressed: () => onCompareVersionSelected?.call(version),
        leading: const fluent.Icon(fluent.FluentIcons.compare, size: 16),
      ));
      items.add(const fluent.MenuFlyoutSeparator());
    }
    
    // 审阅操作
    if (version.status == VersionStatus.pendingReview || version.status == VersionStatus.inReview) {
      items.add(fluent.MenuFlyoutItem(
        text: const Text('批准'),
        onPressed: () => onVersionAction?.call(version, 'approve'),
        leading: const fluent.Icon(fluent.FluentIcons.check_mark, size: 16),
      ));
      items.add(fluent.MenuFlyoutItem(
        text: const Text('拒绝'),
        onPressed: () => onVersionAction?.call(version, 'reject'),
        leading: const fluent.Icon(fluent.FluentIcons.cancel, size: 16),
      ));
      items.add(const fluent.MenuFlyoutSeparator());
    }
    
    if (version.status == VersionStatus.approved) {
      items.add(fluent.MenuFlyoutItem(
        text: const Text('发布'),
        onPressed: () => onVersionAction?.call(version, 'publish'),
        leading: const fluent.Icon(fluent.FluentIcons.publish_content, size: 16),
      ));
      items.add(const fluent.MenuFlyoutSeparator());
    }
    
    // 版本操作
    items.add(fluent.MenuFlyoutItem(
      text: const Text('恢复此版本'),
      onPressed: () => onVersionAction?.call(version, 'restore'),
      leading: const fluent.Icon(fluent.FluentIcons.undo, size: 16),
    ));
    
    if (!version.isMainVersion) {
      items.add(fluent.MenuFlyoutItem(
        text: const Text('设为主版本'),
        onPressed: () => onVersionAction?.call(version, 'setMain'),
        leading: const fluent.Icon(fluent.FluentIcons.crown, size: 16),
      ));
    }
    
    items.add(fluent.MenuFlyoutItem(
      text: const Text('复制版本'),
      onPressed: () => onVersionAction?.call(version, 'duplicate'),
      leading: const fluent.Icon(fluent.FluentIcons.copy, size: 16),
    ));
    
    // 删除操作
    if (version.status.canDelete) {
      items.add(const fluent.MenuFlyoutSeparator());
      items.add(fluent.MenuFlyoutItem(
        text: const Text('删除版本'),
        onPressed: () => onVersionAction?.call(version, 'delete'),
        leading: const fluent.Icon(fluent.FluentIcons.delete, size: 16),
      ));
    }
    
    return items;
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}

/// 版本统计卡片
class VersionStatisticsCard extends StatelessWidget {
  final List<ChapterVersion> versions;

  const VersionStatisticsCard({
    super.key,
    required this.versions,
  });

  @override
  Widget build(BuildContext context) {
    final statusCounts = <VersionStatus, int>{};
    int totalWordCount = 0;
    
    for (final version in versions) {
      statusCounts[version.status] = (statusCounts[version.status] ?? 0) + 1;
      totalWordCount += version.wordCount;
    }
    
    return fluent.Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '版本统计',
              style: fluent.FluentTheme.of(context).typography.bodyStrong,
            ),
            const SizedBox(height: 8),
            Text(
              '总版本数: ${versions.length}',
              style: fluent.FluentTheme.of(context).typography.caption,
            ),
            Text(
              '总字数: $totalWordCount',
              style: fluent.FluentTheme.of(context).typography.caption,
            ),
            const SizedBox(height: 8),
            ...statusCounts.entries.map((entry) => Padding(
                  padding: const EdgeInsets.only(bottom: 2),
                  child: Row(
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: Color(int.parse('0xFF${entry.key.colorHex.substring(1)}')),
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${entry.key.displayName}: ${entry.value}',
                        style: fluent.FluentTheme.of(context).typography.caption,
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }
}