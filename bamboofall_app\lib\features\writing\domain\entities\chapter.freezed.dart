// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chapter.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

Chapter _$ChapterFromJson(Map<String, dynamic> json) {
  return _Chapter.fromJson(json);
}

/// @nodoc
mixin _$Chapter {
  /// 章节唯一标识符
  String get id => throw _privateConstructorUsedError;

  /// 章节标题
  String get title => throw _privateConstructorUsedError;

  /// 章节内容（Markdown格式）
  String get content => throw _privateConstructorUsedError;

  /// 父章节ID（null表示根章节）
  String? get parentId => throw _privateConstructorUsedError;

  /// 章节层级（0为根章节）
  int get level => throw _privateConstructorUsedError;

  /// 章节在同级中的排序
  int get order => throw _privateConstructorUsedError;

  /// 章节状态
  ChapterStatus get status => throw _privateConstructorUsedError;

  /// 字数统计
  int get wordCount => throw _privateConstructorUsedError;

  /// 创建时间
  DateTime get createdAt => throw _privateConstructorUsedError;

  /// 最后修改时间
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// 章节标签
  List<String> get tags => throw _privateConstructorUsedError;

  /// 章节备注
  String get notes => throw _privateConstructorUsedError;

  /// 是否展开（用于UI显示）
  bool get isExpanded => throw _privateConstructorUsedError;

  /// 子章节列表
  List<Chapter> get children => throw _privateConstructorUsedError;

  /// Serializes this Chapter to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Chapter
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChapterCopyWith<Chapter> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChapterCopyWith<$Res> {
  factory $ChapterCopyWith(Chapter value, $Res Function(Chapter) then) =
      _$ChapterCopyWithImpl<$Res, Chapter>;
  @useResult
  $Res call({
    String id,
    String title,
    String content,
    String? parentId,
    int level,
    int order,
    ChapterStatus status,
    int wordCount,
    DateTime createdAt,
    DateTime updatedAt,
    List<String> tags,
    String notes,
    bool isExpanded,
    List<Chapter> children,
  });
}

/// @nodoc
class _$ChapterCopyWithImpl<$Res, $Val extends Chapter>
    implements $ChapterCopyWith<$Res> {
  _$ChapterCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Chapter
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? content = null,
    Object? parentId = freezed,
    Object? level = null,
    Object? order = null,
    Object? status = null,
    Object? wordCount = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? tags = null,
    Object? notes = null,
    Object? isExpanded = null,
    Object? children = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            content: null == content
                ? _value.content
                : content // ignore: cast_nullable_to_non_nullable
                      as String,
            parentId: freezed == parentId
                ? _value.parentId
                : parentId // ignore: cast_nullable_to_non_nullable
                      as String?,
            level: null == level
                ? _value.level
                : level // ignore: cast_nullable_to_non_nullable
                      as int,
            order: null == order
                ? _value.order
                : order // ignore: cast_nullable_to_non_nullable
                      as int,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as ChapterStatus,
            wordCount: null == wordCount
                ? _value.wordCount
                : wordCount // ignore: cast_nullable_to_non_nullable
                      as int,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            tags: null == tags
                ? _value.tags
                : tags // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            notes: null == notes
                ? _value.notes
                : notes // ignore: cast_nullable_to_non_nullable
                      as String,
            isExpanded: null == isExpanded
                ? _value.isExpanded
                : isExpanded // ignore: cast_nullable_to_non_nullable
                      as bool,
            children: null == children
                ? _value.children
                : children // ignore: cast_nullable_to_non_nullable
                      as List<Chapter>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ChapterImplCopyWith<$Res> implements $ChapterCopyWith<$Res> {
  factory _$$ChapterImplCopyWith(
    _$ChapterImpl value,
    $Res Function(_$ChapterImpl) then,
  ) = __$$ChapterImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String content,
    String? parentId,
    int level,
    int order,
    ChapterStatus status,
    int wordCount,
    DateTime createdAt,
    DateTime updatedAt,
    List<String> tags,
    String notes,
    bool isExpanded,
    List<Chapter> children,
  });
}

/// @nodoc
class __$$ChapterImplCopyWithImpl<$Res>
    extends _$ChapterCopyWithImpl<$Res, _$ChapterImpl>
    implements _$$ChapterImplCopyWith<$Res> {
  __$$ChapterImplCopyWithImpl(
    _$ChapterImpl _value,
    $Res Function(_$ChapterImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Chapter
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? content = null,
    Object? parentId = freezed,
    Object? level = null,
    Object? order = null,
    Object? status = null,
    Object? wordCount = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? tags = null,
    Object? notes = null,
    Object? isExpanded = null,
    Object? children = null,
  }) {
    return _then(
      _$ChapterImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        content: null == content
            ? _value.content
            : content // ignore: cast_nullable_to_non_nullable
                  as String,
        parentId: freezed == parentId
            ? _value.parentId
            : parentId // ignore: cast_nullable_to_non_nullable
                  as String?,
        level: null == level
            ? _value.level
            : level // ignore: cast_nullable_to_non_nullable
                  as int,
        order: null == order
            ? _value.order
            : order // ignore: cast_nullable_to_non_nullable
                  as int,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as ChapterStatus,
        wordCount: null == wordCount
            ? _value.wordCount
            : wordCount // ignore: cast_nullable_to_non_nullable
                  as int,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        tags: null == tags
            ? _value._tags
            : tags // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        notes: null == notes
            ? _value.notes
            : notes // ignore: cast_nullable_to_non_nullable
                  as String,
        isExpanded: null == isExpanded
            ? _value.isExpanded
            : isExpanded // ignore: cast_nullable_to_non_nullable
                  as bool,
        children: null == children
            ? _value._children
            : children // ignore: cast_nullable_to_non_nullable
                  as List<Chapter>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ChapterImpl implements _Chapter {
  const _$ChapterImpl({
    required this.id,
    required this.title,
    this.content = '',
    this.parentId,
    this.level = 0,
    this.order = 0,
    this.status = ChapterStatus.draft,
    this.wordCount = 0,
    required this.createdAt,
    required this.updatedAt,
    final List<String> tags = const [],
    this.notes = '',
    this.isExpanded = true,
    final List<Chapter> children = const [],
  }) : _tags = tags,
       _children = children;

  factory _$ChapterImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChapterImplFromJson(json);

  /// 章节唯一标识符
  @override
  final String id;

  /// 章节标题
  @override
  final String title;

  /// 章节内容（Markdown格式）
  @override
  @JsonKey()
  final String content;

  /// 父章节ID（null表示根章节）
  @override
  final String? parentId;

  /// 章节层级（0为根章节）
  @override
  @JsonKey()
  final int level;

  /// 章节在同级中的排序
  @override
  @JsonKey()
  final int order;

  /// 章节状态
  @override
  @JsonKey()
  final ChapterStatus status;

  /// 字数统计
  @override
  @JsonKey()
  final int wordCount;

  /// 创建时间
  @override
  final DateTime createdAt;

  /// 最后修改时间
  @override
  final DateTime updatedAt;

  /// 章节标签
  final List<String> _tags;

  /// 章节标签
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  /// 章节备注
  @override
  @JsonKey()
  final String notes;

  /// 是否展开（用于UI显示）
  @override
  @JsonKey()
  final bool isExpanded;

  /// 子章节列表
  final List<Chapter> _children;

  /// 子章节列表
  @override
  @JsonKey()
  List<Chapter> get children {
    if (_children is EqualUnmodifiableListView) return _children;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_children);
  }

  @override
  String toString() {
    return 'Chapter(id: $id, title: $title, content: $content, parentId: $parentId, level: $level, order: $order, status: $status, wordCount: $wordCount, createdAt: $createdAt, updatedAt: $updatedAt, tags: $tags, notes: $notes, isExpanded: $isExpanded, children: $children)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChapterImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.parentId, parentId) ||
                other.parentId == parentId) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.wordCount, wordCount) ||
                other.wordCount == wordCount) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.isExpanded, isExpanded) ||
                other.isExpanded == isExpanded) &&
            const DeepCollectionEquality().equals(other._children, _children));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    title,
    content,
    parentId,
    level,
    order,
    status,
    wordCount,
    createdAt,
    updatedAt,
    const DeepCollectionEquality().hash(_tags),
    notes,
    isExpanded,
    const DeepCollectionEquality().hash(_children),
  );

  /// Create a copy of Chapter
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChapterImplCopyWith<_$ChapterImpl> get copyWith =>
      __$$ChapterImplCopyWithImpl<_$ChapterImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChapterImplToJson(this);
  }
}

abstract class _Chapter implements Chapter {
  const factory _Chapter({
    required final String id,
    required final String title,
    final String content,
    final String? parentId,
    final int level,
    final int order,
    final ChapterStatus status,
    final int wordCount,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final List<String> tags,
    final String notes,
    final bool isExpanded,
    final List<Chapter> children,
  }) = _$ChapterImpl;

  factory _Chapter.fromJson(Map<String, dynamic> json) = _$ChapterImpl.fromJson;

  /// 章节唯一标识符
  @override
  String get id;

  /// 章节标题
  @override
  String get title;

  /// 章节内容（Markdown格式）
  @override
  String get content;

  /// 父章节ID（null表示根章节）
  @override
  String? get parentId;

  /// 章节层级（0为根章节）
  @override
  int get level;

  /// 章节在同级中的排序
  @override
  int get order;

  /// 章节状态
  @override
  ChapterStatus get status;

  /// 字数统计
  @override
  int get wordCount;

  /// 创建时间
  @override
  DateTime get createdAt;

  /// 最后修改时间
  @override
  DateTime get updatedAt;

  /// 章节标签
  @override
  List<String> get tags;

  /// 章节备注
  @override
  String get notes;

  /// 是否展开（用于UI显示）
  @override
  bool get isExpanded;

  /// 子章节列表
  @override
  List<Chapter> get children;

  /// Create a copy of Chapter
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChapterImplCopyWith<_$ChapterImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
