import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';

import 'package:bamboofall_app/features/project/domain/entities/project.dart';
import 'package:bamboofall_app/features/writing/domain/entities/chapter.dart';
import 'package:bamboofall_app/features/bible/domain/entities/character_bible.dart';
import 'package:bamboofall_app/features/bible/domain/entities/location_bible.dart';
import 'package:bamboofall_app/features/bible/domain/entities/story_bible.dart';
import 'package:bamboofall_app/features/ai_integration/domain/entities/llm_model.dart';
import 'package:bamboofall_app/features/templates/domain/entities/template.dart';

/// 集成测试辅助工具类
class IntegrationTestHelpers {
  /// 验证执行时间是否在预期范围内
  static Future<void> verifyExecutionTime(
    Future<void> Function() operation, {
    Duration? minDuration,
    Duration? maxDuration,
  }) async {
    final stopwatch = Stopwatch()..start();
    
    await operation();
    
    stopwatch.stop();
    final actualDuration = stopwatch.elapsed;

    if (minDuration != null) {
      expect(
        actualDuration.inMilliseconds,
        greaterThanOrEqualTo(minDuration.inMilliseconds),
        reason: '执行时间 ${actualDuration.inMilliseconds}ms 小于最小预期时间 ${minDuration.inMilliseconds}ms',
      );
    }

    if (maxDuration != null) {
      expect(
        actualDuration.inMilliseconds,
        lessThanOrEqualTo(maxDuration.inMilliseconds),
        reason: '执行时间 ${actualDuration.inMilliseconds}ms 超过最大预期时间 ${maxDuration.inMilliseconds}ms',
      );
    }
  }

  /// 验证方法调用
  static void verifyMethodCall(
    dynamic mock,
    String methodName,
    List<dynamic> arguments, {
    int expectedCallCount = 1,
  }) {
    try {
      verify(mock.noSuchMethod(Invocation.method(Symbol(methodName), arguments)))
          .called(expectedCallCount);
    } catch (e) {
      // 如果直接验证失败，尝试其他验证方式
      expect(mock, isA<Mock>());
    }
  }

  /// 创建测试用的 ProviderContainer
  static ProviderContainer createTestContainer({
    List<Override> overrides = const [],
  }) {
    return ProviderContainer(overrides: overrides);
  }

  /// 等待异步操作完成
  static Future<void> waitForAsyncOperations() async {
    await Future.delayed(const Duration(milliseconds: 100));
  }

  /// 模拟用户输入
  static Future<void> simulateUserInput(
    WidgetTester tester,
    String text, {
    Finder? finder,
  }) async {
    final textField = finder ?? find.byType(TextField).first;
    await tester.enterText(textField, text);
    await tester.pumpAndSettle();
  }

  /// 模拟用户点击
  static Future<void> simulateUserTap(
    WidgetTester tester,
    Finder finder,
  ) async {
    await tester.tap(finder);
    await tester.pumpAndSettle();
  }

  /// 模拟用户滚动
  static Future<void> simulateUserScroll(
    WidgetTester tester,
    Finder finder,
    Offset offset,
  ) async {
    await tester.drag(finder, offset);
    await tester.pumpAndSettle();
  }

  /// 验证UI元素存在
  static void verifyUIElement(
    Finder finder, {
    int expectedCount = 1,
  }) {
    if (expectedCount == 1) {
      expect(finder, findsOneWidget);
    } else if (expectedCount == 0) {
      expect(finder, findsNothing);
    } else {
      expect(finder, findsNWidgets(expectedCount));
    }
  }

  /// 验证文本内容
  static void verifyTextContent(
    String expectedText, {
    bool exactMatch = true,
  }) {
    if (exactMatch) {
      expect(find.text(expectedText), findsOneWidget);
    } else {
      expect(find.textContaining(expectedText), findsWidgets);
    }
  }
}

/// 测试数据生成器
class TestDataGenerator {
  /// 生成测试项目
  static Project generateProject({
    String? id,
    String? title,
    String? description,
    ProjectGenre? genre,
    ProjectStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Project(
      id: id ?? 'test-project-${DateTime.now().millisecondsSinceEpoch}',
      title: title ?? '测试项目',
      description: description ?? '这是一个测试项目的描述',
      genre: genre ?? ProjectGenre.fantasy,
      status: status ?? ProjectStatus.active,
      createdAt: createdAt ?? DateTime.now(),
      updatedAt: updatedAt ?? DateTime.now(),
      settings: const ProjectSettings(),
    );
  }

  /// 生成测试章节
  static Chapter generateChapter({
    String? id,
    String? projectId,
    String? title,
    String? content,
    int? order,
    ChapterStatus? status,
    int? wordCount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    final chapterContent = content ?? '这是测试章节的内容。包含了一些示例文字用于测试。';
    return Chapter(
      id: id ?? 'test-chapter-${DateTime.now().millisecondsSinceEpoch}',
      projectId: projectId ?? 'test-project',
      title: title ?? '测试章节',
      content: chapterContent,
      order: order ?? 1,
      status: status ?? ChapterStatus.draft,
      wordCount: wordCount ?? chapterContent.length,
      createdAt: createdAt ?? DateTime.now(),
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// 生成测试角色
  static CharacterBible generateCharacter({
    String? id,
    String? projectId,
    String? name,
    int? age,
    Gender? gender,
    String? occupation,
    List<String>? personality,
    String? appearance,
    String? background,
    Map<String, CharacterRelationship>? relationships,
    List<String>? skills,
    List<String>? goals,
    List<String>? fears,
    List<String>? secrets,
    String? characterArc,
    String? notes,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CharacterBible(
      id: id ?? 'test-char-${DateTime.now().millisecondsSinceEpoch}',
      projectId: projectId ?? 'test-project',
      name: name ?? '测试角色',
      age: age ?? 25,
      gender: gender ?? Gender.male,
      occupation: occupation ?? '测试职业',
      personality: personality ?? ['友善', '聪明'],
      appearance: appearance ?? '中等身材，黑发黑眼',
      background: background ?? '测试角色的背景故事',
      relationships: relationships ?? {},
      skills: skills ?? ['测试技能1', '测试技能2'],
      goals: goals ?? ['测试目标1', '测试目标2'],
      fears: fears ?? ['测试恐惧1'],
      secrets: secrets ?? ['测试秘密1'],
      characterArc: characterArc ?? '角色发展弧线',
      notes: notes ?? '角色备注',
      tags: tags ?? ['主角', '测试'],
      createdAt: createdAt ?? DateTime.now(),
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// 生成测试地点
  static LocationBible generateLocation({
    String? id,
    String? projectId,
    String? name,
    LocationType? type,
    String? description,
    String? geography,
    String? climate,
    String? culture,
    String? history,
    String? significance,
    Map<String, LocationRelationship>? relationships,
    List<String>? connectedCharacters,
    List<String>? tags,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LocationBible(
      id: id ?? 'test-location-${DateTime.now().millisecondsSinceEpoch}',
      projectId: projectId ?? 'test-project',
      name: name ?? '测试地点',
      type: type ?? LocationType.city,
      description: description ?? '测试地点的描述',
      geography: geography ?? '地理环境描述',
      climate: climate ?? '气候描述',
      culture: culture ?? '文化描述',
      history: history ?? '历史背景',
      significance: significance ?? '重要性说明',
      relationships: relationships ?? {},
      connectedCharacters: connectedCharacters ?? [],
      tags: tags ?? ['城市', '测试'],
      notes: notes ?? '地点备注',
      createdAt: createdAt ?? DateTime.now(),
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// 生成测试故事
  static StoryBible generateStory({
    String? id,
    String? projectId,
    String? title,
    String? summary,
    String? theme,
    String? conflict,
    String? resolution,
    List<TimelineEvent>? timeline,
    List<PlotPoint>? plotPoints,
    List<String>? connectedCharacters,
    List<String>? connectedLocations,
    List<String>? tags,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return StoryBible(
      id: id ?? 'test-story-${DateTime.now().millisecondsSinceEpoch}',
      projectId: projectId ?? 'test-project',
      title: title ?? '测试故事',
      summary: summary ?? '测试故事的摘要',
      theme: theme ?? '测试主题',
      conflict: conflict ?? '测试冲突',
      resolution: resolution ?? '测试解决方案',
      timeline: timeline ?? [
        TimelineEvent(
          id: 'event-1',
          title: '测试事件',
          description: '测试事件描述',
          date: DateTime.now(),
          importance: EventImportance.major,
        ),
      ],
      plotPoints: plotPoints ?? [
        PlotPoint(
          id: 'plot-1',
          title: '测试情节点',
          description: '测试情节点描述',
          type: PlotPointType.incitingIncident,
          chapterReference: 'chapter-1',
        ),
      ],
      connectedCharacters: connectedCharacters ?? [],
      connectedLocations: connectedLocations ?? [],
      tags: tags ?? ['主线', '测试'],
      notes: notes ?? '故事备注',
      createdAt: createdAt ?? DateTime.now(),
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// 生成测试AI模型
  static LLMModel generateLLMModel({
    String? id,
    String? name,
    LLMProvider? provider,
    int? maxTokens,
    List<ModelFeature>? supportedFeatures,
    ModelPricing? pricing,
    bool? isAvailable,
    String? description,
    String? version,
  }) {
    return LLMModel(
      id: id ?? 'test-model-${DateTime.now().millisecondsSinceEpoch}',
      name: name ?? '测试模型',
      provider: provider ?? LLMProvider.openai,
      maxTokens: maxTokens ?? 4000,
      supportedFeatures: supportedFeatures ?? [ModelFeature.completion, ModelFeature.streaming],
      pricing: pricing ?? ModelPricing(
        inputTokenPrice: 0.001,
        outputTokenPrice: 0.002,
        currency: 'USD',
      ),
      isAvailable: isAvailable ?? true,
      description: description ?? '测试AI模型描述',
      version: version ?? '1.0.0',
    );
  }

  /// 生成测试模板
  static Template generateTemplate({
    String? id,
    String? name,
    String? description,
    TemplateCategory? category,
    String? content,
    List<TemplateVariable>? variables,
    Map<String, dynamic>? metadata,
    List<String>? tags,
    bool? isPublic,
    String? authorId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Template(
      id: id ?? 'test-template-${DateTime.now().millisecondsSinceEpoch}',
      name: name ?? '测试模板',
      description: description ?? '测试模板描述',
      category: category ?? TemplateCategory.character,
      content: content ?? '这是一个测试模板的内容，包含变量 {{name}} 和 {{description}}。',
      variables: variables ?? [
        TemplateVariable(
          name: 'name',
          description: '名称',
          type: VariableType.text,
          required: true,
          defaultValue: '',
        ),
        TemplateVariable(
          name: 'description',
          description: '描述',
          type: VariableType.text,
          required: false,
          defaultValue: '',
        ),
      ],
      metadata: metadata ?? {},
      tags: tags ?? ['测试', '模板'],
      isPublic: isPublic ?? true,
      authorId: authorId ?? 'test-author',
      createdAt: createdAt ?? DateTime.now(),
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// 生成批量测试数据
  static List<T> generateBatch<T>(
    T Function() generator,
    int count,
  ) {
    return List.generate(count, (_) => generator());
  }

  /// 生成大量测试文本
  static String generateLargeText(int wordCount) {
    final words = [
      '测试', '内容', '文字', '段落', '章节', '故事', '角色', '情节',
      '描述', '背景', '设定', '世界', '冒险', '旅程', '成长', '变化',
    ];
    
    final buffer = StringBuffer();
    for (int i = 0; i < wordCount; i++) {
      buffer.write(words[i % words.length]);
      if (i < wordCount - 1) {
        buffer.write(' ');
      }
    }
    
    return buffer.toString();
  }

  /// 生成随机字符串
  static String generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    return String.fromCharCodes(
      Iterable.generate(length, (index) => chars.codeUnitAt((random + index) % chars.length)),
    );
  }
}

/// 测试断言辅助工具
class TestAssertions {
  /// 验证项目数据完整性
  static void verifyProjectIntegrity(Project project) {
    expect(project.id, isNotEmpty);
    expect(project.title, isNotEmpty);
    expect(project.createdAt, isNotNull);
    expect(project.updatedAt, isNotNull);
    expect(project.updatedAt.isAfter(project.createdAt) || 
           project.updatedAt.isAtSameMomentAs(project.createdAt), isTrue);
  }

  /// 验证章节数据完整性
  static void verifyChapterIntegrity(Chapter chapter) {
    expect(chapter.id, isNotEmpty);
    expect(chapter.projectId, isNotEmpty);
    expect(chapter.title, isNotEmpty);
    expect(chapter.order, greaterThan(0));
    expect(chapter.wordCount, greaterThanOrEqualTo(0));
    expect(chapter.createdAt, isNotNull);
    expect(chapter.updatedAt, isNotNull);
  }

  /// 验证角色数据完整性
  static void verifyCharacterIntegrity(CharacterBible character) {
    expect(character.id, isNotEmpty);
    expect(character.projectId, isNotEmpty);
    expect(character.name, isNotEmpty);
    expect(character.age, greaterThan(0));
    expect(character.createdAt, isNotNull);
    expect(character.updatedAt, isNotNull);
  }

  /// 验证数据关系一致性
  static void verifyDataConsistency(
    String projectId,
    List<Chapter> chapters,
    List<CharacterBible> characters,
  ) {
    // 验证所有章节都属于同一项目
    for (final chapter in chapters) {
      expect(chapter.projectId, equals(projectId));
    }

    // 验证所有角色都属于同一项目
    for (final character in characters) {
      expect(character.projectId, equals(projectId));
    }

    // 验证章节顺序的唯一性
    final orders = chapters.map((c) => c.order).toList();
    final uniqueOrders = orders.toSet();
    expect(orders.length, equals(uniqueOrders.length), 
           reason: '章节顺序应该是唯一的');
  }

  /// 验证性能指标
  static void verifyPerformanceMetrics(
    Duration actualDuration,
    Duration expectedMaxDuration, {
    String? operationName,
  }) {
    final operationDesc = operationName ?? '操作';
    expect(
      actualDuration.inMilliseconds,
      lessThanOrEqualTo(expectedMaxDuration.inMilliseconds),
      reason: '$operationDesc 执行时间 ${actualDuration.inMilliseconds}ms 超过预期的 ${expectedMaxDuration.inMilliseconds}ms',
    );
  }

  /// 验证错误处理
  static void verifyErrorHandling(
    Future<void> Function() operation,
    Type expectedErrorType,
  ) {
    expect(
      operation,
      throwsA(isA<dynamic>().having(
        (e) => e.runtimeType,
        'error type',
        expectedErrorType,
      )),
    );
  }
}

/// Mock数据提供者
class MockDataProvider {
  /// 提供模拟的项目列表
  static List<Project> getMockProjects(int count) {
    return TestDataGenerator.generateBatch(
      () => TestDataGenerator.generateProject(),
      count,
    );
  }

  /// 提供模拟的章节列表
  static List<Chapter> getMockChapters(String projectId, int count) {
    return List.generate(count, (index) => TestDataGenerator.generateChapter(
      projectId: projectId,
      order: index + 1,
      title: '第${index + 1}章',
    ));
  }

  /// 提供模拟的角色列表
  static List<CharacterBible> getMockCharacters(String projectId, int count) {
    return List.generate(count, (index) => TestDataGenerator.generateCharacter(
      projectId: projectId,
      name: '角色${index + 1}',
    ));
  }

  /// 提供模拟的AI模型列表
  static List<LLMModel> getMockLLMModels(int count) {
    return List.generate(count, (index) => TestDataGenerator.generateLLMModel(
      name: '模型${index + 1}',
      provider: LLMProvider.values[index % LLMProvider.values.length],
    ));
  }
}