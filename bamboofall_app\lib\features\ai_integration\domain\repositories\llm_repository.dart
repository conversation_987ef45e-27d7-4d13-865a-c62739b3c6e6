import '../entities/ai_model.dart';

/// AI语言模型仓库接口
/// 定义了与AI模型交互的统一接口
abstract class LLMRepository {
  // ==================== 模型管理 ====================
  
  /// 获取所有可用的AI模型
  Future<List<AIModel>> getAvailableModels();
  
  /// 根据提供商获取模型
  Future<List<AIModel>> getModelsByProvider(AIProvider provider);
  
  /// 根据ID获取特定模型
  Future<AIModel?> getModelById(String modelId);
  
  /// 检查模型是否可用
  Future<bool> isModelAvailable(String modelId);
  
  /// 获取模型能力
  Future<AICapabilities> getModelCapabilities(String modelId);
  
  // ==================== 配置管理 ====================
  
  /// 保存模型配置
  Future<void> saveModelConfig(AIModelConfig config);
  
  /// 获取模型配置
  Future<AIModelConfig?> getModelConfig(String modelId);
  
  /// 获取所有模型配置
  Future<List<AIModelConfig>> getAllModelConfigs();
  
  /// 删除模型配置
  Future<void> deleteModelConfig(String modelId);
  
  /// 更新模型配置
  Future<void> updateModelConfig(String modelId, AIModelConfig config);
  
  /// 测试模型配置连接
  Future<bool> testModelConnection(String modelId);
  
  // ==================== AI交互 ====================
  
  /// 发送聊天请求
  Future<AIResponse> chat(AIRequest request);
  
  /// 发送流式聊天请求
  Stream<AIResponse> chatStream(AIRequest request);
  
  /// 发送补全请求
  Future<AIResponse> completion(AIRequest request);
  
  /// 发送流式补全请求
  Stream<AIResponse> completionStream(AIRequest request);
  
  /// 批量处理请求
  Future<List<AIResponse>> batchProcess(List<AIRequest> requests);
  
  // ==================== 会话管理 ====================
  
  /// 创建新会话
  Future<String> createSession({
    required String modelId,
    String? systemPrompt,
    Map<String, dynamic>? metadata,
  });
  
  /// 获取会话历史
  Future<List<AIMessage>> getSessionHistory(String sessionId);
  
  /// 添加消息到会话
  Future<void> addMessageToSession(String sessionId, AIMessage message);
  
  /// 清除会话历史
  Future<void> clearSessionHistory(String sessionId);
  
  /// 删除会话
  Future<void> deleteSession(String sessionId);
  
  /// 获取所有会话
  Future<List<String>> getAllSessions();
  
  // ==================== 使用统计 ====================
  
  /// 记录使用统计
  Future<void> recordUsage(String modelId, AIUsage usage);
  
  /// 获取使用统计
  Future<AIUsage> getUsageStats(String modelId, {
    DateTime? startDate,
    DateTime? endDate,
  });
  
  /// 获取总使用统计
  Future<Map<String, AIUsage>> getTotalUsageStats({
    DateTime? startDate,
    DateTime? endDate,
  });
  
  /// 重置使用统计
  Future<void> resetUsageStats(String modelId);
  
  // ==================== 错误处理 ====================
  
  /// 记录错误
  Future<void> recordError(String modelId, AIError error);
  
  /// 获取错误历史
  Future<List<AIError>> getErrorHistory(String modelId, {
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  });
  
  /// 清除错误历史
  Future<void> clearErrorHistory(String modelId);
  
  // ==================== 缓存管理 ====================
  
  /// 缓存响应
  Future<void> cacheResponse(String key, AIResponse response);
  
  /// 获取缓存响应
  Future<AIResponse?> getCachedResponse(String key);
  
  /// 清除缓存
  Future<void> clearCache();
  
  /// 清除特定模型的缓存
  Future<void> clearModelCache(String modelId);
  
  // ==================== 健康检查 ====================
  
  /// 检查服务健康状态
  Future<Map<String, bool>> checkHealth();
  
  /// 检查特定模型健康状态
  Future<bool> checkModelHealth(String modelId);
  
  /// 获取服务状态
  Future<Map<String, dynamic>> getServiceStatus();
  
  // ==================== 工具方法 ====================
  
  /// 估算token数量
  int estimateTokens(String text, {String? modelId});
  
  /// 验证请求参数
  bool validateRequest(AIRequest request);
  
  /// 生成请求ID
  String generateRequestId();
  
  /// 格式化错误信息
  String formatError(AIError error);
  
  /// 计算请求成本
  Future<double> calculateCost(String modelId, AIUsage usage);
}

/// AI模型仓库异常
class LLMRepositoryException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;
  
  const LLMRepositoryException(
    this.message, {
    this.code,
    this.originalError,
  });
  
  @override
  String toString() {
    if (code != null) {
      return 'LLMRepositoryException($code): $message';
    }
    return 'LLMRepositoryException: $message';
  }
}

/// 网络异常
class NetworkException extends LLMRepositoryException {
  const NetworkException(super.message, {super.code, super.originalError});
}

/// 认证异常
class AuthenticationException extends LLMRepositoryException {
  const AuthenticationException(super.message, {super.code, super.originalError});
}

/// 配额异常
class QuotaExceededException extends LLMRepositoryException {
  const QuotaExceededException(super.message, {super.code, super.originalError});
}

/// 模型不可用异常
class ModelUnavailableException extends LLMRepositoryException {
  const ModelUnavailableException(super.message, {super.code, super.originalError});
}

/// 参数验证异常
class ValidationException extends LLMRepositoryException {
  const ValidationException(super.message, {super.code, super.originalError});
}

/// 超时异常
class TimeoutException extends LLMRepositoryException {
  const TimeoutException(super.message, {super.code, super.originalError});
}