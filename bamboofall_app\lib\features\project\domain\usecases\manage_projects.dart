import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../entities/project.dart';
import '../repositories/project_repository.dart';

/// 项目管理用例
/// 提供项目生命周期管理的业务逻辑
class ManageProjectsUseCase {
  final ProjectRepository _repository;

  ManageProjectsUseCase(this._repository);

  /// 生成项目ID
  String _generateProjectId() {
    return 'project_${DateTime.now().millisecondsSinceEpoch}_${(1000 + (999 * (DateTime.now().microsecond / 1000000))).round()}';
  }

  /// 获取所有项目
  Future<List<Project>> getAllProjects() async {
    return await _repository.getAllProjects();
  }

  /// 根据ID获取项目
  Future<Project?> getProjectById(String projectId) async {
    return await _repository.getProjectById(projectId);
  }

  /// 获取活跃项目
  Future<List<Project>> getActiveProjects() async {
    return await _repository.getProjectsByStatus(ProjectStatus.active);
  }

  /// 获取已完成项目
  Future<List<Project>> getCompletedProjects() async {
    return await _repository.getProjectsByStatus(ProjectStatus.completed);
  }

  /// 获取已归档项目
  Future<List<Project>> getArchivedProjects() async {
    return await _repository.getProjectsByStatus(ProjectStatus.archived);
  }

  /// 获取收藏项目
  Future<List<Project>> getFavoriteProjects() async {
    return await _repository.getFavoriteProjects();
  }

  /// 获取最近访问的项目
  Future<List<Project>> getRecentProjects({int limit = 10}) async {
    return await _repository.getRecentProjects(limit: limit);
  }

  /// 搜索项目
  Future<List<Project>> searchProjects(String query) async {
    if (query.trim().isEmpty) {
      return [];
    }
    return await _repository.searchProjects(query);
  }

  /// 根据标签筛选项目
  Future<List<Project>> getProjectsByTag(String tag) async {
    return await _repository.getProjectsByTag(tag);
  }

  /// 创建新项目
  Future<Project> createProject({
    required String name,
    required String createdBy,
    String? description,
    ProjectType type = ProjectType.novel,
    List<String>? tags,
    ProjectConfig? config,
  }) async {
    final now = DateTime.now();
    final projectId = _generateProjectId();

    final project = Project(
      id: projectId,
      name: name,
      description: description ?? '',
      type: type,
      status: ProjectStatus.active,
      createdAt: now,
      updatedAt: now,
      createdBy: createdBy,
      tags: tags ?? [],
      config: config ?? ProjectConfig(
        targetWordCount: type.defaultTargetWordCount,
      ),
      statistics: const ProjectStatistics(),
    );

    return await _repository.createProject(project);
  }

  /// 基于模板创建项目
  Future<Project> createProjectFromTemplate({
    required String templateId,
    required String projectName,
    required String createdBy,
    String? description,
  }) async {
    return await _repository.createProjectFromTemplate(
      templateId,
      projectName,
      createdBy,
    );
  }

  /// 更新项目
  Future<Project> updateProject(Project project) async {
    final updatedProject = project.copyWith(updatedAt: DateTime.now());
    return await _repository.updateProject(updatedProject);
  }

  /// 更新项目基本信息
  Future<Project> updateProjectInfo({
    required String projectId,
    String? name,
    String? description,
    String? coverImagePath,
    String? colorTheme,
  }) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    final updatedProject = project.copyWith(
      name: name ?? project.name,
      description: description ?? project.description,
      coverImagePath: coverImagePath ?? project.coverImagePath,
      colorTheme: colorTheme ?? project.colorTheme,
      updatedAt: DateTime.now(),
    );

    return await _repository.updateProject(updatedProject);
  }

  /// 更新项目配置
  Future<Project> updateProjectConfig({
    required String projectId,
    required ProjectConfig config,
  }) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    final updatedProject = project.updateConfig(config);
    return await _repository.updateProject(updatedProject);
  }

  /// 更新项目统计信息
  Future<Project> updateProjectStatistics({
    required String projectId,
    required ProjectStatistics statistics,
  }) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    final updatedProject = project.updateStatistics(statistics);
    return await _repository.updateProject(updatedProject);
  }

  /// 切换项目收藏状态
  Future<Project> toggleProjectFavorite(String projectId) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    final updatedProject = project.toggleFavorite();
    return await _repository.updateProject(updatedProject);
  }

  /// 添加项目标签
  Future<Project> addProjectTag(String projectId, String tag) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    final updatedProject = project.addTag(tag);
    return await _repository.updateProject(updatedProject);
  }

  /// 移除项目标签
  Future<Project> removeProjectTag(String projectId, String tag) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    final updatedProject = project.removeTag(tag);
    return await _repository.updateProject(updatedProject);
  }

  /// 暂停项目
  Future<Project> pauseProject(String projectId) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    if (project.status != ProjectStatus.active) {
      throw Exception('Only active projects can be paused');
    }

    final updatedProject = project.pause();
    return await _repository.updateProject(updatedProject);
  }

  /// 恢复项目
  Future<Project> resumeProject(String projectId) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    if (project.status != ProjectStatus.paused) {
      throw Exception('Only paused projects can be resumed');
    }

    final updatedProject = project.resume();
    return await _repository.updateProject(updatedProject);
  }

  /// 完成项目
  Future<Project> completeProject(String projectId) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    if (!project.isActive && project.status != ProjectStatus.paused) {
      throw Exception('Only active or paused projects can be completed');
    }

    final updatedProject = project.complete();
    return await _repository.updateProject(updatedProject);
  }

  /// 归档项目
  Future<Project> archiveProject(String projectId, String reason) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    if (project.isArchived) {
      throw Exception('Project is already archived');
    }

    return await _repository.archiveProject(projectId, reason);
  }

  /// 恢复归档项目
  Future<Project> restoreProject(String projectId) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    if (!project.isArchived) {
      throw Exception('Project is not archived');
    }

    return await _repository.restoreProject(projectId);
  }

  /// 删除项目
  Future<void> deleteProject(String projectId) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    if (!project.status.canDelete) {
      throw Exception('Project cannot be deleted in current status: ${project.status.displayName}');
    }

    await _repository.deleteProject(projectId);
  }

  /// 复制项目
  Future<Project> duplicateProject(String projectId, String newName) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    return await _repository.duplicateProject(projectId, newName);
  }

  /// 访问项目（更新访问时间）
  Future<void> accessProject(String projectId) async {
    await _repository.updateProjectAccessTime(projectId);
  }

  /// 导出项目
  Future<Map<String, dynamic>> exportProject(String projectId) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    return await _repository.exportProject(projectId);
  }

  /// 导入项目
  Future<Project> importProject(Map<String, dynamic> projectData) async {
    return await _repository.importProject(projectData);
  }

  /// 获取项目统计信息
  Future<ProjectOverviewStatistics> getProjectOverviewStatistics() async {
    final statusStats = await _repository.getProjectStatistics();
    final allProjects = await _repository.getAllProjects();
    
    final totalProjects = allProjects.length;
    final totalWordCount = allProjects.fold<int>(
      0, 
      (sum, project) => sum + project.statistics.totalWordCount,
    );
    
    final completedProjects = statusStats[ProjectStatus.completed] ?? 0;
    final completionRate = totalProjects > 0 ? completedProjects / totalProjects : 0.0;
    
    final recentProjects = allProjects
        .where((p) => p.updatedAt.isAfter(DateTime.now().subtract(const Duration(days: 7))))
        .length;

    return ProjectOverviewStatistics(
      totalProjects: totalProjects,
      activeProjects: statusStats[ProjectStatus.active] ?? 0,
      completedProjects: completedProjects,
      archivedProjects: statusStats[ProjectStatus.archived] ?? 0,
      totalWordCount: totalWordCount,
      completionRate: completionRate,
      recentlyActiveProjects: recentProjects,
    );
  }

  /// 获取项目模板
  Future<List<ProjectTemplate>> getProjectTemplates() async {
    return await _repository.getProjectTemplates();
  }

  /// 创建项目模板
  Future<ProjectTemplate> createProjectTemplate({
    required String projectId,
    required String templateName,
    required String templateDescription,
    bool isBuiltIn = false,
  }) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    final template = ProjectTemplate(
      id: _generateProjectId(),
      name: templateName,
      description: templateDescription,
      type: project.type,
      config: project.config,
      tags: project.tags,
      isBuiltIn: isBuiltIn,
      createdAt: DateTime.now(),
    );

    return await _repository.createProjectTemplate(template);
  }

  /// 删除项目模板
  Future<void> deleteProjectTemplate(String templateId) async {
    await _repository.deleteProjectTemplate(templateId);
  }

  /// 清理已删除的项目
  Future<void> cleanupDeletedProjects({Duration olderThan = const Duration(days: 30)}) async {
    await _repository.cleanupDeletedProjects(olderThan: olderThan);
  }

  /// 获取所有项目标签
  Future<List<String>> getAllProjectTags() async {
    final projects = await _repository.getAllProjects();
    final allTags = <String>{};
    
    for (final project in projects) {
      allTags.addAll(project.tags);
    }
    
    return allTags.toList()..sort();
  }

  /// 验证项目名称是否可用
  Future<bool> isProjectNameAvailable(String name, {String? excludeProjectId}) async {
    final projects = await _repository.getAllProjects();
    return !projects.any((p) => 
        p.name.toLowerCase() == name.toLowerCase() && 
        p.id != excludeProjectId);
  }
}

/// 项目概览统计信息
class ProjectOverviewStatistics {
  final int totalProjects;
  final int activeProjects;
  final int completedProjects;
  final int archivedProjects;
  final int totalWordCount;
  final double completionRate;
  final int recentlyActiveProjects;

  const ProjectOverviewStatistics({
    required this.totalProjects,
    required this.activeProjects,
    required this.completedProjects,
    required this.archivedProjects,
    required this.totalWordCount,
    required this.completionRate,
    required this.recentlyActiveProjects,
  });

  /// 获取平均项目字数
  int get averageWordCount => totalProjects > 0 ? totalWordCount ~/ totalProjects : 0;

  /// 获取活跃项目比例
  double get activeProjectRate => totalProjects > 0 ? activeProjects / totalProjects : 0.0;
}

/// 项目管理用例提供者
final manageProjectsUseCaseProvider = Provider<ManageProjectsUseCase>((ref) {
  final repository = ref.read(projectRepositoryProvider);
  return ManageProjectsUseCase(repository);
});

/// 项目仓库提供者（需要在repository实现文件中定义）
final projectRepositoryProvider = Provider<ProjectRepository>((ref) {
  throw UnimplementedError('ProjectRepository implementation not provided');
});