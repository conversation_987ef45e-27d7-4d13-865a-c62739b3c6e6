import 'package:freezed_annotation/freezed_annotation.dart';

part 'world_settings.freezed.dart';
part 'world_settings.g.dart';

/// 世界设定
@freezed
class WorldSettings with _$WorldSettings {
  const factory WorldSettings({
    required String id,
    required String name,
    String? description,
    required WorldType type,
    required TechnologyLevel technologyLevel,
    required MagicLevel magicLevel,
    WorldPhysics? physics,
    WorldHistory? history,
    WorldGeography? geography,
    WorldSociety? society,
    WorldEconomy? economy,
    WorldReligion? religion,
    @Default([]) List<WorldRule> rules,
    @Default([]) List<WorldEvent> majorEvents,
    @Default([]) List<Species> species,
    @Default([]) List<Language> languages,
    @Default([]) List<Calendar> calendars,
    @Default([]) List<Currency> currencies,
    @Default({}) Map<String, dynamic> customSettings,
    @Default({}) Map<String, dynamic> metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _WorldSettings;

  factory WorldSettings.fromJson(Map<String, dynamic> json) => _$WorldSettingsFromJson(json);
}

/// 世界类型
enum WorldType {
  fantasy('奇幻'),
  sciFi('科幻'),
  modern('现代'),
  historical('历史'),
  postApocalyptic('末世'),
  steampunk('蒸汽朋克'),
  cyberpunk('赛博朋克'),
  alternate('架空'),
  mixed('混合');

  const WorldType(this.displayName);
  final String displayName;
}

/// 科技水平
enum TechnologyLevel {
  primitive('原始'),
  ancient('古代'),
  medieval('中世纪'),
  renaissance('文艺复兴'),
  industrial('工业'),
  modern('现代'),
  futuristic('未来'),
  postApocalyptic('末世'),
  mixed('混合');

  const TechnologyLevel(this.displayName);
  final String displayName;
}

/// 魔法水平
enum MagicLevel {
  none('无魔法'),
  rare('稀有'),
  uncommon('不常见'),
  common('常见'),
  abundant('丰富'),
  omnipresent('无处不在');

  const MagicLevel(this.displayName);
  final String displayName;
}

/// 世界物理法则
@freezed
class WorldPhysics with _$WorldPhysics {
  const factory WorldPhysics({
    @Default([]) List<PhysicalLaw> laws,
    @Default([]) List<PhysicalConstant> constants,
    @Default([]) List<PhysicalForce> forces,
    String? gravityDescription,
    String? timeDescription,
    String? spaceDescription,
    @Default([]) List<String> dimensions,
    @Default({}) Map<String, dynamic> customPhysics,
  }) = _WorldPhysics;

  factory WorldPhysics.fromJson(Map<String, dynamic> json) => _$WorldPhysicsFromJson(json);
}

/// 物理法则
@freezed
class PhysicalLaw with _$PhysicalLaw {
  const factory PhysicalLaw({
    required String name,
    required String description,
    @Default([]) List<String> effects,
    @Default([]) List<String> exceptions,
    @Default({}) Map<String, dynamic> parameters,
  }) = _PhysicalLaw;

  factory PhysicalLaw.fromJson(Map<String, dynamic> json) => _$PhysicalLawFromJson(json);
}

/// 物理常数
@freezed
class PhysicalConstant with _$PhysicalConstant {
  const factory PhysicalConstant({
    required String name,
    required String value,
    String? unit,
    String? description,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _PhysicalConstant;

  factory PhysicalConstant.fromJson(Map<String, dynamic> json) => _$PhysicalConstantFromJson(json);
}

/// 物理力
@freezed
class PhysicalForce with _$PhysicalForce {
  const factory PhysicalForce({
    required String name,
    required String description,
    String? strength,
    String? range,
    @Default([]) List<String> effects,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _PhysicalForce;

  factory PhysicalForce.fromJson(Map<String, dynamic> json) => _$PhysicalForceFromJson(json);
}

/// 世界历史
@freezed
class WorldHistory with _$WorldHistory {
  const factory WorldHistory({
    @Default([]) List<HistoricalEra> eras,
    @Default([]) List<HistoricalEvent> majorEvents,
    @Default([]) List<Civilization> civilizations,
    @Default([]) List<War> wars,
    @Default([]) List<Discovery> discoveries,
    String? creationMyth,
    @Default({}) Map<String, dynamic> customHistory,
  }) = _WorldHistory;

  factory WorldHistory.fromJson(Map<String, dynamic> json) => _$WorldHistoryFromJson(json);
}

/// 历史时代
@freezed
class HistoricalEra with _$HistoricalEra {
  const factory HistoricalEra({
    required String name,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    @Default([]) List<String> characteristics,
    @Default([]) List<String> majorEvents,
    @Default([]) List<String> importantFigures,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _HistoricalEra;

  factory HistoricalEra.fromJson(Map<String, dynamic> json) => _$HistoricalEraFromJson(json);
}

/// 历史事件
@freezed
class HistoricalEvent with _$HistoricalEvent {
  const factory HistoricalEvent({
    required String id,
    required String name,
    required String description,
    required DateTime date,
    HistoricalEventType? type,
    @Default([]) List<String> involvedCivilizations,
    @Default([]) List<String> involvedFigures,
    @Default([]) List<String> consequences,
    String? location,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _HistoricalEvent;

  factory HistoricalEvent.fromJson(Map<String, dynamic> json) => _$HistoricalEventFromJson(json);
}

/// 历史事件类型
enum HistoricalEventType {
  war('战争'),
  discovery('发现'),
  invention('发明'),
  disaster('灾难'),
  founding('建立'),
  collapse('崩溃'),
  treaty('条约'),
  revolution('革命'),
  other('其他');

  const HistoricalEventType(this.displayName);
  final String displayName;
}

/// 文明
@freezed
class Civilization with _$Civilization {
  const factory Civilization({
    required String id,
    required String name,
    String? description,
    CivilizationStatus? status,
    DateTime? foundedDate,
    DateTime? endDate,
    @Default([]) List<String> territories,
    @Default([]) List<String> achievements,
    @Default([]) List<String> technologies,
    String? government,
    String? culture,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _Civilization;

  factory Civilization.fromJson(Map<String, dynamic> json) => _$CivilizationFromJson(json);
}

/// 文明状态
enum CivilizationStatus {
  thriving('繁荣'),
  stable('稳定'),
  declining('衰落'),
  extinct('灭绝'),
  unknown('未知');

  const CivilizationStatus(this.displayName);
  final String displayName;
}

/// 战争
@freezed
class War with _$War {
  const factory War({
    required String id,
    required String name,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    @Default([]) List<String> participants,
    @Default([]) List<String> causes,
    @Default([]) List<String> consequences,
    String? outcome,
    @Default([]) List<String> majorBattles,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _War;

  factory War.fromJson(Map<String, dynamic> json) => _$WarFromJson(json);
}

/// 发现
@freezed
class Discovery with _$Discovery {
  const factory Discovery({
    required String id,
    required String name,
    required String description,
    DateTime? date,
    String? discoverer,
    DiscoveryType? type,
    @Default([]) List<String> impacts,
    String? location,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _Discovery;

  factory Discovery.fromJson(Map<String, dynamic> json) => _$DiscoveryFromJson(json);
}

/// 发现类型
enum DiscoveryType {
  scientific('科学'),
  magical('魔法'),
  geographical('地理'),
  archaeological('考古'),
  technological('技术'),
  other('其他');

  const DiscoveryType(this.displayName);
  final String displayName;
}

/// 世界地理
@freezed
class WorldGeography with _$WorldGeography {
  const factory WorldGeography({
    @Default([]) List<Continent> continents,
    @Default([]) List<Ocean> oceans,
    @Default([]) List<MountainRange> mountainRanges,
    @Default([]) List<River> rivers,
    @Default([]) List<Desert> deserts,
    @Default([]) List<Forest> forests,
    String? worldShape, // 世界形状
    String? size, // 世界大小
    @Default({}) Map<String, dynamic> customGeography,
  }) = _WorldGeography;

  factory WorldGeography.fromJson(Map<String, dynamic> json) => _$WorldGeographyFromJson(json);
}

/// 大陆
@freezed
class Continent with _$Continent {
  const factory Continent({
    required String id,
    required String name,
    String? description,
    double? area,
    @Default([]) List<String> countries,
    @Default([]) List<String> majorFeatures,
    String? climate,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _Continent;

  factory Continent.fromJson(Map<String, dynamic> json) => _$ContinentFromJson(json);
}

/// 海洋
@freezed
class Ocean with _$Ocean {
  const factory Ocean({
    required String id,
    required String name,
    String? description,
    double? area,
    double? averageDepth,
    @Default([]) List<String> borderingContinents,
    @Default([]) List<String> islands,
    @Default([]) List<String> dangers,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _Ocean;

  factory Ocean.fromJson(Map<String, dynamic> json) => _$OceanFromJson(json);
}

/// 山脉
@freezed
class MountainRange with _$MountainRange {
  const factory MountainRange({
    required String id,
    required String name,
    String? description,
    double? length,
    double? highestPeak,
    @Default([]) List<String> countries,
    @Default([]) List<String> resources,
    @Default([]) List<String> dangers,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _MountainRange;

  factory MountainRange.fromJson(Map<String, dynamic> json) => _$MountainRangeFromJson(json);
}

/// 河流
@freezed
class River with _$River {
  const factory River({
    required String id,
    required String name,
    String? description,
    double? length,
    String? source,
    String? mouth,
    @Default([]) List<String> cities,
    @Default([]) List<String> tributaries,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _River;

  factory River.fromJson(Map<String, dynamic> json) => _$RiverFromJson(json);
}

/// 沙漠
@freezed
class Desert with _$Desert {
  const factory Desert({
    required String id,
    required String name,
    String? description,
    double? area,
    DesertType? type,
    @Default([]) List<String> oases,
    @Default([]) List<String> dangers,
    @Default([]) List<String> resources,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _Desert;

  factory Desert.fromJson(Map<String, dynamic> json) => _$DesertFromJson(json);
}

/// 沙漠类型
enum DesertType {
  hot('热沙漠'),
  cold('冷沙漠'),
  coastal('海岸沙漠'),
  semiarid('半干旱');

  const DesertType(this.displayName);
  final String displayName;
}

/// 森林
@freezed
class Forest with _$Forest {
  const factory Forest({
    required String id,
    required String name,
    String? description,
    double? area,
    ForestType? type,
    @Default([]) List<String> wildlife,
    @Default([]) List<String> resources,
    @Default([]) List<String> dangers,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _Forest;

  factory Forest.fromJson(Map<String, dynamic> json) => _$ForestFromJson(json);
}

/// 森林类型
enum ForestType {
  tropical('热带雨林'),
  temperate('温带森林'),
  boreal('北方森林'),
  magical('魔法森林');

  const ForestType(this.displayName);
  final String displayName;
}

/// 世界社会
@freezed
class WorldSociety with _$WorldSociety {
  const factory WorldSociety({
    @Default([]) List<SocialClass> socialClasses,
    @Default([]) List<Institution> institutions,
    @Default([]) List<SocialNorm> norms,
    @Default([]) List<String> values,
    String? familyStructure,
    String? educationSystem,
    @Default({}) Map<String, dynamic> customSociety,
  }) = _WorldSociety;

  factory WorldSociety.fromJson(Map<String, dynamic> json) => _$WorldSocietyFromJson(json);
}

/// 社会阶层
@freezed
class SocialClass with _$SocialClass {
  const factory SocialClass({
    required String name,
    String? description,
    int? rank, // 等级 1-10
    @Default([]) List<String> privileges,
    @Default([]) List<String> responsibilities,
    String? mobility, // 流动性
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _SocialClass;

  factory SocialClass.fromJson(Map<String, dynamic> json) => _$SocialClassFromJson(json);
}

/// 机构
@freezed
class Institution with _$Institution {
  const factory Institution({
    required String id,
    required String name,
    String? description,
    InstitutionType? type,
    String? purpose,
    @Default([]) List<String> functions,
    String? structure,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _Institution;

  factory Institution.fromJson(Map<String, dynamic> json) => _$InstitutionFromJson(json);
}

/// 机构类型
enum InstitutionType {
  government('政府'),
  religious('宗教'),
  educational('教育'),
  military('军事'),
  economic('经济'),
  social('社会'),
  other('其他');

  const InstitutionType(this.displayName);
  final String displayName;
}

/// 社会规范
@freezed
class SocialNorm with _$SocialNorm {
  const factory SocialNorm({
    required String name,
    required String description,
    NormType? type,
    NormStrength? strength,
    @Default([]) List<String> violations,
    @Default([]) List<String> consequences,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _SocialNorm;

  factory SocialNorm.fromJson(Map<String, dynamic> json) => _$SocialNormFromJson(json);
}

/// 规范类型
enum NormType {
  moral('道德'),
  legal('法律'),
  cultural('文化'),
  religious('宗教'),
  etiquette('礼仪');

  const NormType(this.displayName);
  final String displayName;
}

/// 规范强度
enum NormStrength {
  weak('弱'),
  moderate('中等'),
  strong('强'),
  absolute('绝对');

  const NormStrength(this.displayName);
  final String displayName;
}

/// 世界经济
@freezed
class WorldEconomy with _$WorldEconomy {
  const factory WorldEconomy({
    @Default([]) List<EconomicSystem> systems,
    @Default([]) List<TradeNetwork> tradeNetworks,
    @Default([]) List<Resource> resources,
    @Default([]) List<Industry> industries,
    String? primaryCurrency,
    @Default({}) Map<String, dynamic> customEconomy,
  }) = _WorldEconomy;

  factory WorldEconomy.fromJson(Map<String, dynamic> json) => _$WorldEconomyFromJson(json);
}

/// 经济制度
@freezed
class EconomicSystem with _$EconomicSystem {
  const factory EconomicSystem({
    required String name,
    String? description,
    EconomicSystemType? type,
    @Default([]) List<String> characteristics,
    @Default([]) List<String> advantages,
    @Default([]) List<String> disadvantages,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _EconomicSystem;

  factory EconomicSystem.fromJson(Map<String, dynamic> json) => _$EconomicSystemFromJson(json);
}

/// 经济制度类型
enum EconomicSystemType {
  feudal('封建'),
  capitalist('资本主义'),
  socialist('社会主义'),
  barter('物物交换'),
  gift('礼品经济'),
  mixed('混合'),
  other('其他');

  const EconomicSystemType(this.displayName);
  final String displayName;
}

/// 贸易网络
@freezed
class TradeNetwork with _$TradeNetwork {
  const factory TradeNetwork({
    required String id,
    required String name,
    String? description,
    @Default([]) List<String> participants,
    @Default([]) List<String> tradedGoods,
    @Default([]) List<String> routes,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _TradeNetwork;

  factory TradeNetwork.fromJson(Map<String, dynamic> json) => _$TradeNetworkFromJson(json);
}

/// 资源
@freezed
class Resource with _$Resource {
  const factory Resource({
    required String id,
    required String name,
    String? description,
    ResourceType? type,
    ResourceRarity? rarity,
    @Default([]) List<String> locations,
    @Default([]) List<String> uses,
    String? extractionMethod,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _Resource;

  factory Resource.fromJson(Map<String, dynamic> json) => _$ResourceFromJson(json);
}

/// 资源类型
enum ResourceType {
  mineral('矿物'),
  biological('生物'),
  energy('能源'),
  magical('魔法'),
  technological('科技'),
  other('其他');

  const ResourceType(this.displayName);
  final String displayName;
}

/// 资源稀有度
enum ResourceRarity {
  abundant('丰富'),
  common('常见'),
  uncommon('不常见'),
  rare('稀有'),
  legendary('传说');

  const ResourceRarity(this.displayName);
  final String displayName;
}

/// 产业
@freezed
class Industry with _$Industry {
  const factory Industry({
    required String id,
    required String name,
    String? description,
    IndustryType? type,
    @Default([]) List<String> products,
    @Default([]) List<String> requiredResources,
    @Default([]) List<String> locations,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _Industry;

  factory Industry.fromJson(Map<String, dynamic> json) => _$IndustryFromJson(json);
}

/// 产业类型
enum IndustryType {
  agriculture('农业'),
  manufacturing('制造业'),
  mining('采矿业'),
  services('服务业'),
  crafts('手工业'),
  magic('魔法业'),
  other('其他');

  const IndustryType(this.displayName);
  final String displayName;
}

/// 世界宗教
@freezed
class WorldReligion with _$WorldReligion {
  const factory WorldReligion({
    @Default([]) List<Religion> religions,
    @Default([]) List<Deity> deities,
    @Default([]) List<ReligiousOrder> orders,
    @Default([]) List<Prophecy> prophecies,
    @Default({}) Map<String, dynamic> customReligion,
  }) = _WorldReligion;

  factory WorldReligion.fromJson(Map<String, dynamic> json) => _$WorldReligionFromJson(json);
}

/// 宗教
@freezed
class Religion with _$Religion {
  const factory Religion({
    required String id,
    required String name,
    String? description,
    ReligionType? type,
    @Default([]) List<String> beliefs,
    @Default([]) List<String> practices,
    @Default([]) List<String> deityIds,
    @Default([]) List<String> holyTexts,
    @Default([]) List<String> holyPlaces,
    String? afterlife,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _Religion;

  factory Religion.fromJson(Map<String, dynamic> json) => _$ReligionFromJson(json);
}

/// 宗教类型
enum ReligionType {
  monotheistic('一神教'),
  polytheistic('多神教'),
  pantheistic('泛神教'),
  animistic('万物有灵'),
  philosophical('哲学'),
  other('其他');

  const ReligionType(this.displayName);
  final String displayName;
}

/// 神祇
@freezed
class Deity with _$Deity {
  const factory Deity({
    required String id,
    required String name,
    String? description,
    @Default([]) List<String> domains, // 神域
    @Default([]) List<String> symbols,
    String? alignment,
    DeityPower? power,
    @Default([]) List<String> followers,
    @Default([]) List<String> enemies,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _Deity;

  factory Deity.fromJson(Map<String, dynamic> json) => _$DeityFromJson(json);
}

/// 神祇力量
enum DeityPower {
  lesser('次神'),
  intermediate('中等神'),
  greater('大神'),
  overdeity('超神');

  const DeityPower(this.displayName);
  final String displayName;
}

/// 宗教组织
@freezed
class ReligiousOrder with _$ReligiousOrder {
  const factory ReligiousOrder({
    required String id,
    required String name,
    String? description,
    String? religionId,
    OrderType? type,
    @Default([]) List<String> goals,
    @Default([]) List<String> practices,
    String? hierarchy,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _ReligiousOrder;

  factory ReligiousOrder.fromJson(Map<String, dynamic> json) => _$ReligiousOrderFromJson(json);
}

/// 组织类型
enum OrderType {
  monastic('修道'),
  militant('军事'),
  scholarly('学术'),
  missionary('传教'),
  mystical('神秘'),
  other('其他');

  const OrderType(this.displayName);
  final String displayName;
}

/// 预言
@freezed
class Prophecy with _$Prophecy {
  const factory Prophecy({
    required String id,
    required String title,
    required String text,
    String? interpretation,
    String? source,
    ProphecyStatus? status,
    @Default([]) List<String> conditions,
    DateTime? prophesiedDate,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _Prophecy;

  factory Prophecy.fromJson(Map<String, dynamic> json) => _$ProphecyFromJson(json);
}

/// 预言状态
enum ProphecyStatus {
  unfulfilled('未实现'),
  inProgress('进行中'),
  fulfilled('已实现'),
  failed('已失败'),
  unknown('未知');

  const ProphecyStatus(this.displayName);
  final String displayName;
}

/// 世界规则
@freezed
class WorldRule with _$WorldRule {
  const factory WorldRule({
    required String id,
    required String name,
    required String description,
    required WorldRuleType type,
    required WorldRuleSeverity severity,
    @Default([]) List<String> effects,
    @Default([]) List<String> exceptions,
    @Default({}) Map<String, dynamic> parameters,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _WorldRule;

  factory WorldRule.fromJson(Map<String, dynamic> json) => _$WorldRuleFromJson(json);
}

/// 世界规则类型
enum WorldRuleType {
  physical('物理'),
  magical('魔法'),
  social('社会'),
  economic('经济'),
  political('政治'),
  religious('宗教'),
  other('其他');

  const WorldRuleType(this.displayName);
  final String displayName;
}

/// 世界规则严重程度
enum WorldRuleSeverity {
  guideline('指导'),
  rule('规则'),
  law('法则'),
  absolute('绝对');

  const WorldRuleSeverity(this.displayName);
  final String displayName;
}

/// 世界事件
@freezed
class WorldEvent with _$WorldEvent {
  const factory WorldEvent({
    required String id,
    required String name,
    required String description,
    required DateTime date,
    WorldEventType? type,
    WorldEventScope? scope,
    @Default([]) List<String> consequences,
    @Default([]) List<String> involvedEntities,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _WorldEvent;

  factory WorldEvent.fromJson(Map<String, dynamic> json) => _$WorldEventFromJson(json);
}

/// 世界事件类型
enum WorldEventType {
  natural('自然'),
  magical('魔法'),
  political('政治'),
  social('社会'),
  economic('经济'),
  religious('宗教'),
  technological('科技'),
  other('其他');

  const WorldEventType(this.displayName);
  final String displayName;
}

/// 世界事件范围
enum WorldEventScope {
  local('局部'),
  regional('地区'),
  national('国家'),
  continental('大陆'),
  global('全球'),
  cosmic('宇宙');

  const WorldEventScope(this.displayName);
  final String displayName;
}

/// 物种
@freezed
class Species with _$Species {
  const factory Species({
    required String id,
    required String name,
    String? description,
    SpeciesType? type,
    @Default([]) List<String> characteristics,
    @Default([]) List<String> abilities,
    @Default([]) List<String> habitats,
    String? lifespan,
    String? intelligence,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _Species;

  factory Species.fromJson(Map<String, dynamic> json) => _$SpeciesFromJson(json);
}

/// 物种类型
enum SpeciesType {
  humanoid('类人'),
  beast('野兽'),
  magical('魔法生物'),
  elemental('元素'),
  undead('不死'),
  construct('构造体'),
  plant('植物'),
  other('其他');

  const SpeciesType(this.displayName);
  final String displayName;
}

/// 语言
@freezed
class Language with _$Language {
  const factory Language({
    required String id,
    required String name,
    String? description,
    LanguageType? type,
    @Default([]) List<String> speakers,
    String? writingSystem,
    @Default([]) List<String> dialects,
    String? origin,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _Language;

  factory Language.fromJson(Map<String, dynamic> json) => _$LanguageFromJson(json);
}

/// 语言类型
enum LanguageType {
  spoken('口语'),
  written('书面语'),
  sign('手语'),
  telepathic('心灵感应'),
  magical('魔法语言'),
  other('其他');

  const LanguageType(this.displayName);
  final String displayName;
}

/// 历法
@freezed
class Calendar with _$Calendar {
  const factory Calendar({
    required String id,
    required String name,
    String? description,
    int? daysPerYear,
    int? monthsPerYear,
    int? daysPerMonth,
    @Default([]) List<String> monthNames,
    @Default([]) List<String> dayNames,
    @Default([]) List<Holiday> holidays,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _Calendar;

  factory Calendar.fromJson(Map<String, dynamic> json) => _$CalendarFromJson(json);
}

/// 节日
@freezed
class Holiday with _$Holiday {
  const factory Holiday({
    required String name,
    String? description,
    String? date,
    HolidayType? type,
    @Default([]) List<String> traditions,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _Holiday;

  factory Holiday.fromJson(Map<String, dynamic> json) => _$HolidayFromJson(json);
}

/// 节日类型
enum HolidayType {
  religious('宗教'),
  seasonal('季节'),
  historical('历史'),
  cultural('文化'),
  personal('个人'),
  other('其他');

  const HolidayType(this.displayName);
  final String displayName;
}

/// 货币
@freezed
class Currency with _$Currency {
  const factory Currency({
    required String id,
    required String name,
    String? description,
    String? symbol,
    @Default([]) List<Denomination> denominations,
    @Default([]) List<String> acceptedRegions,
    String? backingSystem,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _Currency;

  factory Currency.fromJson(Map<String, dynamic> json) => _$CurrencyFromJson(json);
}

/// 面额
@freezed
class Denomination with _$Denomination {
  const factory Denomination({
    required String name,
    required double value,
    String? material,
    String? description,
    @Default({}) Map<String, dynamic> customAttributes,
  }) = _Denomination;

  factory Denomination.fromJson(Map<String, dynamic> json) => _$DenominationFromJson(json);
}