// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'project.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProjectImpl _$$ProjectImplFromJson(Map<String, dynamic> json) =>
    _$ProjectImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      type:
          $enumDecodeNullable(_$ProjectTypeEnumMap, json['type']) ??
          ProjectType.novel,
      status:
          $enumDecodeNullable(_$ProjectStatusEnumMap, json['status']) ??
          ProjectStatus.active,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      createdBy: json['createdBy'] as String,
      coverImagePath: json['coverImagePath'] as String?,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
          const [],
      config: json['config'] == null
          ? const ProjectConfig()
          : ProjectConfig.fromJson(json['config'] as Map<String, dynamic>),
      statistics: json['statistics'] == null
          ? const ProjectStatistics()
          : ProjectStatistics.fromJson(
              json['statistics'] as Map<String, dynamic>,
            ),
      templateId: json['templateId'] as String?,
      archivedAt: json['archivedAt'] == null
          ? null
          : DateTime.parse(json['archivedAt'] as String),
      archiveReason: json['archiveReason'] as String?,
      isFavorite: json['isFavorite'] as bool? ?? false,
      colorTheme: json['colorTheme'] as String?,
      customFields: json['customFields'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$ProjectImplToJson(_$ProjectImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$ProjectTypeEnumMap[instance.type]!,
      'status': _$ProjectStatusEnumMap[instance.status]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'createdBy': instance.createdBy,
      'coverImagePath': instance.coverImagePath,
      'tags': instance.tags,
      'config': instance.config,
      'statistics': instance.statistics,
      'templateId': instance.templateId,
      'archivedAt': instance.archivedAt?.toIso8601String(),
      'archiveReason': instance.archiveReason,
      'isFavorite': instance.isFavorite,
      'colorTheme': instance.colorTheme,
      'customFields': instance.customFields,
    };

const _$ProjectTypeEnumMap = {
  ProjectType.novel: 'novel',
  ProjectType.shortStory: 'shortStory',
  ProjectType.screenplay: 'screenplay',
  ProjectType.poetry: 'poetry',
  ProjectType.essay: 'essay',
  ProjectType.academic: 'academic',
  ProjectType.technical: 'technical',
  ProjectType.other: 'other',
};

const _$ProjectStatusEnumMap = {
  ProjectStatus.active: 'active',
  ProjectStatus.paused: 'paused',
  ProjectStatus.completed: 'completed',
  ProjectStatus.archived: 'archived',
  ProjectStatus.deleted: 'deleted',
};

_$ProjectConfigImpl _$$ProjectConfigImplFromJson(
  Map<String, dynamic> json,
) => _$ProjectConfigImpl(
  targetWordCount: (json['targetWordCount'] as num?)?.toInt() ?? 0,
  dailyWritingGoal: (json['dailyWritingGoal'] as num?)?.toInt() ?? 0,
  deadline: json['deadline'] == null
      ? null
      : DateTime.parse(json['deadline'] as String),
  autoSaveInterval: (json['autoSaveInterval'] as num?)?.toInt() ?? 5,
  enableVersionControl: json['enableVersionControl'] as bool? ?? true,
  enableAIAssistant: json['enableAIAssistant'] as bool? ?? true,
  defaultAIModel: json['defaultAIModel'] as String? ?? 'gpt-3.5-turbo',
  writingReminder: json['writingReminder'] == null
      ? const WritingReminder()
      : WritingReminder.fromJson(
          json['writingReminder'] as Map<String, dynamic>,
        ),
  exportSettings: json['exportSettings'] == null
      ? const ExportSettings()
      : ExportSettings.fromJson(json['exportSettings'] as Map<String, dynamic>),
  backupSettings: json['backupSettings'] == null
      ? const BackupSettings()
      : BackupSettings.fromJson(json['backupSettings'] as Map<String, dynamic>),
  collaborationSettings: json['collaborationSettings'] == null
      ? const CollaborationSettings()
      : CollaborationSettings.fromJson(
          json['collaborationSettings'] as Map<String, dynamic>,
        ),
);

Map<String, dynamic> _$$ProjectConfigImplToJson(_$ProjectConfigImpl instance) =>
    <String, dynamic>{
      'targetWordCount': instance.targetWordCount,
      'dailyWritingGoal': instance.dailyWritingGoal,
      'deadline': instance.deadline?.toIso8601String(),
      'autoSaveInterval': instance.autoSaveInterval,
      'enableVersionControl': instance.enableVersionControl,
      'enableAIAssistant': instance.enableAIAssistant,
      'defaultAIModel': instance.defaultAIModel,
      'writingReminder': instance.writingReminder,
      'exportSettings': instance.exportSettings,
      'backupSettings': instance.backupSettings,
      'collaborationSettings': instance.collaborationSettings,
    };

_$WritingReminderImpl _$$WritingReminderImplFromJson(
  Map<String, dynamic> json,
) => _$WritingReminderImpl(
  enabled: json['enabled'] as bool? ?? false,
  reminderTimes:
      (json['reminderTimes'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  frequency:
      $enumDecodeNullable(_$ReminderFrequencyEnumMap, json['frequency']) ??
      ReminderFrequency.daily,
  message: json['message'] as String? ?? '该写作了！',
);

Map<String, dynamic> _$$WritingReminderImplToJson(
  _$WritingReminderImpl instance,
) => <String, dynamic>{
  'enabled': instance.enabled,
  'reminderTimes': instance.reminderTimes,
  'frequency': _$ReminderFrequencyEnumMap[instance.frequency]!,
  'message': instance.message,
};

const _$ReminderFrequencyEnumMap = {
  ReminderFrequency.daily: 'daily',
  ReminderFrequency.weekly: 'weekly',
  ReminderFrequency.custom: 'custom',
};

_$ExportSettingsImpl _$$ExportSettingsImplFromJson(Map<String, dynamic> json) =>
    _$ExportSettingsImpl(
      defaultFormat:
          $enumDecodeNullable(_$ExportFormatEnumMap, json['defaultFormat']) ??
          ExportFormat.docx,
      includeMetadata: json['includeMetadata'] as bool? ?? true,
      includeTableOfContents: json['includeTableOfContents'] as bool? ?? true,
      pageSettings: json['pageSettings'] == null
          ? const PageSettings()
          : PageSettings.fromJson(json['pageSettings'] as Map<String, dynamic>),
      fontSettings: json['fontSettings'] == null
          ? const FontSettings()
          : FontSettings.fromJson(json['fontSettings'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ExportSettingsImplToJson(
  _$ExportSettingsImpl instance,
) => <String, dynamic>{
  'defaultFormat': _$ExportFormatEnumMap[instance.defaultFormat]!,
  'includeMetadata': instance.includeMetadata,
  'includeTableOfContents': instance.includeTableOfContents,
  'pageSettings': instance.pageSettings,
  'fontSettings': instance.fontSettings,
};

const _$ExportFormatEnumMap = {
  ExportFormat.docx: 'docx',
  ExportFormat.pdf: 'pdf',
  ExportFormat.txt: 'txt',
  ExportFormat.markdown: 'markdown',
  ExportFormat.html: 'html',
  ExportFormat.epub: 'epub',
};

_$PageSettingsImpl _$$PageSettingsImplFromJson(Map<String, dynamic> json) =>
    _$PageSettingsImpl(
      pageSize: json['pageSize'] as String? ?? 'A4',
      margins: json['margins'] == null
          ? const PageMargins()
          : PageMargins.fromJson(json['margins'] as Map<String, dynamic>),
      lineSpacing: (json['lineSpacing'] as num?)?.toDouble() ?? 1.5,
    );

Map<String, dynamic> _$$PageSettingsImplToJson(_$PageSettingsImpl instance) =>
    <String, dynamic>{
      'pageSize': instance.pageSize,
      'margins': instance.margins,
      'lineSpacing': instance.lineSpacing,
    };

_$PageMarginsImpl _$$PageMarginsImplFromJson(Map<String, dynamic> json) =>
    _$PageMarginsImpl(
      top: (json['top'] as num?)?.toDouble() ?? 2.5,
      bottom: (json['bottom'] as num?)?.toDouble() ?? 2.5,
      left: (json['left'] as num?)?.toDouble() ?? 2.0,
      right: (json['right'] as num?)?.toDouble() ?? 2.0,
    );

Map<String, dynamic> _$$PageMarginsImplToJson(_$PageMarginsImpl instance) =>
    <String, dynamic>{
      'top': instance.top,
      'bottom': instance.bottom,
      'left': instance.left,
      'right': instance.right,
    };

_$FontSettingsImpl _$$FontSettingsImplFromJson(Map<String, dynamic> json) =>
    _$FontSettingsImpl(
      fontFamily: json['fontFamily'] as String? ?? 'Times New Roman',
      fontSize: (json['fontSize'] as num?)?.toInt() ?? 12,
      bold: json['bold'] as bool? ?? false,
      italic: json['italic'] as bool? ?? false,
    );

Map<String, dynamic> _$$FontSettingsImplToJson(_$FontSettingsImpl instance) =>
    <String, dynamic>{
      'fontFamily': instance.fontFamily,
      'fontSize': instance.fontSize,
      'bold': instance.bold,
      'italic': instance.italic,
    };

_$BackupSettingsImpl _$$BackupSettingsImplFromJson(Map<String, dynamic> json) =>
    _$BackupSettingsImpl(
      autoBackup: json['autoBackup'] as bool? ?? true,
      backupFrequency: (json['backupFrequency'] as num?)?.toInt() ?? 24,
      keepBackupCount: (json['keepBackupCount'] as num?)?.toInt() ?? 10,
      backupLocation: json['backupLocation'] as String? ?? '',
      cloudBackup: json['cloudBackup'] == null
          ? const CloudBackupSettings()
          : CloudBackupSettings.fromJson(
              json['cloudBackup'] as Map<String, dynamic>,
            ),
    );

Map<String, dynamic> _$$BackupSettingsImplToJson(
  _$BackupSettingsImpl instance,
) => <String, dynamic>{
  'autoBackup': instance.autoBackup,
  'backupFrequency': instance.backupFrequency,
  'keepBackupCount': instance.keepBackupCount,
  'backupLocation': instance.backupLocation,
  'cloudBackup': instance.cloudBackup,
};

_$CloudBackupSettingsImpl _$$CloudBackupSettingsImplFromJson(
  Map<String, dynamic> json,
) => _$CloudBackupSettingsImpl(
  enabled: json['enabled'] as bool? ?? false,
  provider:
      $enumDecodeNullable(_$CloudProviderEnumMap, json['provider']) ??
      CloudProvider.none,
  accessToken: json['accessToken'] as String?,
  backupPath: json['backupPath'] as String? ?? '',
);

Map<String, dynamic> _$$CloudBackupSettingsImplToJson(
  _$CloudBackupSettingsImpl instance,
) => <String, dynamic>{
  'enabled': instance.enabled,
  'provider': _$CloudProviderEnumMap[instance.provider]!,
  'accessToken': instance.accessToken,
  'backupPath': instance.backupPath,
};

const _$CloudProviderEnumMap = {
  CloudProvider.none: 'none',
  CloudProvider.googleDrive: 'googleDrive',
  CloudProvider.oneDrive: 'oneDrive',
  CloudProvider.dropbox: 'dropbox',
  CloudProvider.iCloud: 'iCloud',
};

_$CollaborationSettingsImpl _$$CollaborationSettingsImplFromJson(
  Map<String, dynamic> json,
) => _$CollaborationSettingsImpl(
  enabled: json['enabled'] as bool? ?? false,
  collaborators:
      (json['collaborators'] as List<dynamic>?)
          ?.map((e) => Collaborator.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  permissions: json['permissions'] == null
      ? const PermissionSettings()
      : PermissionSettings.fromJson(
          json['permissions'] as Map<String, dynamic>,
        ),
);

Map<String, dynamic> _$$CollaborationSettingsImplToJson(
  _$CollaborationSettingsImpl instance,
) => <String, dynamic>{
  'enabled': instance.enabled,
  'collaborators': instance.collaborators,
  'permissions': instance.permissions,
};

_$CollaboratorImpl _$$CollaboratorImplFromJson(Map<String, dynamic> json) =>
    _$CollaboratorImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      role:
          $enumDecodeNullable(_$CollaboratorRoleEnumMap, json['role']) ??
          CollaboratorRole.viewer,
      invitedAt: DateTime.parse(json['invitedAt'] as String),
      acceptedAt: json['acceptedAt'] == null
          ? null
          : DateTime.parse(json['acceptedAt'] as String),
      status:
          $enumDecodeNullable(_$CollaboratorStatusEnumMap, json['status']) ??
          CollaboratorStatus.pending,
    );

Map<String, dynamic> _$$CollaboratorImplToJson(_$CollaboratorImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'role': _$CollaboratorRoleEnumMap[instance.role]!,
      'invitedAt': instance.invitedAt.toIso8601String(),
      'acceptedAt': instance.acceptedAt?.toIso8601String(),
      'status': _$CollaboratorStatusEnumMap[instance.status]!,
    };

const _$CollaboratorRoleEnumMap = {
  CollaboratorRole.owner: 'owner',
  CollaboratorRole.editor: 'editor',
  CollaboratorRole.reviewer: 'reviewer',
  CollaboratorRole.viewer: 'viewer',
};

const _$CollaboratorStatusEnumMap = {
  CollaboratorStatus.pending: 'pending',
  CollaboratorStatus.active: 'active',
  CollaboratorStatus.inactive: 'inactive',
  CollaboratorStatus.removed: 'removed',
};

_$PermissionSettingsImpl _$$PermissionSettingsImplFromJson(
  Map<String, dynamic> json,
) => _$PermissionSettingsImpl(
  allowEdit: json['allowEdit'] as bool? ?? true,
  allowComment: json['allowComment'] as bool? ?? true,
  allowExport: json['allowExport'] as bool? ?? false,
  allowShare: json['allowShare'] as bool? ?? false,
  allowDelete: json['allowDelete'] as bool? ?? false,
);

Map<String, dynamic> _$$PermissionSettingsImplToJson(
  _$PermissionSettingsImpl instance,
) => <String, dynamic>{
  'allowEdit': instance.allowEdit,
  'allowComment': instance.allowComment,
  'allowExport': instance.allowExport,
  'allowShare': instance.allowShare,
  'allowDelete': instance.allowDelete,
};

_$ProjectStatisticsImpl _$$ProjectStatisticsImplFromJson(
  Map<String, dynamic> json,
) => _$ProjectStatisticsImpl(
  totalWordCount: (json['totalWordCount'] as num?)?.toInt() ?? 0,
  totalCharacterCount: (json['totalCharacterCount'] as num?)?.toInt() ?? 0,
  chapterCount: (json['chapterCount'] as num?)?.toInt() ?? 0,
  versionCount: (json['versionCount'] as num?)?.toInt() ?? 0,
  writingDays: (json['writingDays'] as num?)?.toInt() ?? 0,
  averageDailyWords: (json['averageDailyWords'] as num?)?.toInt() ?? 0,
  lastWritingTime: json['lastWritingTime'] == null
      ? null
      : DateTime.parse(json['lastWritingTime'] as String),
  completionProgress: (json['completionProgress'] as num?)?.toDouble() ?? 0.0,
  writingTimeMinutes: (json['writingTimeMinutes'] as num?)?.toInt() ?? 0,
  maxDailyWords: (json['maxDailyWords'] as num?)?.toInt() ?? 0,
  consecutiveWritingDays:
      (json['consecutiveWritingDays'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$$ProjectStatisticsImplToJson(
  _$ProjectStatisticsImpl instance,
) => <String, dynamic>{
  'totalWordCount': instance.totalWordCount,
  'totalCharacterCount': instance.totalCharacterCount,
  'chapterCount': instance.chapterCount,
  'versionCount': instance.versionCount,
  'writingDays': instance.writingDays,
  'averageDailyWords': instance.averageDailyWords,
  'lastWritingTime': instance.lastWritingTime?.toIso8601String(),
  'completionProgress': instance.completionProgress,
  'writingTimeMinutes': instance.writingTimeMinutes,
  'maxDailyWords': instance.maxDailyWords,
  'consecutiveWritingDays': instance.consecutiveWritingDays,
};
