import 'dart:convert';

import '../../domain/entities/project.dart';

/// 项目数据模型
/// 用于JSON序列化的项目模型
class ProjectModel {
  /// 项目唯一标识符
  final String projectId;

  /// 项目名称
  final String name;

  /// 项目描述
  final String description;

  /// 项目类型
  final ProjectType type;

  /// 项目状态
  final ProjectStatus status;

  /// 项目创建时间
  final DateTime createdAt;

  /// 项目最后修改时间
  final DateTime updatedAt;

  /// 项目创建者
  final String createdBy;

  /// 项目封面图片路径
  final String? coverImagePath;

  /// 项目标签
  final List<String> tags;

  /// 项目配置（JSON字符串）
  final String configJson;

  /// 项目统计信息（JSON字符串）
  final String statisticsJson;

  /// 项目模板ID
  final String? templateId;

  /// 项目归档时间
  final DateTime? archivedAt;

  /// 项目归档原因
  final String? archiveReason;

  /// 项目是否为收藏
  final bool isFavorite;

  /// 项目颜色主题
  final String? colorTheme;

  /// 项目自定义字段（JSON字符串）
  final String customFieldsJson;

  const ProjectModel({
    required this.projectId,
    required this.name,
    required this.description,
    required this.type,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    this.coverImagePath,
    required this.tags,
    required this.configJson,
    required this.statisticsJson,
    this.templateId,
    this.archivedAt,
    this.archiveReason,
    required this.isFavorite,
    this.colorTheme,
    required this.customFieldsJson,
  });

  /// 从领域实体创建数据模型
  factory ProjectModel.fromEntity(Project project) {
    return ProjectModel(
      projectId: project.id,
      name: project.name,
      description: project.description,
      type: project.type,
      status: project.status,
      createdAt: project.createdAt,
      updatedAt: project.updatedAt,
      createdBy: project.createdBy,
      coverImagePath: project.coverImagePath,
      tags: project.tags,
      configJson: jsonEncode(project.config.toJson()),
      statisticsJson: jsonEncode(project.statistics.toJson()),
      templateId: project.templateId,
      archivedAt: project.archivedAt,
      archiveReason: project.archiveReason,
      isFavorite: project.isFavorite,
      colorTheme: project.colorTheme,
      customFieldsJson: jsonEncode(project.customFields),
    );
  }

  /// 转换为领域实体
  Project toEntity() {
    return Project(
      id: projectId,
      name: name,
      description: description,
      type: type,
      status: status,
      createdAt: createdAt,
      updatedAt: updatedAt,
      createdBy: createdBy,
      coverImagePath: coverImagePath,
      tags: tags,
      config: ProjectConfig.fromJson(jsonDecode(configJson)),
      statistics: ProjectStatistics.fromJson(jsonDecode(statisticsJson)),
      templateId: templateId,
      archivedAt: archivedAt,
      archiveReason: archiveReason,
      isFavorite: isFavorite,
      colorTheme: colorTheme,
      customFields: Map<String, dynamic>.from(jsonDecode(customFieldsJson)),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'projectId': projectId,
      'name': name,
      'description': description,
      'type': type.name,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdBy': createdBy,
      'coverImagePath': coverImagePath,
      'tags': tags,
      'configJson': configJson,
      'statisticsJson': statisticsJson,
      'templateId': templateId,
      'archivedAt': archivedAt?.toIso8601String(),
      'archiveReason': archiveReason,
      'isFavorite': isFavorite,
      'colorTheme': colorTheme,
      'customFieldsJson': customFieldsJson,
    };
  }

  /// 从JSON创建数据模型
  factory ProjectModel.fromJson(Map<String, dynamic> json) {
    return ProjectModel(
      projectId: json['projectId'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: ProjectType.values.firstWhere((e) => e.name == json['type']),
      status: ProjectStatus.values.firstWhere((e) => e.name == json['status']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      createdBy: json['createdBy'] as String,
      coverImagePath: json['coverImagePath'] as String?,
      tags: List<String>.from(json['tags'] as List),
      configJson: json['configJson'] as String,
      statisticsJson: json['statisticsJson'] as String,
      templateId: json['templateId'] as String?,
      archivedAt: json['archivedAt'] != null 
          ? DateTime.parse(json['archivedAt'] as String)
          : null,
      archiveReason: json['archiveReason'] as String?,
      isFavorite: json['isFavorite'] as bool,
      colorTheme: json['colorTheme'] as String?,
      customFieldsJson: json['customFieldsJson'] as String,
    );
  }
}

/// 项目模板数据模型
class ProjectTemplateModel {
  /// 模板唯一标识符
  final String templateId;

  /// 模板名称
  final String name;

  /// 模板描述
  final String description;

  /// 模板类型
  final ProjectType type;

  /// 模板创建时间
  final DateTime createdAt;

  /// 模板创建者
  final String createdBy;

  /// 模板预览图片路径
  final String? previewImagePath;

  /// 模板标签
  final List<String> tags;

  /// 模板配置（JSON字符串）
  final String configJson;

  /// 模板是否为系统内置
  final bool isBuiltIn;

  /// 模板是否为公开
  final bool isPublic;

  /// 模板使用次数
  final int usageCount;

  /// 模板评分
  final double rating;

  const ProjectTemplateModel({
    required this.templateId,
    required this.name,
    required this.description,
    required this.type,
    required this.createdAt,
    required this.createdBy,
    this.previewImagePath,
    required this.tags,
    required this.configJson,
    required this.isBuiltIn,
    required this.isPublic,
    required this.usageCount,
    required this.rating,
  });

  /// 从项目创建模板
  factory ProjectTemplateModel.fromProject(Project project, {
    required String templateId,
    required String templateName,
    required String templateDescription,
    bool isBuiltIn = false,
    bool isPublic = false,
  }) {
    return ProjectTemplateModel(
      templateId: templateId,
      name: templateName,
      description: templateDescription,
      type: project.type,
      createdAt: DateTime.now(),
      createdBy: project.createdBy,
      previewImagePath: project.coverImagePath,
      tags: project.tags,
      configJson: jsonEncode(project.config.toJson()),
      isBuiltIn: isBuiltIn,
      isPublic: isPublic,
      usageCount: 0,
      rating: 0.0,
    );
  }

  /// 转换为项目配置
  ProjectConfig toProjectConfig() {
    return ProjectConfig.fromJson(jsonDecode(configJson));
  }

  /// 创建基于模板的项目
  Project createProject({
    required String projectId,
    required String projectName,
    required String createdBy,
    String? projectDescription,
  }) {
    return Project(
      id: projectId,
      name: projectName,
      description: projectDescription ?? description,
      type: type,
      status: ProjectStatus.active,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      createdBy: createdBy,
      tags: tags,
      config: toProjectConfig(),
      statistics: const ProjectStatistics(),
      templateId: templateId,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'templateId': templateId,
      'name': name,
      'description': description,
      'type': type.name,
      'createdAt': createdAt.toIso8601String(),
      'createdBy': createdBy,
      'previewImagePath': previewImagePath,
      'tags': tags,
      'configJson': configJson,
      'isBuiltIn': isBuiltIn,
      'isPublic': isPublic,
      'usageCount': usageCount,
      'rating': rating,
    };
  }

  /// 从JSON创建模板模型
  factory ProjectTemplateModel.fromJson(Map<String, dynamic> json) {
    return ProjectTemplateModel(
      templateId: json['templateId'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: ProjectType.values.firstWhere((e) => e.name == json['type']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      createdBy: json['createdBy'] as String,
      previewImagePath: json['previewImagePath'] as String?,
      tags: List<String>.from(json['tags'] as List),
      configJson: json['configJson'] as String,
      isBuiltIn: json['isBuiltIn'] as bool,
      isPublic: json['isPublic'] as bool,
      usageCount: json['usageCount'] as int,
      rating: (json['rating'] as num).toDouble(),
    );
  }

  /// 复制并更新使用次数
  ProjectTemplateModel copyWithIncrementedUsage() {
    return ProjectTemplateModel(
      templateId: templateId,
      name: name,
      description: description,
      type: type,
      createdAt: createdAt,
      createdBy: createdBy,
      previewImagePath: previewImagePath,
      tags: tags,
      configJson: configJson,
      isBuiltIn: isBuiltIn,
      isPublic: isPublic,
      usageCount: usageCount + 1,
      rating: rating,
    );
  }

  /// 复制并更新评分
  ProjectTemplateModel copyWithRating(double newRating) {
    return ProjectTemplateModel(
      templateId: templateId,
      name: name,
      description: description,
      type: type,
      createdAt: createdAt,
      createdBy: createdBy,
      previewImagePath: previewImagePath,
      tags: tags,
      configJson: configJson,
      isBuiltIn: isBuiltIn,
      isPublic: isPublic,
      usageCount: usageCount,
      rating: newRating,
    );
  }
}