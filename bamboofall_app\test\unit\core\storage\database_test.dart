import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:logger/logger.dart';

import 'package:bamboofall_app/core/storage/database.dart';

import 'database_test.mocks.dart';

@GenerateMocks([Logger])
void main() {
  late LocalDatabase database;
  late MockLogger mockLogger;

  setUp(() {
    mockLogger = MockLogger();
    database = LocalDatabase(logger: mockLogger);
  });

  tearDown(() async {
    await database.close();
  });

  group('LocalDatabase - Initialization', () {
    test('should initialize database successfully', () async {
      // Act
      await database.initialize();

      // Assert
      expect(database.isInitialized, true);
    });

    test('should handle initialization failure gracefully', () async {
      // This test would require mocking the underlying Isar database
      // For now, we'll test the basic initialization flow
      expect(() => database.initialize(), returnsNormally);
    });

    test('should not initialize twice', () async {
      // Arrange
      await database.initialize();
      expect(database.isInitialized, true);

      // Act & Assert
      await database.initialize(); // Should not throw
      expect(database.isInitialized, true);
    });
  });

  group('LocalDatabase - Collection Operations', () {
    const testCollection = 'test_collection';
    final testData = {
      'id': 'test-1',
      'name': 'Test Item',
      'value': 42,
      'created_at': DateTime.now().toIso8601String(),
    };

    setUp(() async {
      await database.initialize();
    });

    test('should create collection if not exists', () async {
      // Act
      await database.createCollection(testCollection);

      // Assert
      final collections = await database.listCollections();
      expect(collections.contains(testCollection), true);
    });

    test('should write document to collection', () async {
      // Arrange
      await database.createCollection(testCollection);

      // Act
      await database.writeDocument(testCollection, testData['id'] as String, testData);

      // Assert
      final document = await database.readDocument(testCollection, testData['id'] as String);
      expect(document, isNotNull);
      expect(document!['name'], testData['name']);
      expect(document['value'], testData['value']);
    });

    test('should read document from collection', () async {
      // Arrange
      await database.createCollection(testCollection);
      await database.writeDocument(testCollection, testData['id'] as String, testData);

      // Act
      final document = await database.readDocument(testCollection, testData['id'] as String);

      // Assert
      expect(document, isNotNull);
      expect(document!['id'], testData['id']);
      expect(document['name'], testData['name']);
    });

    test('should return null for non-existent document', () async {
      // Arrange
      await database.createCollection(testCollection);

      // Act
      final document = await database.readDocument(testCollection, 'non-existent');

      // Assert
      expect(document, isNull);
    });

    test('should update existing document', () async {
      // Arrange
      await database.createCollection(testCollection);
      await database.writeDocument(testCollection, testData['id'] as String, testData);

      final updatedData = Map<String, dynamic>.from(testData);
      updatedData['name'] = 'Updated Test Item';
      updatedData['value'] = 84;

      // Act
      await database.updateDocument(testCollection, testData['id'] as String, updatedData);

      // Assert
      final document = await database.readDocument(testCollection, testData['id'] as String);
      expect(document!['name'], 'Updated Test Item');
      expect(document['value'], 84);
    });

    test('should delete document from collection', () async {
      // Arrange
      await database.createCollection(testCollection);
      await database.writeDocument(testCollection, testData['id'] as String, testData);

      // Act
      await database.deleteDocument(testCollection, testData['id'] as String);

      // Assert
      final document = await database.readDocument(testCollection, testData['id'] as String);
      expect(document, isNull);
    });

    test('should read entire collection', () async {
      // Arrange
      await database.createCollection(testCollection);
      
      final documents = [
        {'id': 'doc-1', 'name': 'Document 1'},
        {'id': 'doc-2', 'name': 'Document 2'},
        {'id': 'doc-3', 'name': 'Document 3'},
      ];

      for (final doc in documents) {
        await database.writeDocument(testCollection, doc['id'] as String, doc);
      }

      // Act
      final collection = await database.readCollection(testCollection);

      // Assert
      expect(collection.length, 3);
      expect(collection.any((doc) => doc['name'] == 'Document 1'), true);
      expect(collection.any((doc) => doc['name'] == 'Document 2'), true);
      expect(collection.any((doc) => doc['name'] == 'Document 3'), true);
    });

    test('should clear collection', () async {
      // Arrange
      await database.createCollection(testCollection);
      await database.writeDocument(testCollection, 'doc-1', {'name': 'Document 1'});
      await database.writeDocument(testCollection, 'doc-2', {'name': 'Document 2'});

      // Act
      await database.clearCollection(testCollection);

      // Assert
      final collection = await database.readCollection(testCollection);
      expect(collection.isEmpty, true);
    });

    test('should drop collection', () async {
      // Arrange
      await database.createCollection(testCollection);
      await database.writeDocument(testCollection, 'doc-1', {'name': 'Document 1'});

      // Act
      await database.dropCollection(testCollection);

      // Assert
      final collections = await database.listCollections();
      expect(collections.contains(testCollection), false);
    });
  });

  group('LocalDatabase - Query Operations', () {
    const testCollection = 'query_test_collection';
    
    setUp(() async {
      await database.initialize();
      await database.createCollection(testCollection);
      
      // Insert test data
      final testDocuments = [
        {'id': 'user-1', 'name': 'Alice', 'age': 25, 'city': 'New York', 'active': true},
        {'id': 'user-2', 'name': 'Bob', 'age': 30, 'city': 'Los Angeles', 'active': true},
        {'id': 'user-3', 'name': 'Charlie', 'age': 35, 'city': 'New York', 'active': false},
        {'id': 'user-4', 'name': 'Diana', 'age': 28, 'city': 'Chicago', 'active': true},
      ];

      for (final doc in testDocuments) {
        await database.writeDocument(testCollection, doc['id'] as String, doc);
      }
    });

    test('should query documents with simple filter', () async {
      // Act
      final results = await database.queryDocuments(
        testCollection,
        where: {'city': 'New York'},
      );

      // Assert
      expect(results.length, 2);
      expect(results.every((doc) => doc['city'] == 'New York'), true);
    });

    test('should query documents with multiple filters', () async {
      // Act
      final results = await database.queryDocuments(
        testCollection,
        where: {'city': 'New York', 'active': true},
      );

      // Assert
      expect(results.length, 1);
      expect(results.first['name'], 'Alice');
    });

    test('should query documents with limit', () async {
      // Act
      final results = await database.queryDocuments(
        testCollection,
        limit: 2,
      );

      // Assert
      expect(results.length, 2);
    });

    test('should query documents with offset', () async {
      // Act
      final results = await database.queryDocuments(
        testCollection,
        offset: 2,
      );

      // Assert
      expect(results.length, 2);
    });

    test('should sort query results', () async {
      // Act
      final results = await database.queryDocuments(
        testCollection,
        orderBy: 'age',
        ascending: true,
      );

      // Assert
      expect(results.length, 4);
      expect(results[0]['age'], 25); // Alice
      expect(results[1]['age'], 28); // Diana
      expect(results[2]['age'], 30); // Bob
      expect(results[3]['age'], 35); // Charlie
    });

    test('should sort query results in descending order', () async {
      // Act
      final results = await database.queryDocuments(
        testCollection,
        orderBy: 'age',
        ascending: false,
      );

      // Assert
      expect(results.length, 4);
      expect(results[0]['age'], 35); // Charlie
      expect(results[1]['age'], 30); // Bob
      expect(results[2]['age'], 28); // Diana
      expect(results[3]['age'], 25); // Alice
    });

    test('should count documents in collection', () async {
      // Act
      final count = await database.countDocuments(testCollection);

      // Assert
      expect(count, 4);
    });

    test('should count documents with filter', () async {
      // Act
      final count = await database.countDocuments(
        testCollection,
        where: {'active': true},
      );

      // Assert
      expect(count, 3);
    });
  });

  group('LocalDatabase - Transaction Operations', () {
    const testCollection = 'transaction_test_collection';

    setUp(() async {
      await database.initialize();
      await database.createCollection(testCollection);
    });

    test('should execute transaction successfully', () async {
      // Arrange
      final documents = [
        {'id': 'tx-1', 'name': 'Transaction Doc 1'},
        {'id': 'tx-2', 'name': 'Transaction Doc 2'},
      ];

      // Act
      await database.executeTransaction(() async {
        for (final doc in documents) {
          await database.writeDocument(testCollection, doc['id'] as String, doc);
        }
      });

      // Assert
      final collection = await database.readCollection(testCollection);
      expect(collection.length, 2);
    });

    test('should rollback transaction on error', () async {
      // Arrange
      await database.writeDocument(testCollection, 'existing-doc', {'name': 'Existing'});

      // Act & Assert
      expect(() async {
        await database.executeTransaction(() async {
          await database.writeDocument(testCollection, 'tx-doc', {'name': 'Transaction Doc'});
          throw Exception('Simulated error');
        });
      }, throwsException);

      // Verify rollback
      final collection = await database.readCollection(testCollection);
      expect(collection.length, 1); // Only the existing document should remain
      expect(collection.first['name'], 'Existing');
    });
  });

  group('LocalDatabase - Backup and Restore', () {
    const testCollection = 'backup_test_collection';

    setUp(() async {
      await database.initialize();
      await database.createCollection(testCollection);
      
      // Add test data
      await database.writeDocument(testCollection, 'backup-1', {'name': 'Backup Test 1'});
      await database.writeDocument(testCollection, 'backup-2', {'name': 'Backup Test 2'});
    });

    test('should create backup successfully', () async {
      // Act
      final backupData = await database.createBackup();

      // Assert
      expect(backupData, isNotNull);
      expect(backupData.isNotEmpty, true);
      expect(backupData.containsKey('collections'), true);
    });

    test('should restore from backup successfully', () async {
      // Arrange
      final backupData = await database.createBackup();
      
      // Clear the database
      await database.clearCollection(testCollection);
      expect((await database.readCollection(testCollection)).isEmpty, true);

      // Act
      await database.restoreFromBackup(backupData);

      // Assert
      final collection = await database.readCollection(testCollection);
      expect(collection.length, 2);
      expect(collection.any((doc) => doc['name'] == 'Backup Test 1'), true);
      expect(collection.any((doc) => doc['name'] == 'Backup Test 2'), true);
    });
  });

  group('LocalDatabase - Performance and Optimization', () {
    const testCollection = 'performance_test_collection';

    setUp(() async {
      await database.initialize();
      await database.createCollection(testCollection);
    });

    test('should handle large batch operations efficiently', () async {
      // Arrange
      const batchSize = 1000;
      final documents = List.generate(batchSize, (index) => {
        'id': 'perf-$index',
        'name': 'Performance Test $index',
        'value': index,
      });

      // Act
      final stopwatch = Stopwatch()..start();
      
      await database.executeBatch(() async {
        for (final doc in documents) {
          await database.writeDocument(testCollection, doc['id'] as String, doc);
        }
      });
      
      stopwatch.stop();

      // Assert
      final collection = await database.readCollection(testCollection);
      expect(collection.length, batchSize);
      
      // Performance assertion (should complete within reasonable time)
      expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // 5 seconds max
    });

    test('should optimize query performance with indexes', () async {
      // Arrange
      const documentCount = 100;
      for (int i = 0; i < documentCount; i++) {
        await database.writeDocument(testCollection, 'doc-$i', {
          'id': 'doc-$i',
          'indexed_field': i % 10,
          'name': 'Document $i',
        });
      }

      // Act
      final stopwatch = Stopwatch()..start();
      
      final results = await database.queryDocuments(
        testCollection,
        where: {'indexed_field': 5},
      );
      
      stopwatch.stop();

      // Assert
      expect(results.length, 10); // Should find 10 documents with indexed_field = 5
      expect(stopwatch.elapsedMilliseconds, lessThan(100)); // Should be fast with proper indexing
    });
  });

  group('LocalDatabase - Error Handling', () {
    test('should handle invalid collection names', () async {
      // Arrange
      await database.initialize();

      // Act & Assert
      expect(
        () => database.createCollection(''),
        throwsA(isA<ArgumentError>()),
      );
    });

    test('should handle operations on non-existent collections', () async {
      // Arrange
      await database.initialize();

      // Act & Assert
      expect(
        () => database.readDocument('non_existent_collection', 'doc-1'),
        throwsA(isA<Exception>()),
      );
    });

    test('should handle database not initialized', () async {
      // Arrange
      final uninitializedDb = LocalDatabase();

      // Act & Assert
      expect(
        () => uninitializedDb.createCollection('test'),
        throwsA(isA<StateError>()),
      );
    });
  });
}