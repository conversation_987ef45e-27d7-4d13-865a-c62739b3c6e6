import 'package:freezed_annotation/freezed_annotation.dart';

part 'ai_model.freezed.dart';
part 'ai_model.g.dart';

/// AI模型提供商枚举
enum AIProvider {
  openai('OpenAI'),
  anthropic('Anthropic'),
  google('Google'),
  deepseek('DeepSeek'),
  z<PERSON><PERSON>('智谱AI'),
  moonshot('月之暗面'),
  baidu('百度'),
  al<PERSON><PERSON>('阿里巴巴'),
  custom('自定义');

  const AIProvider(this.displayName);
  final String displayName;
}

/// AI模型类型枚举
enum AIModelType {
  chat('对话模型'),
  completion('补全模型'),
  embedding('嵌入模型');

  const AIModelType(this.displayName);
  final String displayName;
}

/// AI模型状态枚举
enum AIModelStatus {
  available('可用'),
  unavailable('不可用'),
  deprecated('已弃用'),
  beta('测试版');

  const AIModelStatus(this.displayName);
  final String displayName;
}

/// AI模型实体
@freezed
class AIModel with _$AIModel {
  const factory AIModel({
    required String id,
    required String name,
    required String displayName,
    required AIProvider provider,
    required AIModelType type,
    required AIModelStatus status,
    String? description,
    String? version,
    @Default(4096) int maxTokens,
    @Default(0.7) double defaultTemperature,
    @Default(1.0) double defaultTopP,
    @Default(1.0) double defaultFrequencyPenalty,
    @Default(1.0) double defaultPresencePenalty,
    @Default([]) List<String> supportedFeatures,
    Map<String, dynamic>? metadata,
    @Default(false) bool isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _AIModel;

  factory AIModel.fromJson(Map<String, dynamic> json) => _$AIModelFromJson(json);
}

/// AI模型配置
@freezed
class AIModelConfig with _$AIModelConfig {
  const factory AIModelConfig({
    required String modelId,
    required String apiKey,
    String? baseUrl,
    String? organizationId,
    @Default(0.7) double temperature,
    @Default(1.0) double topP,
    @Default(1.0) double frequencyPenalty,
    @Default(1.0) double presencePenalty,
    @Default(4096) int maxTokens,
    @Default(1) int maxRetries,
    @Default(30000) int timeoutMs,
    Map<String, String>? customHeaders,
    Map<String, dynamic>? additionalParams,
    @Default(true) bool isEnabled,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _AIModelConfig;

  factory AIModelConfig.fromJson(Map<String, dynamic> json) => _$AIModelConfigFromJson(json);
}

/// AI请求参数
@freezed
class AIRequest with _$AIRequest {
  const factory AIRequest({
    required String modelId,
    required String prompt,
    List<AIMessage>? messages,
    @Default(0.7) double temperature,
    @Default(1.0) double topP,
    @Default(1.0) double frequencyPenalty,
    @Default(1.0) double presencePenalty,
    @Default(4096) int maxTokens,
    List<String>? stop,
    String? systemPrompt,
    Map<String, dynamic>? metadata,
    String? requestId,
  }) = _AIRequest;

  factory AIRequest.fromJson(Map<String, dynamic> json) => _$AIRequestFromJson(json);
}

/// AI消息
@freezed
class AIMessage with _$AIMessage {
  const factory AIMessage({
    required String role,
    required String content,
    String? name,
    Map<String, dynamic>? metadata,
    DateTime? timestamp,
  }) = _AIMessage;

  factory AIMessage.fromJson(Map<String, dynamic> json) => _$AIMessageFromJson(json);
}

/// AI响应
@freezed
class AIResponse with _$AIResponse {
  const factory AIResponse({
    required String id,
    required String modelId,
    required String content,
    required AIUsage usage,
    String? finishReason,
    List<AIChoice>? choices,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    int? responseTimeMs,
  }) = _AIResponse;

  factory AIResponse.fromJson(Map<String, dynamic> json) => _$AIResponseFromJson(json);
}

/// AI选择项
@freezed
class AIChoice with _$AIChoice {
  const factory AIChoice({
    required int index,
    required AIMessage message,
    String? finishReason,
    double? logprobs,
  }) = _AIChoice;

  factory AIChoice.fromJson(Map<String, dynamic> json) => _$AIChoiceFromJson(json);
}

/// AI使用统计
@freezed
class AIUsage with _$AIUsage {
  const factory AIUsage({
    required int promptTokens,
    required int completionTokens,
    required int totalTokens,
    double? cost,
    String? currency,
  }) = _AIUsage;

  factory AIUsage.fromJson(Map<String, dynamic> json) => _$AIUsageFromJson(json);
}

/// AI错误
@freezed
class AIError with _$AIError {
  const factory AIError({
    required String code,
    required String message,
    String? type,
    String? param,
    Map<String, dynamic>? details,
    DateTime? timestamp,
  }) = _AIError;

  factory AIError.fromJson(Map<String, dynamic> json) => _$AIErrorFromJson(json);
}

/// AI模型能力
@freezed
class AICapabilities with _$AICapabilities {
  const factory AICapabilities({
    @Default(false) bool supportsStreaming,
    @Default(false) bool supportsSystemPrompt,
    @Default(false) bool supportsFunctionCalling,
    @Default(false) bool supportsImageInput,
    @Default(false) bool supportsImageOutput,
    @Default(false) bool supportsAudioInput,
    @Default(false) bool supportsAudioOutput,
    @Default(false) bool supportsCodeExecution,
    @Default(false) bool supportsWebSearch,
    @Default([]) List<String> supportedLanguages,
    @Default([]) List<String> supportedFormats,
  }) = _AICapabilities;

  factory AICapabilities.fromJson(Map<String, dynamic> json) => _$AICapabilitiesFromJson(json);
}

/// 预定义的AI模型
class PredefinedAIModels {
  static const List<AIModel> models = [
    // OpenAI Models
    AIModel(
      id: 'gpt-4o',
      name: 'gpt-4o',
      displayName: 'GPT-4o',
      provider: AIProvider.openai,
      type: AIModelType.chat,
      status: AIModelStatus.available,
      description: 'OpenAI最新的多模态大型语言模型',
      maxTokens: 128000,
      supportedFeatures: ['chat', 'vision', 'function_calling'],
    ),
    AIModel(
      id: 'gpt-4o-mini',
      name: 'gpt-4o-mini',
      displayName: 'GPT-4o Mini',
      provider: AIProvider.openai,
      type: AIModelType.chat,
      status: AIModelStatus.available,
      description: 'OpenAI的轻量级多模态模型',
      maxTokens: 128000,
      supportedFeatures: ['chat', 'vision'],
    ),
    AIModel(
      id: 'gpt-3.5-turbo',
      name: 'gpt-3.5-turbo',
      displayName: 'GPT-3.5 Turbo',
      provider: AIProvider.openai,
      type: AIModelType.chat,
      status: AIModelStatus.available,
      description: 'OpenAI的高效对话模型',
      maxTokens: 16385,
      supportedFeatures: ['chat', 'function_calling'],
    ),
    
    // Anthropic Models
    AIModel(
      id: 'claude-3-5-sonnet-20241022',
      name: 'claude-3-5-sonnet-20241022',
      displayName: 'Claude 3.5 Sonnet',
      provider: AIProvider.anthropic,
      type: AIModelType.chat,
      status: AIModelStatus.available,
      description: 'Anthropic最强的对话模型',
      maxTokens: 200000,
      supportedFeatures: ['chat', 'vision', 'code_execution'],
    ),
    AIModel(
      id: 'claude-3-haiku-20240307',
      name: 'claude-3-haiku-20240307',
      displayName: 'Claude 3 Haiku',
      provider: AIProvider.anthropic,
      type: AIModelType.chat,
      status: AIModelStatus.available,
      description: 'Anthropic的快速轻量级模型',
      maxTokens: 200000,
      supportedFeatures: ['chat', 'vision'],
    ),
    
    // Google Models
    AIModel(
      id: 'gemini-2.0-flash-exp',
      name: 'gemini-2.0-flash-exp',
      displayName: 'Gemini 2.0 Flash',
      provider: AIProvider.google,
      type: AIModelType.chat,
      status: AIModelStatus.beta,
      description: 'Google最新的实验性多模态模型',
      maxTokens: 1048576,
      supportedFeatures: ['chat', 'vision', 'audio', 'code_execution'],
    ),
    AIModel(
      id: 'gemini-1.5-pro',
      name: 'gemini-1.5-pro',
      displayName: 'Gemini 1.5 Pro',
      provider: AIProvider.google,
      type: AIModelType.chat,
      status: AIModelStatus.available,
      description: 'Google的专业级多模态模型',
      maxTokens: 2097152,
      supportedFeatures: ['chat', 'vision', 'audio'],
    ),
    
    // DeepSeek Models
    AIModel(
      id: 'deepseek-v3',
      name: 'deepseek-v3',
      displayName: 'DeepSeek V3',
      provider: AIProvider.deepseek,
      type: AIModelType.chat,
      status: AIModelStatus.available,
      description: 'DeepSeek最新的大型语言模型',
      maxTokens: 64000,
      supportedFeatures: ['chat', 'code'],
    ),
    AIModel(
      id: 'deepseek-r1',
      name: 'deepseek-r1',
      displayName: 'DeepSeek R1',
      provider: AIProvider.deepseek,
      type: AIModelType.chat,
      status: AIModelStatus.available,
      description: 'DeepSeek的推理优化模型',
      maxTokens: 64000,
      supportedFeatures: ['chat', 'reasoning'],
    ),
  ];
  
  /// 根据提供商获取模型
  static List<AIModel> getModelsByProvider(AIProvider provider) {
    return models.where((model) => model.provider == provider).toList();
  }
  
  /// 根据ID获取模型
  static AIModel? getModelById(String id) {
    try {
      return models.firstWhere((model) => model.id == id);
    } catch (e) {
      return null;
    }
  }
  
  /// 获取可用模型
  static List<AIModel> getAvailableModels() {
    return models.where((model) => model.status == AIModelStatus.available).toList();
  }
}