import 'package:flutter/material.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;
import '../../domain/entities/app_settings.dart';

/// 语言选择器
class LanguageSelector extends StatelessWidget {
  final String currentLanguage;
  final Function(String) onLanguageChanged;

  const LanguageSelector({
    super.key,
    required this.currentLanguage,
    required this.onLanguageChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 语言选项
        ...SupportedLanguage.values.map((language) {
          final isSelected = currentLanguage == language.code;
          
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: fluent.RadioButton(
              checked: isSelected,
              onChanged: (checked) {
                if (checked) {
                  onLanguageChanged(language.code);
                }
              },
              content: Row(
                children: [
                  _buildLanguageFlag(language),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        language.displayName,
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                      Text(
                        language.nativeName,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        }),
        const SizedBox(height: 16),
        
        // 语言信息
        _buildLanguageInfo(),
      ],
    );
  }

  /// 构建语言标志
  Widget _buildLanguageFlag(SupportedLanguage language) {
    return Container(
      width: 32,
      height: 24,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Center(
        child: Text(
          _getLanguageFlag(language),
          style: const TextStyle(fontSize: 16),
        ),
      ),
    );
  }

  /// 获取语言标志
  String _getLanguageFlag(SupportedLanguage language) {
    switch (language) {
      case SupportedLanguage.zhCN:
        return '🇨🇳';
      case SupportedLanguage.zhTW:
        return '🇹🇼';
      case SupportedLanguage.en:
        return '🇺🇸';
      case SupportedLanguage.ja:
        return '🇯🇵';
      case SupportedLanguage.ko:
        return '🇰🇷';
    }
  }

  /// 构建语言信息
  Widget _buildLanguageInfo() {
    final currentLang = SupportedLanguage.values.firstWhere(
      (lang) => lang.code == currentLanguage,
      orElse: () => SupportedLanguage.zhCN,
    );

    return fluent.InfoBar(
      title: const Text('语言设置'),
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('当前语言: ${currentLang.displayName}'),
          const SizedBox(height: 4),
          const Text('重启应用后生效'),
        ],
      ),
      severity: fluent.InfoBarSeverity.info,
    );
  }
}

/// 下拉语言选择器
class DropdownLanguageSelector extends StatelessWidget {
  final String currentLanguage;
  final Function(String) onLanguageChanged;

  const DropdownLanguageSelector({
    super.key,
    required this.currentLanguage,
    required this.onLanguageChanged,
  });

  @override
  Widget build(BuildContext context) {
    final currentLang = SupportedLanguage.values.firstWhere(
      (lang) => lang.code == currentLanguage,
      orElse: () => SupportedLanguage.zhCN,
    );

    return fluent.ComboBox<SupportedLanguage>(
      value: currentLang,
      items: SupportedLanguage.values.map((language) {
        return fluent.ComboBoxItem<SupportedLanguage>(
          value: language,
          child: Row(
            children: [
              Text(_getLanguageFlag(language)),
              const SizedBox(width: 8),
              Text(language.displayName),
            ],
          ),
        );
      }).toList(),
      onChanged: (language) {
        if (language != null) {
          onLanguageChanged(language.code);
        }
      },
    );
  }

  /// 获取语言标志
  String _getLanguageFlag(SupportedLanguage language) {
    switch (language) {
      case SupportedLanguage.zhCN:
        return '🇨🇳';
      case SupportedLanguage.zhTW:
        return '🇹🇼';
      case SupportedLanguage.en:
        return '🇺🇸';
      case SupportedLanguage.ja:
        return '🇯🇵';
      case SupportedLanguage.ko:
        return '🇰🇷';
    }
  }
}

/// 语言网格选择器
class GridLanguageSelector extends StatelessWidget {
  final String currentLanguage;
  final Function(String) onLanguageChanged;

  const GridLanguageSelector({
    super.key,
    required this.currentLanguage,
    required this.onLanguageChanged,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: SupportedLanguage.values.length,
      itemBuilder: (context, index) {
        final language = SupportedLanguage.values[index];
        final isSelected = currentLanguage == language.code;

        return GestureDetector(
          onTap: () => onLanguageChanged(language.code),
          child: Container(
            decoration: BoxDecoration(
              color: isSelected
                  ? fluent.FluentTheme.of(context).accentColor.withValues(alpha: 0.1)
                  : Colors.transparent,
              border: Border.all(
                color: isSelected
                    ? fluent.FluentTheme.of(context).accentColor
                    : Colors.grey.shade300,
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  Text(
                    _getLanguageFlag(language),
                    style: const TextStyle(fontSize: 20),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          language.displayName,
                          style: TextStyle(
                            fontWeight: isSelected
                                ? FontWeight.w600
                                : FontWeight.w400,
                          ),
                        ),
                        Text(
                          language.nativeName,
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (isSelected)
                    Icon(
                      fluent.FluentIcons.check_mark,
                      size: 16,
                      color: fluent.FluentTheme.of(context).accentColor,
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 获取语言标志
  String _getLanguageFlag(SupportedLanguage language) {
    switch (language) {
      case SupportedLanguage.zhCN:
        return '🇨🇳';
      case SupportedLanguage.zhTW:
        return '🇹🇼';
      case SupportedLanguage.en:
        return '🇺🇸';
      case SupportedLanguage.ja:
        return '🇯🇵';
      case SupportedLanguage.ko:
        return '🇰🇷';
    }
  }
}

/// 语言切换按钮
class LanguageToggleButton extends StatelessWidget {
  final String currentLanguage;
  final Function(String) onLanguageChanged;

  const LanguageToggleButton({
    super.key,
    required this.currentLanguage,
    required this.onLanguageChanged,
  });

  @override
  Widget build(BuildContext context) {
    final currentLang = SupportedLanguage.values.firstWhere(
      (lang) => lang.code == currentLanguage,
      orElse: () => SupportedLanguage.zhCN,
    );

    return fluent.DropDownButton(
      leading: Text(_getLanguageFlag(currentLang)),
      title: Text(currentLang.displayName),
      items: SupportedLanguage.values.map((language) {
        return fluent.MenuFlyoutItem(
          leading: Text(_getLanguageFlag(language)),
          text: Text(language.displayName),
          onPressed: () => onLanguageChanged(language.code),
        );
      }).toList(),
    );
  }

  /// 获取语言标志
  String _getLanguageFlag(SupportedLanguage language) {
    switch (language) {
      case SupportedLanguage.zhCN:
        return '🇨🇳';
      case SupportedLanguage.zhTW:
        return '🇹🇼';
      case SupportedLanguage.en:
        return '🇺🇸';
      case SupportedLanguage.ja:
        return '🇯🇵';
      case SupportedLanguage.ko:
        return '🇰🇷';
    }
  }
}

/// 语言设置面板
class LanguageSettingsPanel extends StatelessWidget {
  final String currentLanguage;
  final Function(String) onLanguageChanged;

  const LanguageSettingsPanel({
    super.key,
    required this.currentLanguage,
    required this.onLanguageChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 语言选择
        Text(
          '界面语言',
          style: fluent.FluentTheme.of(context).typography.subtitle,
        ),
        const SizedBox(height: 12),
        GridLanguageSelector(
          currentLanguage: currentLanguage,
          onLanguageChanged: onLanguageChanged,
        ),
        const SizedBox(height: 24),
        
        // 区域设置
        Text(
          '区域设置',
          style: fluent.FluentTheme.of(context).typography.subtitle,
        ),
        const SizedBox(height: 12),
        fluent.Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSettingRow('日期格式', 'YYYY-MM-DD'),
                const SizedBox(height: 8),
                _buildSettingRow('时间格式', '24小时制'),
                const SizedBox(height: 8),
                _buildSettingRow('数字格式', '1,234.56'),
                const SizedBox(height: 8),
                _buildSettingRow('货币符号', '¥'),
              ],
            ),
          ),
        ),
        const SizedBox(height: 24),
        
        // 输入法设置
        Text(
          '输入法设置',
          style: fluent.FluentTheme.of(context).typography.subtitle,
        ),
        const SizedBox(height: 12),
        fluent.Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                fluent.ToggleSwitch(
                  checked: true,
                  onChanged: (value) {},
                  content: const Text('启用智能输入建议'),
                ),
                const SizedBox(height: 12),
                fluent.ToggleSwitch(
                  checked: false,
                  onChanged: (value) {},
                  content: const Text('启用自动纠错'),
                ),
                const SizedBox(height: 12),
                fluent.ToggleSwitch(
                  checked: true,
                  onChanged: (value) {},
                  content: const Text('启用词汇联想'),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建设置行
  Widget _buildSettingRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label),
        Text(
          value,
          style: const TextStyle(color: Colors.grey),
        ),
      ],
    );
  }
}