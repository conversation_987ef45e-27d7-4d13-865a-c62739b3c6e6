import '../entities/template.dart';

/// 模板仓库接口
abstract class TemplateRepository {
  /// 获取所有模板
  Future<List<PromptTemplate>> getAllTemplates();

  /// 根据分类获取模板
  Future<List<PromptTemplate>> getTemplatesByCategory(TemplateCategory category);

  /// 根据ID获取模板
  Future<PromptTemplate?> getTemplateById(String id);

  /// 搜索模板
  Future<List<PromptTemplate>> searchTemplates(String query);

  /// 根据标签获取模板
  Future<List<PromptTemplate>> getTemplatesByTags(List<String> tags);

  /// 创建模板
  Future<void> createTemplate(PromptTemplate template);

  /// 更新模板
  Future<void> updateTemplate(PromptTemplate template);

  /// 删除模板
  Future<void> deleteTemplate(String id);

  /// 复制模板
  Future<PromptTemplate> duplicateTemplate(String id, String newName);

  /// 获取内置模板
  Future<List<PromptTemplate>> getBuiltInTemplates();

  /// 获取用户自定义模板
  Future<List<PromptTemplate>> getUserTemplates();

  /// 增加模板使用次数
  Future<void> incrementUsageCount(String id);

  /// 获取最常用的模板
  Future<List<PromptTemplate>> getMostUsedTemplates({int limit = 10});

  /// 获取最近使用的模板
  Future<List<PromptTemplate>> getRecentlyUsedTemplates({int limit = 10});

  /// 导出模板
  Future<String> exportTemplate(String id);

  /// 导入模板
  Future<PromptTemplate> importTemplate(String templateData);

  /// 批量导入模板
  Future<List<PromptTemplate>> importTemplates(List<String> templatesData);

  /// 验证模板
  Future<bool> validateTemplate(PromptTemplate template);

  /// 获取模板统计信息
  Future<TemplateStatistics> getTemplateStatistics();

  /// 清理未使用的模板
  Future<int> cleanupUnusedTemplates({int daysThreshold = 30});

  /// 备份所有模板
  Future<String> backupAllTemplates();

  /// 恢复模板备份
  Future<void> restoreTemplatesFromBackup(String backupData);
}

