// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'location.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LocationImpl _$$LocationImplFromJson(
  Map<String, dynamic> json,
) => _$LocationImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  aliases:
      (json['aliases'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  description: json['description'] as String?,
  type: $enumDecode(_$LocationTypeEnumMap, json['type']),
  status: $enumDecode(_$LocationStatusEnumMap, json['status']),
  geography: json['geography'] == null
      ? null
      : LocationGeography.fromJson(json['geography'] as Map<String, dynamic>),
  climate: json['climate'] == null
      ? null
      : LocationClimate.fromJson(json['climate'] as Map<String, dynamic>),
  culture: json['culture'] == null
      ? null
      : LocationCulture.fromJson(json['culture'] as Map<String, dynamic>),
  economy: json['economy'] == null
      ? null
      : LocationEconomy.fromJson(json['economy'] as Map<String, dynamic>),
  politics: json['politics'] == null
      ? null
      : LocationPolitics.fromJson(json['politics'] as Map<String, dynamic>),
  parentLocationIds:
      (json['parentLocationIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  childLocationIds:
      (json['childLocationIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  connectedLocationIds:
      (json['connectedLocationIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  features:
      (json['features'] as List<dynamic>?)
          ?.map((e) => LocationFeature.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  residentCharacterIds:
      (json['residentCharacterIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  visitingCharacterIds:
      (json['visitingCharacterIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  events:
      (json['events'] as List<dynamic>?)
          ?.map((e) => LocationEvent.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  availableItemIds:
      (json['availableItemIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  secrets:
      (json['secrets'] as List<dynamic>?)
          ?.map((e) => LocationSecret.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  imageUrl: json['imageUrl'] as String?,
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$LocationImplToJson(_$LocationImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'aliases': instance.aliases,
      'description': instance.description,
      'type': _$LocationTypeEnumMap[instance.type]!,
      'status': _$LocationStatusEnumMap[instance.status]!,
      'geography': instance.geography,
      'climate': instance.climate,
      'culture': instance.culture,
      'economy': instance.economy,
      'politics': instance.politics,
      'parentLocationIds': instance.parentLocationIds,
      'childLocationIds': instance.childLocationIds,
      'connectedLocationIds': instance.connectedLocationIds,
      'features': instance.features,
      'residentCharacterIds': instance.residentCharacterIds,
      'visitingCharacterIds': instance.visitingCharacterIds,
      'events': instance.events,
      'availableItemIds': instance.availableItemIds,
      'secrets': instance.secrets,
      'imageUrl': instance.imageUrl,
      'customAttributes': instance.customAttributes,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$LocationTypeEnumMap = {
  LocationType.continent: 'continent',
  LocationType.country: 'country',
  LocationType.region: 'region',
  LocationType.city: 'city',
  LocationType.town: 'town',
  LocationType.village: 'village',
  LocationType.district: 'district',
  LocationType.building: 'building',
  LocationType.room: 'room',
  LocationType.natural: 'natural',
  LocationType.dungeon: 'dungeon',
  LocationType.fortress: 'fortress',
  LocationType.temple: 'temple',
  LocationType.market: 'market',
  LocationType.tavern: 'tavern',
  LocationType.forest: 'forest',
  LocationType.mountain: 'mountain',
  LocationType.river: 'river',
  LocationType.ocean: 'ocean',
  LocationType.desert: 'desert',
  LocationType.other: 'other',
};

const _$LocationStatusEnumMap = {
  LocationStatus.active: 'active',
  LocationStatus.abandoned: 'abandoned',
  LocationStatus.destroyed: 'destroyed',
  LocationStatus.hidden: 'hidden',
  LocationStatus.restricted: 'restricted',
  LocationStatus.unknown: 'unknown',
};

_$LocationGeographyImpl _$$LocationGeographyImplFromJson(
  Map<String, dynamic> json,
) => _$LocationGeographyImpl(
  coordinates: json['coordinates'] == null
      ? null
      : LocationCoordinates.fromJson(
          json['coordinates'] as Map<String, dynamic>,
        ),
  terrain: json['terrain'] as String?,
  elevation: (json['elevation'] as num?)?.toDouble(),
  area: (json['area'] as num?)?.toDouble(),
  naturalFeatures:
      (json['naturalFeatures'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  landmarks:
      (json['landmarks'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  waterSources: json['waterSources'] as String?,
  vegetation: json['vegetation'] as String?,
  naturalResources:
      (json['naturalResources'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  customGeography: json['customGeography'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$LocationGeographyImplToJson(
  _$LocationGeographyImpl instance,
) => <String, dynamic>{
  'coordinates': instance.coordinates,
  'terrain': instance.terrain,
  'elevation': instance.elevation,
  'area': instance.area,
  'naturalFeatures': instance.naturalFeatures,
  'landmarks': instance.landmarks,
  'waterSources': instance.waterSources,
  'vegetation': instance.vegetation,
  'naturalResources': instance.naturalResources,
  'customGeography': instance.customGeography,
};

_$LocationCoordinatesImpl _$$LocationCoordinatesImplFromJson(
  Map<String, dynamic> json,
) => _$LocationCoordinatesImpl(
  latitude: (json['latitude'] as num).toDouble(),
  longitude: (json['longitude'] as num).toDouble(),
  coordinateSystem: json['coordinateSystem'] as String?,
  customCoordinates:
      json['customCoordinates'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$LocationCoordinatesImplToJson(
  _$LocationCoordinatesImpl instance,
) => <String, dynamic>{
  'latitude': instance.latitude,
  'longitude': instance.longitude,
  'coordinateSystem': instance.coordinateSystem,
  'customCoordinates': instance.customCoordinates,
};

_$LocationClimateImpl _$$LocationClimateImplFromJson(
  Map<String, dynamic> json,
) => _$LocationClimateImpl(
  climateType: json['climateType'] as String?,
  seasons:
      (json['seasons'] as List<dynamic>?)
          ?.map((e) => Season.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  averageTemperature: (json['averageTemperature'] as num?)?.toDouble(),
  averageRainfall: (json['averageRainfall'] as num?)?.toDouble(),
  weatherPatterns:
      (json['weatherPatterns'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  extremeWeather:
      (json['extremeWeather'] as List<dynamic>?)
          ?.map((e) => WeatherEvent.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  customClimate: json['customClimate'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$LocationClimateImplToJson(
  _$LocationClimateImpl instance,
) => <String, dynamic>{
  'climateType': instance.climateType,
  'seasons': instance.seasons,
  'averageTemperature': instance.averageTemperature,
  'averageRainfall': instance.averageRainfall,
  'weatherPatterns': instance.weatherPatterns,
  'extremeWeather': instance.extremeWeather,
  'customClimate': instance.customClimate,
};

_$SeasonImpl _$$SeasonImplFromJson(Map<String, dynamic> json) => _$SeasonImpl(
  name: json['name'] as String,
  description: json['description'] as String?,
  durationDays: (json['durationDays'] as num?)?.toInt(),
  averageTemperature: (json['averageTemperature'] as num?)?.toDouble(),
  averageRainfall: (json['averageRainfall'] as num?)?.toDouble(),
  characteristics:
      (json['characteristics'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$SeasonImplToJson(_$SeasonImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'durationDays': instance.durationDays,
      'averageTemperature': instance.averageTemperature,
      'averageRainfall': instance.averageRainfall,
      'characteristics': instance.characteristics,
      'customAttributes': instance.customAttributes,
    };

_$WeatherEventImpl _$$WeatherEventImplFromJson(Map<String, dynamic> json) =>
    _$WeatherEventImpl(
      name: json['name'] as String,
      description: json['description'] as String?,
      type: $enumDecodeNullable(_$WeatherEventTypeEnumMap, json['type']),
      severity: $enumDecodeNullable(
        _$WeatherEventSeverityEnumMap,
        json['severity'],
      ),
      frequency: (json['frequency'] as num?)?.toInt(),
      durationDays: (json['durationDays'] as num?)?.toInt(),
      effects:
          (json['effects'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      customAttributes:
          json['customAttributes'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$WeatherEventImplToJson(_$WeatherEventImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'type': _$WeatherEventTypeEnumMap[instance.type],
      'severity': _$WeatherEventSeverityEnumMap[instance.severity],
      'frequency': instance.frequency,
      'durationDays': instance.durationDays,
      'effects': instance.effects,
      'customAttributes': instance.customAttributes,
    };

const _$WeatherEventTypeEnumMap = {
  WeatherEventType.storm: 'storm',
  WeatherEventType.drought: 'drought',
  WeatherEventType.flood: 'flood',
  WeatherEventType.blizzard: 'blizzard',
  WeatherEventType.heatwave: 'heatwave',
  WeatherEventType.earthquake: 'earthquake',
  WeatherEventType.volcanic: 'volcanic',
  WeatherEventType.magical: 'magical',
};

const _$WeatherEventSeverityEnumMap = {
  WeatherEventSeverity.mild: 'mild',
  WeatherEventSeverity.moderate: 'moderate',
  WeatherEventSeverity.severe: 'severe',
  WeatherEventSeverity.catastrophic: 'catastrophic',
};

_$LocationCultureImpl _$$LocationCultureImplFromJson(
  Map<String, dynamic> json,
) => _$LocationCultureImpl(
  ethnicGroups:
      (json['ethnicGroups'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  languages:
      (json['languages'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  religions:
      (json['religions'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  traditions:
      (json['traditions'] as List<dynamic>?)
          ?.map((e) => CulturalTradition.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  festivals:
      (json['festivals'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  customs:
      (json['customs'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  architecture: json['architecture'] as String?,
  artStyle: json['artStyle'] as String?,
  cuisine:
      (json['cuisine'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  clothing:
      (json['clothing'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  customCulture: json['customCulture'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$LocationCultureImplToJson(
  _$LocationCultureImpl instance,
) => <String, dynamic>{
  'ethnicGroups': instance.ethnicGroups,
  'languages': instance.languages,
  'religions': instance.religions,
  'traditions': instance.traditions,
  'festivals': instance.festivals,
  'customs': instance.customs,
  'architecture': instance.architecture,
  'artStyle': instance.artStyle,
  'cuisine': instance.cuisine,
  'clothing': instance.clothing,
  'customCulture': instance.customCulture,
};

_$CulturalTraditionImpl _$$CulturalTraditionImplFromJson(
  Map<String, dynamic> json,
) => _$CulturalTraditionImpl(
  name: json['name'] as String,
  description: json['description'] as String?,
  origin: json['origin'] as String?,
  type: $enumDecodeNullable(_$TraditionTypeEnumMap, json['type']),
  practices:
      (json['practices'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  significance: json['significance'] as String?,
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$CulturalTraditionImplToJson(
  _$CulturalTraditionImpl instance,
) => <String, dynamic>{
  'name': instance.name,
  'description': instance.description,
  'origin': instance.origin,
  'type': _$TraditionTypeEnumMap[instance.type],
  'practices': instance.practices,
  'significance': instance.significance,
  'customAttributes': instance.customAttributes,
};

const _$TraditionTypeEnumMap = {
  TraditionType.religious: 'religious',
  TraditionType.social: 'social',
  TraditionType.ceremonial: 'ceremonial',
  TraditionType.seasonal: 'seasonal',
  TraditionType.lifecycle: 'lifecycle',
  TraditionType.occupational: 'occupational',
  TraditionType.other: 'other',
};

_$LocationEconomyImpl _$$LocationEconomyImplFromJson(
  Map<String, dynamic> json,
) => _$LocationEconomyImpl(
  economicSystem: json['economicSystem'] as String?,
  primaryIndustries:
      (json['primaryIndustries'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  exports:
      (json['exports'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  imports:
      (json['imports'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  currency: json['currency'] as String?,
  economicStatus: $enumDecodeNullable(
    _$EconomicStatusEnumMap,
    json['economicStatus'],
  ),
  tradeRoutes:
      (json['tradeRoutes'] as List<dynamic>?)
          ?.map((e) => TradeRoute.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  marketplaces:
      (json['marketplaces'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  customEconomy: json['customEconomy'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$LocationEconomyImplToJson(
  _$LocationEconomyImpl instance,
) => <String, dynamic>{
  'economicSystem': instance.economicSystem,
  'primaryIndustries': instance.primaryIndustries,
  'exports': instance.exports,
  'imports': instance.imports,
  'currency': instance.currency,
  'economicStatus': _$EconomicStatusEnumMap[instance.economicStatus],
  'tradeRoutes': instance.tradeRoutes,
  'marketplaces': instance.marketplaces,
  'customEconomy': instance.customEconomy,
};

const _$EconomicStatusEnumMap = {
  EconomicStatus.prosperous: 'prosperous',
  EconomicStatus.stable: 'stable',
  EconomicStatus.declining: 'declining',
  EconomicStatus.poor: 'poor',
  EconomicStatus.recovering: 'recovering',
};

_$TradeRouteImpl _$$TradeRouteImplFromJson(Map<String, dynamic> json) =>
    _$TradeRouteImpl(
      name: json['name'] as String,
      description: json['description'] as String?,
      connectedLocationIds:
          (json['connectedLocationIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      tradedGoods:
          (json['tradedGoods'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      status: $enumDecodeNullable(_$TradeRouteStatusEnumMap, json['status']),
      dangers:
          (json['dangers'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      travelTimeDays: (json['travelTimeDays'] as num?)?.toInt(),
      customAttributes:
          json['customAttributes'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$TradeRouteImplToJson(_$TradeRouteImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'connectedLocationIds': instance.connectedLocationIds,
      'tradedGoods': instance.tradedGoods,
      'status': _$TradeRouteStatusEnumMap[instance.status],
      'dangers': instance.dangers,
      'travelTimeDays': instance.travelTimeDays,
      'customAttributes': instance.customAttributes,
    };

const _$TradeRouteStatusEnumMap = {
  TradeRouteStatus.active: 'active',
  TradeRouteStatus.inactive: 'inactive',
  TradeRouteStatus.dangerous: 'dangerous',
  TradeRouteStatus.blocked: 'blocked',
  TradeRouteStatus.seasonal: 'seasonal',
};

_$LocationPoliticsImpl _$$LocationPoliticsImplFromJson(
  Map<String, dynamic> json,
) => _$LocationPoliticsImpl(
  governmentType: json['governmentType'] as String?,
  leaders:
      (json['leaders'] as List<dynamic>?)
          ?.map((e) => PoliticalFigure.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  factions:
      (json['factions'] as List<dynamic>?)
          ?.map((e) => PoliticalFaction.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  laws:
      (json['laws'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  conflicts:
      (json['conflicts'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  alliances:
      (json['alliances'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  militaryStrength: json['militaryStrength'] as String?,
  customPolitics: json['customPolitics'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$LocationPoliticsImplToJson(
  _$LocationPoliticsImpl instance,
) => <String, dynamic>{
  'governmentType': instance.governmentType,
  'leaders': instance.leaders,
  'factions': instance.factions,
  'laws': instance.laws,
  'conflicts': instance.conflicts,
  'alliances': instance.alliances,
  'militaryStrength': instance.militaryStrength,
  'customPolitics': instance.customPolitics,
};

_$PoliticalFigureImpl _$$PoliticalFigureImplFromJson(
  Map<String, dynamic> json,
) => _$PoliticalFigureImpl(
  characterId: json['characterId'] as String,
  position: json['position'] as String,
  title: json['title'] as String?,
  influence: (json['influence'] as num?)?.toInt(),
  responsibilities:
      (json['responsibilities'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  termStart: json['termStart'] == null
      ? null
      : DateTime.parse(json['termStart'] as String),
  termEnd: json['termEnd'] == null
      ? null
      : DateTime.parse(json['termEnd'] as String),
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$PoliticalFigureImplToJson(
  _$PoliticalFigureImpl instance,
) => <String, dynamic>{
  'characterId': instance.characterId,
  'position': instance.position,
  'title': instance.title,
  'influence': instance.influence,
  'responsibilities': instance.responsibilities,
  'termStart': instance.termStart?.toIso8601String(),
  'termEnd': instance.termEnd?.toIso8601String(),
  'customAttributes': instance.customAttributes,
};

_$PoliticalFactionImpl _$$PoliticalFactionImplFromJson(
  Map<String, dynamic> json,
) => _$PoliticalFactionImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String?,
  memberCharacterIds:
      (json['memberCharacterIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  ideology: json['ideology'] as String?,
  power: (json['power'] as num?)?.toInt(),
  goals:
      (json['goals'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  enemies:
      (json['enemies'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  allies:
      (json['allies'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$PoliticalFactionImplToJson(
  _$PoliticalFactionImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'description': instance.description,
  'memberCharacterIds': instance.memberCharacterIds,
  'ideology': instance.ideology,
  'power': instance.power,
  'goals': instance.goals,
  'enemies': instance.enemies,
  'allies': instance.allies,
  'customAttributes': instance.customAttributes,
};

_$LocationFeatureImpl _$$LocationFeatureImplFromJson(
  Map<String, dynamic> json,
) => _$LocationFeatureImpl(
  name: json['name'] as String,
  description: json['description'] as String?,
  type: $enumDecode(_$FeatureTypeEnumMap, json['type']),
  importance: $enumDecodeNullable(
    _$FeatureImportanceEnumMap,
    json['importance'],
  ),
  effects:
      (json['effects'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  requirements:
      (json['requirements'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  customAttributes:
      json['customAttributes'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$LocationFeatureImplToJson(
  _$LocationFeatureImpl instance,
) => <String, dynamic>{
  'name': instance.name,
  'description': instance.description,
  'type': _$FeatureTypeEnumMap[instance.type]!,
  'importance': _$FeatureImportanceEnumMap[instance.importance],
  'effects': instance.effects,
  'requirements': instance.requirements,
  'customAttributes': instance.customAttributes,
};

const _$FeatureTypeEnumMap = {
  FeatureType.architectural: 'architectural',
  FeatureType.natural: 'natural',
  FeatureType.magical: 'magical',
  FeatureType.historical: 'historical',
  FeatureType.cultural: 'cultural',
  FeatureType.defensive: 'defensive',
  FeatureType.commercial: 'commercial',
  FeatureType.religious: 'religious',
};

const _$FeatureImportanceEnumMap = {
  FeatureImportance.minor: 'minor',
  FeatureImportance.moderate: 'moderate',
  FeatureImportance.major: 'major',
  FeatureImportance.critical: 'critical',
};

_$LocationEventImpl _$$LocationEventImplFromJson(Map<String, dynamic> json) =>
    _$LocationEventImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      eventDate: DateTime.parse(json['eventDate'] as String),
      type: $enumDecodeNullable(_$LocationEventTypeEnumMap, json['type']),
      involvedCharacterIds:
          (json['involvedCharacterIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      consequences:
          (json['consequences'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      chapterId: json['chapterId'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$LocationEventImplToJson(_$LocationEventImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'eventDate': instance.eventDate.toIso8601String(),
      'type': _$LocationEventTypeEnumMap[instance.type],
      'involvedCharacterIds': instance.involvedCharacterIds,
      'consequences': instance.consequences,
      'chapterId': instance.chapterId,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$LocationEventTypeEnumMap = {
  LocationEventType.battle: 'battle',
  LocationEventType.celebration: 'celebration',
  LocationEventType.disaster: 'disaster',
  LocationEventType.discovery: 'discovery',
  LocationEventType.construction: 'construction',
  LocationEventType.destruction: 'destruction',
  LocationEventType.meeting: 'meeting',
  LocationEventType.ceremony: 'ceremony',
  LocationEventType.other: 'other',
};

_$LocationSecretImpl _$$LocationSecretImplFromJson(Map<String, dynamic> json) =>
    _$LocationSecretImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$SecretTypeEnumMap, json['type']),
      severity: $enumDecode(_$SecretSeverityEnumMap, json['severity']),
      knownByCharacterIds:
          (json['knownByCharacterIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      discoveryCondition: json['discoveryCondition'] as String?,
      consequence: json['consequence'] as String?,
      isDiscovered: json['isDiscovered'] as bool?,
      discoveredAt: json['discoveredAt'] == null
          ? null
          : DateTime.parse(json['discoveredAt'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$LocationSecretImplToJson(
  _$LocationSecretImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'description': instance.description,
  'type': _$SecretTypeEnumMap[instance.type]!,
  'severity': _$SecretSeverityEnumMap[instance.severity]!,
  'knownByCharacterIds': instance.knownByCharacterIds,
  'discoveryCondition': instance.discoveryCondition,
  'consequence': instance.consequence,
  'isDiscovered': instance.isDiscovered,
  'discoveredAt': instance.discoveredAt?.toIso8601String(),
  'metadata': instance.metadata,
  'createdAt': instance.createdAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
};

const _$SecretTypeEnumMap = {
  SecretType.treasure: 'treasure',
  SecretType.passage: 'passage',
  SecretType.history: 'history',
  SecretType.danger: 'danger',
  SecretType.resource: 'resource',
  SecretType.magic: 'magic',
  SecretType.conspiracy: 'conspiracy',
  SecretType.other: 'other',
};

const _$SecretSeverityEnumMap = {
  SecretSeverity.minor: 'minor',
  SecretSeverity.moderate: 'moderate',
  SecretSeverity.major: 'major',
  SecretSeverity.critical: 'critical',
};

_$LocationStatsImpl _$$LocationStatsImplFromJson(Map<String, dynamic> json) =>
    _$LocationStatsImpl(
      locationId: json['locationId'] as String,
      visitCount: (json['visitCount'] as num?)?.toInt(),
      eventCount: (json['eventCount'] as num?)?.toInt(),
      frequentVisitors:
          (json['frequentVisitors'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      significantEvents:
          (json['significantEvents'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      lastVisit: json['lastVisit'] == null
          ? null
          : DateTime.parse(json['lastVisit'] as String),
      characterVisitCounts:
          (json['characterVisitCounts'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, (e as num).toInt()),
          ) ??
          const {},
      customStats: json['customStats'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$LocationStatsImplToJson(_$LocationStatsImpl instance) =>
    <String, dynamic>{
      'locationId': instance.locationId,
      'visitCount': instance.visitCount,
      'eventCount': instance.eventCount,
      'frequentVisitors': instance.frequentVisitors,
      'significantEvents': instance.significantEvents,
      'lastVisit': instance.lastVisit?.toIso8601String(),
      'characterVisitCounts': instance.characterVisitCounts,
      'customStats': instance.customStats,
    };
