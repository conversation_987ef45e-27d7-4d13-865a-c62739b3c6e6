import 'package:flutter/material.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

import '../../domain/entities/project.dart';

/// 项目卡片组件
/// 显示项目的基本信息和操作按钮
class ProjectCard extends StatelessWidget {
  final Project project;
  final VoidCallback? onTap;
  final VoidCallback? onFavoriteToggle;
  final VoidCallback? onEdit;
  final VoidCallback? onArchive;
  final VoidCallback? onDelete;
  final VoidCallback? onDuplicate;

  const ProjectCard({
    super.key,
    required this.project,
    this.onTap,
    this.onFavoriteToggle,
    this.onEdit,
    this.onArchive,
    this.onDelete,
    this.onDuplicate,
  });

  @override
  Widget build(BuildContext context) {
    return fluent.Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: _getStatusColor().withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 头部：标题和收藏按钮
              Row(
                children: [
                  Expanded(
                    child: Text(
                      project.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  fluent.IconButton(
                    icon: fluent.Icon(
                      project.isFavorite 
                          ? fluent.FluentIcons.favorite_star_fill 
                          : fluent.FluentIcons.favorite_star,
                      color: project.isFavorite ? Colors.amber : null,
                    ),
                    onPressed: onFavoriteToggle,
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // 项目类型和状态
              Row(
                children: [
                  _buildTypeChip(),
                  const SizedBox(width: 8),
                  _buildStatusChip(),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // 描述
              if (project.description.isNotEmpty) ...[
                Text(
                  project.description,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
              ],
              
              // 进度条
              _buildProgressBar(),
              
              const SizedBox(height: 8),
              
              // 统计信息
              _buildStatistics(),
              
              const Spacer(),
              
              // 标签
              if (project.tags.isNotEmpty) ...[
                const SizedBox(height: 8),
                _buildTags(),
              ],
              
              const SizedBox(height: 8),
              
              // 操作按钮
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTypeChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getTypeColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getTypeColor().withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            project.type.icon,
            style: const TextStyle(fontSize: 10),
          ),
          const SizedBox(width: 4),
          Text(
            project.type.displayName,
            style: TextStyle(
              fontSize: 10,
              color: _getTypeColor(),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getStatusColor().withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        project.status.displayName,
        style: TextStyle(
          fontSize: 10,
          color: _getStatusColor(),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildProgressBar() {
    final progress = project.completionPercentage / 100;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '进度',
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey[600],
              ),
            ),
            Text(
              '${project.completionPercentage.toStringAsFixed(1)}%',
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(_getStatusColor()),
        ),
      ],
    );
  }

  Widget _buildStatistics() {
    return Row(
      children: [
        Expanded(
          child: _buildStatItem(
            '字数',
            _formatNumber(project.statistics.totalWordCount),
            fluent.FluentIcons.edit,
          ),
        ),
        Expanded(
          child: _buildStatItem(
            '章节',
            project.statistics.chapterCount.toString(),
            fluent.FluentIcons.document,
          ),
        ),
        Expanded(
          child: _buildStatItem(
            '天数',
            project.ageInDays.toString(),
            fluent.FluentIcons.calendar,
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        fluent.Icon(
          icon,
          size: 14,
          color: Colors.grey[600],
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildTags() {
    return Wrap(
      spacing: 4,
      runSpacing: 4,
      children: project.tags.take(3).map((tag) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            tag,
            style: TextStyle(
              fontSize: 9,
              color: Colors.grey[700],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: fluent.Button(
            onPressed: onTap,
            child: const Text('打开'),
          ),
        ),
        const SizedBox(width: 8),
        fluent.DropDownButton(
          leading: const fluent.Icon(fluent.FluentIcons.more),
          items: [
            fluent.MenuFlyoutItem(
              leading: const fluent.Icon(fluent.FluentIcons.edit),
              text: const Text('编辑'),
              onPressed: onEdit,
            ),
            fluent.MenuFlyoutItem(
              leading: const fluent.Icon(fluent.FluentIcons.copy),
              text: const Text('复制'),
              onPressed: onDuplicate,
            ),
            if (project.status != ProjectStatus.archived)
              fluent.MenuFlyoutItem(
                leading: const fluent.Icon(fluent.FluentIcons.archive),
                text: const Text('归档'),
                onPressed: onArchive,
              ),
            fluent.MenuFlyoutSeparator(),
            fluent.MenuFlyoutItem(
              leading: const fluent.Icon(
                fluent.FluentIcons.delete,
                color: Colors.red,
              ),
              text: const Text(
                '删除',
                style: TextStyle(color: Colors.red),
              ),
              onPressed: onDelete,
            ),
          ],
        ),
      ],
    );
  }

  Color _getTypeColor() {
    switch (project.type) {
      case ProjectType.novel:
        return Colors.blue;
      case ProjectType.shortStory:
        return Colors.green;
      case ProjectType.screenplay:
        return Colors.purple;
      case ProjectType.poetry:
        return Colors.pink;
      case ProjectType.essay:
        return Colors.orange;
      case ProjectType.academic:
        return Colors.indigo;
      case ProjectType.technical:
        return Colors.teal;
      case ProjectType.other:
        return Colors.grey;
    }
  }

  Color _getStatusColor() {
    if (project.colorTheme != null) {
      try {
        return Color(int.parse(project.colorTheme!.replaceFirst('#', '0xFF')));
      } catch (e) {
        // 如果颜色解析失败，使用默认颜色
      }
    }
    
    switch (project.status) {
      case ProjectStatus.active:
        return Colors.green;
      case ProjectStatus.paused:
        return Colors.orange;
      case ProjectStatus.completed:
        return Colors.blue;
      case ProjectStatus.archived:
        return Colors.grey;
      case ProjectStatus.deleted:
        return Colors.red;
    }
  }

  String _formatNumber(int number) {
    if (number >= 10000) {
      return '${(number / 10000).toStringAsFixed(1)}万';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}k';
    } else {
      return number.toString();
    }
  }
}