// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chapter.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ChapterImpl _$$ChapterImplFromJson(Map<String, dynamic> json) =>
    _$ChapterImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String? ?? '',
      parentId: json['parentId'] as String?,
      level: (json['level'] as num?)?.toInt() ?? 0,
      order: (json['order'] as num?)?.toInt() ?? 0,
      status:
          $enumDecodeNullable(_$ChapterStatusEnumMap, json['status']) ??
          ChapterStatus.draft,
      wordCount: (json['wordCount'] as num?)?.toInt() ?? 0,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
          const [],
      notes: json['notes'] as String? ?? '',
      isExpanded: json['isExpanded'] as bool? ?? true,
      children:
          (json['children'] as List<dynamic>?)
              ?.map((e) => Chapter.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$ChapterImplToJson(_$ChapterImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'content': instance.content,
      'parentId': instance.parentId,
      'level': instance.level,
      'order': instance.order,
      'status': _$ChapterStatusEnumMap[instance.status]!,
      'wordCount': instance.wordCount,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'tags': instance.tags,
      'notes': instance.notes,
      'isExpanded': instance.isExpanded,
      'children': instance.children,
    };

const _$ChapterStatusEnumMap = {
  ChapterStatus.draft: 'draft',
  ChapterStatus.inProgress: 'inProgress',
  ChapterStatus.completed: 'completed',
  ChapterStatus.published: 'published',
  ChapterStatus.archived: 'archived',
};
