^E:\PROJECT\BAMBOOFALL\BAMBOOFALL_APP\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"D:\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/project/bamboofall/bamboofall_app/windows -BE:/project/bamboofall/bamboofall_app/build/windows/x64 --check-stamp-file E:/project/bamboofall/bamboofall_app/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
