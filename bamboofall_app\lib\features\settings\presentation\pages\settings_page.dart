import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;
import '../../../../app/theme.dart';
import '../../domain/entities/app_settings.dart';
import '../providers/settings_providers.dart';
import '../widgets/theme_selector.dart';
import '../widgets/language_selector.dart';
import '../widgets/editor_settings_panel.dart';
import '../widgets/ui_settings_panel.dart';
import '../widgets/privacy_settings_panel.dart';
import '../widgets/backup_settings_panel.dart';
import '../widgets/shortcuts_settings_panel.dart';

/// 设置页面
class SettingsPage extends ConsumerStatefulWidget {
  const SettingsPage({super.key});

  @override
  ConsumerState<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends ConsumerState<SettingsPage> {
  int _selectedIndex = 0;

  final List<SettingsCategory> _categories = [
    SettingsCategory(
      title: '外观',
      icon: fluent.FluentIcons.color,
      description: '主题、语言和界面设置',
    ),
    SettingsCategory(
      title: '编辑器',
      icon: fluent.FluentIcons.edit,
      description: '编辑器行为和显示设置',
    ),
    SettingsCategory(
      title: '界面',
      icon: fluent.FluentIcons.view_dashboard,
      description: '界面布局和交互设置',
    ),
    SettingsCategory(
      title: '隐私',
      icon: fluent.FluentIcons.shield,
      description: '数据收集和隐私保护',
    ),
    SettingsCategory(
      title: '备份',
      icon: fluent.FluentIcons.save,
      description: '数据备份和恢复设置',
    ),
    SettingsCategory(
      title: '快捷键',
      icon: fluent.FluentIcons.settings,
      description: '自定义键盘快捷键',
    ),
    SettingsCategory(
      title: '关于',
      icon: fluent.FluentIcons.info,
      description: '应用信息和帮助',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final settings = ref.watch(settingsNotifierProvider);

    return fluent.ScaffoldPage(
      header: fluent.PageHeader(
        title: const Text('设置'),
        commandBar: fluent.CommandBar(
          primaryItems: [
            fluent.CommandBarButton(
              icon: const Icon(fluent.FluentIcons.reset),
              label: const Text('重置'),
              onPressed: _showResetDialog,
            ),
            fluent.CommandBarButton(
              icon: const Icon(fluent.FluentIcons.save),
              label: const Text('保存'),
              onPressed: _saveSettings,
            ),
          ],
        ),
      ),
      content: _buildContent(settings),
    );
  }

  /// 构建内容
  Widget _buildContent(AppSettings settings) {
    return Row(
      children: [
        // 左侧导航
        SizedBox(
          width: 250,
          child: _buildNavigation(),
        ),
        const fluent.Divider(direction: Axis.vertical),
        // 右侧内容
        Expanded(
          child: _buildSettingsPanel(settings),
        ),
      ],
    );
  }

  /// 构建导航
  Widget _buildNavigation() {
    return fluent.NavigationView(
      pane: fluent.NavigationPane(
        selected: _selectedIndex,
        onChanged: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        displayMode: fluent.PaneDisplayMode.open,
        items: _categories.asMap().entries.map<fluent.NavigationPaneItem>((entry) {
          final index = entry.key;
          final category = entry.value;

          return fluent.PaneItem(
            key: ValueKey(index),
            icon: Icon(category.icon),
            title: Text(category.title),
            body: const SizedBox.shrink(),
          );
        }).toList(),
      ),
    );
  }

  /// 构建设置面板
  Widget _buildSettingsPanel(AppSettings settings) {
    final category = _categories[_selectedIndex];
    
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题和描述
          Row(
            children: [
              Icon(category.icon, size: 24),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    category.title,
                    style: fluent.FluentTheme.of(context).typography.title,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    category.description,
                    style: fluent.FluentTheme.of(context).typography.caption,
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 24),
          // 设置内容
          Expanded(
            child: _buildCategoryContent(settings),
          ),
        ],
      ),
    );
  }

  /// 构建分类内容
  Widget _buildCategoryContent(AppSettings settings) {
    switch (_selectedIndex) {
      case 0: // 外观
        return _buildAppearanceSettings(settings);
      case 1: // 编辑器
        return EditorSettingsPanel(settings: settings.editorSettings);
      case 2: // 界面
        return UISettingsPanel(settings: settings.uiSettings);
      case 3: // 隐私
        return PrivacySettingsPanel(settings: settings.privacySettings);
      case 4: // 备份
        return BackupSettingsPanel(settings: settings.backupSettings);
      case 5: // 快捷键
        return ShortcutsSettingsPanel(shortcuts: settings.shortcuts);
      case 6: // 关于
        return _buildAboutPanel();
      default:
        return const Center(child: Text('未知设置分类'));
    }
  }

  /// 构建外观设置
  Widget _buildAppearanceSettings(AppSettings settings) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 主题设置
          fluent.Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '主题',
                    style: fluent.FluentTheme.of(context).typography.subtitle,
                  ),
                  const SizedBox(height: 12),
                  ThemeSelector(
                    currentTheme: settings.themeMode,
                    onThemeChanged: (theme) {
                      ref.read(settingsNotifierProvider.notifier)
                          .updateThemeMode(theme);
                    },
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // 语言设置
          fluent.Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '语言',
                    style: fluent.FluentTheme.of(context).typography.subtitle,
                  ),
                  const SizedBox(height: 12),
                  LanguageSelector(
                    currentLanguage: settings.language,
                    onLanguageChanged: (language) {
                      ref.read(settingsNotifierProvider.notifier)
                          .updateLanguage(language);
                    },
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // 字体设置
          fluent.Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '字体',
                    style: fluent.FluentTheme.of(context).typography.subtitle,
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      const Text('字体大小'),
                      const SizedBox(width: 16),
                      Expanded(
                        child: fluent.Slider(
                          value: settings.fontSize,
                          min: 10,
                          max: 24,
                          divisions: 14,
                          label: '${settings.fontSize.toInt()}px',
                          onChanged: (value) {
                            ref.read(settingsNotifierProvider.notifier)
                                .updateFontSize(value);
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Text('${settings.fontSize.toInt()}px'),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // 自动保存设置
          fluent.Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '自动保存',
                    style: fluent.FluentTheme.of(context).typography.subtitle,
                  ),
                  const SizedBox(height: 12),
                  fluent.ToggleSwitch(
                    checked: settings.enableAutoSave,
                    onChanged: (value) {
                      ref.read(settingsNotifierProvider.notifier)
                          .updateAutoSave(value);
                    },
                    content: const Text('启用自动保存'),
                  ),
                  if (settings.enableAutoSave) ...[
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        const Text('保存间隔'),
                        const SizedBox(width: 16),
                        Expanded(
                          child: fluent.Slider(
                            value: settings.autoSaveInterval.toDouble(),
                            min: 10,
                            max: 300,
                            divisions: 29,
                            label: '${settings.autoSaveInterval}秒',
                            onChanged: (value) {
                              ref.read(settingsNotifierProvider.notifier)
                                  .updateAutoSaveInterval(value.toInt());
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Text('${settings.autoSaveInterval}秒'),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建关于面板
  Widget _buildAboutPanel() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 应用信息
          fluent.Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 64,
                        height: 64,
                        decoration: BoxDecoration(
                          color: AppColors.writingPrimary,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          fluent.FluentIcons.edit,
                          color: Colors.white,
                          size: 32,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '笔落 - BambooFall',
                            style: fluent.FluentTheme.of(context).typography.title,
                          ),
                          const SizedBox(height: 4),
                          const Text('版本 1.0.0'),
                          const SizedBox(height: 4),
                          const Text('AI驱动的小说创作工具'),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    '笔落是一款专为小说创作者设计的AI辅助写作工具，'
                    '集成了多种AI模型，提供智能写作建议、角色管理、'
                    '情节规划等功能，让创作更加高效和有趣。',
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // 系统信息
          fluent.Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '系统信息',
                    style: fluent.FluentTheme.of(context).typography.subtitle,
                  ),
                  const SizedBox(height: 12),
                  _buildInfoRow('操作系统', 'Windows 11'),
                  _buildInfoRow('Flutter版本', '3.24.0'),
                  _buildInfoRow('Dart版本', '3.5.0'),
                  _buildInfoRow('构建日期', '2024-01-15'),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // 链接
          fluent.Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '链接',
                    style: fluent.FluentTheme.of(context).typography.subtitle,
                  ),
                  const SizedBox(height: 12),
                  fluent.HyperlinkButton(
                    onPressed: () {},
                    child: const Text('官方网站'),
                  ),
                  fluent.HyperlinkButton(
                    onPressed: () {},
                    child: const Text('用户手册'),
                  ),
                  fluent.HyperlinkButton(
                    onPressed: () {},
                    child: const Text('反馈问题'),
                  ),
                  fluent.HyperlinkButton(
                    onPressed: () {},
                    child: const Text('隐私政策'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(color: Colors.grey),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  /// 显示重置对话框
  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: const Text('重置设置'),
        content: const Text('确定要将所有设置重置为默认值吗？此操作无法撤销。'),
        actions: [
          fluent.Button(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          fluent.FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              _resetSettings();
            },
            child: const Text('重置'),
          ),
        ],
      ),
    );
  }

  /// 重置设置
  void _resetSettings() async {
    try {
      await ref.read(settingsNotifierProvider.notifier).resetToDefaults();
      
      if (mounted) {
        fluent.displayInfoBar(
          context,
          builder: (context, close) => fluent.InfoBar(
            title: const Text('设置已重置'),
            content: const Text('所有设置已恢复为默认值'),
            severity: fluent.InfoBarSeverity.success,
            action: fluent.IconButton(
              icon: const Icon(fluent.FluentIcons.clear),
              onPressed: close,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        fluent.displayInfoBar(
          context,
          builder: (context, close) => fluent.InfoBar(
            title: const Text('重置失败'),
            content: Text(e.toString()),
            severity: fluent.InfoBarSeverity.error,
            action: fluent.IconButton(
              icon: const Icon(fluent.FluentIcons.clear),
              onPressed: close,
            ),
          ),
        );
      }
    }
  }

  /// 保存设置
  void _saveSettings() async {
    try {
      await ref.read(settingsNotifierProvider.notifier).saveSettings();
      
      if (mounted) {
        fluent.displayInfoBar(
          context,
          builder: (context, close) => fluent.InfoBar(
            title: const Text('设置已保存'),
            severity: fluent.InfoBarSeverity.success,
            action: fluent.IconButton(
              icon: const Icon(fluent.FluentIcons.clear),
              onPressed: close,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        fluent.displayInfoBar(
          context,
          builder: (context, close) => fluent.InfoBar(
            title: const Text('保存失败'),
            content: Text(e.toString()),
            severity: fluent.InfoBarSeverity.error,
            action: fluent.IconButton(
              icon: const Icon(fluent.FluentIcons.clear),
              onPressed: close,
            ),
          ),
        );
      }
    }
  }
}

/// 设置分类
class SettingsCategory {
  final String title;
  final IconData icon;
  final String description;

  const SettingsCategory({
    required this.title,
    required this.icon,
    required this.description,
  });
}