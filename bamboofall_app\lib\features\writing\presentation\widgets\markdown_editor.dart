import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:super_editor/super_editor.dart';

import 'editor_toolbar.dart';

/// Markdown编辑器状态
class MarkdownEditorState {
  final MutableDocument document;
  final Editor editor;
  final MutableDocumentComposer composer;
  final bool isPreviewMode;
  final String rawMarkdown;

  const MarkdownEditorState({
    required this.document,
    required this.editor,
    required this.composer,
    this.isPreviewMode = false,
    this.rawMarkdown = '',
  });

  MarkdownEditorState copyWith({
    MutableDocument? document,
    Editor? editor,
    MutableDocumentComposer? composer,
    bool? isPreviewMode,
    String? rawMarkdown,
  }) {
    return MarkdownEditorState(
      document: document ?? this.document,
      editor: editor ?? this.editor,
      composer: composer ?? this.composer,
      isPreviewMode: isPreviewMode ?? this.isPreviewMode,
      rawMarkdown: rawMarkdown ?? this.rawMarkdown,
    );
  }
}

/// Markdown编辑器状态管理
final markdownEditorProvider = StateNotifierProvider<MarkdownEditorNotifier, MarkdownEditorState>((ref) {
  return MarkdownEditorNotifier();
});

class MarkdownEditorNotifier extends StateNotifier<MarkdownEditorState> {
  MarkdownEditorNotifier() : super(_createInitialState());

  static MarkdownEditorState _createInitialState() {
    final document = MutableDocument(
      nodes: [
        ParagraphNode(
          id: Editor.createNodeId(),
          text: AttributedText('开始写作...'),
        ),
      ],
    );
    
    final composer = MutableDocumentComposer();
    final editor = createDefaultDocumentEditor(document: document, composer: composer);
    
    return MarkdownEditorState(
      document: document,
      editor: editor,
      composer: composer,
    );
  }

  void togglePreviewMode() {
    if (!state.isPreviewMode) {
      // 切换到预览模式时，将文档转换为Markdown
      final markdown = _documentToMarkdown(state.document);
      state = state.copyWith(
        isPreviewMode: true,
        rawMarkdown: markdown,
      );
    } else {
      // 切换到编辑模式时，将Markdown转换回文档
      final document = _markdownToDocument(state.rawMarkdown);
      final composer = MutableDocumentComposer();
      final editor = createDefaultDocumentEditor(document: document, composer: composer);
      state = state.copyWith(
        document: document,
        editor: editor,
        composer: composer,
        isPreviewMode: false,
      );
    }
  }

  void updateContent(String markdown) {
    if (state.isPreviewMode) {
      state = state.copyWith(rawMarkdown: markdown);
    } else {
      final document = _markdownToDocument(markdown);
      final composer = MutableDocumentComposer();
      final editor = createDefaultDocumentEditor(document: document, composer: composer);
      state = state.copyWith(
        document: document,
        editor: editor,
        composer: composer,
      );
    }
  }

  void insertText(String text) {
    if (!state.isPreviewMode) {
      state.editor.execute([
        InsertTextRequest(
          documentPosition: state.composer.selection?.extent ?? DocumentPosition(
            nodeId: state.document.first.id,
            nodePosition: const TextNodePosition(offset: 0),
          ),
          textToInsert: text,
          attributions: {},
        ),
      ]);
    }
  }

  void applyFormatting(Attribution attribution) {
    if (!state.isPreviewMode && state.composer.selection != null) {
      state.editor.execute([
        ToggleTextAttributionsRequest(
          documentRange: state.composer.selection!,
          attributions: {attribution},
        ),
      ]);
    }
  }

  void insertHeader(int level) {
    if (!state.isPreviewMode && state.composer.selection != null) {
      final nodeId = state.composer.selection!.extent.nodeId;
      final node = state.document.getNodeById(nodeId);
      if (node is ParagraphNode) {
        state.editor.execute([
          ReplaceNodeRequest(
            existingNodeId: nodeId,
            newNode: ParagraphNode(
              id: nodeId,
              text: node.text,
              metadata: {
                'blockType': level == 1 ? header1Attribution : header2Attribution,
              },
            ),
          ),
        ]);
      }
    }
  }

  void insertList(ListItemType type) {
    if (!state.isPreviewMode && state.composer.selection != null) {
      final nodeId = state.composer.selection!.extent.nodeId;
      final node = state.document.getNodeById(nodeId);
      if (node is ParagraphNode) {
        state.editor.execute([
          ReplaceNodeRequest(
            existingNodeId: nodeId,
            newNode: ListItemNode(
              id: nodeId,
              itemType: type,
              text: node.text,
            ),
          ),
        ]);
      }
    }
  }

  String _documentToMarkdown(Document document) {
    final buffer = StringBuffer();
    
    for (final node in document) {
      if (node is TextNode) {
        if (node is ParagraphNode) {
          final blockType = node.getMetadataValue('blockType');
          if (blockType == header1Attribution) {
              buffer.writeln('# ${node.text.toPlainText()}');
            } else if (blockType == header2Attribution) {
              buffer.writeln('## ${node.text.toPlainText()}');
            } else {
              buffer.writeln(node.text.toPlainText());
            }
        } else if (node is ListItemNode) {
          final prefix = node.type == ListItemType.ordered ? '1. ' : '- ';
          buffer.writeln('$prefix${node.text.toPlainText()}');
        }
      }
      buffer.writeln();
    }
    
    return buffer.toString().trim();
  }

  MutableDocument _markdownToDocument(String markdown) {
    final lines = markdown.split('\n');
    final nodes = <DocumentNode>[];
    
    for (final line in lines) {
      if (line.trim().isEmpty) continue;
      
      DocumentNode node;
      
      if (line.startsWith('# ')) {
        final text = line.substring(2);
        node = ParagraphNode(
          id: Editor.createNodeId(),
          text: AttributedText(text),
          metadata: {
            'blockType': header1Attribution,
          },
        );
      } else if (line.startsWith('## ')) {
        final text = line.substring(3);
        node = ParagraphNode(
          id: Editor.createNodeId(),
          text: AttributedText(text),
          metadata: {
            'blockType': header2Attribution,
          },
        );
      } else if (line.startsWith('- ') || line.startsWith('* ')) {
        final text = line.substring(2);
        node = ListItemNode(
          id: Editor.createNodeId(),
          itemType: ListItemType.unordered,
          text: AttributedText(text),
        );
      } else if (RegExp(r'^\d+\. ').hasMatch(line)) {
        final text = line.substring(line.indexOf(' ') + 1);
        node = ListItemNode(
          id: Editor.createNodeId(),
          itemType: ListItemType.ordered,
          text: AttributedText(text),
        );
      } else {
        node = ParagraphNode(
          id: Editor.createNodeId(),
          text: AttributedText(line),
        );
      }
      
      nodes.add(node);
    }
    
    if (nodes.isEmpty) {
      nodes.add(ParagraphNode(
        id: Editor.createNodeId(),
        text: AttributedText(''),
      ));
    }
    
    return MutableDocument(nodes: nodes);
  }
}

/// Markdown编辑器组件
/// 集成super_editor实现富文本编辑功能
class MarkdownEditor extends ConsumerStatefulWidget {
  const MarkdownEditor({super.key});

  @override
  ConsumerState<MarkdownEditor> createState() => _MarkdownEditorState();
}

class _MarkdownEditorState extends ConsumerState<MarkdownEditor> {
  late FocusNode _editorFocusNode;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _editorFocusNode = FocusNode();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _editorFocusNode.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final editorState = ref.watch(markdownEditorProvider);
    final editorNotifier = ref.read(markdownEditorProvider.notifier);

    return Column(
      children: [
        // 编辑器工具栏
        EditorToolbar(
          onBoldPressed: () => editorNotifier.applyFormatting(boldAttribution),
          onItalicPressed: () => editorNotifier.applyFormatting(italicsAttribution),
          onUnderlinePressed: () => editorNotifier.applyFormatting(underlineAttribution),
          onHeader1Pressed: () => editorNotifier.insertHeader(1),
          onHeader2Pressed: () => editorNotifier.insertHeader(2),
          onBulletListPressed: () => editorNotifier.insertList(ListItemType.unordered),
          onNumberedListPressed: () => editorNotifier.insertList(ListItemType.ordered),
          onPreviewToggled: editorNotifier.togglePreviewMode,
          isPreviewMode: editorState.isPreviewMode,
        ),
        
        // 编辑器内容区域
        Expanded(
          child: editorState.isPreviewMode
              ? _buildPreviewMode(editorState.rawMarkdown)
              : _buildEditMode(editorState),
        ),
      ],
    );
  }

  Widget _buildEditMode(MarkdownEditorState editorState) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SuperEditor(
        editor: editorState.editor,
        focusNode: _editorFocusNode,
        scrollController: _scrollController,
        stylesheet: _createEditorStylesheet(),
      ),
    );
  }

  Widget _buildPreviewMode(String markdown) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        controller: _scrollController,
        child: Container(
          width: double.infinity,
          alignment: Alignment.topLeft,
          child: Text(
            markdown.isEmpty ? '预览模式\n\n切换回编辑模式以继续编辑' : markdown,
            style: const TextStyle(
              fontSize: 16,
              height: 1.6,
              fontFamily: 'Consolas',
            ),
          ),
        ),
      ),
    );
  }

  Stylesheet _createEditorStylesheet() {
    return defaultStylesheet.copyWith(
      addRulesAfter: [
        StyleRule(
          BlockSelector.all,
          (doc, docNode) {
            return {
              Styles.padding: const CascadingPadding.symmetric(vertical: 4),
            };
          },
        ),
        StyleRule(
          const BlockSelector("paragraph"),
          (doc, docNode) {
            return {
              Styles.textStyle: const TextStyle(
                fontSize: 16,
                height: 1.6,
                fontFamily: 'Consolas',
              ),
            };
          },
        ),
        StyleRule(
          const BlockSelector("header1"),
          (doc, docNode) {
            return {
              Styles.textStyle: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                height: 1.4,
              ),
              Styles.padding: const CascadingPadding.only(bottom: 8),
            };
          },
        ),
        StyleRule(
          const BlockSelector("header2"),
          (doc, docNode) {
            return {
              Styles.textStyle: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                height: 1.4,
              ),
              Styles.padding: const CascadingPadding.only(bottom: 6),
            };
          },
        ),
        StyleRule(
          const BlockSelector("listItem"),
          (doc, docNode) {
            return {
              Styles.textStyle: const TextStyle(
                fontSize: 16,
                height: 1.6,
              ),
              Styles.padding: const CascadingPadding.symmetric(vertical: 2),
            };
          },
        ),
      ],
    );
  }
}