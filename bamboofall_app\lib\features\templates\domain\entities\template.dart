// 模板相关实体导出文件
import 'prompt_template.dart';

export 'prompt_template.dart';

// 模板统计信息
class TemplateStatistics {
  final int totalTemplates;
  final int userTemplates;
  final int systemTemplates;
  final int totalUsage;
  final Map<TemplateCategory, int> categoryCount;
  final List<PromptTemplate> mostUsedTemplates;
  final List<PromptTemplate> recentTemplates;

  const TemplateStatistics({
    required this.totalTemplates,
    required this.userTemplates,
    required this.systemTemplates,
    required this.totalUsage,
    required this.categoryCount,
    required this.mostUsedTemplates,
    required this.recentTemplates,
  });

  TemplateStatistics copyWith({
    int? totalTemplates,
    int? userTemplates,
    int? systemTemplates,
    int? totalUsage,
    Map<TemplateCategory, int>? categoryCount,
    List<PromptTemplate>? mostUsedTemplates,
    List<PromptTemplate>? recentTemplates,
  }) {
    return TemplateStatistics(
      totalTemplates: totalTemplates ?? this.totalTemplates,
      userTemplates: userTemplates ?? this.userTemplates,
      systemTemplates: systemTemplates ?? this.systemTemplates,
      totalUsage: totalUsage ?? this.totalUsage,
      categoryCount: categoryCount ?? this.categoryCount,
      mostUsedTemplates: mostUsedTemplates ?? this.mostUsedTemplates,
      recentTemplates: recentTemplates ?? this.recentTemplates,
    );
  }
}

// 模板验证结果
class TemplateValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;
  final List<String> suggestions;

  const TemplateValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
    required this.suggestions,
  });

  TemplateValidationResult copyWith({
    bool? isValid,
    List<String>? errors,
    List<String>? warnings,
    List<String>? suggestions,
  }) {
    return TemplateValidationResult(
      isValid: isValid ?? this.isValid,
      errors: errors ?? this.errors,
      warnings: warnings ?? this.warnings,
      suggestions: suggestions ?? this.suggestions,
    );
  }
}
