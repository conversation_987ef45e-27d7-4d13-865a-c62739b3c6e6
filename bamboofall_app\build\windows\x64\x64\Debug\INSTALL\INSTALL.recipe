﻿<?xml version="1.0" encoding="utf-8"?>
<Project>
  <ProjectOutputs>
    <ProjectOutput>
      <FullPath>E:\project\bamboofall\bamboofall_app\build\windows\x64\x64\Debug\ZERO_CHECK</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\project\bamboofall\bamboofall_app\build\windows\x64\flutter\x64\Debug\flutter_assemble</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\project\bamboofall\bamboofall_app\build\windows\x64\plugins\flutter_secure_storage_windows\Debug\flutter_secure_storage_windows_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\project\bamboofall\bamboofall_app\build\windows\x64\plugins\screen_retriever_windows\Debug\screen_retriever_windows_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\project\bamboofall\bamboofall_app\build\windows\x64\plugins\url_launcher_windows\Debug\url_launcher_windows_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\project\bamboofall\bamboofall_app\build\windows\x64\plugins\window_manager\Debug\window_manager_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\project\bamboofall\bamboofall_app\build\windows\x64\runner\Debug\bamboofall_app.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\project\bamboofall\bamboofall_app\build\windows\x64\x64\Debug\ALL_BUILD</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\project\bamboofall\bamboofall_app\build\windows\x64\x64\Debug\INSTALL</FullPath>
    </ProjectOutput>
  </ProjectOutputs>
  <ContentFiles />
  <SatelliteDlls />
  <NonRecipeFileRefs />
</Project>