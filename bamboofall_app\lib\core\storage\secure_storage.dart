import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:logger/logger.dart';
import 'dart:convert';

/// AI配置数据结构
class AIConfig {
  final String provider;
  final String apiKey;
  final String? baseUrl;
  final Map<String, dynamic> parameters;
  final bool isActive;
  
  AIConfig({
    required this.provider,
    required this.apiKey,
    this.baseUrl,
    this.parameters = const {},
    this.isActive = true,
  });
  
  Map<String, dynamic> toJson() => {
    'provider': provider,
    'apiKey': apiKey,
    'baseUrl': baseUrl,
    'parameters': parameters,
    'isActive': isActive,
  };
  
  factory AIConfig.fromJson(Map<String, dynamic> json) => AIConfig(
    provider: json['provider'],
    apiKey: json['apiKey'],
    baseUrl: json['baseUrl'],
    parameters: json['parameters'] ?? {},
    isActive: json['isActive'] ?? true,
  );
}

/// 安全存储管理类
/// 处理敏感数据如API密钥、用户凭证等的安全存储
class SecureStorageManager {
  static SecureStorageManager? _instance;
  static SecureStorageManager get instance => _instance ??= SecureStorageManager._();
  
  SecureStorageManager._();
  
  final Logger _logger = Logger();
  late FlutterSecureStorage _storage;
  bool _initialized = false;
  
  /// 存储键名常量
  static const String _aiConfigKey = 'ai_config';
  static const String _userPreferencesKey = 'user_preferences';
  static const String _projectSecretsKey = 'project_secrets';
  
  /// 初始化安全存储
  Future<void> initialize() async {
    if (_initialized) return;
    
    try {
      _storage = const FlutterSecureStorage(
        aOptions: AndroidOptions(
          encryptedSharedPreferences: true,
        ),
        iOptions: IOSOptions(
          accessibility: KeychainAccessibility.first_unlock_this_device,
        ),
        wOptions: WindowsOptions(
          useBackwardCompatibility: false,
        ),
      );
      
      _initialized = true;
      _logger.i('Secure storage initialized successfully');
    } catch (e) {
      _logger.e('Failed to initialize secure storage: $e');
      rethrow;
    }
  }
  
  /// 确保安全存储已初始化
  void _ensureInitialized() {
    if (!_initialized) {
      throw StateError('Secure storage not initialized. Call initialize() first.');
    }
  }
  
  // ==================== AI配置管理 ====================
  
  /// 保存AI配置
  Future<void> saveAIConfig(List<AIConfig> configs) async {
    _ensureInitialized();
    
    try {
      final configsJson = configs.map((config) => config.toJson()).toList();
      final jsonString = jsonEncode(configsJson);
      
      await _storage.write(key: _aiConfigKey, value: jsonString);
      _logger.d('Saved ${configs.length} AI configurations');
    } catch (e) {
      _logger.e('Failed to save AI config: $e');
      rethrow;
    }
  }
  
  /// 读取AI配置
  Future<List<AIConfig>> getAIConfigs() async {
    _ensureInitialized();
    
    try {
      final jsonString = await _storage.read(key: _aiConfigKey);
      
      if (jsonString == null || jsonString.isEmpty) {
        return [];
      }
      
      final configsJson = jsonDecode(jsonString) as List;
      return configsJson
          .map((json) => AIConfig.fromJson(json))
          .toList();
    } catch (e) {
      _logger.e('Failed to get AI configs: $e');
      return [];
    }
  }
  
  /// 添加AI配置
  Future<void> addAIConfig(AIConfig config) async {
    final configs = await getAIConfigs();
    
    // 检查是否已存在相同的provider
    final existingIndex = configs.indexWhere((c) => c.provider == config.provider);
    
    if (existingIndex >= 0) {
      configs[existingIndex] = config;
      _logger.d('Updated AI config for provider: ${config.provider}');
    } else {
      configs.add(config);
      _logger.d('Added new AI config for provider: ${config.provider}');
    }
    
    await saveAIConfig(configs);
  }
  
  /// 删除AI配置
  Future<void> removeAIConfig(String provider) async {
    final configs = await getAIConfigs();
    configs.removeWhere((config) => config.provider == provider);
    await saveAIConfig(configs);
    _logger.d('Removed AI config for provider: $provider');
  }
  
  /// 获取特定AI配置
  Future<AIConfig?> getAIConfig(String provider) async {
    final configs = await getAIConfigs();
    
    try {
      return configs.firstWhere((config) => config.provider == provider);
    } catch (e) {
      return null;
    }
  }
  
  /// 获取活跃的AI配置
  Future<List<AIConfig>> getActiveAIConfigs() async {
    final configs = await getAIConfigs();
    return configs.where((config) => config.isActive).toList();
  }
  
  // ==================== 用户偏好设置 ====================
  
  /// 保存用户偏好设置
  Future<void> saveUserPreferences(Map<String, dynamic> preferences) async {
    _ensureInitialized();
    
    try {
      final jsonString = jsonEncode(preferences);
      await _storage.write(key: _userPreferencesKey, value: jsonString);
      _logger.d('Saved user preferences');
    } catch (e) {
      _logger.e('Failed to save user preferences: $e');
      rethrow;
    }
  }
  
  /// 读取用户偏好设置
  Future<Map<String, dynamic>> getUserPreferences() async {
    _ensureInitialized();
    
    try {
      final jsonString = await _storage.read(key: _userPreferencesKey);
      
      if (jsonString == null || jsonString.isEmpty) {
        return {};
      }
      
      return jsonDecode(jsonString);
    } catch (e) {
      _logger.e('Failed to get user preferences: $e');
      return {};
    }
  }
  
  /// 更新用户偏好设置
  Future<void> updateUserPreference(String key, dynamic value) async {
    final preferences = await getUserPreferences();
    preferences[key] = value;
    await saveUserPreferences(preferences);
  }
  
  /// 获取用户偏好设置值
  Future<T?> getUserPreference<T>(String key) async {
    final preferences = await getUserPreferences();
    return preferences[key] as T?;
  }
  
  // ==================== 项目密钥管理 ====================
  
  /// 保存项目密钥
  Future<void> saveProjectSecret(String projectName, String key, String value) async {
    _ensureInitialized();
    
    try {
      final secretKey = '${_projectSecretsKey}_${projectName}_$key';
      await _storage.write(key: secretKey, value: value);
      _logger.d('Saved project secret for $projectName: $key');
    } catch (e) {
      _logger.e('Failed to save project secret: $e');
      rethrow;
    }
  }
  
  /// 读取项目密钥
  Future<String?> getProjectSecret(String projectName, String key) async {
    _ensureInitialized();
    
    try {
      final secretKey = '${_projectSecretsKey}_${projectName}_$key';
      return await _storage.read(key: secretKey);
    } catch (e) {
      _logger.e('Failed to get project secret: $e');
      return null;
    }
  }
  
  /// 删除项目密钥
  Future<void> deleteProjectSecret(String projectName, String key) async {
    _ensureInitialized();
    
    try {
      final secretKey = '${_projectSecretsKey}_${projectName}_$key';
      await _storage.delete(key: secretKey);
      _logger.d('Deleted project secret for $projectName: $key');
    } catch (e) {
      _logger.e('Failed to delete project secret: $e');
      rethrow;
    }
  }
  
  /// 删除项目所有密钥
  Future<void> deleteAllProjectSecrets(String projectName) async {
    _ensureInitialized();
    
    try {
      final allKeys = await _storage.readAll();
      final projectSecretPrefix = '${_projectSecretsKey}_$projectName';
      
      for (final key in allKeys.keys) {
        if (key.startsWith(projectSecretPrefix)) {
          await _storage.delete(key: key);
        }
      }
      
      _logger.d('Deleted all project secrets for $projectName');
    } catch (e) {
      _logger.e('Failed to delete all project secrets: $e');
      rethrow;
    }
  }
  
  // ==================== 通用存储方法 ====================
  
  /// 保存安全数据
  Future<void> saveSecureData(String key, String value) async {
    _ensureInitialized();
    
    try {
      await _storage.write(key: key, value: value);
      _logger.d('Saved secure data: $key');
    } catch (e) {
      _logger.e('Failed to save secure data: $e');
      rethrow;
    }
  }
  
  /// 读取安全数据
  Future<String?> getSecureData(String key) async {
    _ensureInitialized();
    
    try {
      return await _storage.read(key: key);
    } catch (e) {
      _logger.e('Failed to get secure data: $e');
      return null;
    }
  }
  
  /// 删除安全数据
  Future<void> deleteSecureData(String key) async {
    _ensureInitialized();
    
    try {
      await _storage.delete(key: key);
      _logger.d('Deleted secure data: $key');
    } catch (e) {
      _logger.e('Failed to delete secure data: $e');
      rethrow;
    }
  }
  
  /// 清空所有安全数据
  Future<void> clearAllSecureData() async {
    _ensureInitialized();
    
    try {
      await _storage.deleteAll();
      _logger.w('Cleared all secure data');
    } catch (e) {
      _logger.e('Failed to clear all secure data: $e');
      rethrow;
    }
  }
  
  /// 获取所有存储的键
  Future<List<String>> getAllKeys() async {
    _ensureInitialized();
    
    try {
      final allData = await _storage.readAll();
      return allData.keys.toList();
    } catch (e) {
      _logger.e('Failed to get all keys: $e');
      return [];
    }
  }
  
  /// 检查键是否存在
  Future<bool> containsKey(String key) async {
    _ensureInitialized();
    
    try {
      final value = await _storage.read(key: key);
      return value != null;
    } catch (e) {
      _logger.e('Failed to check key existence: $e');
      return false;
    }
  }
  
  // ==================== 工具方法 ====================
  
  /// 获取存储统计信息
  Future<Map<String, dynamic>> getStorageStats() async {
    _ensureInitialized();
    
    try {
      final allKeys = await getAllKeys();
      final aiConfigs = await getAIConfigs();
      final userPreferences = await getUserPreferences();
      
      return {
        'totalKeys': allKeys.length,
        'aiConfigsCount': aiConfigs.length,
        'userPreferencesCount': userPreferences.length,
        'hasUserPreferences': userPreferences.isNotEmpty,
      };
    } catch (e) {
      _logger.e('Failed to get storage stats: $e');
      return {};
    }
  }
  
  /// 导出配置（不包含敏感信息）
  Future<Map<String, dynamic>> exportConfig() async {
    try {
      final aiConfigs = await getAIConfigs();
      final userPreferences = await getUserPreferences();
      
      // 移除敏感信息
      final sanitizedConfigs = aiConfigs.map((config) => {
        'provider': config.provider,
        'baseUrl': config.baseUrl,
        'parameters': config.parameters,
        'isActive': config.isActive,
        'hasApiKey': config.apiKey.isNotEmpty,
      }).toList();
      
      return {
        'aiConfigs': sanitizedConfigs,
        'userPreferences': userPreferences,
        'exportedAt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      _logger.e('Failed to export config: $e');
      return {};
    }
  }
}