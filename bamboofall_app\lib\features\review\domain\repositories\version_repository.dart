import '../entities/chapter_version.dart';
import '../entities/version_diff.dart';

/// 版本仓库接口
/// 定义版本数据访问的抽象方法
abstract class VersionRepository {
  /// 获取章节的所有版本
  Future<List<ChapterVersion>> getChapterVersions(String chapterId);

  /// 根据ID获取版本
  Future<ChapterVersion?> getVersionById(String versionId);

  /// 获取章节的最新版本
  Future<ChapterVersion?> getLatestVersion(String chapterId);

  /// 获取章节的主版本
  Future<ChapterVersion?> getMainVersion(String chapterId);

  /// 创建新版本
  Future<ChapterVersion> createVersion(ChapterVersion version);

  /// 更新版本
  Future<ChapterVersion> updateVersion(ChapterVersion version);

  /// 删除版本
  Future<void> deleteVersion(String versionId);

  /// 批量创建版本
  Future<List<ChapterVersion>> createVersions(List<ChapterVersion> versions);

  /// 根据状态获取版本
  Future<List<ChapterVersion>> getVersionsByStatus(VersionStatus status);

  /// 根据创建者获取版本
  Future<List<ChapterVersion>> getVersionsByCreator(String createdBy);

  /// 搜索版本
  Future<List<ChapterVersion>> searchVersions(String query);

  /// 获取版本数量
  Future<int> getVersionCount(String chapterId);

  /// 获取自动保存版本
  Future<List<ChapterVersion>> getAutoSaveVersions(String chapterId);

  /// 清理旧的自动保存版本
  Future<void> cleanupAutoSaveVersions(String chapterId, {int keepCount = 10});

  /// 获取版本差异
  Future<VersionDiff> getVersionDiff(String sourceVersionId, String targetVersionId);

  /// 保存版本差异
  Future<VersionDiff> saveVersionDiff(VersionDiff diff);

  /// 获取版本的所有差异
  Future<List<VersionDiff>> getVersionDiffs(String versionId);

  /// 清空章节的所有版本
  Future<void> clearChapterVersions(String chapterId);

  /// 清空所有版本
  Future<void> clearAllVersions();
}