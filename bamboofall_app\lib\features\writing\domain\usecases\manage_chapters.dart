import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../entities/chapter.dart';
import '../repositories/chapter_repository.dart';
import '../../data/repositories/chapter_repository_impl.dart';

/// 章节管理用例
/// 提供章节的创建、读取、更新、删除等业务操作
class ManageChaptersUseCase {
  final ChapterRepository _repository;

  ManageChaptersUseCase(this._repository);

  /// 获取所有章节
  Future<List<Chapter>> getAllChapters() async {
    return await _repository.getAllChapters();
  }

  /// 获取根章节列表
  Future<List<Chapter>> getRootChapters() async {
    final allChapters = await _repository.getAllChapters();
    return allChapters.where((chapter) => chapter.isRoot).toList()
      ..sort((a, b) => a.order.compareTo(b.order));
  }

  /// 根据ID获取章节
  Future<Chapter?> getChapterById(String id) async {
    return await _repository.getChapterById(id);
  }

  /// 获取章节的子章节
  Future<List<Chapter>> getChildChapters(String parentId) async {
    return await _repository.getChildChapters(parentId);
  }

  /// 创建新章节
  Future<Chapter> createChapter({
    required String title,
    String? parentId,
    String content = '',
    List<String> tags = const [],
    String notes = '',
  }) async {
    // 计算章节层级和排序
    int level = 0;
    int order = 0;

    if (parentId != null) {
      final parent = await _repository.getChapterById(parentId);
      if (parent != null) {
        level = parent.level + 1;
        final siblings = await _repository.getChildChapters(parentId);
        order = siblings.length;
      }
    } else {
      final rootChapters = await getRootChapters();
      order = rootChapters.length;
    }

    final now = DateTime.now();
    final chapter = Chapter(
      id: _generateChapterId(),
      title: title,
      content: content,
      parentId: parentId,
      level: level,
      order: order,
      status: ChapterStatus.draft,
      wordCount: _countWords(content),
      createdAt: now,
      updatedAt: now,
      tags: tags,
      notes: notes,
    );

    return await _repository.createChapter(chapter);
  }

  /// 更新章节
  Future<Chapter> updateChapter(Chapter chapter) async {
    final updatedChapter = chapter.copyWith(
      wordCount: _countWords(chapter.content),
      updatedAt: DateTime.now(),
    );
    return await _repository.updateChapter(updatedChapter);
  }

  /// 更新章节内容
  Future<Chapter> updateChapterContent(String id, String content) async {
    final chapter = await _repository.getChapterById(id);
    if (chapter == null) {
      throw Exception('Chapter not found: $id');
    }

    final updatedChapter = chapter.copyWith(
      content: content,
      wordCount: _countWords(content),
      updatedAt: DateTime.now(),
    );

    return await _repository.updateChapter(updatedChapter);
  }

  /// 更新章节标题
  Future<Chapter> updateChapterTitle(String id, String title) async {
    final chapter = await _repository.getChapterById(id);
    if (chapter == null) {
      throw Exception('Chapter not found: $id');
    }

    final updatedChapter = chapter.copyWith(
      title: title,
      updatedAt: DateTime.now(),
    );

    return await _repository.updateChapter(updatedChapter);
  }

  /// 更新章节状态
  Future<Chapter> updateChapterStatus(String id, ChapterStatus status) async {
    final chapter = await _repository.getChapterById(id);
    if (chapter == null) {
      throw Exception('Chapter not found: $id');
    }

    final updatedChapter = chapter.copyWith(
      status: status,
      updatedAt: DateTime.now(),
    );

    return await _repository.updateChapter(updatedChapter);
  }

  /// 删除章节
  Future<void> deleteChapter(String id) async {
    final chapter = await _repository.getChapterById(id);
    if (chapter == null) {
      throw Exception('Chapter not found: $id');
    }

    // 检查是否有子章节
    final children = await _repository.getChildChapters(id);
    if (children.isNotEmpty) {
      throw Exception('Cannot delete chapter with children. Please delete or move child chapters first.');
    }

    await _repository.deleteChapter(id);
  }

  /// 移动章节到新的父章节下
  Future<Chapter> moveChapter(String chapterId, String? newParentId) async {
    final chapter = await _repository.getChapterById(chapterId);
    if (chapter == null) {
      throw Exception('Chapter not found: $chapterId');
    }

    // 检查是否会造成循环引用
    if (newParentId != null) {
      if (await _wouldCreateCycle(chapterId, newParentId)) {
        throw Exception('Cannot move chapter: would create a cycle');
      }
    }

    // 计算新的层级和排序
    int newLevel = 0;
    int newOrder = 0;

    if (newParentId != null) {
      final newParent = await _repository.getChapterById(newParentId);
      if (newParent != null) {
        newLevel = newParent.level + 1;
        final siblings = await _repository.getChildChapters(newParentId);
        newOrder = siblings.length;
      }
    } else {
      final rootChapters = await getRootChapters();
      newOrder = rootChapters.length;
    }

    final updatedChapter = chapter.copyWith(
      parentId: newParentId,
      level: newLevel,
      order: newOrder,
      updatedAt: DateTime.now(),
    );

    // 更新所有子章节的层级
    await _updateChildrenLevels(updatedChapter);

    return await _repository.updateChapter(updatedChapter);
  }

  /// 重新排序章节
  Future<void> reorderChapters(String? parentId, List<String> newOrder) async {
    final chapters = parentId != null
        ? await _repository.getChildChapters(parentId)
        : await getRootChapters();

    for (int i = 0; i < newOrder.length; i++) {
      final chapterId = newOrder[i];
      final chapter = chapters.firstWhere((c) => c.id == chapterId);
      
      if (chapter.order != i) {
        final updatedChapter = chapter.copyWith(
          order: i,
          updatedAt: DateTime.now(),
        );
        await _repository.updateChapter(updatedChapter);
      }
    }
  }

  /// 复制章节
  Future<Chapter> duplicateChapter(String id, {String? newTitle}) async {
    final chapter = await _repository.getChapterById(id);
    if (chapter == null) {
      throw Exception('Chapter not found: $id');
    }

    final now = DateTime.now();
    final duplicatedChapter = chapter.copyWith(
      id: _generateChapterId(),
      title: newTitle ?? '${chapter.title} (副本)',
      createdAt: now,
      updatedAt: now,
      status: ChapterStatus.draft,
    );

    return await _repository.createChapter(duplicatedChapter);
  }

  /// 搜索章节
  Future<List<Chapter>> searchChapters(String query) async {
    if (query.trim().isEmpty) {
      return [];
    }

    final allChapters = await _repository.getAllChapters();
    final lowercaseQuery = query.toLowerCase();

    return allChapters.where((chapter) {
      return chapter.title.toLowerCase().contains(lowercaseQuery) ||
             chapter.content.toLowerCase().contains(lowercaseQuery) ||
             chapter.notes.toLowerCase().contains(lowercaseQuery) ||
             chapter.tags.any((tag) => tag.toLowerCase().contains(lowercaseQuery));
    }).toList();
  }

  /// 获取章节统计信息
  Future<ChapterStatistics> getChapterStatistics() async {
    final allChapters = await _repository.getAllChapters();
    
    int totalWordCount = 0;
    final statusCounts = <ChapterStatus, int>{};
    
    for (final chapter in allChapters) {
      totalWordCount += chapter.wordCount;
      statusCounts[chapter.status] = (statusCounts[chapter.status] ?? 0) + 1;
    }

    return ChapterStatistics(
      totalChapters: allChapters.length,
      totalWordCount: totalWordCount,
      statusCounts: statusCounts,
    );
  }

  /// 构建章节树结构
  Future<List<Chapter>> buildChapterTree() async {
    final allChapters = await _repository.getAllChapters();
    final chapterMap = <String, Chapter>{};
    final rootChapters = <Chapter>[];

    // 创建章节映射
    for (final chapter in allChapters) {
      chapterMap[chapter.id] = chapter;
    }

    // 构建树结构
    for (final chapter in allChapters) {
      if (chapter.isRoot) {
        rootChapters.add(chapter);
      } else if (chapter.parentId != null) {
        final parent = chapterMap[chapter.parentId!];
        if (parent != null) {
          final updatedParent = parent.addChild(chapter);
          chapterMap[parent.id] = updatedParent;
        }
      }
    }

    // 排序根章节
    rootChapters.sort((a, b) => a.order.compareTo(b.order));
    
    return rootChapters;
  }

  /// 生成章节ID
  String _generateChapterId() {
    return 'chapter_${DateTime.now().millisecondsSinceEpoch}_${(1000 + (999 * (DateTime.now().microsecond / 1000000))).round()}';
  }

  /// 统计字数
  int _countWords(String text) {
    if (text.isEmpty) return 0;
    
    // 简单的中英文字数统计
    final chineseChars = text.replaceAll(RegExp(r'[^\u4e00-\u9fa5]'), '').length;
    final englishWords = text
        .replaceAll(RegExp(r'[\u4e00-\u9fa5]'), ' ')
        .split(RegExp(r'\s+'))
        .where((word) => word.isNotEmpty)
        .length;
    
    return chineseChars + englishWords;
  }

  /// 检查是否会造成循环引用
  Future<bool> _wouldCreateCycle(String chapterId, String newParentId) async {
    String? currentParentId = newParentId;
    
    while (currentParentId != null) {
      if (currentParentId == chapterId) {
        return true;
      }
      
      final parent = await _repository.getChapterById(currentParentId);
      currentParentId = parent?.parentId;
    }
    
    return false;
  }

  /// 更新子章节的层级
  Future<void> _updateChildrenLevels(Chapter parentChapter) async {
    final children = await _repository.getChildChapters(parentChapter.id);
    
    for (final child in children) {
      final updatedChild = child.copyWith(
        level: parentChapter.level + 1,
        updatedAt: DateTime.now(),
      );
      
      await _repository.updateChapter(updatedChild);
      await _updateChildrenLevels(updatedChild);
    }
  }
}

/// 章节统计信息
class ChapterStatistics {
  final int totalChapters;
  final int totalWordCount;
  final Map<ChapterStatus, int> statusCounts;

  const ChapterStatistics({
    required this.totalChapters,
    required this.totalWordCount,
    required this.statusCounts,
  });
}

/// 章节管理用例提供者
final manageChaptersUseCaseProvider = Provider<ManageChaptersUseCase>((ref) {
  final repository = ref.read(chapterRepositoryProvider);
  return ManageChaptersUseCase(repository);
});