import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:logger/logger.dart';

import 'package:bamboofall_app/features/writing/domain/entities/chapter.dart';
import 'package:bamboofall_app/features/writing/domain/repositories/chapter_repository.dart';
import 'package:bamboofall_app/features/writing/domain/usecases/manage_chapters.dart';

import 'manage_chapters_test.mocks.dart';

@GenerateMocks([ChapterRepository, Logger])
void main() {
  late ManageChaptersUseCase useCase;
  late MockChapterRepository mockRepository;
  late MockLogger mockLogger;

  setUp(() {
    mockRepository = MockChapterRepository();
    mockLogger = MockLogger();
    useCase = ManageChaptersUseCase(
      repository: mockRepository,
      logger: mockLogger,
    );
  });

  group('ManageChaptersUseCase - Chapter Creation', () {
    final testChapter = Chapter(
      id: 'chapter-1',
      projectId: 'project-1',
      title: 'Test Chapter',
      content: 'This is test content',
      order: 1,
      wordCount: 4,
      status: ChapterStatus.draft,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    test('should create chapter successfully', () async {
      // Arrange
      when(mockRepository.saveChapter(any)).thenAnswer((_) async {});
      when(mockRepository.getChaptersByProject('project-1')).thenAnswer((_) async => []);

      final creationData = ChapterCreationData(
        projectId: testChapter.projectId,
        title: testChapter.title,
        content: testChapter.content,
        parentChapterId: null,
      );

      // Act
      final result = await useCase.createChapter(creationData);

      // Assert
      expect(result.isSuccess, true);
      expect(result.data?.title, testChapter.title);
      expect(result.data?.projectId, testChapter.projectId);
      verify(mockRepository.saveChapter(any)).called(1);
    });

    test('should create sub-chapter with correct hierarchy', () async {
      // Arrange
      final parentChapter = testChapter.copyWith(id: 'parent-chapter');
      when(mockRepository.getChapter('parent-chapter')).thenAnswer((_) async => parentChapter);
      when(mockRepository.saveChapter(any)).thenAnswer((_) async {});
      when(mockRepository.getChaptersByProject('project-1')).thenAnswer((_) async => [parentChapter]);

      final creationData = ChapterCreationData(
        projectId: testChapter.projectId,
        title: 'Sub Chapter',
        content: 'Sub chapter content',
        parentChapterId: 'parent-chapter',
      );

      // Act
      final result = await useCase.createChapter(creationData);

      // Assert
      expect(result.isSuccess, true);
      expect(result.data?.parentChapterId, 'parent-chapter');
      expect(result.data?.level, 1); // Parent is level 0, so child is level 1
      verify(mockRepository.saveChapter(any)).called(1);
    });

    test('should fail to create chapter with invalid project', () async {
      // Arrange
      final creationData = ChapterCreationData(
        projectId: '', // Invalid project ID
        title: testChapter.title,
        content: testChapter.content,
      );

      // Act
      final result = await useCase.createChapter(creationData);

      // Assert
      expect(result.isSuccess, false);
      expect(result.error?.type, ChapterOperationErrorType.validationFailed);
      verifyNever(mockRepository.saveChapter(any));
    });

    test('should auto-generate chapter order', () async {
      // Arrange
      final existingChapters = [
        testChapter.copyWith(id: 'chapter-1', order: 1),
        testChapter.copyWith(id: 'chapter-2', order: 2),
      ];
      
      when(mockRepository.getChaptersByProject('project-1')).thenAnswer((_) async => existingChapters);
      when(mockRepository.saveChapter(any)).thenAnswer((_) async {});

      final creationData = ChapterCreationData(
        projectId: testChapter.projectId,
        title: 'New Chapter',
        content: 'New content',
      );

      // Act
      final result = await useCase.createChapter(creationData);

      // Assert
      expect(result.isSuccess, true);
      expect(result.data?.order, 3); // Should be next in sequence
    });
  });

  group('ManageChaptersUseCase - Chapter Updates', () {
    final testChapter = Chapter(
      id: 'chapter-1',
      projectId: 'project-1',
      title: 'Test Chapter',
      content: 'Original content',
      order: 1,
      wordCount: 2,
      status: ChapterStatus.draft,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    test('should update chapter content successfully', () async {
      // Arrange
      when(mockRepository.getChapter(testChapter.id)).thenAnswer((_) async => testChapter);
      when(mockRepository.updateChapter(any)).thenAnswer((_) async {});

      const newContent = 'Updated content with more words';
      final updatedChapter = testChapter.copyWith(
        content: newContent,
        wordCount: newContent.split(' ').length,
      );

      // Act
      final result = await useCase.updateChapterContent(testChapter.id, newContent);

      // Assert
      expect(result.isSuccess, true);
      expect(result.data?.content, newContent);
      expect(result.data?.wordCount, 5); // Updated word count
      verify(mockRepository.updateChapter(any)).called(1);
    });

    test('should update chapter title successfully', () async {
      // Arrange
      when(mockRepository.getChapter(testChapter.id)).thenAnswer((_) async => testChapter);
      when(mockRepository.updateChapter(any)).thenAnswer((_) async {});

      const newTitle = 'Updated Chapter Title';

      // Act
      final result = await useCase.updateChapterTitle(testChapter.id, newTitle);

      // Assert
      expect(result.isSuccess, true);
      expect(result.data?.title, newTitle);
      verify(mockRepository.updateChapter(any)).called(1);
    });

    test('should update chapter status successfully', () async {
      // Arrange
      when(mockRepository.getChapter(testChapter.id)).thenAnswer((_) async => testChapter);
      when(mockRepository.updateChapter(any)).thenAnswer((_) async {});

      // Act
      final result = await useCase.updateChapterStatus(testChapter.id, ChapterStatus.published);

      // Assert
      expect(result.isSuccess, true);
      expect(result.data?.status, ChapterStatus.published);
      verify(mockRepository.updateChapter(any)).called(1);
    });

    test('should fail to update non-existent chapter', () async {
      // Arrange
      when(mockRepository.getChapter('non-existent')).thenAnswer((_) async => null);

      // Act
      final result = await useCase.updateChapterContent('non-existent', 'New content');

      // Assert
      expect(result.isSuccess, false);
      expect(result.error?.type, ChapterOperationErrorType.notFound);
      verifyNever(mockRepository.updateChapter(any));
    });
  });

  group('ManageChaptersUseCase - Chapter Reordering', () {
    final chapters = [
      Chapter(
        id: 'chapter-1',
        projectId: 'project-1',
        title: 'Chapter 1',
        content: 'Content 1',
        order: 1,
        wordCount: 2,
        status: ChapterStatus.draft,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Chapter(
        id: 'chapter-2',
        projectId: 'project-1',
        title: 'Chapter 2',
        content: 'Content 2',
        order: 2,
        wordCount: 2,
        status: ChapterStatus.draft,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Chapter(
        id: 'chapter-3',
        projectId: 'project-1',
        title: 'Chapter 3',
        content: 'Content 3',
        order: 3,
        wordCount: 2,
        status: ChapterStatus.draft,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    test('should reorder chapters successfully', () async {
      // Arrange
      when(mockRepository.getChaptersByProject('project-1')).thenAnswer((_) async => chapters);
      when(mockRepository.updateChapter(any)).thenAnswer((_) async {});

      final newOrder = ['chapter-3', 'chapter-1', 'chapter-2'];

      // Act
      final result = await useCase.reorderChapters('project-1', newOrder);

      // Assert
      expect(result.isSuccess, true);
      verify(mockRepository.updateChapter(any)).called(3); // All chapters updated
    });

    test('should move chapter to specific position', () async {
      // Arrange
      when(mockRepository.getChaptersByProject('project-1')).thenAnswer((_) async => chapters);
      when(mockRepository.updateChapter(any)).thenAnswer((_) async {});

      // Act - Move chapter-3 to position 1 (first)
      final result = await useCase.moveChapterToPosition('chapter-3', 1);

      // Assert
      expect(result.isSuccess, true);
      verify(mockRepository.updateChapter(any)).called(greaterThan(0));
    });

    test('should handle invalid reorder request', () async {
      // Arrange
      when(mockRepository.getChaptersByProject('project-1')).thenAnswer((_) async => chapters);

      final invalidOrder = ['chapter-1', 'chapter-2']; // Missing chapter-3

      // Act
      final result = await useCase.reorderChapters('project-1', invalidOrder);

      // Assert
      expect(result.isSuccess, false);
      expect(result.error?.type, ChapterOperationErrorType.validationFailed);
      verifyNever(mockRepository.updateChapter(any));
    });
  });

  group('ManageChaptersUseCase - Chapter Deletion', () {
    final testChapter = Chapter(
      id: 'chapter-1',
      projectId: 'project-1',
      title: 'Test Chapter',
      content: 'Test content',
      order: 1,
      wordCount: 2,
      status: ChapterStatus.draft,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    test('should delete chapter successfully', () async {
      // Arrange
      when(mockRepository.getChapter(testChapter.id)).thenAnswer((_) async => testChapter);
      when(mockRepository.getChildChapters(testChapter.id)).thenAnswer((_) async => []);
      when(mockRepository.deleteChapter(testChapter.id)).thenAnswer((_) async {});

      // Act
      final result = await useCase.deleteChapter(testChapter.id);

      // Assert
      expect(result.isSuccess, true);
      verify(mockRepository.deleteChapter(testChapter.id)).called(1);
    });

    test('should fail to delete chapter with sub-chapters', () async {
      // Arrange
      final subChapter = testChapter.copyWith(
        id: 'sub-chapter',
        parentChapterId: testChapter.id,
      );
      
      when(mockRepository.getChapter(testChapter.id)).thenAnswer((_) async => testChapter);
      when(mockRepository.getChildChapters(testChapter.id)).thenAnswer((_) async => [subChapter]);

      // Act
      final result = await useCase.deleteChapter(testChapter.id);

      // Assert
      expect(result.isSuccess, false);
      expect(result.error?.type, ChapterOperationErrorType.hasChildren);
      verifyNever(mockRepository.deleteChapter(any));
    });

    test('should delete chapter with cascade option', () async {
      // Arrange
      final subChapter = testChapter.copyWith(
        id: 'sub-chapter',
        parentChapterId: testChapter.id,
      );
      
      when(mockRepository.getChapter(testChapter.id)).thenAnswer((_) async => testChapter);
      when(mockRepository.getChildChapters(testChapter.id)).thenAnswer((_) async => [subChapter]);
      when(mockRepository.getChildChapters(subChapter.id)).thenAnswer((_) async => []);
      when(mockRepository.deleteChapter(any)).thenAnswer((_) async {});

      // Act
      final result = await useCase.deleteChapter(testChapter.id, cascade: true);

      // Assert
      expect(result.isSuccess, true);
      verify(mockRepository.deleteChapter(testChapter.id)).called(1);
      verify(mockRepository.deleteChapter(subChapter.id)).called(1);
    });
  });

  group('ManageChaptersUseCase - Chapter Queries', () {
    final chapters = [
      Chapter(
        id: 'chapter-1',
        projectId: 'project-1',
        title: 'Introduction',
        content: 'This is the introduction chapter',
        order: 1,
        wordCount: 5,
        status: ChapterStatus.published,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Chapter(
        id: 'chapter-2',
        projectId: 'project-1',
        title: 'The Journey Begins',
        content: 'The adventure starts here',
        order: 2,
        wordCount: 4,
        status: ChapterStatus.draft,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    test('should get chapters by project', () async {
      // Arrange
      when(mockRepository.getChaptersByProject('project-1')).thenAnswer((_) async => chapters);

      // Act
      final result = await useCase.getChaptersByProject('project-1');

      // Assert
      expect(result.isSuccess, true);
      expect(result.data?.length, 2);
      expect(result.data?.first.title, 'Introduction');
    });

    test('should search chapters by title', () async {
      // Arrange
      when(mockRepository.searchChapters('project-1', 'Journey')).thenAnswer((_) async => [chapters[1]]);

      // Act
      final result = await useCase.searchChapters('project-1', 'Journey');

      // Assert
      expect(result.isSuccess, true);
      expect(result.data?.length, 1);
      expect(result.data?.first.title, 'The Journey Begins');
    });

    test('should get chapter statistics', () async {
      // Arrange
      when(mockRepository.getChaptersByProject('project-1')).thenAnswer((_) async => chapters);

      // Act
      final result = await useCase.getChapterStatistics('project-1');

      // Assert
      expect(result.isSuccess, true);
      expect(result.data?.totalChapters, 2);
      expect(result.data?.totalWordCount, 9); // 5 + 4
      expect(result.data?.publishedChapters, 1);
      expect(result.data?.draftChapters, 1);
    });

    test('should get chapter tree structure', () async {
      // Arrange
      final parentChapter = chapters[0];
      final childChapter = chapters[1].copyWith(
        parentChapterId: parentChapter.id,
        level: 1,
      );
      
      when(mockRepository.getChaptersByProject('project-1'))
          .thenAnswer((_) async => [parentChapter, childChapter]);
      when(mockRepository.getChildChapters(parentChapter.id))
          .thenAnswer((_) async => [childChapter]);

      // Act
      final result = await useCase.getChapterTree('project-1');

      // Assert
      expect(result.isSuccess, true);
      expect(result.data?.isNotEmpty, true);
      // Verify tree structure is properly built
    });
  });

  group('ManageChaptersUseCase - Error Handling', () {
    test('should handle repository exceptions', () async {
      // Arrange
      when(mockRepository.getChapter(any)).thenThrow(Exception('Database error'));

      // Act
      final result = await useCase.getChapter('chapter-1');

      // Assert
      expect(result.isSuccess, false);
      expect(result.error?.type, ChapterOperationErrorType.unknown);
    });

    test('should validate chapter creation data', () async {
      // Arrange
      final invalidData = ChapterCreationData(
        projectId: '', // Empty project ID
        title: '', // Empty title
        content: 'Valid content',
      );

      // Act
      final result = await useCase.createChapter(invalidData);

      // Assert
      expect(result.isSuccess, false);
      expect(result.error?.type, ChapterOperationErrorType.validationFailed);
    });
  });
}