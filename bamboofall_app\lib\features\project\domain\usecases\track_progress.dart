import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../entities/project.dart';
import '../repositories/project_repository.dart';

/// 进度跟踪用例
/// 提供项目进度跟踪和统计分析功能
class TrackProgressUseCase {
  final ProjectRepository _repository;

  TrackProgressUseCase(this._repository);

  /// 获取项目进度概览
  Future<ProjectProgressOverview> getProjectProgressOverview(String projectId) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    final statistics = project.statistics;
    final config = project.config;

    // 计算各种进度指标
    final wordCountProgress = config.targetWordCount > 0 
        ? (statistics.totalWordCount / config.targetWordCount * 100).clamp(0.0, 100.0)
        : 0.0;

    final dailyProgress = config.dailyWritingGoal > 0 && statistics.writingDays > 0
        ? (statistics.averageDailyWords / config.dailyWritingGoal * 100).clamp(0.0, 100.0)
        : 0.0;

    // 预计完成时间
    DateTime? estimatedCompletion;
    if (statistics.averageDailyWords > 0 && project.remainingWordCount > 0) {
      final remainingDays = (project.remainingWordCount / statistics.averageDailyWords).ceil();
      estimatedCompletion = DateTime.now().add(Duration(days: remainingDays));
    }

    // 写作效率趋势
    final efficiencyTrend = _calculateEfficiencyTrend(statistics);

    return ProjectProgressOverview(
      project: project,
      wordCountProgress: wordCountProgress,
      dailyProgress: dailyProgress,
      estimatedCompletion: estimatedCompletion,
      efficiencyTrend: efficiencyTrend,
      milestones: _generateMilestones(project),
    );
  }

  /// 获取写作统计数据
  Future<WritingStatistics> getWritingStatistics(String projectId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    // TODO: 从写作会话数据中获取详细统计
    // 这里先使用项目统计数据作为基础
    final stats = project.statistics;

    return WritingStatistics(
      totalWords: stats.totalWordCount,
      totalCharacters: stats.totalCharacterCount,
      totalChapters: stats.chapterCount,
      writingDays: stats.writingDays,
      averageDailyWords: stats.averageDailyWords,
      maxDailyWords: stats.maxDailyWords,
      consecutiveDays: stats.consecutiveWritingDays,
      writingTimeMinutes: stats.writingTimeMinutes,
      wordsPerMinute: stats.writingTimeMinutes > 0 
          ? stats.totalWordCount / stats.writingTimeMinutes 
          : 0.0,
      productivity: _calculateProductivity(stats),
      consistency: _calculateConsistency(stats),
    );
  }

  /// 获取进度历史数据
  Future<List<ProgressDataPoint>> getProgressHistory(String projectId, {
    Duration period = const Duration(days: 30),
  }) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    // TODO: 从实际的写作会话数据生成历史数据
    // 这里生成模拟数据用于演示
    return _generateMockProgressHistory(project, period);
  }

  /// 获取写作目标达成情况
  Future<GoalAchievement> getGoalAchievement(String projectId) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    final config = project.config;
    final stats = project.statistics;

    // 字数目标达成情况
    final wordGoalAchievement = config.targetWordCount > 0
        ? (stats.totalWordCount / config.targetWordCount * 100).clamp(0.0, 100.0)
        : 0.0;

    // 每日目标达成情况
    final dailyGoalAchievement = config.dailyWritingGoal > 0
        ? (stats.averageDailyWords / config.dailyWritingGoal * 100).clamp(0.0, 100.0)
        : 0.0;

    // 时间目标达成情况
    double? timeGoalAchievement;
    if (config.deadline != null) {
      final totalDays = config.deadline!.difference(project.createdAt).inDays;
      final passedDays = DateTime.now().difference(project.createdAt).inDays;
      final expectedProgress = totalDays > 0 ? (passedDays / totalDays * 100).clamp(0.0, 100.0) : 0.0;
      timeGoalAchievement = wordGoalAchievement - expectedProgress;
    }

    return GoalAchievement(
      wordGoalAchievement: wordGoalAchievement,
      dailyGoalAchievement: dailyGoalAchievement,
      timeGoalAchievement: timeGoalAchievement,
      isOnTrack: _isProjectOnTrack(project),
      recommendations: _generateRecommendations(project),
    );
  }

  /// 更新项目统计数据
  Future<Project> updateProjectStatistics(String projectId, {
    int? newWordCount,
    int? newChapterCount,
    int? writingTimeMinutes,
  }) async {
    final project = await _repository.getProjectById(projectId);
    if (project == null) {
      throw Exception('Project not found: $projectId');
    }

    final currentStats = project.statistics;
    final now = DateTime.now();

    // 计算新的统计数据
    final updatedStats = ProjectStatistics(
      totalWordCount: newWordCount ?? currentStats.totalWordCount,
      totalCharacterCount: newWordCount != null 
          ? (newWordCount * 2) // 估算字符数
          : currentStats.totalCharacterCount,
      chapterCount: newChapterCount ?? currentStats.chapterCount,
      versionCount: currentStats.versionCount,
      writingDays: _calculateWritingDays(project, now),
      averageDailyWords: _calculateAverageDailyWords(
        newWordCount ?? currentStats.totalWordCount,
        _calculateWritingDays(project, now),
      ),
      lastWritingTime: now,
      completionProgress: _calculateCompletionProgress(
        newWordCount ?? currentStats.totalWordCount,
        project.config.targetWordCount,
      ),
      writingTimeMinutes: (writingTimeMinutes ?? 0) + currentStats.writingTimeMinutes,
      maxDailyWords: _calculateMaxDailyWords(
        currentStats.maxDailyWords,
        newWordCount ?? currentStats.totalWordCount,
        currentStats.totalWordCount,
      ),
      consecutiveWritingDays: _calculateConsecutiveDays(project, now),
    );

    final updatedProject = project.updateStatistics(updatedStats);
    return await _repository.updateProject(updatedProject);
  }

  /// 记录写作会话
  Future<void> recordWritingSession({
    required String projectId,
    required int startWordCount,
    required int endWordCount,
    required Duration duration,
    String? notes,
  }) async {
    final wordsAdded = endWordCount - startWordCount;
    if (wordsAdded <= 0) return;

    await updateProjectStatistics(
      projectId,
      newWordCount: endWordCount,
      writingTimeMinutes: duration.inMinutes,
    );
  }

  /// 计算效率趋势
  EfficiencyTrend _calculateEfficiencyTrend(ProjectStatistics stats) {
    // 基于平均每日字数和写作时间计算效率
    final wordsPerHour = stats.writingTimeMinutes > 0 
        ? (stats.totalWordCount / (stats.writingTimeMinutes / 60.0))
        : 0.0;

    if (wordsPerHour >= 1000) {
      return EfficiencyTrend.improving;
    } else if (wordsPerHour >= 500) {
      return EfficiencyTrend.stable;
    } else {
      return EfficiencyTrend.declining;
    }
  }

  /// 生成里程碑
  List<Milestone> _generateMilestones(Project project) {
    final milestones = <Milestone>[];
    final targetWords = project.config.targetWordCount;
    final currentWords = project.statistics.totalWordCount;

    // 字数里程碑
    final wordMilestones = [0.25, 0.5, 0.75, 1.0];
    for (final ratio in wordMilestones) {
      final milestoneWords = (targetWords * ratio).round();
      final isCompleted = currentWords >= milestoneWords;
      
      milestones.add(Milestone(
        id: 'words_${(ratio * 100).round()}',
        title: '${(ratio * 100).round()}% 字数目标',
        description: '达到 ${milestoneWords.toString()} 字',
        targetValue: milestoneWords.toDouble(),
        currentValue: currentWords.toDouble(),
        isCompleted: isCompleted,
        completedAt: isCompleted ? DateTime.now() : null,
        type: MilestoneType.wordCount,
      ));
    }

    return milestones;
  }

  /// 计算生产力指数
  double _calculateProductivity(ProjectStatistics stats) {
    if (stats.writingDays == 0) return 0.0;
    
    // 基于平均每日字数和连续写作天数计算
    final dailyScore = (stats.averageDailyWords / 1000.0).clamp(0.0, 1.0);
    final consistencyScore = (stats.consecutiveWritingDays / 30.0).clamp(0.0, 1.0);
    
    return (dailyScore * 0.7 + consistencyScore * 0.3) * 100;
  }

  /// 计算一致性指数
  double _calculateConsistency(ProjectStatistics stats) {
    if (stats.writingDays == 0) return 0.0;
    
    // 基于连续写作天数和总写作天数的比例
    final totalDays = DateTime.now().difference(DateTime.now().subtract(Duration(days: stats.writingDays))).inDays;
    final consistencyRatio = totalDays > 0 ? (stats.writingDays / totalDays) : 0.0;
    
    return (consistencyRatio * 100).clamp(0.0, 100.0);
  }

  /// 生成模拟进度历史数据
  List<ProgressDataPoint> _generateMockProgressHistory(Project project, Duration period) {
    final dataPoints = <ProgressDataPoint>[];
    final endDate = DateTime.now();
    final startDate = endDate.subtract(period);
    final totalWords = project.statistics.totalWordCount;
    
    // 生成每日数据点
    for (int i = 0; i <= period.inDays; i++) {
      final date = startDate.add(Duration(days: i));
      final progress = (i / period.inDays) * totalWords;
      
      dataPoints.add(ProgressDataPoint(
        date: date,
        wordCount: progress.round(),
        dailyWords: i > 0 ? (progress - dataPoints[i-1].wordCount).round() : 0,
        writingTime: Duration(minutes: (progress / 10).round()),
      ));
    }
    
    return dataPoints;
  }

  /// 判断项目是否按计划进行
  bool _isProjectOnTrack(Project project) {
    if (project.config.deadline == null) return true;
    
    final totalDays = project.config.deadline!.difference(project.createdAt).inDays;
    final passedDays = DateTime.now().difference(project.createdAt).inDays;
    final expectedProgress = totalDays > 0 ? (passedDays / totalDays) : 0.0;
    final actualProgress = project.completionPercentage / 100.0;
    
    return actualProgress >= expectedProgress * 0.8; // 允许20%的偏差
  }

  /// 生成改进建议
  List<String> _generateRecommendations(Project project) {
    final recommendations = <String>[];
    final stats = project.statistics;
    final config = project.config;
    
    if (stats.averageDailyWords < config.dailyWritingGoal) {
      recommendations.add('建议增加每日写作时间，当前平均每日字数低于目标');
    }
    
    if (stats.consecutiveWritingDays < 7) {
      recommendations.add('建议保持连续写作习惯，提高写作一致性');
    }
    
    if (project.isOverdue) {
      recommendations.add('项目已超过截止日期，建议调整时间安排或降低目标');
    }
    
    if (stats.writingTimeMinutes > 0 && stats.totalWordCount / (stats.writingTimeMinutes / 60.0) < 300) {
      recommendations.add('写作效率较低，建议减少干扰，专注写作');
    }
    
    return recommendations;
  }

  /// 计算写作天数
  int _calculateWritingDays(Project project, DateTime now) {
    return now.difference(project.createdAt).inDays + 1;
  }

  /// 计算平均每日字数
  int _calculateAverageDailyWords(int totalWords, int writingDays) {
    return writingDays > 0 ? (totalWords / writingDays).round() : 0;
  }

  /// 计算完成进度
  double _calculateCompletionProgress(int currentWords, int targetWords) {
    return targetWords > 0 ? (currentWords / targetWords * 100).clamp(0.0, 100.0) : 0.0;
  }

  /// 计算最高日字数
  int _calculateMaxDailyWords(int currentMax, int newTotal, int oldTotal) {
    final dailyIncrease = newTotal - oldTotal;
    return dailyIncrease > currentMax ? dailyIncrease : currentMax;
  }

  /// 计算连续写作天数
  int _calculateConsecutiveDays(Project project, DateTime now) {
    // 简化实现，实际应该基于写作会话数据
    final daysSinceLastWriting = project.statistics.lastWritingTime != null
        ? now.difference(project.statistics.lastWritingTime!).inDays
        : 0;
    
    return daysSinceLastWriting <= 1 ? project.statistics.consecutiveWritingDays + 1 : 1;
  }
}

/// 项目进度概览
class ProjectProgressOverview {
  final Project project;
  final double wordCountProgress;
  final double dailyProgress;
  final DateTime? estimatedCompletion;
  final EfficiencyTrend efficiencyTrend;
  final List<Milestone> milestones;

  const ProjectProgressOverview({
    required this.project,
    required this.wordCountProgress,
    required this.dailyProgress,
    this.estimatedCompletion,
    required this.efficiencyTrend,
    required this.milestones,
  });
}

/// 写作统计数据
class WritingStatistics {
  final int totalWords;
  final int totalCharacters;
  final int totalChapters;
  final int writingDays;
  final int averageDailyWords;
  final int maxDailyWords;
  final int consecutiveDays;
  final int writingTimeMinutes;
  final double wordsPerMinute;
  final double productivity;
  final double consistency;

  const WritingStatistics({
    required this.totalWords,
    required this.totalCharacters,
    required this.totalChapters,
    required this.writingDays,
    required this.averageDailyWords,
    required this.maxDailyWords,
    required this.consecutiveDays,
    required this.writingTimeMinutes,
    required this.wordsPerMinute,
    required this.productivity,
    required this.consistency,
  });

  /// 获取写作时长（小时）
  double get writingTimeHours => writingTimeMinutes / 60.0;

  /// 获取平均写作时长（分钟/天）
  double get averageWritingTimePerDay => writingDays > 0 ? writingTimeMinutes / writingDays : 0.0;
}

/// 进度数据点
class ProgressDataPoint {
  final DateTime date;
  final int wordCount;
  final int dailyWords;
  final Duration writingTime;

  const ProgressDataPoint({
    required this.date,
    required this.wordCount,
    required this.dailyWords,
    required this.writingTime,
  });
}

/// 目标达成情况
class GoalAchievement {
  final double wordGoalAchievement;
  final double dailyGoalAchievement;
  final double? timeGoalAchievement;
  final bool isOnTrack;
  final List<String> recommendations;

  const GoalAchievement({
    required this.wordGoalAchievement,
    required this.dailyGoalAchievement,
    this.timeGoalAchievement,
    required this.isOnTrack,
    required this.recommendations,
  });
}

/// 里程碑
class Milestone {
  final String id;
  final String title;
  final String description;
  final double targetValue;
  final double currentValue;
  final bool isCompleted;
  final DateTime? completedAt;
  final MilestoneType type;

  const Milestone({
    required this.id,
    required this.title,
    required this.description,
    required this.targetValue,
    required this.currentValue,
    required this.isCompleted,
    this.completedAt,
    required this.type,
  });

  /// 获取完成百分比
  double get completionPercentage {
    return targetValue > 0 ? (currentValue / targetValue * 100).clamp(0.0, 100.0) : 0.0;
  }
}

/// 里程碑类型
enum MilestoneType {
  wordCount,
  chapterCount,
  timeSpent,
  deadline,
}

/// 效率趋势
enum EfficiencyTrend {
  improving,
  stable,
  declining,
}

/// 进度跟踪用例提供者
final trackProgressUseCaseProvider = Provider<TrackProgressUseCase>((ref) {
  final repository = ref.read(projectRepositoryProvider);
  return TrackProgressUseCase(repository);
});

/// 项目仓库提供者引用
final projectRepositoryProvider = Provider<ProjectRepository>((ref) {
  throw UnimplementedError('ProjectRepository implementation not provided');
});