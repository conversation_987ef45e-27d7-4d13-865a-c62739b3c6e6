// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$StoryBibleImpl _$$StoryBibleImplFromJson(Map<String, dynamic> json) =>
    _$StoryBibleImpl(
      id: json['id'] as String,
      projectId: json['projectId'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      worldSettings: WorldSettings.fromJson(
        json['worldSettings'] as Map<String, dynamic>,
      ),
      characters:
          (json['characters'] as List<dynamic>?)
              ?.map((e) => Character.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      locations:
          (json['locations'] as List<dynamic>?)
              ?.map((e) => Location.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      plotLines:
          (json['plotLines'] as List<dynamic>?)
              ?.map((e) => PlotLine.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      items:
          (json['items'] as List<dynamic>?)
              ?.map((e) => Item.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      skills:
          (json['skills'] as List<dynamic>?)
              ?.map((e) => Skill.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      constraints:
          (json['constraints'] as List<dynamic>?)
              ?.map((e) => Constraint.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      version: (json['version'] as num?)?.toInt() ?? 1,
    );

Map<String, dynamic> _$$StoryBibleImplToJson(_$StoryBibleImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'projectId': instance.projectId,
      'title': instance.title,
      'description': instance.description,
      'worldSettings': instance.worldSettings,
      'characters': instance.characters,
      'locations': instance.locations,
      'plotLines': instance.plotLines,
      'items': instance.items,
      'skills': instance.skills,
      'constraints': instance.constraints,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'version': instance.version,
    };

_$PlotLineImpl _$$PlotLineImplFromJson(Map<String, dynamic> json) =>
    _$PlotLineImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      type: $enumDecode(_$PlotLineTypeEnumMap, json['type']),
      status: $enumDecode(_$PlotLineStatusEnumMap, json['status']),
      plotPoints:
          (json['plotPoints'] as List<dynamic>?)
              ?.map((e) => PlotPoint.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      involvedCharacterIds:
          (json['involvedCharacterIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      involvedLocationIds:
          (json['involvedLocationIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$PlotLineImplToJson(_$PlotLineImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$PlotLineTypeEnumMap[instance.type]!,
      'status': _$PlotLineStatusEnumMap[instance.status]!,
      'plotPoints': instance.plotPoints,
      'involvedCharacterIds': instance.involvedCharacterIds,
      'involvedLocationIds': instance.involvedLocationIds,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$PlotLineTypeEnumMap = {
  PlotLineType.main: 'main',
  PlotLineType.sub: 'sub',
  PlotLineType.character: 'character',
  PlotLineType.world: 'world',
};

const _$PlotLineStatusEnumMap = {
  PlotLineStatus.planning: 'planning',
  PlotLineStatus.active: 'active',
  PlotLineStatus.completed: 'completed',
  PlotLineStatus.paused: 'paused',
  PlotLineStatus.cancelled: 'cancelled',
};

_$PlotPointImpl _$$PlotPointImplFromJson(
  Map<String, dynamic> json,
) => _$PlotPointImpl(
  id: json['id'] as String,
  title: json['title'] as String,
  description: json['description'] as String?,
  order: (json['order'] as num).toInt(),
  status: $enumDecodeNullable(_$PlotPointStatusEnumMap, json['status']),
  characterIds:
      (json['characterIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  locationIds:
      (json['locationIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  itemIds:
      (json['itemIds'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  skillIds:
      (json['skillIds'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  chapterId: json['chapterId'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$PlotPointImplToJson(_$PlotPointImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'order': instance.order,
      'status': _$PlotPointStatusEnumMap[instance.status],
      'characterIds': instance.characterIds,
      'locationIds': instance.locationIds,
      'itemIds': instance.itemIds,
      'skillIds': instance.skillIds,
      'chapterId': instance.chapterId,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$PlotPointStatusEnumMap = {
  PlotPointStatus.planned: 'planned',
  PlotPointStatus.inProgress: 'inProgress',
  PlotPointStatus.completed: 'completed',
  PlotPointStatus.skipped: 'skipped',
};

_$ItemImpl _$$ItemImplFromJson(Map<String, dynamic> json) => _$ItemImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String?,
  type: $enumDecode(_$ItemTypeEnumMap, json['type']),
  rarity: $enumDecode(_$ItemRarityEnumMap, json['rarity']),
  origin: json['origin'] as String?,
  currentOwnerId: json['currentOwnerId'] as String?,
  previousOwnerIds:
      (json['previousOwnerIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  properties:
      (json['properties'] as List<dynamic>?)
          ?.map((e) => ItemProperty.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  relatedSkillIds:
      (json['relatedSkillIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  imageUrl: json['imageUrl'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$ItemImplToJson(_$ItemImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$ItemTypeEnumMap[instance.type]!,
      'rarity': _$ItemRarityEnumMap[instance.rarity]!,
      'origin': instance.origin,
      'currentOwnerId': instance.currentOwnerId,
      'previousOwnerIds': instance.previousOwnerIds,
      'properties': instance.properties,
      'relatedSkillIds': instance.relatedSkillIds,
      'imageUrl': instance.imageUrl,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$ItemTypeEnumMap = {
  ItemType.weapon: 'weapon',
  ItemType.armor: 'armor',
  ItemType.accessory: 'accessory',
  ItemType.consumable: 'consumable',
  ItemType.tool: 'tool',
  ItemType.book: 'book',
  ItemType.artifact: 'artifact',
  ItemType.currency: 'currency',
  ItemType.other: 'other',
};

const _$ItemRarityEnumMap = {
  ItemRarity.common: 'common',
  ItemRarity.uncommon: 'uncommon',
  ItemRarity.rare: 'rare',
  ItemRarity.epic: 'epic',
  ItemRarity.legendary: 'legendary',
  ItemRarity.mythic: 'mythic',
};

_$ItemPropertyImpl _$$ItemPropertyImplFromJson(Map<String, dynamic> json) =>
    _$ItemPropertyImpl(
      name: json['name'] as String,
      value: json['value'] as String,
      description: json['description'] as String?,
      type: $enumDecodeNullable(_$PropertyTypeEnumMap, json['type']),
    );

Map<String, dynamic> _$$ItemPropertyImplToJson(_$ItemPropertyImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'value': instance.value,
      'description': instance.description,
      'type': _$PropertyTypeEnumMap[instance.type],
    };

const _$PropertyTypeEnumMap = {
  PropertyType.numeric: 'numeric',
  PropertyType.text: 'text',
  PropertyType.boolean: 'boolean',
  PropertyType.list: 'list',
};

_$SkillImpl _$$SkillImplFromJson(Map<String, dynamic> json) => _$SkillImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String?,
  type: $enumDecode(_$SkillTypeEnumMap, json['type']),
  level: $enumDecode(_$SkillLevelEnumMap, json['level']),
  prerequisites:
      (json['prerequisites'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  effects:
      (json['effects'] as List<dynamic>?)
          ?.map((e) => SkillEffect.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  learnMethod: json['learnMethod'] as String?,
  cost: (json['cost'] as num?)?.toInt(),
  cooldown: (json['cooldown'] as num?)?.toInt(),
  relatedItemIds:
      (json['relatedItemIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$SkillImplToJson(_$SkillImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$SkillTypeEnumMap[instance.type]!,
      'level': _$SkillLevelEnumMap[instance.level]!,
      'prerequisites': instance.prerequisites,
      'effects': instance.effects,
      'learnMethod': instance.learnMethod,
      'cost': instance.cost,
      'cooldown': instance.cooldown,
      'relatedItemIds': instance.relatedItemIds,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$SkillTypeEnumMap = {
  SkillType.combat: 'combat',
  SkillType.magic: 'magic',
  SkillType.social: 'social',
  SkillType.crafting: 'crafting',
  SkillType.knowledge: 'knowledge',
  SkillType.survival: 'survival',
  SkillType.other: 'other',
};

const _$SkillLevelEnumMap = {
  SkillLevel.novice: 'novice',
  SkillLevel.apprentice: 'apprentice',
  SkillLevel.journeyman: 'journeyman',
  SkillLevel.expert: 'expert',
  SkillLevel.master: 'master',
  SkillLevel.grandmaster: 'grandmaster',
};

_$SkillEffectImpl _$$SkillEffectImplFromJson(Map<String, dynamic> json) =>
    _$SkillEffectImpl(
      name: json['name'] as String,
      description: json['description'] as String,
      type: $enumDecodeNullable(_$EffectTypeEnumMap, json['type']),
      target: json['target'] as String?,
      duration: json['duration'] as String?,
      parameters: json['parameters'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$SkillEffectImplToJson(_$SkillEffectImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'type': _$EffectTypeEnumMap[instance.type],
      'target': instance.target,
      'duration': instance.duration,
      'parameters': instance.parameters,
    };

const _$EffectTypeEnumMap = {
  EffectType.damage: 'damage',
  EffectType.heal: 'heal',
  EffectType.buff: 'buff',
  EffectType.debuff: 'debuff',
  EffectType.control: 'control',
  EffectType.utility: 'utility',
};

_$ConstraintImpl _$$ConstraintImplFromJson(Map<String, dynamic> json) =>
    _$ConstraintImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$ConstraintTypeEnumMap, json['type']),
      severity: $enumDecode(_$ConstraintSeverityEnumMap, json['severity']),
      affectedEntityIds:
          (json['affectedEntityIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      rules:
          (json['rules'] as List<dynamic>?)
              ?.map((e) => ConstraintRule.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      isActive: json['isActive'] as bool?,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$ConstraintImplToJson(_$ConstraintImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$ConstraintTypeEnumMap[instance.type]!,
      'severity': _$ConstraintSeverityEnumMap[instance.severity]!,
      'affectedEntityIds': instance.affectedEntityIds,
      'rules': instance.rules,
      'isActive': instance.isActive,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$ConstraintTypeEnumMap = {
  ConstraintType.character: 'character',
  ConstraintType.world: 'world',
  ConstraintType.plot: 'plot',
  ConstraintType.relationship: 'relationship',
  ConstraintType.timeline: 'timeline',
  ConstraintType.logic: 'logic',
};

const _$ConstraintSeverityEnumMap = {
  ConstraintSeverity.suggestion: 'suggestion',
  ConstraintSeverity.warning: 'warning',
  ConstraintSeverity.error: 'error',
  ConstraintSeverity.critical: 'critical',
};

_$ConstraintRuleImpl _$$ConstraintRuleImplFromJson(Map<String, dynamic> json) =>
    _$ConstraintRuleImpl(
      condition: json['condition'] as String,
      action: json['action'] as String,
      message: json['message'] as String?,
      parameters: json['parameters'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$ConstraintRuleImplToJson(
  _$ConstraintRuleImpl instance,
) => <String, dynamic>{
  'condition': instance.condition,
  'action': instance.action,
  'message': instance.message,
  'parameters': instance.parameters,
};

_$RelationshipImpl _$$RelationshipImplFromJson(Map<String, dynamic> json) =>
    _$RelationshipImpl(
      id: json['id'] as String,
      fromEntityId: json['fromEntityId'] as String,
      toEntityId: json['toEntityId'] as String,
      type: $enumDecode(_$RelationshipTypeEnumMap, json['type']),
      description: json['description'] as String?,
      strength: (json['strength'] as num?)?.toInt(),
      history:
          (json['history'] as List<dynamic>?)
              ?.map(
                (e) => RelationshipEvent.fromJson(e as Map<String, dynamic>),
              )
              .toList() ??
          const [],
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$RelationshipImplToJson(_$RelationshipImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'fromEntityId': instance.fromEntityId,
      'toEntityId': instance.toEntityId,
      'type': _$RelationshipTypeEnumMap[instance.type]!,
      'description': instance.description,
      'strength': instance.strength,
      'history': instance.history,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$RelationshipTypeEnumMap = {
  RelationshipType.family: 'family',
  RelationshipType.friend: 'friend',
  RelationshipType.enemy: 'enemy',
  RelationshipType.ally: 'ally',
  RelationshipType.romantic: 'romantic',
  RelationshipType.mentor: 'mentor',
  RelationshipType.student: 'student',
  RelationshipType.rival: 'rival',
  RelationshipType.neutral: 'neutral',
  RelationshipType.unknown: 'unknown',
};

_$RelationshipEventImpl _$$RelationshipEventImplFromJson(
  Map<String, dynamic> json,
) => _$RelationshipEventImpl(
  id: json['id'] as String,
  description: json['description'] as String,
  timestamp: DateTime.parse(json['timestamp'] as String),
  impactOnStrength: (json['impactOnStrength'] as num?)?.toInt(),
  chapterId: json['chapterId'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$RelationshipEventImplToJson(
  _$RelationshipEventImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'description': instance.description,
  'timestamp': instance.timestamp.toIso8601String(),
  'impactOnStrength': instance.impactOnStrength,
  'chapterId': instance.chapterId,
  'metadata': instance.metadata,
};

_$TimelineImpl _$$TimelineImplFromJson(Map<String, dynamic> json) =>
    _$TimelineImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      events:
          (json['events'] as List<dynamic>?)
              ?.map((e) => TimelineEvent.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$TimelineImplToJson(_$TimelineImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'events': instance.events,
      'startDate': instance.startDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'metadata': instance.metadata,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

_$TimelineEventImpl _$$TimelineEventImplFromJson(Map<String, dynamic> json) =>
    _$TimelineEventImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      eventDate: DateTime.parse(json['eventDate'] as String),
      involvedCharacterIds:
          (json['involvedCharacterIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      involvedLocationIds:
          (json['involvedLocationIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      chapterId: json['chapterId'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$TimelineEventImplToJson(_$TimelineEventImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'eventDate': instance.eventDate.toIso8601String(),
      'involvedCharacterIds': instance.involvedCharacterIds,
      'involvedLocationIds': instance.involvedLocationIds,
      'chapterId': instance.chapterId,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

_$ChapterReferenceImpl _$$ChapterReferenceImplFromJson(
  Map<String, dynamic> json,
) => _$ChapterReferenceImpl(
  id: json['id'] as String,
  title: json['title'] as String,
  chapterNumber: (json['chapterNumber'] as num?)?.toInt(),
  summary: json['summary'] as String?,
  characterIds:
      (json['characterIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  locationIds:
      (json['locationIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  plotPointIds:
      (json['plotPointIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  publishDate: json['publishDate'] == null
      ? null
      : DateTime.parse(json['publishDate'] as String),
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$$ChapterReferenceImplToJson(
  _$ChapterReferenceImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'chapterNumber': instance.chapterNumber,
  'summary': instance.summary,
  'characterIds': instance.characterIds,
  'locationIds': instance.locationIds,
  'plotPointIds': instance.plotPointIds,
  'publishDate': instance.publishDate?.toIso8601String(),
  'metadata': instance.metadata,
};
