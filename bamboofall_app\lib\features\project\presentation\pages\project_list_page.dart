import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

import '../../domain/entities/project.dart';
import '../../domain/usecases/manage_projects.dart';
import '../widgets/project_card.dart';
import '../widgets/project_filter_bar.dart';
import '../widgets/create_project_dialog.dart';

/// 项目列表页面
/// 显示所有项目并提供项目管理功能
class ProjectListPage extends ConsumerStatefulWidget {
  const ProjectListPage({super.key});

  @override
  ConsumerState<ProjectListPage> createState() => _ProjectListPageState();
}

class _ProjectListPageState extends ConsumerState<ProjectListPage> {
  String _searchQuery = '';
  ProjectStatus? _statusFilter;
  String? _tagFilter;
  ProjectSortBy _sortBy = ProjectSortBy.updatedAt;
  bool _sortAscending = false;
  bool _showFavoritesOnly = false;

  @override
  Widget build(BuildContext context) {
    return fluent.ScaffoldPage(
      header: _buildHeader(),
      content: _buildContent(),
    );
  }

  Widget _buildHeader() {
    return fluent.PageHeader(
      title: const Text('项目管理'),
      commandBar: fluent.CommandBar(
        primaryItems: [
          fluent.CommandBarButton(
            icon: const fluent.Icon(fluent.FluentIcons.add),
            label: const Text('新建项目'),
            onPressed: _showCreateProjectDialog,
          ),
          fluent.CommandBarButton(
            icon: const fluent.Icon(fluent.FluentIcons.refresh),
            label: const Text('刷新'),
            onPressed: () => setState(() {}),
          ),
        ],
        secondaryItems: [
          fluent.CommandBarButton(
            icon: const fluent.Icon(fluent.FluentIcons.import),
            label: const Text('导入项目'),
            onPressed: _showImportDialog,
          ),
          fluent.CommandBarButton(
            icon: const fluent.Icon(fluent.FluentIcons.settings),
            label: const Text('设置'),
            onPressed: _showSettingsDialog,
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      children: [
        // 筛选栏
        ProjectFilterBar(
          searchQuery: _searchQuery,
          statusFilter: _statusFilter,
          tagFilter: _tagFilter,
          sortBy: _sortBy,
          sortAscending: _sortAscending,
          showFavoritesOnly: _showFavoritesOnly,
          onSearchChanged: (query) => setState(() => _searchQuery = query),
          onStatusFilterChanged: (status) => setState(() => _statusFilter = status),
          onTagFilterChanged: (tag) => setState(() => _tagFilter = tag),
          onSortChanged: (sortBy, ascending) => setState(() {
            _sortBy = sortBy;
            _sortAscending = ascending;
          }),
          onFavoritesToggled: (showFavorites) => setState(() => _showFavoritesOnly = showFavorites),
        ),
        
        // 项目列表
        Expanded(
          child: _buildProjectList(),
        ),
      ],
    );
  }

  Widget _buildProjectList() {
    return FutureBuilder<List<Project>>(
      future: _loadProjects(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: fluent.ProgressRing(),
          );
        }

        if (snapshot.hasError) {
          return Center(
            child: fluent.InfoBar(
              title: const Text('加载失败'),
              content: Text('无法加载项目列表: ${snapshot.error}'),
              severity: fluent.InfoBarSeverity.error,
            ),
          );
        }

        final projects = snapshot.data ?? [];
        
        if (projects.isEmpty) {
          return _buildEmptyState();
        }

        return _buildProjectGrid(projects);
      },
    );
  }

  Widget _buildEmptyState() {
    if (_searchQuery.isNotEmpty || _statusFilter != null || _tagFilter != null || _showFavoritesOnly) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const fluent.Icon(
              fluent.FluentIcons.search,
              size: 48,
            ),
            const SizedBox(height: 16),
            const Text(
              '没有找到匹配的项目',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            const Text('尝试调整筛选条件或搜索关键词'),
            const SizedBox(height: 16),
            fluent.FilledButton(
              onPressed: _clearFilters,
              child: const Text('清除筛选'),
            ),
          ],
        ),
      );
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const fluent.Icon(
            fluent.FluentIcons.document_set,
            size: 48,
          ),
          const SizedBox(height: 16),
          const Text(
            '还没有项目',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          const Text('创建你的第一个写作项目开始创作吧！'),
          const SizedBox(height: 16),
          fluent.FilledButton(
            onPressed: _showCreateProjectDialog,
            child: const Text('创建项目'),
          ),
        ],
      ),
    );
  }

  Widget _buildProjectGrid(List<Project> projects) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.2,
        ),
        itemCount: projects.length,
        itemBuilder: (context, index) {
          final project = projects[index];
          return ProjectCard(
            project: project,
            onTap: () => _openProject(project),
            onFavoriteToggle: () => _toggleProjectFavorite(project),
            onEdit: () => _editProject(project),
            onArchive: () => _archiveProject(project),
            onDelete: () => _deleteProject(project),
            onDuplicate: () => _duplicateProject(project),
          );
        },
      ),
    );
  }

  Future<List<Project>> _loadProjects() async {
    final useCase = ref.read(manageProjectsUseCaseProvider);
    
    List<Project> projects;
    
    if (_showFavoritesOnly) {
      projects = await useCase.getFavoriteProjects();
    } else if (_statusFilter != null) {
      projects = await useCase.getAllProjects(); // TODO: 实现按状态过滤
    } else if (_tagFilter != null) {
      projects = await useCase.getProjectsByTag(_tagFilter!);
    } else if (_searchQuery.isNotEmpty) {
      projects = await useCase.searchProjects(_searchQuery);
    } else {
      projects = await useCase.getAllProjects();
    }

    // 应用排序
    projects.sort((a, b) {
      int comparison;
      switch (_sortBy) {
        case ProjectSortBy.name:
          comparison = a.name.compareTo(b.name);
          break;
        case ProjectSortBy.createdAt:
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case ProjectSortBy.updatedAt:
          comparison = a.updatedAt.compareTo(b.updatedAt);
          break;
        case ProjectSortBy.wordCount:
          comparison = a.statistics.totalWordCount.compareTo(b.statistics.totalWordCount);
          break;
        case ProjectSortBy.progress:
          comparison = a.completionPercentage.compareTo(b.completionPercentage);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return projects;
  }

  void _clearFilters() {
    setState(() {
      _searchQuery = '';
      _statusFilter = null;
      _tagFilter = null;
      _showFavoritesOnly = false;
    });
  }

  void _showCreateProjectDialog() {
    showDialog(
      context: context,
      builder: (context) => const CreateProjectDialog(),
    ).then((result) {
      if (result == true) {
        setState(() {}); // 刷新列表
      }
    });
  }

  void _showImportDialog() {
    // TODO: 实现导入项目对话框
    fluent.showDialog(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: const Text('导入项目'),
        content: const Text('导入项目功能正在开发中...'),
        actions: [
          fluent.Button(
            child: const Text('确定'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  void _showSettingsDialog() {
    // TODO: 实现设置对话框
    fluent.showDialog(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: const Text('项目设置'),
        content: const Text('项目设置功能正在开发中...'),
        actions: [
          fluent.Button(
            child: const Text('确定'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  void _openProject(Project project) async {
    final useCase = ref.read(manageProjectsUseCaseProvider);
    await useCase.accessProject(project.id);
    
    // TODO: 导航到项目详情页面或写作界面
    if (mounted) {
      fluent.showDialog(
        context: context,
        builder: (context) => fluent.ContentDialog(
          title: Text('打开项目: ${project.name}'),
          content: const Text('项目详情页面正在开发中...'),
          actions: [
            fluent.Button(
              child: const Text('确定'),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        ),
      );
    }
  }

  void _toggleProjectFavorite(Project project) async {
    try {
      final useCase = ref.read(manageProjectsUseCaseProvider);
      await useCase.toggleProjectFavorite(project.id);
      setState(() {}); // 刷新列表
    } catch (e) {
      if (mounted) {
        fluent.showDialog(
          context: context,
          builder: (context) => fluent.ContentDialog(
            title: const Text('操作失败'),
            content: Text('无法切换收藏状态: $e'),
            actions: [
              fluent.Button(
                child: const Text('确定'),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
        );
      }
    }
  }

  void _editProject(Project project) {
    // TODO: 实现编辑项目对话框
    fluent.showDialog(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: Text('编辑项目: ${project.name}'),
        content: const Text('编辑项目功能正在开发中...'),
        actions: [
          fluent.Button(
            child: const Text('确定'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  void _archiveProject(Project project) async {
    final result = await fluent.showDialog<bool>(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: const Text('归档项目'),
        content: Text('确定要归档项目"${project.name}"吗？归档后的项目可以在归档列表中找到。'),
        actions: [
          fluent.Button(
            child: const Text('取消'),
            onPressed: () => Navigator.of(context).pop(false),
          ),
          fluent.FilledButton(
            child: const Text('归档'),
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ],
      ),
    );

    if (result == true) {
      try {
        final useCase = ref.read(manageProjectsUseCaseProvider);
        await useCase.archiveProject(project.id, '用户手动归档');
        setState(() {}); // 刷新列表
      } catch (e) {
        if (mounted) {
          fluent.showDialog(
            context: context,
            builder: (context) => fluent.ContentDialog(
              title: const Text('归档失败'),
              content: Text('无法归档项目: $e'),
              actions: [
                fluent.Button(
                  child: const Text('确定'),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
          );
        }
      }
    }
  }

  void _deleteProject(Project project) async {
    final result = await fluent.showDialog<bool>(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: const Text('删除项目'),
        content: Text('确定要删除项目"${project.name}"吗？此操作无法撤销。'),
        actions: [
          fluent.Button(
            child: const Text('取消'),
            onPressed: () => Navigator.of(context).pop(false),
          ),
          fluent.FilledButton(
            child: const Text('删除'),
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ],
      ),
    );

    if (result == true) {
      try {
        final useCase = ref.read(manageProjectsUseCaseProvider);
        await useCase.deleteProject(project.id);
        setState(() {}); // 刷新列表
      } catch (e) {
        if (mounted) {
          fluent.showDialog(
            context: context,
            builder: (context) => fluent.ContentDialog(
              title: const Text('删除失败'),
              content: Text('无法删除项目: $e'),
              actions: [
                fluent.Button(
                  child: const Text('确定'),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
          );
        }
      }
    }
  }

  void _duplicateProject(Project project) async {
    final nameController = TextEditingController(text: '${project.name} - 副本');
    
    final result = await fluent.showDialog<String>(
      context: context,
      builder: (context) => fluent.ContentDialog(
        title: const Text('复制项目'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('复制项目"${project.name}"'),
            const SizedBox(height: 16),
            fluent.TextBox(
              controller: nameController,
              placeholder: '新项目名称',
            ),
          ],
        ),
        actions: [
          fluent.Button(
            child: const Text('取消'),
            onPressed: () => Navigator.of(context).pop(),
          ),
          fluent.FilledButton(
            child: const Text('复制'),
            onPressed: () => Navigator.of(context).pop(nameController.text),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      try {
        final useCase = ref.read(manageProjectsUseCaseProvider);
        await useCase.duplicateProject(project.id, result);
        setState(() {}); // 刷新列表
      } catch (e) {
        if (mounted) {
          fluent.showDialog(
            context: context,
            builder: (context) => fluent.ContentDialog(
              title: const Text('复制失败'),
              content: Text('无法复制项目: $e'),
              actions: [
                fluent.Button(
                  child: const Text('确定'),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
          );
        }
      }
    }
  }
}

/// 项目排序方式
enum ProjectSortBy {
  name,
  createdAt,
  updatedAt,
  wordCount,
  progress,
}

/// 项目排序方式扩展
extension ProjectSortByExtension on ProjectSortBy {
  String get displayName {
    switch (this) {
      case ProjectSortBy.name:
        return '名称';
      case ProjectSortBy.createdAt:
        return '创建时间';
      case ProjectSortBy.updatedAt:
        return '修改时间';
      case ProjectSortBy.wordCount:
        return '字数';
      case ProjectSortBy.progress:
        return '进度';
    }
  }
}