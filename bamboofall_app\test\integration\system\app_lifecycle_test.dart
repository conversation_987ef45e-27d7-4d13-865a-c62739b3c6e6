import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:bamboofall_app/main.dart';
import 'package:bamboofall_app/core/storage/database.dart';
import 'package:bamboofall_app/core/providers/app_providers.dart';
import 'package:bamboofall_app/features/project/domain/repositories/project_repository.dart';
import 'package:bamboofall_app/features/writing/domain/repositories/chapter_repository.dart';
import 'package:bamboofall_app/features/ai_integration/domain/repositories/llm_repository.dart';
import 'package:bamboofall_app/features/bible/domain/repositories/bible_repository.dart';

import 'app_lifecycle_test.mocks.dart';

@GenerateMocks([
  LocalDatabase,
  ProjectRepository,
  ChapterRepository,
  LLMRepository,
  BibleRepository,
])
void main() {
  group('App Lifecycle Integration Tests', () {
    late MockLocalDatabase mockDatabase;
    late MockProjectRepository mockProjectRepository;
    late MockChapterRepository mockChapterRepository;
    late MockLLMRepository mockLLMRepository;
    late MockBibleRepository mockBibleRepository;

    setUp(() {
      mockDatabase = MockLocalDatabase();
      mockProjectRepository = MockProjectRepository();
      mockChapterRepository = MockChapterRepository();
      mockLLMRepository = MockLLMRepository();
      mockBibleRepository = MockBibleRepository();
    });

    group('App Initialization', () {
      testWidgets('should initialize app with proper dependencies', (WidgetTester tester) async {
        // Arrange
        when(mockDatabase.initialize()).thenAnswer((_) async => {});
        when(mockProjectRepository.getProjects()).thenAnswer((_) async => []);
        when(mockLLMRepository.getAvailableModels()).thenAnswer((_) async => []);

        // Create provider overrides
        final container = ProviderContainer(
          overrides: [
            databaseProvider.overrideWithValue(mockDatabase),
            projectRepositoryProvider.overrideWithValue(mockProjectRepository),
            chapterRepositoryProvider.overrideWithValue(mockChapterRepository),
            llmRepositoryProvider.overrideWithValue(mockLLMRepository),
            bibleRepositoryProvider.overrideWithValue(mockBibleRepository),
          ],
        );

        // Act
        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: const BambooFallApp(),
          ),
        );

        // Wait for initialization
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(MaterialApp), findsOneWidget);
        
        // Verify initialization calls
        verify(mockDatabase.initialize()).called(1);
        verify(mockProjectRepository.getProjects()).called(1);
        verify(mockLLMRepository.getAvailableModels()).called(1);

        container.dispose();
      });

      testWidgets('should handle initialization failures gracefully', (WidgetTester tester) async {
        // Arrange
        when(mockDatabase.initialize()).thenThrow(Exception('数据库初始化失败'));

        final container = ProviderContainer(
          overrides: [
            databaseProvider.overrideWithValue(mockDatabase),
            projectRepositoryProvider.overrideWithValue(mockProjectRepository),
            chapterRepositoryProvider.overrideWithValue(mockChapterRepository),
            llmRepositoryProvider.overrideWithValue(mockLLMRepository),
            bibleRepositoryProvider.overrideWithValue(mockBibleRepository),
          ],
        );

        // Act
        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: const BambooFallApp(),
          ),
        );

        await tester.pumpAndSettle();

        // Assert - App should still render with error handling
        expect(find.byType(MaterialApp), findsOneWidget);
        
        // Should show error message or fallback UI
        expect(find.textContaining('错误'), findsWidgets);

        verify(mockDatabase.initialize()).called(1);
        container.dispose();
      });

      testWidgets('should load user preferences and settings', (WidgetTester tester) async {
        // Arrange
        when(mockDatabase.initialize()).thenAnswer((_) async => {});
        when(mockProjectRepository.getProjects()).thenAnswer((_) async => []);
        when(mockLLMRepository.getAvailableModels()).thenAnswer((_) async => []);

        final container = ProviderContainer(
          overrides: [
            databaseProvider.overrideWithValue(mockDatabase),
            projectRepositoryProvider.overrideWithValue(mockProjectRepository),
            chapterRepositoryProvider.overrideWithValue(mockChapterRepository),
            llmRepositoryProvider.overrideWithValue(mockLLMRepository),
            bibleRepositoryProvider.overrideWithValue(mockBibleRepository),
          ],
        );

        // Act
        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: const BambooFallApp(),
          ),
        );

        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(MaterialApp), findsOneWidget);

        // Verify theme and settings are applied
        final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
        expect(materialApp.theme, isNotNull);
        expect(materialApp.darkTheme, isNotNull);

        container.dispose();
      });
    });

    group('State Management', () {
      testWidgets('should maintain state across navigation', (WidgetTester tester) async {
        // Arrange
        when(mockDatabase.initialize()).thenAnswer((_) async => {});
        when(mockProjectRepository.getProjects()).thenAnswer((_) async => []);
        when(mockLLMRepository.getAvailableModels()).thenAnswer((_) async => []);

        final container = ProviderContainer(
          overrides: [
            databaseProvider.overrideWithValue(mockDatabase),
            projectRepositoryProvider.overrideWithValue(mockProjectRepository),
            chapterRepositoryProvider.overrideWithValue(mockChapterRepository),
            llmRepositoryProvider.overrideWithValue(mockLLMRepository),
            bibleRepositoryProvider.overrideWithValue(mockBibleRepository),
          ],
        );

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: const BambooFallApp(),
          ),
        );

        await tester.pumpAndSettle();

        // Act - Navigate to different screens
        // Find and tap navigation elements
        final projectsTab = find.text('项目');
        if (projectsTab.evaluate().isNotEmpty) {
          await tester.tap(projectsTab);
          await tester.pumpAndSettle();
        }

        final writingTab = find.text('写作');
        if (writingTab.evaluate().isNotEmpty) {
          await tester.tap(writingTab);
          await tester.pumpAndSettle();
        }

        // Assert - State should be maintained
        expect(find.byType(MaterialApp), findsOneWidget);

        container.dispose();
      });

      testWidgets('should handle provider state updates correctly', (WidgetTester tester) async {
        // Arrange
        when(mockDatabase.initialize()).thenAnswer((_) async => {});
        when(mockProjectRepository.getProjects()).thenAnswer((_) async => []);
        when(mockLLMRepository.getAvailableModels()).thenAnswer((_) async => []);

        final container = ProviderContainer(
          overrides: [
            databaseProvider.overrideWithValue(mockDatabase),
            projectRepositoryProvider.overrideWithValue(mockProjectRepository),
            chapterRepositoryProvider.overrideWithValue(mockChapterRepository),
            llmRepositoryProvider.overrideWithValue(mockLLMRepository),
            bibleRepositoryProvider.overrideWithValue(mockBibleRepository),
          ],
        );

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: const BambooFallApp(),
          ),
        );

        await tester.pumpAndSettle();

        // Act - Trigger state updates
        // This would typically involve user interactions that update providers

        // Assert
        expect(find.byType(MaterialApp), findsOneWidget);

        container.dispose();
      });

      testWidgets('should handle memory pressure gracefully', (WidgetTester tester) async {
        // Arrange
        when(mockDatabase.initialize()).thenAnswer((_) async => {});
        when(mockProjectRepository.getProjects()).thenAnswer((_) async => []);
        when(mockLLMRepository.getAvailableModels()).thenAnswer((_) async => []);

        final container = ProviderContainer(
          overrides: [
            databaseProvider.overrideWithValue(mockDatabase),
            projectRepositoryProvider.overrideWithValue(mockProjectRepository),
            chapterRepositoryProvider.overrideWithValue(mockChapterRepository),
            llmRepositoryProvider.overrideWithValue(mockLLMRepository),
            bibleRepositoryProvider.overrideWithValue(mockBibleRepository),
          ],
        );

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: const BambooFallApp(),
          ),
        );

        await tester.pumpAndSettle();

        // Act - Simulate memory pressure
        await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
          'flutter/system',
          const StandardMethodCodec().encodeMethodCall(
            const MethodCall('System.requestAppExitResponse', <String, dynamic>{
              'type': 'required',
            }),
          ),
          (data) {},
        );

        await tester.pumpAndSettle();

        // Assert - App should handle memory pressure
        expect(find.byType(MaterialApp), findsOneWidget);

        container.dispose();
      });
    });

    group('Background Processing', () {
      testWidgets('should handle background tasks correctly', (WidgetTester tester) async {
        // Arrange
        when(mockDatabase.initialize()).thenAnswer((_) async => {});
        when(mockProjectRepository.getProjects()).thenAnswer((_) async => []);
        when(mockLLMRepository.getAvailableModels()).thenAnswer((_) async => []);

        final container = ProviderContainer(
          overrides: [
            databaseProvider.overrideWithValue(mockDatabase),
            projectRepositoryProvider.overrideWithValue(mockProjectRepository),
            chapterRepositoryProvider.overrideWithValue(mockChapterRepository),
            llmRepositoryProvider.overrideWithValue(mockLLMRepository),
            bibleRepositoryProvider.overrideWithValue(mockBibleRepository),
          ],
        );

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: const BambooFallApp(),
          ),
        );

        await tester.pumpAndSettle();

        // Act - Simulate app going to background
        await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
          'flutter/lifecycle',
          const StringCodec().encode('AppLifecycleState.paused'),
          (data) {},
        );

        await tester.pumpAndSettle();

        // Simulate app coming back to foreground
        await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
          'flutter/lifecycle',
          const StringCodec().encode('AppLifecycleState.resumed'),
          (data) {},
        );

        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(MaterialApp), findsOneWidget);

        container.dispose();
      });

      testWidgets('should save state when app is paused', (WidgetTester tester) async {
        // Arrange
        when(mockDatabase.initialize()).thenAnswer((_) async => {});
        when(mockProjectRepository.getProjects()).thenAnswer((_) async => []);
        when(mockLLMRepository.getAvailableModels()).thenAnswer((_) async => []);

        final container = ProviderContainer(
          overrides: [
            databaseProvider.overrideWithValue(mockDatabase),
            projectRepositoryProvider.overrideWithValue(mockProjectRepository),
            chapterRepositoryProvider.overrideWithValue(mockChapterRepository),
            llmRepositoryProvider.overrideWithValue(mockLLMRepository),
            bibleRepositoryProvider.overrideWithValue(mockBibleRepository),
          ],
        );

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: const BambooFallApp(),
          ),
        );

        await tester.pumpAndSettle();

        // Act - Simulate app pause
        await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
          'flutter/lifecycle',
          const StringCodec().encode('AppLifecycleState.paused'),
          (data) {},
        );

        await tester.pumpAndSettle();

        // Assert - State should be saved
        expect(find.byType(MaterialApp), findsOneWidget);

        container.dispose();
      });
    });

    group('Error Recovery', () {
      testWidgets('should recover from provider errors', (WidgetTester tester) async {
        // Arrange
        when(mockDatabase.initialize()).thenAnswer((_) async => {});
        when(mockProjectRepository.getProjects()).thenThrow(Exception('临时网络错误'));
        when(mockLLMRepository.getAvailableModels()).thenAnswer((_) async => []);

        final container = ProviderContainer(
          overrides: [
            databaseProvider.overrideWithValue(mockDatabase),
            projectRepositoryProvider.overrideWithValue(mockProjectRepository),
            chapterRepositoryProvider.overrideWithValue(mockChapterRepository),
            llmRepositoryProvider.overrideWithValue(mockLLMRepository),
            bibleRepositoryProvider.overrideWithValue(mockBibleRepository),
          ],
        );

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: const BambooFallApp(),
          ),
        );

        await tester.pumpAndSettle();

        // Act - Fix the error and retry
        when(mockProjectRepository.getProjects()).thenAnswer((_) async => []);

        // Trigger a refresh or retry mechanism
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(MaterialApp), findsOneWidget);

        container.dispose();
      });

      testWidgets('should handle widget rebuild after errors', (WidgetTester tester) async {
        // Arrange
        when(mockDatabase.initialize()).thenAnswer((_) async => {});
        when(mockProjectRepository.getProjects()).thenAnswer((_) async => []);
        when(mockLLMRepository.getAvailableModels()).thenAnswer((_) async => []);

        final container = ProviderContainer(
          overrides: [
            databaseProvider.overrideWithValue(mockDatabase),
            projectRepositoryProvider.overrideWithValue(mockProjectRepository),
            chapterRepositoryProvider.overrideWithValue(mockChapterRepository),
            llmRepositoryProvider.overrideWithValue(mockLLMRepository),
            bibleRepositoryProvider.overrideWithValue(mockBibleRepository),
          ],
        );

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: const BambooFallApp(),
          ),
        );

        await tester.pumpAndSettle();

        // Act - Force widget rebuild
        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: const BambooFallApp(),
          ),
        );

        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(MaterialApp), findsOneWidget);

        container.dispose();
      });
    });

    group('Resource Management', () {
      testWidgets('should properly dispose resources on app termination', (WidgetTester tester) async {
        // Arrange
        when(mockDatabase.initialize()).thenAnswer((_) async => {});
        when(mockDatabase.close()).thenAnswer((_) async => {});
        when(mockProjectRepository.getProjects()).thenAnswer((_) async => []);
        when(mockLLMRepository.getAvailableModels()).thenAnswer((_) async => []);

        final container = ProviderContainer(
          overrides: [
            databaseProvider.overrideWithValue(mockDatabase),
            projectRepositoryProvider.overrideWithValue(mockProjectRepository),
            chapterRepositoryProvider.overrideWithValue(mockChapterRepository),
            llmRepositoryProvider.overrideWithValue(mockLLMRepository),
            bibleRepositoryProvider.overrideWithValue(mockBibleRepository),
          ],
        );

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: const BambooFallApp(),
          ),
        );

        await tester.pumpAndSettle();

        // Act - Simulate app termination
        container.dispose();

        // Assert - Resources should be cleaned up
        // Note: In a real app, we would verify that dispose methods are called
        verify(mockDatabase.initialize()).called(1);
      });

      testWidgets('should handle memory leaks prevention', (WidgetTester tester) async {
        // Arrange
        when(mockDatabase.initialize()).thenAnswer((_) async => {});
        when(mockProjectRepository.getProjects()).thenAnswer((_) async => []);
        when(mockLLMRepository.getAvailableModels()).thenAnswer((_) async => []);

        final container = ProviderContainer(
          overrides: [
            databaseProvider.overrideWithValue(mockDatabase),
            projectRepositoryProvider.overrideWithValue(mockProjectRepository),
            chapterRepositoryProvider.overrideWithValue(mockChapterRepository),
            llmRepositoryProvider.overrideWithValue(mockLLMRepository),
            bibleRepositoryProvider.overrideWithValue(mockBibleRepository),
          ],
        );

        // Act - Create and dispose multiple instances
        for (int i = 0; i < 3; i++) {
          await tester.pumpWidget(
            UncontrolledProviderScope(
              container: container,
              child: const BambooFallApp(),
            ),
          );

          await tester.pumpAndSettle();

          // Simulate navigation and state changes
          await tester.pumpWidget(Container());
          await tester.pumpAndSettle();
        }

        // Assert - No memory leaks should occur
        expect(find.byType(Container), findsOneWidget);

        container.dispose();
      });
    });

    group('Performance Monitoring', () {
      testWidgets('should maintain acceptable startup time', (WidgetTester tester) async {
        // Arrange
        when(mockDatabase.initialize()).thenAnswer((_) async => {});
        when(mockProjectRepository.getProjects()).thenAnswer((_) async => []);
        when(mockLLMRepository.getAvailableModels()).thenAnswer((_) async => []);

        final container = ProviderContainer(
          overrides: [
            databaseProvider.overrideWithValue(mockDatabase),
            projectRepositoryProvider.overrideWithValue(mockProjectRepository),
            chapterRepositoryProvider.overrideWithValue(mockChapterRepository),
            llmRepositoryProvider.overrideWithValue(mockLLMRepository),
            bibleRepositoryProvider.overrideWithValue(mockBibleRepository),
          ],
        );

        // Act - Measure startup time
        final stopwatch = Stopwatch()..start();

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: const BambooFallApp(),
          ),
        );

        await tester.pumpAndSettle();

        stopwatch.stop();

        // Assert - Startup should be reasonably fast
        expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // Less than 5 seconds
        expect(find.byType(MaterialApp), findsOneWidget);

        container.dispose();
      });

      testWidgets('should handle large datasets efficiently', (WidgetTester tester) async {
        // Arrange
        final largeProjectList = List.generate(100, (index) => 
          Project(
            id: 'project-$index',
            title: '项目 $index',
            description: '描述 $index',
            genre: ProjectGenre.fantasy,
            status: ProjectStatus.active,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            settings: const ProjectSettings(),
          ),
        );

        when(mockDatabase.initialize()).thenAnswer((_) async => {});
        when(mockProjectRepository.getProjects()).thenAnswer((_) async => largeProjectList);
        when(mockLLMRepository.getAvailableModels()).thenAnswer((_) async => []);

        final container = ProviderContainer(
          overrides: [
            databaseProvider.overrideWithValue(mockDatabase),
            projectRepositoryProvider.overrideWithValue(mockProjectRepository),
            chapterRepositoryProvider.overrideWithValue(mockChapterRepository),
            llmRepositoryProvider.overrideWithValue(mockLLMRepository),
            bibleRepositoryProvider.overrideWithValue(mockBibleRepository),
          ],
        );

        // Act
        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: const BambooFallApp(),
          ),
        );

        await tester.pumpAndSettle();

        // Assert - Should handle large datasets without performance issues
        expect(find.byType(MaterialApp), findsOneWidget);

        container.dispose();
      });
    });
  });
}