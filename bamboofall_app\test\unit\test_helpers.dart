import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

/// 测试辅助工具类
/// 提供通用的测试工具和mock对象创建方法
class TestHelpers {
  /// 创建测试用的DateTime对象
  static DateTime createTestDateTime([int? year, int? month, int? day]) {
    return DateTime(year ?? 2024, month ?? 1, day ?? 1);
  }

  /// 创建测试用的ID
  static String createTestId([String? prefix]) {
    return '${prefix ?? 'test'}-${DateTime.now().millisecondsSinceEpoch}';
  }

  /// 验证两个DateTime是否在指定的时间范围内相等
  static bool isDateTimeNear(DateTime actual, DateTime expected, {Duration tolerance = const Duration(seconds: 1)}) {
    final difference = actual.difference(expected).abs();
    return difference <= tolerance;
  }

  /// 创建测试用的Map数据
  static Map<String, dynamic> createTestData({
    String? id,
    String? name,
    Map<String, dynamic>? additionalData,
  }) {
    final data = <String, dynamic>{
      'id': id ?? createTestId(),
      'name': name ?? 'Test Item',
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };

    if (additionalData != null) {
      data.addAll(additionalData);
    }

    return data;
  }

  /// 验证Mock对象的方法调用
  static void verifyMethodCall(dynamic mock, String methodName, {int times = 1}) {
    // 这里可以添加更复杂的验证逻辑
    // 目前只是一个占位符，实际使用时需要根据具体的Mock对象来实现
  }

  /// 创建测试用的异步延迟
  static Future<void> delay([Duration? duration]) {
    return Future.delayed(duration ?? const Duration(milliseconds: 10));
  }

  /// 验证列表是否按指定字段排序
  static bool isListSorted<T>(List<T> list, Comparable Function(T) keyExtractor, {bool ascending = true}) {
    if (list.length <= 1) return true;

    for (int i = 0; i < list.length - 1; i++) {
      final current = keyExtractor(list[i]);
      final next = keyExtractor(list[i + 1]);
      
      final comparison = current.compareTo(next);
      if (ascending && comparison > 0) return false;
      if (!ascending && comparison < 0) return false;
    }

    return true;
  }

  /// 创建测试用的错误对象
  static Exception createTestError([String? message]) {
    return Exception(message ?? 'Test error');
  }

  /// 验证字符串是否为有效的UUID格式
  static bool isValidUuid(String? value) {
    if (value == null) return false;
    
    final uuidRegex = RegExp(
      r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$'
    );
    
    return uuidRegex.hasMatch(value);
  }

  /// 创建测试用的分页数据
  static Map<String, dynamic> createPaginatedData<T>({
    required List<T> items,
    int page = 1,
    int pageSize = 10,
    int? total,
  }) {
    return {
      'items': items,
      'page': page,
      'page_size': pageSize,
      'total': total ?? items.length,
      'has_next': (page * pageSize) < (total ?? items.length),
      'has_previous': page > 1,
    };
  }

  /// 验证对象的必需字段
  static void verifyRequiredFields(Map<String, dynamic> data, List<String> requiredFields) {
    for (final field in requiredFields) {
      expect(data.containsKey(field), true, reason: 'Missing required field: $field');
      expect(data[field], isNotNull, reason: 'Required field $field is null');
      
      if (data[field] is String) {
        expect((data[field] as String).isNotEmpty, true, reason: 'Required field $field is empty');
      }
    }
  }

  /// 创建测试用的配置数据
  static Map<String, dynamic> createTestConfig({
    String? environment,
    bool? debugMode,
    Map<String, dynamic>? customSettings,
  }) {
    final config = <String, dynamic>{
      'environment': environment ?? 'test',
      'debug_mode': debugMode ?? true,
      'version': '1.0.0-test',
      'build_number': 1,
    };

    if (customSettings != null) {
      config.addAll(customSettings);
    }

    return config;
  }

  /// 验证回调函数是否被正确调用
  static void verifyCallback<T>(
    void Function() callback,
    T expectedResult, {
    Duration? timeout,
  }) {
    var callbackCalled = false;
    
    void wrappedCallback() {
      callbackCalled = true;
      callback();
    }

    // 执行测试逻辑
    wrappedCallback();
    
    expect(callbackCalled, true, reason: 'Callback was not called');
  }

  /// 创建测试用的文件路径
  static String createTestFilePath([String? filename, String? extension]) {
    final name = filename ?? 'test_file';
    final ext = extension ?? 'txt';
    return '/test/path/$name.$ext';
  }

  /// 验证字符串是否符合特定格式
  static bool matchesPattern(String value, String pattern) {
    final regex = RegExp(pattern);
    return regex.hasMatch(value);
  }

  /// 创建测试用的网络响应数据
  static Map<String, dynamic> createNetworkResponse({
    int statusCode = 200,
    String? message,
    Map<String, dynamic>? data,
    Map<String, String>? headers,
  }) {
    return {
      'status_code': statusCode,
      'message': message ?? 'Success',
      'data': data ?? {},
      'headers': headers ?? {'content-type': 'application/json'},
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// 验证数据结构的完整性
  static void verifyDataStructure(
    Map<String, dynamic> actual,
    Map<String, Type> expectedStructure,
  ) {
    for (final entry in expectedStructure.entries) {
      final key = entry.key;
      final expectedType = entry.value;
      
      expect(actual.containsKey(key), true, reason: 'Missing key: $key');
      expect(actual[key].runtimeType, expectedType, reason: 'Wrong type for key $key');
    }
  }

  /// 创建测试用的用户数据
  static Map<String, dynamic> createTestUser({
    String? id,
    String? name,
    String? email,
    bool? isActive,
  }) {
    return {
      'id': id ?? createTestId('user'),
      'name': name ?? 'Test User',
      'email': email ?? '<EMAIL>',
      'is_active': isActive ?? true,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };
  }

  /// 验证异步操作的执行时间
  static Future<void> verifyExecutionTime(
    Future<void> Function() operation,
    Duration expectedDuration, {
    Duration tolerance = const Duration(milliseconds: 100),
  }) async {
    final stopwatch = Stopwatch()..start();
    
    await operation();
    
    stopwatch.stop();
    final actualDuration = stopwatch.elapsed;
    
    final minDuration = expectedDuration.inMilliseconds - tolerance.inMilliseconds;
    final maxDuration = expectedDuration.inMilliseconds + tolerance.inMilliseconds;
    
    expect(
      actualDuration.inMilliseconds,
      inInclusiveRange(minDuration, maxDuration),
      reason: 'Operation took ${actualDuration.inMilliseconds}ms, expected ${expectedDuration.inMilliseconds}ms ± ${tolerance.inMilliseconds}ms',
    );
  }
}