// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in bamboofall_app/test/integration/user_flows/writing_workflow_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:bamboofall_app/features/project/domain/entities/project.dart'
    as _i2;
import 'package:bamboofall_app/features/project/domain/repositories/project_repository.dart'
    as _i3;
import 'package:bamboofall_app/features/writing/domain/entities/chapter.dart'
    as _i4;
import 'package:bamboofall_app/features/writing/domain/repositories/chapter_repository.dart'
    as _i6;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeProject_0 extends _i1.SmartFake implements _i2.Project {
  _FakeProject_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeProjectTemplate_1 extends _i1.SmartFake
    implements _i3.ProjectTemplate {
  _FakeProjectTemplate_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeChapter_2 extends _i1.SmartFake implements _i4.Chapter {
  _FakeChapter_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [ProjectRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockProjectRepository extends _i1.Mock implements _i3.ProjectRepository {
  MockProjectRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<List<_i2.Project>> getAllProjects() =>
      (super.noSuchMethod(
            Invocation.method(#getAllProjects, []),
            returnValue: _i5.Future<List<_i2.Project>>.value(<_i2.Project>[]),
          )
          as _i5.Future<List<_i2.Project>>);

  @override
  _i5.Future<_i2.Project?> getProjectById(String? projectId) =>
      (super.noSuchMethod(
            Invocation.method(#getProjectById, [projectId]),
            returnValue: _i5.Future<_i2.Project?>.value(),
          )
          as _i5.Future<_i2.Project?>);

  @override
  _i5.Future<List<_i2.Project>> getProjectsByStatus(
    _i2.ProjectStatus? status,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getProjectsByStatus, [status]),
            returnValue: _i5.Future<List<_i2.Project>>.value(<_i2.Project>[]),
          )
          as _i5.Future<List<_i2.Project>>);

  @override
  _i5.Future<List<_i2.Project>> getFavoriteProjects() =>
      (super.noSuchMethod(
            Invocation.method(#getFavoriteProjects, []),
            returnValue: _i5.Future<List<_i2.Project>>.value(<_i2.Project>[]),
          )
          as _i5.Future<List<_i2.Project>>);

  @override
  _i5.Future<List<_i2.Project>> getProjectsByTag(String? tag) =>
      (super.noSuchMethod(
            Invocation.method(#getProjectsByTag, [tag]),
            returnValue: _i5.Future<List<_i2.Project>>.value(<_i2.Project>[]),
          )
          as _i5.Future<List<_i2.Project>>);

  @override
  _i5.Future<List<_i2.Project>> searchProjects(String? query) =>
      (super.noSuchMethod(
            Invocation.method(#searchProjects, [query]),
            returnValue: _i5.Future<List<_i2.Project>>.value(<_i2.Project>[]),
          )
          as _i5.Future<List<_i2.Project>>);

  @override
  _i5.Future<_i2.Project> createProject(_i2.Project? project) =>
      (super.noSuchMethod(
            Invocation.method(#createProject, [project]),
            returnValue: _i5.Future<_i2.Project>.value(
              _FakeProject_0(
                this,
                Invocation.method(#createProject, [project]),
              ),
            ),
          )
          as _i5.Future<_i2.Project>);

  @override
  _i5.Future<_i2.Project> updateProject(_i2.Project? project) =>
      (super.noSuchMethod(
            Invocation.method(#updateProject, [project]),
            returnValue: _i5.Future<_i2.Project>.value(
              _FakeProject_0(
                this,
                Invocation.method(#updateProject, [project]),
              ),
            ),
          )
          as _i5.Future<_i2.Project>);

  @override
  _i5.Future<void> deleteProject(String? projectId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteProject, [projectId]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<_i2.Project> archiveProject(String? projectId, String? reason) =>
      (super.noSuchMethod(
            Invocation.method(#archiveProject, [projectId, reason]),
            returnValue: _i5.Future<_i2.Project>.value(
              _FakeProject_0(
                this,
                Invocation.method(#archiveProject, [projectId, reason]),
              ),
            ),
          )
          as _i5.Future<_i2.Project>);

  @override
  _i5.Future<_i2.Project> restoreProject(String? projectId) =>
      (super.noSuchMethod(
            Invocation.method(#restoreProject, [projectId]),
            returnValue: _i5.Future<_i2.Project>.value(
              _FakeProject_0(
                this,
                Invocation.method(#restoreProject, [projectId]),
              ),
            ),
          )
          as _i5.Future<_i2.Project>);

  @override
  _i5.Future<Map<_i2.ProjectStatus, int>> getProjectStatistics() =>
      (super.noSuchMethod(
            Invocation.method(#getProjectStatistics, []),
            returnValue: _i5.Future<Map<_i2.ProjectStatus, int>>.value(
              <_i2.ProjectStatus, int>{},
            ),
          )
          as _i5.Future<Map<_i2.ProjectStatus, int>>);

  @override
  _i5.Future<List<_i2.Project>> getRecentProjects({int? limit = 10}) =>
      (super.noSuchMethod(
            Invocation.method(#getRecentProjects, [], {#limit: limit}),
            returnValue: _i5.Future<List<_i2.Project>>.value(<_i2.Project>[]),
          )
          as _i5.Future<List<_i2.Project>>);

  @override
  _i5.Future<void> updateProjectAccessTime(String? projectId) =>
      (super.noSuchMethod(
            Invocation.method(#updateProjectAccessTime, [projectId]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> cleanupDeletedProjects({
    Duration? olderThan = const Duration(days: 30),
  }) =>
      (super.noSuchMethod(
            Invocation.method(#cleanupDeletedProjects, [], {
              #olderThan: olderThan,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<Map<String, dynamic>> exportProject(String? projectId) =>
      (super.noSuchMethod(
            Invocation.method(#exportProject, [projectId]),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<_i2.Project> importProject(Map<String, dynamic>? projectData) =>
      (super.noSuchMethod(
            Invocation.method(#importProject, [projectData]),
            returnValue: _i5.Future<_i2.Project>.value(
              _FakeProject_0(
                this,
                Invocation.method(#importProject, [projectData]),
              ),
            ),
          )
          as _i5.Future<_i2.Project>);

  @override
  _i5.Future<_i2.Project> duplicateProject(
    String? projectId,
    String? newName,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#duplicateProject, [projectId, newName]),
            returnValue: _i5.Future<_i2.Project>.value(
              _FakeProject_0(
                this,
                Invocation.method(#duplicateProject, [projectId, newName]),
              ),
            ),
          )
          as _i5.Future<_i2.Project>);

  @override
  _i5.Future<List<_i3.ProjectTemplate>> getProjectTemplates() =>
      (super.noSuchMethod(
            Invocation.method(#getProjectTemplates, []),
            returnValue: _i5.Future<List<_i3.ProjectTemplate>>.value(
              <_i3.ProjectTemplate>[],
            ),
          )
          as _i5.Future<List<_i3.ProjectTemplate>>);

  @override
  _i5.Future<_i3.ProjectTemplate> createProjectTemplate(
    _i3.ProjectTemplate? template,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createProjectTemplate, [template]),
            returnValue: _i5.Future<_i3.ProjectTemplate>.value(
              _FakeProjectTemplate_1(
                this,
                Invocation.method(#createProjectTemplate, [template]),
              ),
            ),
          )
          as _i5.Future<_i3.ProjectTemplate>);

  @override
  _i5.Future<void> deleteProjectTemplate(String? templateId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteProjectTemplate, [templateId]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<_i2.Project> createProjectFromTemplate(
    String? templateId,
    String? projectName,
    String? createdBy,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createProjectFromTemplate, [
              templateId,
              projectName,
              createdBy,
            ]),
            returnValue: _i5.Future<_i2.Project>.value(
              _FakeProject_0(
                this,
                Invocation.method(#createProjectFromTemplate, [
                  templateId,
                  projectName,
                  createdBy,
                ]),
              ),
            ),
          )
          as _i5.Future<_i2.Project>);
}

/// A class which mocks [ChapterRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockChapterRepository extends _i1.Mock implements _i6.ChapterRepository {
  MockChapterRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<List<_i4.Chapter>> getAllChapters() =>
      (super.noSuchMethod(
            Invocation.method(#getAllChapters, []),
            returnValue: _i5.Future<List<_i4.Chapter>>.value(<_i4.Chapter>[]),
          )
          as _i5.Future<List<_i4.Chapter>>);

  @override
  _i5.Future<_i4.Chapter?> getChapterById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getChapterById, [id]),
            returnValue: _i5.Future<_i4.Chapter?>.value(),
          )
          as _i5.Future<_i4.Chapter?>);

  @override
  _i5.Future<List<_i4.Chapter>> getChildChapters(String? parentId) =>
      (super.noSuchMethod(
            Invocation.method(#getChildChapters, [parentId]),
            returnValue: _i5.Future<List<_i4.Chapter>>.value(<_i4.Chapter>[]),
          )
          as _i5.Future<List<_i4.Chapter>>);

  @override
  _i5.Future<_i4.Chapter> createChapter(_i4.Chapter? chapter) =>
      (super.noSuchMethod(
            Invocation.method(#createChapter, [chapter]),
            returnValue: _i5.Future<_i4.Chapter>.value(
              _FakeChapter_2(
                this,
                Invocation.method(#createChapter, [chapter]),
              ),
            ),
          )
          as _i5.Future<_i4.Chapter>);

  @override
  _i5.Future<_i4.Chapter> updateChapter(_i4.Chapter? chapter) =>
      (super.noSuchMethod(
            Invocation.method(#updateChapter, [chapter]),
            returnValue: _i5.Future<_i4.Chapter>.value(
              _FakeChapter_2(
                this,
                Invocation.method(#updateChapter, [chapter]),
              ),
            ),
          )
          as _i5.Future<_i4.Chapter>);

  @override
  _i5.Future<void> deleteChapter(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteChapter, [id]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> updateChapters(List<_i4.Chapter>? chapters) =>
      (super.noSuchMethod(
            Invocation.method(#updateChapters, [chapters]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<List<_i4.Chapter>> getChaptersByStatus(
    _i4.ChapterStatus? status,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getChaptersByStatus, [status]),
            returnValue: _i5.Future<List<_i4.Chapter>>.value(<_i4.Chapter>[]),
          )
          as _i5.Future<List<_i4.Chapter>>);

  @override
  _i5.Future<List<_i4.Chapter>> getChaptersByTag(String? tag) =>
      (super.noSuchMethod(
            Invocation.method(#getChaptersByTag, [tag]),
            returnValue: _i5.Future<List<_i4.Chapter>>.value(<_i4.Chapter>[]),
          )
          as _i5.Future<List<_i4.Chapter>>);

  @override
  _i5.Future<List<_i4.Chapter>> searchChapters(String? query) =>
      (super.noSuchMethod(
            Invocation.method(#searchChapters, [query]),
            returnValue: _i5.Future<List<_i4.Chapter>>.value(<_i4.Chapter>[]),
          )
          as _i5.Future<List<_i4.Chapter>>);

  @override
  _i5.Future<int> getChapterCount() =>
      (super.noSuchMethod(
            Invocation.method(#getChapterCount, []),
            returnValue: _i5.Future<int>.value(0),
          )
          as _i5.Future<int>);

  @override
  _i5.Future<void> clearAllChapters() =>
      (super.noSuchMethod(
            Invocation.method(#clearAllChapters, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}
