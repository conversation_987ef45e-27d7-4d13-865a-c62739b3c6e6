import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:logger/logger.dart';

/// 本地数据库管理类
/// 使用JSON文件作为简单的本地数据库存储
class LocalDatabase {
  static LocalDatabase? _instance;
  static LocalDatabase get instance => _instance ??= LocalDatabase._();
  
  LocalDatabase._();
  
  final Logger _logger = Logger();
  late Directory _appDir;
  bool _initialized = false;
  
  /// 初始化数据库
  Future<void> initialize() async {
    if (_initialized) return;
    
    try {
      _appDir = await getApplicationDocumentsDirectory();
      final dbDir = Directory('${_appDir.path}/BambooFall');
      
      if (!await dbDir.exists()) {
        await dbDir.create(recursive: true);
        _logger.i('Created database directory: ${dbDir.path}');
      }
      
      _appDir = dbDir;
      _initialized = true;
      _logger.i('Database initialized successfully');
    } catch (e) {
      _logger.e('Failed to initialize database: $e');
      rethrow;
    }
  }
  
  /// 确保数据库已初始化
  void _ensureInitialized() {
    if (!_initialized) {
      throw StateError('Database not initialized. Call initialize() first.');
    }
  }
  
  /// 获取集合文件路径
  String _getCollectionPath(String collection) {
    return '${_appDir.path}/$collection.json';
  }
  
  /// 读取集合数据
  Future<List<Map<String, dynamic>>> readCollection(String collection) async {
    _ensureInitialized();
    
    try {
      final file = File(_getCollectionPath(collection));
      
      if (!await file.exists()) {
        _logger.d('Collection $collection does not exist, returning empty list');
        return [];
      }
      
      final content = await file.readAsString();
      if (content.isEmpty) {
        return [];
      }
      
      final data = jsonDecode(content);
      if (data is List) {
        return data.cast<Map<String, dynamic>>();
      } else {
        _logger.w('Collection $collection is not a list, returning empty list');
        return [];
      }
    } catch (e) {
      _logger.e('Failed to read collection $collection: $e');
      return [];
    }
  }
  
  /// 写入集合数据
  Future<void> writeCollection(String collection, List<Map<String, dynamic>> data) async {
    _ensureInitialized();
    
    try {
      final file = File(_getCollectionPath(collection));
      final jsonString = jsonEncode(data);
      await file.writeAsString(jsonString);
      _logger.d('Successfully wrote ${data.length} items to collection $collection');
    } catch (e) {
      _logger.e('Failed to write collection $collection: $e');
      rethrow;
    }
  }
  
  /// 添加文档到集合
  Future<void> addDocument(String collection, Map<String, dynamic> document) async {
    final data = await readCollection(collection);
    
    // 添加时间戳
    document['createdAt'] = DateTime.now().toIso8601String();
    document['updatedAt'] = DateTime.now().toIso8601String();
    
    // 生成ID（如果没有）
    if (!document.containsKey('id')) {
      document['id'] = DateTime.now().millisecondsSinceEpoch.toString();
    }
    
    data.add(document);
    await writeCollection(collection, data);
  }
  
  /// 更新文档
  Future<bool> updateDocument(String collection, String id, Map<String, dynamic> updates) async {
    final data = await readCollection(collection);
    
    for (int i = 0; i < data.length; i++) {
      if (data[i]['id'] == id) {
        data[i] = {...data[i], ...updates};
        data[i]['updatedAt'] = DateTime.now().toIso8601String();
        await writeCollection(collection, data);
        return true;
      }
    }
    
    return false;
  }
  
  /// 删除文档
  Future<bool> deleteDocument(String collection, String id) async {
    final data = await readCollection(collection);
    final originalLength = data.length;
    
    data.removeWhere((doc) => doc['id'] == id);
    
    if (data.length < originalLength) {
      await writeCollection(collection, data);
      return true;
    }
    
    return false;
  }
  
  /// 查找文档
  Future<Map<String, dynamic>?> findDocument(String collection, String id) async {
    final data = await readCollection(collection);
    
    try {
      return data.firstWhere((doc) => doc['id'] == id);
    } catch (e) {
      return null;
    }
  }
  
  /// 查询文档
  Future<List<Map<String, dynamic>>> queryDocuments(
    String collection, {
    bool Function(Map<String, dynamic>)? where,
    int? limit,
    int? offset,
  }) async {
    var data = await readCollection(collection);
    
    if (where != null) {
      data = data.where(where).toList();
    }
    
    if (offset != null && offset > 0) {
      data = data.skip(offset).toList();
    }
    
    if (limit != null && limit > 0) {
      data = data.take(limit).toList();
    }
    
    return data;
  }
  
  /// 清空集合
  Future<void> clearCollection(String collection) async {
    await writeCollection(collection, []);
  }
  
  /// 删除集合
  Future<void> deleteCollection(String collection) async {
    _ensureInitialized();
    
    try {
      final file = File(_getCollectionPath(collection));
      if (await file.exists()) {
        await file.delete();
        _logger.d('Deleted collection $collection');
      }
    } catch (e) {
      _logger.e('Failed to delete collection $collection: $e');
      rethrow;
    }
  }
  
  /// 获取所有集合名称
  Future<List<String>> getCollections() async {
    _ensureInitialized();
    
    try {
      final files = await _appDir.list().toList();
      return files
          .where((file) => file is File && file.path.endsWith('.json'))
          .map((file) => file.path.split('/').last.replaceAll('.json', ''))
          .toList();
    } catch (e) {
      _logger.e('Failed to get collections: $e');
      return [];
    }
  }
  
  /// 获取数据库统计信息
  Future<Map<String, dynamic>> getStats() async {
    final collections = await getCollections();
    final stats = <String, dynamic>{
      'collections': collections.length,
      'collectionsDetail': <String, int>{},
    };
    
    for (final collection in collections) {
      final data = await readCollection(collection);
      stats['collectionsDetail'][collection] = data.length;
    }
    
    return stats;
  }
}