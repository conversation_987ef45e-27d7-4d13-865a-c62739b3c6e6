import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:logger/logger.dart';

import 'package:bamboofall_app/features/bible/domain/entities/character.dart';
import 'package:bamboofall_app/features/bible/domain/entities/location.dart';
import 'package:bamboofall_app/features/bible/domain/entities/story_bible.dart';
import 'package:bamboofall_app/features/bible/domain/repositories/bible_repository.dart';
import 'package:bamboofall_app/features/bible/domain/usecases/manage_bible_elements.dart';

import 'manage_bible_elements_test.mocks.dart';

@GenerateMocks([BibleRepository, Logger])
void main() {
  late ManageBibleElementsUseCase useCase;
  late MockBibleRepository mockRepository;
  late MockLogger mockLogger;

  setUp(() {
    mockRepository = MockBibleRepository();
    mockLogger = MockLogger();
    useCase = ManageBibleElementsUseCase(
      repository: mockRepository,
      logger: mockLogger,
    );
  });

  group('ManageBibleElementsUseCase - Character Management', () {
    final testCharacter = Character(
      id: 'char-1',
      name: 'Test Character',
      type: CharacterType.protagonist,
      description: 'A test character',
      appearance: const CharacterAppearance(
        age: 25,
        gender: 'Male',
        height: '180cm',
        build: 'Athletic',
        hairColor: 'Brown',
        eyeColor: 'Blue',
      ),
      personality: const CharacterPersonality(
        traits: ['Brave', 'Kind'],
        motivations: ['Save the world'],
        fears: ['Spiders'],
        goals: ['Defeat evil'],
      ),
      background: const CharacterBackground(
        birthplace: 'Village',
        family: 'Orphan',
        education: 'Self-taught',
        occupation: 'Knight',
        history: 'Found as a baby',
      ),
      status: CharacterStatus.active,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    test('should create character successfully', () async {
      // Arrange
      when(mockRepository.getCharacter(any)).thenAnswer((_) async => null);
      when(mockRepository.saveCharacter(any)).thenAnswer((_) async {});

      final creationData = CharacterCreationData(
        name: testCharacter.name,
        type: testCharacter.type,
        description: testCharacter.description,
        appearance: testCharacter.appearance,
        personality: testCharacter.personality,
        background: testCharacter.background,
      );

      // Act
      final result = await useCase.createCharacter(creationData);

      // Assert
      expect(result.isSuccess, true);
      expect(result.data?.name, testCharacter.name);
      verify(mockRepository.saveCharacter(any)).called(1);
    });

    test('should fail to create character with duplicate name', () async {
      // Arrange
      when(mockRepository.searchCharacters(any)).thenAnswer((_) async => [testCharacter]);

      final creationData = CharacterCreationData(
        name: testCharacter.name,
        type: testCharacter.type,
      );

      // Act
      final result = await useCase.createCharacter(creationData);

      // Assert
      expect(result.isSuccess, false);
      expect(result.error?.type, BibleOperationErrorType.alreadyExists);
      verifyNever(mockRepository.saveCharacter(any));
    });

    test('should update character successfully', () async {
      // Arrange
      when(mockRepository.getCharacter(testCharacter.id)).thenAnswer((_) async => testCharacter);
      when(mockRepository.updateCharacter(any)).thenAnswer((_) async {});

      final updatedCharacter = testCharacter.copyWith(
        description: 'Updated description',
      );

      // Act
      final result = await useCase.updateCharacter(updatedCharacter);

      // Assert
      expect(result.isSuccess, true);
      verify(mockRepository.updateCharacter(any)).called(1);
    });

    test('should delete character successfully', () async {
      // Arrange
      when(mockRepository.getCharacter(testCharacter.id)).thenAnswer((_) async => testCharacter);
      when(mockRepository.deleteCharacter(testCharacter.id)).thenAnswer((_) async {});

      // Act
      final result = await useCase.deleteCharacter(testCharacter.id);

      // Assert
      expect(result.isSuccess, true);
      verify(mockRepository.deleteCharacter(testCharacter.id)).called(1);
    });

    test('should search characters with filters', () async {
      // Arrange
      final characters = [testCharacter];
      when(mockRepository.searchCharacters(any)).thenAnswer((_) async => characters);

      // Act
      final result = await useCase.searchCharacters(
        query: 'Test',
        type: CharacterType.protagonist,
        status: CharacterStatus.active,
        limit: 10,
      );

      // Assert
      expect(result.isSuccess, true);
      expect(result.data?.length, 1);
      expect(result.data?.first.name, testCharacter.name);
    });
  });

  group('ManageBibleElementsUseCase - Location Management', () {
    final testLocation = Location(
      id: 'loc-1',
      name: 'Test City',
      type: LocationType.city,
      description: 'A test city',
      geography: const LocationGeography(
        climate: 'Temperate',
        terrain: 'Plains',
        naturalResources: ['Water', 'Fertile soil'],
        landmarks: ['Central Plaza'],
      ),
      culture: const LocationCulture(
        population: 10000,
        government: 'Democracy',
        economy: 'Trade',
        religion: 'Multiple',
        customs: ['Annual festival'],
        languages: ['Common'],
      ),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    test('should create location successfully', () async {
      // Arrange
      when(mockRepository.getLocation(any)).thenAnswer((_) async => null);
      when(mockRepository.saveLocation(any)).thenAnswer((_) async {});

      final creationData = LocationCreationData(
        name: testLocation.name,
        type: testLocation.type,
        description: testLocation.description,
        geography: testLocation.geography,
        culture: testLocation.culture,
      );

      // Act
      final result = await useCase.createLocation(creationData);

      // Assert
      expect(result.isSuccess, true);
      expect(result.data?.name, testLocation.name);
      verify(mockRepository.saveLocation(any)).called(1);
    });

    test('should update location successfully', () async {
      // Arrange
      when(mockRepository.getLocation(testLocation.id)).thenAnswer((_) async => testLocation);
      when(mockRepository.updateLocation(any)).thenAnswer((_) async {});

      final updatedLocation = testLocation.copyWith(
        description: 'Updated description',
      );

      // Act
      final result = await useCase.updateLocation(updatedLocation);

      // Assert
      expect(result.isSuccess, true);
      verify(mockRepository.updateLocation(any)).called(1);
    });

    test('should get location hierarchy', () async {
      // Arrange
      final childLocation = testLocation.copyWith(
        id: 'loc-2',
        name: 'Child Location',
        parentLocationId: testLocation.id,
      );
      
      when(mockRepository.getLocation(testLocation.id)).thenAnswer((_) async => testLocation);
      when(mockRepository.getChildLocations(testLocation.id)).thenAnswer((_) async => [childLocation]);

      // Act
      final result = await useCase.getLocationHierarchy(testLocation.id);

      // Assert
      expect(result.isSuccess, true);
      expect(result.data?.isNotEmpty, true);
    });
  });

  group('ManageBibleElementsUseCase - Story Bible Management', () {
    final testStoryBible = StoryBible(
      id: 'bible-1',
      projectId: 'project-1',
      title: 'Test Story',
      genre: 'Fantasy',
      theme: 'Good vs Evil',
      setting: 'Medieval world',
      tone: 'Epic',
      targetAudience: 'Young Adult',
      synopsis: 'A story about heroes',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    test('should create story bible successfully', () async {
      // Arrange
      when(mockRepository.getStoryBibleByProjectId(any)).thenAnswer((_) async => null);
      when(mockRepository.saveStoryBible(any)).thenAnswer((_) async {});

      final creationData = StoryBibleCreationData(
        projectId: testStoryBible.projectId,
        title: testStoryBible.title,
        genre: testStoryBible.genre,
        theme: testStoryBible.theme,
        setting: testStoryBible.setting,
        tone: testStoryBible.tone,
        targetAudience: testStoryBible.targetAudience,
        synopsis: testStoryBible.synopsis,
      );

      // Act
      final result = await useCase.createStoryBible(creationData);

      // Assert
      expect(result.isSuccess, true);
      expect(result.data?.title, testStoryBible.title);
      verify(mockRepository.saveStoryBible(any)).called(1);
    });

    test('should update story bible successfully', () async {
      // Arrange
      when(mockRepository.getStoryBible(testStoryBible.id)).thenAnswer((_) async => testStoryBible);
      when(mockRepository.updateStoryBible(any)).thenAnswer((_) async {});

      final updatedBible = testStoryBible.copyWith(
        synopsis: 'Updated synopsis',
      );

      // Act
      final result = await useCase.updateStoryBible(updatedBible);

      // Assert
      expect(result.isSuccess, true);
      verify(mockRepository.updateStoryBible(any)).called(1);
    });
  });

  group('ManageBibleElementsUseCase - Error Handling', () {
    test('should handle repository exceptions', () async {
      // Arrange
      when(mockRepository.getCharacter(any)).thenThrow(Exception('Database error'));

      // Act
      final result = await useCase.getCharacter('invalid-id');

      // Assert
      expect(result.isSuccess, false);
      expect(result.error?.type, BibleOperationErrorType.unknown);
    });

    test('should validate character creation data', () async {
      // Arrange
      final invalidData = CharacterCreationData(
        name: '', // Empty name should fail validation
        type: CharacterType.protagonist,
      );

      // Act
      final result = await useCase.createCharacter(invalidData);

      // Assert
      expect(result.isSuccess, false);
      expect(result.error?.type, BibleOperationErrorType.validationFailed);
    });
  });
}