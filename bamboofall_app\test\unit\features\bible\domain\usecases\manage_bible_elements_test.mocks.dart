// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in bamboofall_app/test/unit/features/bible/domain/usecases/manage_bible_elements_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:bamboofall_app/features/bible/domain/entities/character.dart'
    as _i5;
import 'package:bamboofall_app/features/bible/domain/entities/location.dart'
    as _i6;
import 'package:bamboofall_app/features/bible/domain/entities/story_bible.dart'
    as _i4;
import 'package:bamboofall_app/features/bible/domain/entities/world_settings.dart'
    as _i7;
import 'package:bamboofall_app/features/bible/domain/repositories/bible_repository.dart'
    as _i2;
import 'package:logger/src/log_level.dart' as _i9;
import 'package:logger/src/logger.dart' as _i8;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [BibleRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockBibleRepository extends _i1.Mock implements _i2.BibleRepository {
  MockBibleRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<void> saveStoryBible(_i4.StoryBible? storyBible) =>
      (super.noSuchMethod(
            Invocation.method(#saveStoryBible, [storyBible]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<_i4.StoryBible?> getStoryBible(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getStoryBible, [id]),
            returnValue: _i3.Future<_i4.StoryBible?>.value(),
          )
          as _i3.Future<_i4.StoryBible?>);

  @override
  _i3.Future<_i4.StoryBible?> getStoryBibleByProjectId(String? projectId) =>
      (super.noSuchMethod(
            Invocation.method(#getStoryBibleByProjectId, [projectId]),
            returnValue: _i3.Future<_i4.StoryBible?>.value(),
          )
          as _i3.Future<_i4.StoryBible?>);

  @override
  _i3.Future<void> updateStoryBible(_i4.StoryBible? storyBible) =>
      (super.noSuchMethod(
            Invocation.method(#updateStoryBible, [storyBible]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> deleteStoryBible(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteStoryBible, [id]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<List<_i4.StoryBible>> getAllStoryBibles() =>
      (super.noSuchMethod(
            Invocation.method(#getAllStoryBibles, []),
            returnValue: _i3.Future<List<_i4.StoryBible>>.value(
              <_i4.StoryBible>[],
            ),
          )
          as _i3.Future<List<_i4.StoryBible>>);

  @override
  _i3.Future<void> saveCharacter(_i5.Character? character) =>
      (super.noSuchMethod(
            Invocation.method(#saveCharacter, [character]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<_i5.Character?> getCharacter(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getCharacter, [id]),
            returnValue: _i3.Future<_i5.Character?>.value(),
          )
          as _i3.Future<_i5.Character?>);

  @override
  _i3.Future<void> updateCharacter(_i5.Character? character) =>
      (super.noSuchMethod(
            Invocation.method(#updateCharacter, [character]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> deleteCharacter(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteCharacter, [id]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<List<_i5.Character>> getCharactersByType(
    _i5.CharacterType? type,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getCharactersByType, [type]),
            returnValue: _i3.Future<List<_i5.Character>>.value(
              <_i5.Character>[],
            ),
          )
          as _i3.Future<List<_i5.Character>>);

  @override
  _i3.Future<List<_i5.Character>> getCharactersByStatus(
    _i5.CharacterStatus? status,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getCharactersByStatus, [status]),
            returnValue: _i3.Future<List<_i5.Character>>.value(
              <_i5.Character>[],
            ),
          )
          as _i3.Future<List<_i5.Character>>);

  @override
  _i3.Future<List<_i5.Character>> searchCharacters(String? query) =>
      (super.noSuchMethod(
            Invocation.method(#searchCharacters, [query]),
            returnValue: _i3.Future<List<_i5.Character>>.value(
              <_i5.Character>[],
            ),
          )
          as _i3.Future<List<_i5.Character>>);

  @override
  _i3.Future<void> saveLocation(_i6.Location? location) =>
      (super.noSuchMethod(
            Invocation.method(#saveLocation, [location]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<_i6.Location?> getLocation(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getLocation, [id]),
            returnValue: _i3.Future<_i6.Location?>.value(),
          )
          as _i3.Future<_i6.Location?>);

  @override
  _i3.Future<void> updateLocation(_i6.Location? location) =>
      (super.noSuchMethod(
            Invocation.method(#updateLocation, [location]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> deleteLocation(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteLocation, [id]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<List<_i6.Location>> getLocationsByType(_i6.LocationType? type) =>
      (super.noSuchMethod(
            Invocation.method(#getLocationsByType, [type]),
            returnValue: _i3.Future<List<_i6.Location>>.value(<_i6.Location>[]),
          )
          as _i3.Future<List<_i6.Location>>);

  @override
  _i3.Future<List<_i6.Location>> getChildLocations(String? parentId) =>
      (super.noSuchMethod(
            Invocation.method(#getChildLocations, [parentId]),
            returnValue: _i3.Future<List<_i6.Location>>.value(<_i6.Location>[]),
          )
          as _i3.Future<List<_i6.Location>>);

  @override
  _i3.Future<void> saveWorldSettings(_i7.WorldSettings? worldSettings) =>
      (super.noSuchMethod(
            Invocation.method(#saveWorldSettings, [worldSettings]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<_i7.WorldSettings?> getWorldSettings(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getWorldSettings, [id]),
            returnValue: _i3.Future<_i7.WorldSettings?>.value(),
          )
          as _i3.Future<_i7.WorldSettings?>);

  @override
  _i3.Future<void> updateWorldSettings(_i7.WorldSettings? worldSettings) =>
      (super.noSuchMethod(
            Invocation.method(#updateWorldSettings, [worldSettings]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> deleteWorldSettings(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteWorldSettings, [id]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<List<String>> validateDataIntegrity() =>
      (super.noSuchMethod(
            Invocation.method(#validateDataIntegrity, []),
            returnValue: _i3.Future<List<String>>.value(<String>[]),
          )
          as _i3.Future<List<String>>);

  @override
  _i3.Future<bool> validateCharacterConstraints(_i5.Character? character) =>
      (super.noSuchMethod(
            Invocation.method(#validateCharacterConstraints, [character]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<bool> validateLocationConstraints(_i6.Location? location) =>
      (super.noSuchMethod(
            Invocation.method(#validateLocationConstraints, [location]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<Map<String, dynamic>> getStatistics() =>
      (super.noSuchMethod(
            Invocation.method(#getStatistics, []),
            returnValue: _i3.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> analyzeCharacterRelationships() =>
      (super.noSuchMethod(
            Invocation.method(#analyzeCharacterRelationships, []),
            returnValue: _i3.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> analyzeLocationHierarchy() =>
      (super.noSuchMethod(
            Invocation.method(#analyzeLocationHierarchy, []),
            returnValue: _i3.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> exportData() =>
      (super.noSuchMethod(
            Invocation.method(#exportData, []),
            returnValue: _i3.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<void> importData(Map<String, dynamic>? data) =>
      (super.noSuchMethod(
            Invocation.method(#importData, [data]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> cleanup() =>
      (super.noSuchMethod(
            Invocation.method(#cleanup, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);
}

/// A class which mocks [Logger].
///
/// See the documentation for Mockito's code generation for more information.
class MockLogger extends _i1.Mock implements _i8.Logger {
  MockLogger() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<void> get init =>
      (super.noSuchMethod(
            Invocation.getter(#init),
            returnValue: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  void v(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #v,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void t(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #t,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void d(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #d,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void i(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #i,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void w(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #w,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void e(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #e,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void wtf(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #wtf,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void f(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #f,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void log(
    _i9.Level? level,
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #log,
      [level, message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  bool isClosed() =>
      (super.noSuchMethod(Invocation.method(#isClosed, []), returnValue: false)
          as bool);

  @override
  _i3.Future<void> close() =>
      (super.noSuchMethod(
            Invocation.method(#close, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);
}
