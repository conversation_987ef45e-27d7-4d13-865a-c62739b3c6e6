import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

import '../features/home/<USER>/pages/home_page.dart';
import '../core/providers/app_providers.dart';
import 'theme.dart';

class BambooFallApp extends ConsumerWidget {
  const BambooFallApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeModeProvider);
    final appState = ref.watch(appStateProvider);

    // 如果应用未初始化，显示加载界面
    if (!appState.isInitialized) {
      return fluent.FluentApp(
        title: '笔落 - BambooFall',
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        home: const _LoadingScreen(),
        debugShowCheckedModeBanner: false,
      );
    }

    return fluent.FluentApp(
      title: '笔落 - BambooFall',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeMode,
      home: const HomePage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class _LoadingScreen extends StatelessWidget {
  const _LoadingScreen();

  @override
  Widget build(BuildContext context) {
    return fluent.ScaffoldPage(
      content: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const fluent.ProgressRing(),
            const SizedBox(height: 16),
            Text(
              '正在初始化笔落...',
              style: fluent.FluentTheme.of(context).typography.body,
            ),
          ],
        ),
      ),
    );
  }
}