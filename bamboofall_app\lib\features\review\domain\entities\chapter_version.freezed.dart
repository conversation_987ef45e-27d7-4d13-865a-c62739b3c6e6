// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chapter_version.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

ChapterVersion _$ChapterVersionFromJson(Map<String, dynamic> json) {
  return _ChapterVersion.fromJson(json);
}

/// @nodoc
mixin _$ChapterVersion {
  /// 版本唯一标识符
  String get id => throw _privateConstructorUsedError;

  /// 关联的章节ID
  String get chapterId => throw _privateConstructorUsedError;

  /// 版本号（递增）
  int get versionNumber => throw _privateConstructorUsedError;

  /// 版本标题
  String get title => throw _privateConstructorUsedError;

  /// 版本内容（Markdown格式）
  String get content => throw _privateConstructorUsedError;

  /// 版本创建时间
  DateTime get createdAt => throw _privateConstructorUsedError;

  /// 版本创建者
  String get createdBy => throw _privateConstructorUsedError;

  /// 版本描述/备注
  String get description => throw _privateConstructorUsedError;

  /// 版本标签
  List<String> get tags => throw _privateConstructorUsedError;

  /// 版本状态
  VersionStatus get status => throw _privateConstructorUsedError;

  /// 字数统计
  int get wordCount => throw _privateConstructorUsedError;

  /// 字符数统计
  int get characterCount => throw _privateConstructorUsedError;

  /// 段落数统计
  int get paragraphCount => throw _privateConstructorUsedError;

  /// 版本大小（字节）
  int get sizeInBytes => throw _privateConstructorUsedError;

  /// 是否为主版本
  bool get isMainVersion => throw _privateConstructorUsedError;

  /// 是否为自动保存版本
  bool get isAutoSave => throw _privateConstructorUsedError;

  /// 父版本ID（基于哪个版本创建）
  String? get parentVersionId => throw _privateConstructorUsedError;

  /// 合并的源版本ID列表（如果是合并版本）
  List<String> get mergedFromVersionIds => throw _privateConstructorUsedError;

  /// 版本元数据
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;

  /// 校验和（用于完整性检查）
  String? get checksum => throw _privateConstructorUsedError;

  /// Serializes this ChapterVersion to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChapterVersion
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChapterVersionCopyWith<ChapterVersion> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChapterVersionCopyWith<$Res> {
  factory $ChapterVersionCopyWith(
    ChapterVersion value,
    $Res Function(ChapterVersion) then,
  ) = _$ChapterVersionCopyWithImpl<$Res, ChapterVersion>;
  @useResult
  $Res call({
    String id,
    String chapterId,
    int versionNumber,
    String title,
    String content,
    DateTime createdAt,
    String createdBy,
    String description,
    List<String> tags,
    VersionStatus status,
    int wordCount,
    int characterCount,
    int paragraphCount,
    int sizeInBytes,
    bool isMainVersion,
    bool isAutoSave,
    String? parentVersionId,
    List<String> mergedFromVersionIds,
    Map<String, dynamic> metadata,
    String? checksum,
  });
}

/// @nodoc
class _$ChapterVersionCopyWithImpl<$Res, $Val extends ChapterVersion>
    implements $ChapterVersionCopyWith<$Res> {
  _$ChapterVersionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChapterVersion
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? chapterId = null,
    Object? versionNumber = null,
    Object? title = null,
    Object? content = null,
    Object? createdAt = null,
    Object? createdBy = null,
    Object? description = null,
    Object? tags = null,
    Object? status = null,
    Object? wordCount = null,
    Object? characterCount = null,
    Object? paragraphCount = null,
    Object? sizeInBytes = null,
    Object? isMainVersion = null,
    Object? isAutoSave = null,
    Object? parentVersionId = freezed,
    Object? mergedFromVersionIds = null,
    Object? metadata = null,
    Object? checksum = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            chapterId: null == chapterId
                ? _value.chapterId
                : chapterId // ignore: cast_nullable_to_non_nullable
                      as String,
            versionNumber: null == versionNumber
                ? _value.versionNumber
                : versionNumber // ignore: cast_nullable_to_non_nullable
                      as int,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            content: null == content
                ? _value.content
                : content // ignore: cast_nullable_to_non_nullable
                      as String,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            createdBy: null == createdBy
                ? _value.createdBy
                : createdBy // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            tags: null == tags
                ? _value.tags
                : tags // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as VersionStatus,
            wordCount: null == wordCount
                ? _value.wordCount
                : wordCount // ignore: cast_nullable_to_non_nullable
                      as int,
            characterCount: null == characterCount
                ? _value.characterCount
                : characterCount // ignore: cast_nullable_to_non_nullable
                      as int,
            paragraphCount: null == paragraphCount
                ? _value.paragraphCount
                : paragraphCount // ignore: cast_nullable_to_non_nullable
                      as int,
            sizeInBytes: null == sizeInBytes
                ? _value.sizeInBytes
                : sizeInBytes // ignore: cast_nullable_to_non_nullable
                      as int,
            isMainVersion: null == isMainVersion
                ? _value.isMainVersion
                : isMainVersion // ignore: cast_nullable_to_non_nullable
                      as bool,
            isAutoSave: null == isAutoSave
                ? _value.isAutoSave
                : isAutoSave // ignore: cast_nullable_to_non_nullable
                      as bool,
            parentVersionId: freezed == parentVersionId
                ? _value.parentVersionId
                : parentVersionId // ignore: cast_nullable_to_non_nullable
                      as String?,
            mergedFromVersionIds: null == mergedFromVersionIds
                ? _value.mergedFromVersionIds
                : mergedFromVersionIds // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            checksum: freezed == checksum
                ? _value.checksum
                : checksum // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ChapterVersionImplCopyWith<$Res>
    implements $ChapterVersionCopyWith<$Res> {
  factory _$$ChapterVersionImplCopyWith(
    _$ChapterVersionImpl value,
    $Res Function(_$ChapterVersionImpl) then,
  ) = __$$ChapterVersionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String chapterId,
    int versionNumber,
    String title,
    String content,
    DateTime createdAt,
    String createdBy,
    String description,
    List<String> tags,
    VersionStatus status,
    int wordCount,
    int characterCount,
    int paragraphCount,
    int sizeInBytes,
    bool isMainVersion,
    bool isAutoSave,
    String? parentVersionId,
    List<String> mergedFromVersionIds,
    Map<String, dynamic> metadata,
    String? checksum,
  });
}

/// @nodoc
class __$$ChapterVersionImplCopyWithImpl<$Res>
    extends _$ChapterVersionCopyWithImpl<$Res, _$ChapterVersionImpl>
    implements _$$ChapterVersionImplCopyWith<$Res> {
  __$$ChapterVersionImplCopyWithImpl(
    _$ChapterVersionImpl _value,
    $Res Function(_$ChapterVersionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ChapterVersion
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? chapterId = null,
    Object? versionNumber = null,
    Object? title = null,
    Object? content = null,
    Object? createdAt = null,
    Object? createdBy = null,
    Object? description = null,
    Object? tags = null,
    Object? status = null,
    Object? wordCount = null,
    Object? characterCount = null,
    Object? paragraphCount = null,
    Object? sizeInBytes = null,
    Object? isMainVersion = null,
    Object? isAutoSave = null,
    Object? parentVersionId = freezed,
    Object? mergedFromVersionIds = null,
    Object? metadata = null,
    Object? checksum = freezed,
  }) {
    return _then(
      _$ChapterVersionImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        chapterId: null == chapterId
            ? _value.chapterId
            : chapterId // ignore: cast_nullable_to_non_nullable
                  as String,
        versionNumber: null == versionNumber
            ? _value.versionNumber
            : versionNumber // ignore: cast_nullable_to_non_nullable
                  as int,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        content: null == content
            ? _value.content
            : content // ignore: cast_nullable_to_non_nullable
                  as String,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        createdBy: null == createdBy
            ? _value.createdBy
            : createdBy // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        tags: null == tags
            ? _value._tags
            : tags // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as VersionStatus,
        wordCount: null == wordCount
            ? _value.wordCount
            : wordCount // ignore: cast_nullable_to_non_nullable
                  as int,
        characterCount: null == characterCount
            ? _value.characterCount
            : characterCount // ignore: cast_nullable_to_non_nullable
                  as int,
        paragraphCount: null == paragraphCount
            ? _value.paragraphCount
            : paragraphCount // ignore: cast_nullable_to_non_nullable
                  as int,
        sizeInBytes: null == sizeInBytes
            ? _value.sizeInBytes
            : sizeInBytes // ignore: cast_nullable_to_non_nullable
                  as int,
        isMainVersion: null == isMainVersion
            ? _value.isMainVersion
            : isMainVersion // ignore: cast_nullable_to_non_nullable
                  as bool,
        isAutoSave: null == isAutoSave
            ? _value.isAutoSave
            : isAutoSave // ignore: cast_nullable_to_non_nullable
                  as bool,
        parentVersionId: freezed == parentVersionId
            ? _value.parentVersionId
            : parentVersionId // ignore: cast_nullable_to_non_nullable
                  as String?,
        mergedFromVersionIds: null == mergedFromVersionIds
            ? _value._mergedFromVersionIds
            : mergedFromVersionIds // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        checksum: freezed == checksum
            ? _value.checksum
            : checksum // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ChapterVersionImpl implements _ChapterVersion {
  const _$ChapterVersionImpl({
    required this.id,
    required this.chapterId,
    required this.versionNumber,
    required this.title,
    required this.content,
    required this.createdAt,
    required this.createdBy,
    this.description = '',
    final List<String> tags = const [],
    this.status = VersionStatus.draft,
    this.wordCount = 0,
    this.characterCount = 0,
    this.paragraphCount = 0,
    this.sizeInBytes = 0,
    this.isMainVersion = false,
    this.isAutoSave = false,
    this.parentVersionId,
    final List<String> mergedFromVersionIds = const [],
    final Map<String, dynamic> metadata = const {},
    this.checksum,
  }) : _tags = tags,
       _mergedFromVersionIds = mergedFromVersionIds,
       _metadata = metadata;

  factory _$ChapterVersionImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChapterVersionImplFromJson(json);

  /// 版本唯一标识符
  @override
  final String id;

  /// 关联的章节ID
  @override
  final String chapterId;

  /// 版本号（递增）
  @override
  final int versionNumber;

  /// 版本标题
  @override
  final String title;

  /// 版本内容（Markdown格式）
  @override
  final String content;

  /// 版本创建时间
  @override
  final DateTime createdAt;

  /// 版本创建者
  @override
  final String createdBy;

  /// 版本描述/备注
  @override
  @JsonKey()
  final String description;

  /// 版本标签
  final List<String> _tags;

  /// 版本标签
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  /// 版本状态
  @override
  @JsonKey()
  final VersionStatus status;

  /// 字数统计
  @override
  @JsonKey()
  final int wordCount;

  /// 字符数统计
  @override
  @JsonKey()
  final int characterCount;

  /// 段落数统计
  @override
  @JsonKey()
  final int paragraphCount;

  /// 版本大小（字节）
  @override
  @JsonKey()
  final int sizeInBytes;

  /// 是否为主版本
  @override
  @JsonKey()
  final bool isMainVersion;

  /// 是否为自动保存版本
  @override
  @JsonKey()
  final bool isAutoSave;

  /// 父版本ID（基于哪个版本创建）
  @override
  final String? parentVersionId;

  /// 合并的源版本ID列表（如果是合并版本）
  final List<String> _mergedFromVersionIds;

  /// 合并的源版本ID列表（如果是合并版本）
  @override
  @JsonKey()
  List<String> get mergedFromVersionIds {
    if (_mergedFromVersionIds is EqualUnmodifiableListView)
      return _mergedFromVersionIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_mergedFromVersionIds);
  }

  /// 版本元数据
  final Map<String, dynamic> _metadata;

  /// 版本元数据
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  /// 校验和（用于完整性检查）
  @override
  final String? checksum;

  @override
  String toString() {
    return 'ChapterVersion(id: $id, chapterId: $chapterId, versionNumber: $versionNumber, title: $title, content: $content, createdAt: $createdAt, createdBy: $createdBy, description: $description, tags: $tags, status: $status, wordCount: $wordCount, characterCount: $characterCount, paragraphCount: $paragraphCount, sizeInBytes: $sizeInBytes, isMainVersion: $isMainVersion, isAutoSave: $isAutoSave, parentVersionId: $parentVersionId, mergedFromVersionIds: $mergedFromVersionIds, metadata: $metadata, checksum: $checksum)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChapterVersionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.chapterId, chapterId) ||
                other.chapterId == chapterId) &&
            (identical(other.versionNumber, versionNumber) ||
                other.versionNumber == versionNumber) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.wordCount, wordCount) ||
                other.wordCount == wordCount) &&
            (identical(other.characterCount, characterCount) ||
                other.characterCount == characterCount) &&
            (identical(other.paragraphCount, paragraphCount) ||
                other.paragraphCount == paragraphCount) &&
            (identical(other.sizeInBytes, sizeInBytes) ||
                other.sizeInBytes == sizeInBytes) &&
            (identical(other.isMainVersion, isMainVersion) ||
                other.isMainVersion == isMainVersion) &&
            (identical(other.isAutoSave, isAutoSave) ||
                other.isAutoSave == isAutoSave) &&
            (identical(other.parentVersionId, parentVersionId) ||
                other.parentVersionId == parentVersionId) &&
            const DeepCollectionEquality().equals(
              other._mergedFromVersionIds,
              _mergedFromVersionIds,
            ) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.checksum, checksum) ||
                other.checksum == checksum));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    chapterId,
    versionNumber,
    title,
    content,
    createdAt,
    createdBy,
    description,
    const DeepCollectionEquality().hash(_tags),
    status,
    wordCount,
    characterCount,
    paragraphCount,
    sizeInBytes,
    isMainVersion,
    isAutoSave,
    parentVersionId,
    const DeepCollectionEquality().hash(_mergedFromVersionIds),
    const DeepCollectionEquality().hash(_metadata),
    checksum,
  ]);

  /// Create a copy of ChapterVersion
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChapterVersionImplCopyWith<_$ChapterVersionImpl> get copyWith =>
      __$$ChapterVersionImplCopyWithImpl<_$ChapterVersionImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ChapterVersionImplToJson(this);
  }
}

abstract class _ChapterVersion implements ChapterVersion {
  const factory _ChapterVersion({
    required final String id,
    required final String chapterId,
    required final int versionNumber,
    required final String title,
    required final String content,
    required final DateTime createdAt,
    required final String createdBy,
    final String description,
    final List<String> tags,
    final VersionStatus status,
    final int wordCount,
    final int characterCount,
    final int paragraphCount,
    final int sizeInBytes,
    final bool isMainVersion,
    final bool isAutoSave,
    final String? parentVersionId,
    final List<String> mergedFromVersionIds,
    final Map<String, dynamic> metadata,
    final String? checksum,
  }) = _$ChapterVersionImpl;

  factory _ChapterVersion.fromJson(Map<String, dynamic> json) =
      _$ChapterVersionImpl.fromJson;

  /// 版本唯一标识符
  @override
  String get id;

  /// 关联的章节ID
  @override
  String get chapterId;

  /// 版本号（递增）
  @override
  int get versionNumber;

  /// 版本标题
  @override
  String get title;

  /// 版本内容（Markdown格式）
  @override
  String get content;

  /// 版本创建时间
  @override
  DateTime get createdAt;

  /// 版本创建者
  @override
  String get createdBy;

  /// 版本描述/备注
  @override
  String get description;

  /// 版本标签
  @override
  List<String> get tags;

  /// 版本状态
  @override
  VersionStatus get status;

  /// 字数统计
  @override
  int get wordCount;

  /// 字符数统计
  @override
  int get characterCount;

  /// 段落数统计
  @override
  int get paragraphCount;

  /// 版本大小（字节）
  @override
  int get sizeInBytes;

  /// 是否为主版本
  @override
  bool get isMainVersion;

  /// 是否为自动保存版本
  @override
  bool get isAutoSave;

  /// 父版本ID（基于哪个版本创建）
  @override
  String? get parentVersionId;

  /// 合并的源版本ID列表（如果是合并版本）
  @override
  List<String> get mergedFromVersionIds;

  /// 版本元数据
  @override
  Map<String, dynamic> get metadata;

  /// 校验和（用于完整性检查）
  @override
  String? get checksum;

  /// Create a copy of ChapterVersion
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChapterVersionImplCopyWith<_$ChapterVersionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
