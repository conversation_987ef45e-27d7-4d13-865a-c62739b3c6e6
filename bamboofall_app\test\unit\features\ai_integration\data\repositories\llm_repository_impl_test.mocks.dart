// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in bamboofall_app/test/unit/features/ai_integration/data/repositories/llm_repository_impl_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:bamboofall_app/core/storage/database.dart' as _i8;
import 'package:bamboofall_app/core/storage/secure_storage.dart' as _i9;
import 'package:bamboofall_app/features/ai_integration/data/datasources/claude_datasource.dart'
    as _i5;
import 'package:bamboofall_app/features/ai_integration/data/datasources/deepseek_datasource.dart'
    as _i7;
import 'package:bamboofall_app/features/ai_integration/data/datasources/gemini_datasource.dart'
    as _i6;
import 'package:bamboofall_app/features/ai_integration/data/datasources/openai_datasource.dart'
    as _i3;
import 'package:bamboofall_app/features/ai_integration/domain/entities/ai_model.dart'
    as _i2;
import 'package:logger/logger.dart' as _i10;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAIResponse_0 extends _i1.SmartFake implements _i2.AIResponse {
  _FakeAIResponse_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [OpenAIDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockOpenAIDataSource extends _i1.Mock implements _i3.OpenAIDataSource {
  MockOpenAIDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void setApiKey(String? apiKey, {String? organizationId}) =>
      super.noSuchMethod(
        Invocation.method(
          #setApiKey,
          [apiKey],
          {#organizationId: organizationId},
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Future<_i2.AIResponse> chatCompletion(_i2.AIRequest? request) =>
      (super.noSuchMethod(
            Invocation.method(#chatCompletion, [request]),
            returnValue: _i4.Future<_i2.AIResponse>.value(
              _FakeAIResponse_0(
                this,
                Invocation.method(#chatCompletion, [request]),
              ),
            ),
          )
          as _i4.Future<_i2.AIResponse>);

  @override
  _i4.Stream<_i2.AIResponse> chatCompletionStream(_i2.AIRequest? request) =>
      (super.noSuchMethod(
            Invocation.method(#chatCompletionStream, [request]),
            returnValue: _i4.Stream<_i2.AIResponse>.empty(),
          )
          as _i4.Stream<_i2.AIResponse>);

  @override
  _i4.Future<_i2.AIResponse> completion(_i2.AIRequest? request) =>
      (super.noSuchMethod(
            Invocation.method(#completion, [request]),
            returnValue: _i4.Future<_i2.AIResponse>.value(
              _FakeAIResponse_0(
                this,
                Invocation.method(#completion, [request]),
              ),
            ),
          )
          as _i4.Future<_i2.AIResponse>);

  @override
  _i4.Future<List<_i2.AIModel>> getModels() =>
      (super.noSuchMethod(
            Invocation.method(#getModels, []),
            returnValue: _i4.Future<List<_i2.AIModel>>.value(<_i2.AIModel>[]),
          )
          as _i4.Future<List<_i2.AIModel>>);
}

/// A class which mocks [ClaudeDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockClaudeDataSource extends _i1.Mock implements _i5.ClaudeDataSource {
  MockClaudeDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void setApiKey(String? apiKey) => super.noSuchMethod(
    Invocation.method(#setApiKey, [apiKey]),
    returnValueForMissingStub: null,
  );

  @override
  _i4.Future<_i2.AIResponse> sendMessage(_i2.AIRequest? request) =>
      (super.noSuchMethod(
            Invocation.method(#sendMessage, [request]),
            returnValue: _i4.Future<_i2.AIResponse>.value(
              _FakeAIResponse_0(
                this,
                Invocation.method(#sendMessage, [request]),
              ),
            ),
          )
          as _i4.Future<_i2.AIResponse>);

  @override
  _i4.Stream<_i2.AIResponse> sendMessageStream(_i2.AIRequest? request) =>
      (super.noSuchMethod(
            Invocation.method(#sendMessageStream, [request]),
            returnValue: _i4.Stream<_i2.AIResponse>.empty(),
          )
          as _i4.Stream<_i2.AIResponse>);

  @override
  List<_i2.AIModel> getSupportedModels() =>
      (super.noSuchMethod(
            Invocation.method(#getSupportedModels, []),
            returnValue: <_i2.AIModel>[],
          )
          as List<_i2.AIModel>);
}

/// A class which mocks [GeminiDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockGeminiDataSource extends _i1.Mock implements _i6.GeminiDataSource {
  MockGeminiDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void setApiKey(String? apiKey) => super.noSuchMethod(
    Invocation.method(#setApiKey, [apiKey]),
    returnValueForMissingStub: null,
  );

  @override
  _i4.Future<_i2.AIResponse> generateContent(_i2.AIRequest? request) =>
      (super.noSuchMethod(
            Invocation.method(#generateContent, [request]),
            returnValue: _i4.Future<_i2.AIResponse>.value(
              _FakeAIResponse_0(
                this,
                Invocation.method(#generateContent, [request]),
              ),
            ),
          )
          as _i4.Future<_i2.AIResponse>);

  @override
  _i4.Stream<_i2.AIResponse> generateContentStream(_i2.AIRequest? request) =>
      (super.noSuchMethod(
            Invocation.method(#generateContentStream, [request]),
            returnValue: _i4.Stream<_i2.AIResponse>.empty(),
          )
          as _i4.Stream<_i2.AIResponse>);

  @override
  _i4.Future<List<_i2.AIModel>> getModels() =>
      (super.noSuchMethod(
            Invocation.method(#getModels, []),
            returnValue: _i4.Future<List<_i2.AIModel>>.value(<_i2.AIModel>[]),
          )
          as _i4.Future<List<_i2.AIModel>>);

  @override
  List<_i2.AIModel> getSupportedModels() =>
      (super.noSuchMethod(
            Invocation.method(#getSupportedModels, []),
            returnValue: <_i2.AIModel>[],
          )
          as List<_i2.AIModel>);
}

/// A class which mocks [DeepSeekDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockDeepSeekDataSource extends _i1.Mock
    implements _i7.DeepSeekDataSource {
  MockDeepSeekDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void setApiKey(String? apiKey) => super.noSuchMethod(
    Invocation.method(#setApiKey, [apiKey]),
    returnValueForMissingStub: null,
  );

  @override
  _i4.Future<_i2.AIResponse> chatCompletion(_i2.AIRequest? request) =>
      (super.noSuchMethod(
            Invocation.method(#chatCompletion, [request]),
            returnValue: _i4.Future<_i2.AIResponse>.value(
              _FakeAIResponse_0(
                this,
                Invocation.method(#chatCompletion, [request]),
              ),
            ),
          )
          as _i4.Future<_i2.AIResponse>);

  @override
  _i4.Stream<_i2.AIResponse> chatCompletionStream(_i2.AIRequest? request) =>
      (super.noSuchMethod(
            Invocation.method(#chatCompletionStream, [request]),
            returnValue: _i4.Stream<_i2.AIResponse>.empty(),
          )
          as _i4.Stream<_i2.AIResponse>);

  @override
  _i4.Future<List<_i2.AIModel>> getModels() =>
      (super.noSuchMethod(
            Invocation.method(#getModels, []),
            returnValue: _i4.Future<List<_i2.AIModel>>.value(<_i2.AIModel>[]),
          )
          as _i4.Future<List<_i2.AIModel>>);

  @override
  List<_i2.AIModel> getSupportedModels() =>
      (super.noSuchMethod(
            Invocation.method(#getSupportedModels, []),
            returnValue: <_i2.AIModel>[],
          )
          as List<_i2.AIModel>);
}

/// A class which mocks [LocalDatabase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLocalDatabase extends _i1.Mock implements _i8.LocalDatabase {
  MockLocalDatabase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<Map<String, dynamic>>> readCollection(String? collection) =>
      (super.noSuchMethod(
            Invocation.method(#readCollection, [collection]),
            returnValue: _i4.Future<List<Map<String, dynamic>>>.value(
              <Map<String, dynamic>>[],
            ),
          )
          as _i4.Future<List<Map<String, dynamic>>>);

  @override
  _i4.Future<void> writeCollection(
    String? collection,
    List<Map<String, dynamic>>? data,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#writeCollection, [collection, data]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> addDocument(
    String? collection,
    Map<String, dynamic>? document,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#addDocument, [collection, document]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<bool> updateDocument(
    String? collection,
    String? id,
    Map<String, dynamic>? updates,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateDocument, [collection, id, updates]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<bool> deleteDocument(String? collection, String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteDocument, [collection, id]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<Map<String, dynamic>?> findDocument(
    String? collection,
    String? id,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#findDocument, [collection, id]),
            returnValue: _i4.Future<Map<String, dynamic>?>.value(),
          )
          as _i4.Future<Map<String, dynamic>?>);

  @override
  _i4.Future<List<Map<String, dynamic>>> queryDocuments(
    String? collection, {
    bool Function(Map<String, dynamic>)? where,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #queryDocuments,
              [collection],
              {#where: where, #limit: limit, #offset: offset},
            ),
            returnValue: _i4.Future<List<Map<String, dynamic>>>.value(
              <Map<String, dynamic>>[],
            ),
          )
          as _i4.Future<List<Map<String, dynamic>>>);

  @override
  _i4.Future<void> clearCollection(String? collection) =>
      (super.noSuchMethod(
            Invocation.method(#clearCollection, [collection]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> deleteCollection(String? collection) =>
      (super.noSuchMethod(
            Invocation.method(#deleteCollection, [collection]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<String>> getCollections() =>
      (super.noSuchMethod(
            Invocation.method(#getCollections, []),
            returnValue: _i4.Future<List<String>>.value(<String>[]),
          )
          as _i4.Future<List<String>>);

  @override
  _i4.Future<Map<String, dynamic>> getStats() =>
      (super.noSuchMethod(
            Invocation.method(#getStats, []),
            returnValue: _i4.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i4.Future<Map<String, dynamic>>);
}

/// A class which mocks [SecureStorageManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockSecureStorageManager extends _i1.Mock
    implements _i9.SecureStorageManager {
  MockSecureStorageManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> saveAIConfig(List<_i9.AIConfig>? configs) =>
      (super.noSuchMethod(
            Invocation.method(#saveAIConfig, [configs]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<_i9.AIConfig>> getAIConfigs() =>
      (super.noSuchMethod(
            Invocation.method(#getAIConfigs, []),
            returnValue: _i4.Future<List<_i9.AIConfig>>.value(<_i9.AIConfig>[]),
          )
          as _i4.Future<List<_i9.AIConfig>>);

  @override
  _i4.Future<void> addAIConfig(_i9.AIConfig? config) =>
      (super.noSuchMethod(
            Invocation.method(#addAIConfig, [config]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> removeAIConfig(String? provider) =>
      (super.noSuchMethod(
            Invocation.method(#removeAIConfig, [provider]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<_i9.AIConfig?> getAIConfig(String? provider) =>
      (super.noSuchMethod(
            Invocation.method(#getAIConfig, [provider]),
            returnValue: _i4.Future<_i9.AIConfig?>.value(),
          )
          as _i4.Future<_i9.AIConfig?>);

  @override
  _i4.Future<List<_i9.AIConfig>> getActiveAIConfigs() =>
      (super.noSuchMethod(
            Invocation.method(#getActiveAIConfigs, []),
            returnValue: _i4.Future<List<_i9.AIConfig>>.value(<_i9.AIConfig>[]),
          )
          as _i4.Future<List<_i9.AIConfig>>);

  @override
  _i4.Future<void> saveUserPreferences(Map<String, dynamic>? preferences) =>
      (super.noSuchMethod(
            Invocation.method(#saveUserPreferences, [preferences]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<Map<String, dynamic>> getUserPreferences() =>
      (super.noSuchMethod(
            Invocation.method(#getUserPreferences, []),
            returnValue: _i4.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<void> updateUserPreference(String? key, dynamic value) =>
      (super.noSuchMethod(
            Invocation.method(#updateUserPreference, [key, value]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<T?> getUserPreference<T>(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#getUserPreference, [key]),
            returnValue: _i4.Future<T?>.value(),
          )
          as _i4.Future<T?>);

  @override
  _i4.Future<void> saveProjectSecret(
    String? projectName,
    String? key,
    String? value,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#saveProjectSecret, [projectName, key, value]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<String?> getProjectSecret(String? projectName, String? key) =>
      (super.noSuchMethod(
            Invocation.method(#getProjectSecret, [projectName, key]),
            returnValue: _i4.Future<String?>.value(),
          )
          as _i4.Future<String?>);

  @override
  _i4.Future<void> deleteProjectSecret(String? projectName, String? key) =>
      (super.noSuchMethod(
            Invocation.method(#deleteProjectSecret, [projectName, key]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> deleteAllProjectSecrets(String? projectName) =>
      (super.noSuchMethod(
            Invocation.method(#deleteAllProjectSecrets, [projectName]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> saveSecureData(String? key, String? value) =>
      (super.noSuchMethod(
            Invocation.method(#saveSecureData, [key, value]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<String?> getSecureData(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#getSecureData, [key]),
            returnValue: _i4.Future<String?>.value(),
          )
          as _i4.Future<String?>);

  @override
  _i4.Future<void> deleteSecureData(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#deleteSecureData, [key]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> clearAllSecureData() =>
      (super.noSuchMethod(
            Invocation.method(#clearAllSecureData, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<String>> getAllKeys() =>
      (super.noSuchMethod(
            Invocation.method(#getAllKeys, []),
            returnValue: _i4.Future<List<String>>.value(<String>[]),
          )
          as _i4.Future<List<String>>);

  @override
  _i4.Future<bool> containsKey(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#containsKey, [key]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<Map<String, dynamic>> getStorageStats() =>
      (super.noSuchMethod(
            Invocation.method(#getStorageStats, []),
            returnValue: _i4.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<Map<String, dynamic>> exportConfig() =>
      (super.noSuchMethod(
            Invocation.method(#exportConfig, []),
            returnValue: _i4.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i4.Future<Map<String, dynamic>>);
}

/// A class which mocks [Logger].
///
/// See the documentation for Mockito's code generation for more information.
class MockLogger extends _i1.Mock implements _i10.Logger {
  MockLogger() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> get init =>
      (super.noSuchMethod(
            Invocation.getter(#init),
            returnValue: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  void v(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #v,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void t(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #t,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void d(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #d,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void i(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #i,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void w(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #w,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void e(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #e,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void wtf(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #wtf,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void f(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #f,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void log(
    _i10.Level? level,
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #log,
      [level, message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  bool isClosed() =>
      (super.noSuchMethod(Invocation.method(#isClosed, []), returnValue: false)
          as bool);

  @override
  _i4.Future<void> close() =>
      (super.noSuchMethod(
            Invocation.method(#close, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}
