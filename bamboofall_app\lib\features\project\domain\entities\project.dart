import 'package:freezed_annotation/freezed_annotation.dart';

part 'project.freezed.dart';
part 'project.g.dart';

/// 项目实体
/// 表示一个创作项目，包含项目的基本信息和配置
@freezed
class Project with _$Project {
  const factory Project({
    /// 项目唯一标识符
    required String id,
    
    /// 项目名称
    required String name,
    
    /// 项目描述
    @Default('') String description,
    
    /// 项目类型
    @Default(ProjectType.novel) ProjectType type,
    
    /// 项目状态
    @Default(ProjectStatus.active) ProjectStatus status,
    
    /// 项目创建时间
    required DateTime createdAt,
    
    /// 项目最后修改时间
    required DateTime updatedAt,
    
    /// 项目创建者
    required String createdBy,
    
    /// 项目封面图片路径
    String? coverImagePath,
    
    /// 项目标签
    @Default([]) List<String> tags,
    
    /// 项目配置
    @Default(ProjectConfig()) ProjectConfig config,
    
    /// 项目统计信息
    @Default(ProjectStatistics()) ProjectStatistics statistics,
    
    /// 项目模板ID（如果基于模板创建）
    String? templateId,
    
    /// 项目归档时间（如果已归档）
    DateTime? archivedAt,
    
    /// 项目归档原因
    String? archiveReason,
    
    /// 项目是否为收藏
    @Default(false) bool isFavorite,
    
    /// 项目颜色主题
    String? colorTheme,
    
    /// 项目自定义字段
    @Default({}) Map<String, dynamic> customFields,
  }) = _Project;

  factory Project.fromJson(Map<String, dynamic> json) => _$ProjectFromJson(json);
}

/// 项目类型枚举
enum ProjectType {
  /// 小说
  novel,
  
  /// 短篇故事
  shortStory,
  
  /// 剧本
  screenplay,
  
  /// 诗歌
  poetry,
  
  /// 散文
  essay,
  
  /// 学术论文
  academic,
  
  /// 技术文档
  technical,
  
  /// 其他
  other,
}

/// 项目状态枚举
enum ProjectStatus {
  /// 活跃
  active,
  
  /// 暂停
  paused,
  
  /// 完成
  completed,
  
  /// 已归档
  archived,
  
  /// 已删除
  deleted,
}

/// 项目配置
@freezed
class ProjectConfig with _$ProjectConfig {
  const factory ProjectConfig({
    /// 目标字数
    @Default(0) int targetWordCount,
    
    /// 每日写作目标
    @Default(0) int dailyWritingGoal,
    
    /// 截止日期
    DateTime? deadline,
    
    /// 自动保存间隔（分钟）
    @Default(5) int autoSaveInterval,
    
    /// 启用版本控制
    @Default(true) bool enableVersionControl,
    
    /// 启用AI助手
    @Default(true) bool enableAIAssistant,
    
    /// 默认AI模型
    @Default('gpt-3.5-turbo') String defaultAIModel,
    
    /// 写作提醒设置
    @Default(WritingReminder()) WritingReminder writingReminder,
    
    /// 导出设置
    @Default(ExportSettings()) ExportSettings exportSettings,
    
    /// 备份设置
    @Default(BackupSettings()) BackupSettings backupSettings,
    
    /// 协作设置
    @Default(CollaborationSettings()) CollaborationSettings collaborationSettings,
  }) = _ProjectConfig;

  factory ProjectConfig.fromJson(Map<String, dynamic> json) => _$ProjectConfigFromJson(json);
}

/// 写作提醒设置
@freezed
class WritingReminder with _$WritingReminder {
  const factory WritingReminder({
    /// 是否启用提醒
    @Default(false) bool enabled,
    
    /// 提醒时间
    @Default([]) List<String> reminderTimes,
    
    /// 提醒频率
    @Default(ReminderFrequency.daily) ReminderFrequency frequency,
    
    /// 提醒消息
    @Default('该写作了！') String message,
  }) = _WritingReminder;

  factory WritingReminder.fromJson(Map<String, dynamic> json) => _$WritingReminderFromJson(json);
}

/// 提醒频率枚举
enum ReminderFrequency {
  daily,
  weekly,
  custom,
}

/// 导出设置
@freezed
class ExportSettings with _$ExportSettings {
  const factory ExportSettings({
    /// 默认导出格式
    @Default(ExportFormat.docx) ExportFormat defaultFormat,
    
    /// 包含元数据
    @Default(true) bool includeMetadata,
    
    /// 包含目录
    @Default(true) bool includeTableOfContents,
    
    /// 页面设置
    @Default(PageSettings()) PageSettings pageSettings,
    
    /// 字体设置
    @Default(FontSettings()) FontSettings fontSettings,
  }) = _ExportSettings;

  factory ExportSettings.fromJson(Map<String, dynamic> json) => _$ExportSettingsFromJson(json);
}

/// 导出格式枚举
enum ExportFormat {
  docx,
  pdf,
  txt,
  markdown,
  html,
  epub,
}

/// 页面设置
@freezed
class PageSettings with _$PageSettings {
  const factory PageSettings({
    /// 页面大小
    @Default('A4') String pageSize,
    
    /// 页边距
    @Default(PageMargins()) PageMargins margins,
    
    /// 行间距
    @Default(1.5) double lineSpacing,
  }) = _PageSettings;

  factory PageSettings.fromJson(Map<String, dynamic> json) => _$PageSettingsFromJson(json);
}

/// 页边距
@freezed
class PageMargins with _$PageMargins {
  const factory PageMargins({
    @Default(2.5) double top,
    @Default(2.5) double bottom,
    @Default(2.0) double left,
    @Default(2.0) double right,
  }) = _PageMargins;

  factory PageMargins.fromJson(Map<String, dynamic> json) => _$PageMarginsFromJson(json);
}

/// 字体设置
@freezed
class FontSettings with _$FontSettings {
  const factory FontSettings({
    /// 字体族
    @Default('Times New Roman') String fontFamily,
    
    /// 字体大小
    @Default(12) int fontSize,
    
    /// 是否粗体
    @Default(false) bool bold,
    
    /// 是否斜体
    @Default(false) bool italic,
  }) = _FontSettings;

  factory FontSettings.fromJson(Map<String, dynamic> json) => _$FontSettingsFromJson(json);
}

/// 备份设置
@freezed
class BackupSettings with _$BackupSettings {
  const factory BackupSettings({
    /// 是否启用自动备份
    @Default(true) bool autoBackup,
    
    /// 备份频率（小时）
    @Default(24) int backupFrequency,
    
    /// 保留备份数量
    @Default(10) int keepBackupCount,
    
    /// 备份位置
    @Default('') String backupLocation,
    
    /// 云备份设置
    @Default(CloudBackupSettings()) CloudBackupSettings cloudBackup,
  }) = _BackupSettings;

  factory BackupSettings.fromJson(Map<String, dynamic> json) => _$BackupSettingsFromJson(json);
}

/// 云备份设置
@freezed
class CloudBackupSettings with _$CloudBackupSettings {
  const factory CloudBackupSettings({
    /// 是否启用云备份
    @Default(false) bool enabled,
    
    /// 云服务提供商
    @Default(CloudProvider.none) CloudProvider provider,
    
    /// 访问令牌
    String? accessToken,
    
    /// 备份路径
    @Default('') String backupPath,
  }) = _CloudBackupSettings;

  factory CloudBackupSettings.fromJson(Map<String, dynamic> json) => _$CloudBackupSettingsFromJson(json);
}

/// 云服务提供商枚举
enum CloudProvider {
  none,
  googleDrive,
  oneDrive,
  dropbox,
  iCloud,
}

/// 协作设置
@freezed
class CollaborationSettings with _$CollaborationSettings {
  const factory CollaborationSettings({
    /// 是否启用协作
    @Default(false) bool enabled,
    
    /// 协作者列表
    @Default([]) List<Collaborator> collaborators,
    
    /// 权限设置
    @Default(PermissionSettings()) PermissionSettings permissions,
  }) = _CollaborationSettings;

  factory CollaborationSettings.fromJson(Map<String, dynamic> json) => _$CollaborationSettingsFromJson(json);
}

/// 协作者
@freezed
class Collaborator with _$Collaborator {
  const factory Collaborator({
    /// 协作者ID
    required String id,
    
    /// 协作者姓名
    required String name,
    
    /// 协作者邮箱
    required String email,
    
    /// 协作者角色
    @Default(CollaboratorRole.viewer) CollaboratorRole role,
    
    /// 邀请时间
    required DateTime invitedAt,
    
    /// 接受邀请时间
    DateTime? acceptedAt,
    
    /// 协作者状态
    @Default(CollaboratorStatus.pending) CollaboratorStatus status,
  }) = _Collaborator;

  factory Collaborator.fromJson(Map<String, dynamic> json) => _$CollaboratorFromJson(json);
}

/// 协作者角色枚举
enum CollaboratorRole {
  owner,
  editor,
  reviewer,
  viewer,
}

/// 协作者状态枚举
enum CollaboratorStatus {
  pending,
  active,
  inactive,
  removed,
}

/// 权限设置
@freezed
class PermissionSettings with _$PermissionSettings {
  const factory PermissionSettings({
    /// 允许编辑
    @Default(true) bool allowEdit,
    
    /// 允许评论
    @Default(true) bool allowComment,
    
    /// 允许导出
    @Default(false) bool allowExport,
    
    /// 允许分享
    @Default(false) bool allowShare,
    
    /// 允许删除
    @Default(false) bool allowDelete,
  }) = _PermissionSettings;

  factory PermissionSettings.fromJson(Map<String, dynamic> json) => _$PermissionSettingsFromJson(json);
}

/// 项目统计信息
@freezed
class ProjectStatistics with _$ProjectStatistics {
  const factory ProjectStatistics({
    /// 总字数
    @Default(0) int totalWordCount,
    
    /// 总字符数
    @Default(0) int totalCharacterCount,
    
    /// 章节数
    @Default(0) int chapterCount,
    
    /// 版本数
    @Default(0) int versionCount,
    
    /// 写作天数
    @Default(0) int writingDays,
    
    /// 平均每日字数
    @Default(0) int averageDailyWords,
    
    /// 最后写作时间
    DateTime? lastWritingTime,
    
    /// 完成进度（0-100）
    @Default(0.0) double completionProgress,
    
    /// 写作时长（分钟）
    @Default(0) int writingTimeMinutes,
    
    /// 最高日写作字数
    @Default(0) int maxDailyWords,
    
    /// 连续写作天数
    @Default(0) int consecutiveWritingDays,
  }) = _ProjectStatistics;

  factory ProjectStatistics.fromJson(Map<String, dynamic> json) => _$ProjectStatisticsFromJson(json);
}

/// 项目类型扩展
extension ProjectTypeExtension on ProjectType {
  /// 显示名称
  String get displayName {
    switch (this) {
      case ProjectType.novel:
        return '小说';
      case ProjectType.shortStory:
        return '短篇故事';
      case ProjectType.screenplay:
        return '剧本';
      case ProjectType.poetry:
        return '诗歌';
      case ProjectType.essay:
        return '散文';
      case ProjectType.academic:
        return '学术论文';
      case ProjectType.technical:
        return '技术文档';
      case ProjectType.other:
        return '其他';
    }
  }

  /// 图标
  String get icon {
    switch (this) {
      case ProjectType.novel:
        return '📚';
      case ProjectType.shortStory:
        return '📖';
      case ProjectType.screenplay:
        return '🎬';
      case ProjectType.poetry:
        return '🎭';
      case ProjectType.essay:
        return '📝';
      case ProjectType.academic:
        return '🎓';
      case ProjectType.technical:
        return '💻';
      case ProjectType.other:
        return '📄';
    }
  }

  /// 默认目标字数
  int get defaultTargetWordCount {
    switch (this) {
      case ProjectType.novel:
        return 80000;
      case ProjectType.shortStory:
        return 5000;
      case ProjectType.screenplay:
        return 25000;
      case ProjectType.poetry:
        return 1000;
      case ProjectType.essay:
        return 3000;
      case ProjectType.academic:
        return 10000;
      case ProjectType.technical:
        return 15000;
      case ProjectType.other:
        return 5000;
    }
  }
}

/// 项目状态扩展
extension ProjectStatusExtension on ProjectStatus {
  /// 显示名称
  String get displayName {
    switch (this) {
      case ProjectStatus.active:
        return '活跃';
      case ProjectStatus.paused:
        return '暂停';
      case ProjectStatus.completed:
        return '完成';
      case ProjectStatus.archived:
        return '已归档';
      case ProjectStatus.deleted:
        return '已删除';
    }
  }

  /// 颜色
  String get color {
    switch (this) {
      case ProjectStatus.active:
        return '#4CAF50'; // 绿色
      case ProjectStatus.paused:
        return '#FF9800'; // 橙色
      case ProjectStatus.completed:
        return '#2196F3'; // 蓝色
      case ProjectStatus.archived:
        return '#9E9E9E'; // 灰色
      case ProjectStatus.deleted:
        return '#F44336'; // 红色
    }
  }

  /// 是否可编辑
  bool get canEdit {
    switch (this) {
      case ProjectStatus.active:
      case ProjectStatus.paused:
        return true;
      case ProjectStatus.completed:
      case ProjectStatus.archived:
      case ProjectStatus.deleted:
        return false;
    }
  }

  /// 是否可删除
  bool get canDelete {
    switch (this) {
      case ProjectStatus.active:
      case ProjectStatus.paused:
      case ProjectStatus.completed:
      case ProjectStatus.archived:
        return true;
      case ProjectStatus.deleted:
        return false;
    }
  }
}

/// 项目扩展方法
extension ProjectExtension on Project {
  /// 是否为活跃项目
  bool get isActive => status == ProjectStatus.active;

  /// 是否已完成
  bool get isCompleted => status == ProjectStatus.completed;

  /// 是否已归档
  bool get isArchived => status == ProjectStatus.archived;

  /// 是否已删除
  bool get isDeleted => status == ProjectStatus.deleted;

  /// 获取完成进度百分比
  double get completionPercentage {
    if (config.targetWordCount <= 0) return 0.0;
    return (statistics.totalWordCount / config.targetWordCount * 100).clamp(0.0, 100.0);
  }

  /// 获取剩余字数
  int get remainingWordCount {
    final remaining = config.targetWordCount - statistics.totalWordCount;
    return remaining > 0 ? remaining : 0;
  }

  /// 获取预计完成时间
  DateTime? get estimatedCompletionDate {
    if (statistics.averageDailyWords <= 0 || remainingWordCount <= 0) {
      return null;
    }
    
    final remainingDays = (remainingWordCount / statistics.averageDailyWords).ceil();
    return DateTime.now().add(Duration(days: remainingDays));
  }

  /// 是否超过截止日期
  bool get isOverdue {
    if (config.deadline == null) return false;
    return DateTime.now().isAfter(config.deadline!) && !isCompleted;
  }

  /// 获取项目年龄（天数）
  int get ageInDays {
    return DateTime.now().difference(createdAt).inDays;
  }

  /// 更新统计信息
  Project updateStatistics(ProjectStatistics newStatistics) {
    return copyWith(
      statistics: newStatistics,
      updatedAt: DateTime.now(),
    );
  }

  /// 归档项目
  Project archive(String reason) {
    return copyWith(
      status: ProjectStatus.archived,
      archivedAt: DateTime.now(),
      archiveReason: reason,
      updatedAt: DateTime.now(),
    );
  }

  /// 恢复项目
  Project restore() {
    return copyWith(
      status: ProjectStatus.active,
      archivedAt: null,
      archiveReason: null,
      updatedAt: DateTime.now(),
    );
  }

  /// 完成项目
  Project complete() {
    return copyWith(
      status: ProjectStatus.completed,
      updatedAt: DateTime.now(),
    );
  }

  /// 暂停项目
  Project pause() {
    return copyWith(
      status: ProjectStatus.paused,
      updatedAt: DateTime.now(),
    );
  }

  /// 恢复活跃状态
  Project resume() {
    return copyWith(
      status: ProjectStatus.active,
      updatedAt: DateTime.now(),
    );
  }

  /// 切换收藏状态
  Project toggleFavorite() {
    return copyWith(
      isFavorite: !isFavorite,
      updatedAt: DateTime.now(),
    );
  }

  /// 添加标签
  Project addTag(String tag) {
    if (tags.contains(tag)) return this;
    return copyWith(
      tags: [...tags, tag],
      updatedAt: DateTime.now(),
    );
  }

  /// 移除标签
  Project removeTag(String tag) {
    return copyWith(
      tags: tags.where((t) => t != tag).toList(),
      updatedAt: DateTime.now(),
    );
  }

  /// 更新配置
  Project updateConfig(ProjectConfig newConfig) {
    return copyWith(
      config: newConfig,
      updatedAt: DateTime.now(),
    );
  }
}