import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

import '../../domain/entities/ai_model.dart';
import '../../domain/repositories/llm_repository.dart';

/// OpenAI数据源
/// 实现OpenAI API的具体集成
class OpenAIDataSource {
  final Dio _dio;
  final Logger _logger;
  
  static const String _baseUrl = 'https://api.openai.com/v1';
  static const String _chatCompletionsEndpoint = '/chat/completions';
  static const String _completionsEndpoint = '/completions';
  static const String _modelsEndpoint = '/models';
  
  OpenAIDataSource({
    Dio? dio,
    Logger? logger,
  }) : _dio = dio ?? Dio(),
       _logger = logger ?? Logger() {
    _setupDio();
  }
  
  /// 配置Dio实例
  void _setupDio() {
    _dio.options.baseUrl = _baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 60);
    _dio.options.sendTimeout = const Duration(seconds: 30);
    
    // 添加拦截器
    _dio.interceptors.add(LogInterceptor(
      requestBody: false, // 不记录请求体，避免泄露敏感信息
      responseBody: false, // 不记录响应体，避免日志过长
      logPrint: (obj) => _logger.d(obj),
    ));
    
    // 添加重试拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onError: (error, handler) async {
        if (_shouldRetry(error)) {
          try {
            final response = await _dio.fetch(error.requestOptions);
            handler.resolve(response);
            return;
          } catch (e) {
            // 重试失败，继续抛出原错误
          }
        }
        handler.next(error);
      },
    ));
  }
  
  /// 判断是否应该重试
  bool _shouldRetry(DioException error) {
    if (error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.receiveTimeout ||
        error.type == DioExceptionType.sendTimeout) {
      return true;
    }
    
    if (error.response?.statusCode == 429 || // Rate limit
        error.response?.statusCode == 502 || // Bad gateway
        error.response?.statusCode == 503 || // Service unavailable
        error.response?.statusCode == 504) { // Gateway timeout
      return true;
    }
    
    return false;
  }
  
  /// 设置API密钥
  void setApiKey(String apiKey, {String? organizationId}) {
    _dio.options.headers['Authorization'] = 'Bearer $apiKey';
    if (organizationId != null) {
      _dio.options.headers['OpenAI-Organization'] = organizationId;
    }
  }
  
  /// 聊天补全
  Future<AIResponse> chatCompletion(AIRequest request) async {
    try {
      _logger.d('Sending chat completion request to OpenAI');
      
      final requestData = _buildChatCompletionRequest(request);
      final response = await _dio.post(_chatCompletionsEndpoint, data: requestData);
      
      return _parseChatCompletionResponse(response.data, request.requestId);
    } on DioException catch (e) {
      _logger.e('OpenAI chat completion error: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      _logger.e('Unexpected error in chat completion: $e');
      throw LLMRepositoryException('Unexpected error: $e');
    }
  }
  
  /// 流式聊天补全
  Stream<AIResponse> chatCompletionStream(AIRequest request) async* {
    try {
      _logger.d('Sending streaming chat completion request to OpenAI');
      
      final requestData = _buildChatCompletionRequest(request, stream: true);
      
      final response = await _dio.post(
        _chatCompletionsEndpoint,
        data: requestData,
        options: Options(
          responseType: ResponseType.stream,
          headers: {'Accept': 'text/event-stream'},
        ),
      );
      
      await for (final chunk in _parseStreamResponse(response.data.stream)) {
        if (chunk != null) {
          yield chunk;
        }
      }
    } on DioException catch (e) {
      _logger.e('OpenAI streaming chat completion error: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      _logger.e('Unexpected error in streaming chat completion: $e');
      throw LLMRepositoryException('Unexpected error: $e');
    }
  }
  
  /// 文本补全
  Future<AIResponse> completion(AIRequest request) async {
    try {
      _logger.d('Sending completion request to OpenAI');
      
      final requestData = _buildCompletionRequest(request);
      final response = await _dio.post(_completionsEndpoint, data: requestData);
      
      return _parseCompletionResponse(response.data, request.requestId);
    } on DioException catch (e) {
      _logger.e('OpenAI completion error: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      _logger.e('Unexpected error in completion: $e');
      throw LLMRepositoryException('Unexpected error: $e');
    }
  }
  
  /// 获取可用模型
  Future<List<AIModel>> getModels() async {
    try {
      _logger.d('Fetching available models from OpenAI');
      
      final response = await _dio.get(_modelsEndpoint);
      return _parseModelsResponse(response.data);
    } on DioException catch (e) {
      _logger.e('OpenAI get models error: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      _logger.e('Unexpected error in get models: $e');
      throw LLMRepositoryException('Unexpected error: $e');
    }
  }
  
  /// 构建聊天补全请求
  Map<String, dynamic> _buildChatCompletionRequest(AIRequest request, {bool stream = false}) {
    final messages = request.messages?.map((msg) => {
      'role': msg.role,
      'content': msg.content,
      if (msg.name != null) 'name': msg.name,
    }).toList() ?? [
      if (request.systemPrompt != null) {
        'role': 'system',
        'content': request.systemPrompt,
      },
      {
        'role': 'user',
        'content': request.prompt,
      },
    ];
    
    return {
      'model': request.modelId,
      'messages': messages,
      'temperature': request.temperature,
      'top_p': request.topP,
      'max_tokens': request.maxTokens,
      'frequency_penalty': request.frequencyPenalty,
      'presence_penalty': request.presencePenalty,
      if (request.stop != null && request.stop!.isNotEmpty) 'stop': request.stop,
      'stream': stream,
    };
  }
  
  /// 构建文本补全请求
  Map<String, dynamic> _buildCompletionRequest(AIRequest request) {
    return {
      'model': request.modelId,
      'prompt': request.prompt,
      'temperature': request.temperature,
      'top_p': request.topP,
      'max_tokens': request.maxTokens,
      'frequency_penalty': request.frequencyPenalty,
      'presence_penalty': request.presencePenalty,
      if (request.stop != null && request.stop!.isNotEmpty) 'stop': request.stop,
    };
  }
  
  /// 解析聊天补全响应
  AIResponse _parseChatCompletionResponse(Map<String, dynamic> data, String? requestId) {
    final choice = data['choices'][0];
    final message = choice['message'];
    final usage = data['usage'];
    
    return AIResponse(
      id: data['id'],
      modelId: data['model'],
      content: message['content'] ?? '',
      usage: AIUsage(
        promptTokens: usage['prompt_tokens'],
        completionTokens: usage['completion_tokens'],
        totalTokens: usage['total_tokens'],
      ),
      finishReason: choice['finish_reason'],
      choices: (data['choices'] as List).map((choice) => AIChoice(
        index: choice['index'],
        message: AIMessage(
          role: choice['message']['role'],
          content: choice['message']['content'] ?? '',
          name: choice['message']['name'],
        ),
        finishReason: choice['finish_reason'],
      )).toList(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['created'] * 1000),
    );
  }
  
  /// 解析文本补全响应
  AIResponse _parseCompletionResponse(Map<String, dynamic> data, String? requestId) {
    final choice = data['choices'][0];
    final usage = data['usage'];
    
    return AIResponse(
      id: data['id'],
      modelId: data['model'],
      content: choice['text'] ?? '',
      usage: AIUsage(
        promptTokens: usage['prompt_tokens'],
        completionTokens: usage['completion_tokens'],
        totalTokens: usage['total_tokens'],
      ),
      finishReason: choice['finish_reason'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['created'] * 1000),
    );
  }
  
  /// 解析模型列表响应
  List<AIModel> _parseModelsResponse(Map<String, dynamic> data) {
    final models = data['data'] as List;
    return models.map((model) => AIModel(
      id: model['id'],
      name: model['id'],
      displayName: model['id'],
      provider: AIProvider.openai,
      type: _getModelType(model['id']),
      status: AIModelStatus.available,
      createdAt: DateTime.fromMillisecondsSinceEpoch(model['created'] * 1000),
    )).toList();
  }
  
  /// 解析流式响应
  Stream<AIResponse?> _parseStreamResponse(Stream<List<int>> stream) async* {
    String buffer = '';
    
    await for (final chunk in stream) {
      buffer += utf8.decode(chunk);
      final lines = buffer.split('\n');
      buffer = lines.removeLast(); // 保留最后一行（可能不完整）
      
      for (final line in lines) {
        if (line.startsWith('data: ')) {
          final data = line.substring(6).trim();
          if (data == '[DONE]') {
            return;
          }
          
          try {
            final json = jsonDecode(data);
            final choice = json['choices'][0];
            final delta = choice['delta'];
            
            if (delta['content'] != null) {
              yield AIResponse(
                id: json['id'],
                modelId: json['model'],
                content: delta['content'],
                usage: const AIUsage(
                  promptTokens: 0,
                  completionTokens: 0,
                  totalTokens: 0,
                ),
                finishReason: choice['finish_reason'],
                createdAt: DateTime.fromMillisecondsSinceEpoch(json['created'] * 1000),
              );
            }
          } catch (e) {
            _logger.w('Failed to parse streaming chunk: $e');
          }
        }
      }
    }
  }
  
  /// 获取模型类型
  AIModelType _getModelType(String modelId) {
    if (modelId.contains('gpt') || modelId.contains('chat')) {
      return AIModelType.chat;
    } else if (modelId.contains('embedding')) {
      return AIModelType.embedding;
    } else {
      return AIModelType.completion;
    }
  }
  
  /// 处理Dio异常
  LLMRepositoryException _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return TimeoutException('Request timeout: ${e.message}');
      
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final responseData = e.response?.data;
        
        switch (statusCode) {
          case 401:
            return AuthenticationException('Invalid API key');
          case 429:
            return QuotaExceededException('Rate limit exceeded');
          case 404:
            return ModelUnavailableException('Model not found');
          case 500:
          case 502:
          case 503:
          case 504:
            return NetworkException('Server error: $statusCode');
          default:
            String message = 'HTTP error: $statusCode';
            if (responseData is Map && responseData['error'] != null) {
              message = responseData['error']['message'] ?? message;
            }
            return LLMRepositoryException(message, code: statusCode.toString());
        }
      
      case DioExceptionType.connectionError:
        return NetworkException('Connection error: ${e.message}');
      
      case DioExceptionType.badCertificate:
        return NetworkException('SSL certificate error: ${e.message}');
      
      case DioExceptionType.cancel:
        return LLMRepositoryException('Request cancelled');
      
      default:
        return LLMRepositoryException('Unknown error: ${e.message}');
    }
  }
}