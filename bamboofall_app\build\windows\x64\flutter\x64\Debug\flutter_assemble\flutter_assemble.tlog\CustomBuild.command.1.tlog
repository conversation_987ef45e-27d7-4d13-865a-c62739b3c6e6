^E:\PROJECT\BAMBOOFALL\BAMBOOFALL_APP\BUILD\WINDOWS\X64\CMAKEFILES\CAB9B84204F4FB10657156BEDA00292B\FLUTTER_WINDOWS.DLL.RULE
setlocal
"D:\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=D:\flutter PROJECT_DIR=E:\project\bamboofall\bamboofall_app FLUTTER_ROOT=D:\flutter FLUTTER_EPHEMERAL_DIR=E:\project\bamboofall\bamboofall_app\windows\flutter\ephemeral PROJECT_DIR=E:\project\bamboofall\bamboofall_app FLUTTER_TARGET=E:\project\bamboofall\bamboofall_app\lib\main.dart DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzUuNA==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZDY5M2I0YjlkYg==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049YzI5ODA5MTM1MQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My45LjI= DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=E:\project\bamboofall\bamboofall_app\.dart_tool\package_config.json D:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECT\BAMBOOFALL\BAMBOOFALL_APP\BUILD\WINDOWS\X64\CMAKEFILES\490E8BCF738B7E5C6AAA7D06732731E1\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\PROJECT\BAMBOOFALL\BAMBOOFALL_APP\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"D:\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/project/bamboofall/bamboofall_app/windows -BE:/project/bamboofall/bamboofall_app/build/windows/x64 --check-stamp-file E:/project/bamboofall/bamboofall_app/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
