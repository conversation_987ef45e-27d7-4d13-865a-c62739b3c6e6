// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ChapterVersionImpl _$$ChapterVersionImplFromJson(Map<String, dynamic> json) =>
    _$ChapterVersionImpl(
      id: json['id'] as String,
      chapterId: json['chapterId'] as String,
      versionNumber: (json['versionNumber'] as num).toInt(),
      title: json['title'] as String,
      content: json['content'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      createdBy: json['createdBy'] as String,
      description: json['description'] as String? ?? '',
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
          const [],
      status:
          $enumDecodeNullable(_$VersionStatusEnumMap, json['status']) ??
          VersionStatus.draft,
      wordCount: (json['wordCount'] as num?)?.toInt() ?? 0,
      characterCount: (json['characterCount'] as num?)?.toInt() ?? 0,
      paragraphCount: (json['paragraphCount'] as num?)?.toInt() ?? 0,
      sizeInBytes: (json['sizeInBytes'] as num?)?.toInt() ?? 0,
      isMainVersion: json['isMainVersion'] as bool? ?? false,
      isAutoSave: json['isAutoSave'] as bool? ?? false,
      parentVersionId: json['parentVersionId'] as String?,
      mergedFromVersionIds:
          (json['mergedFromVersionIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      checksum: json['checksum'] as String?,
    );

Map<String, dynamic> _$$ChapterVersionImplToJson(
  _$ChapterVersionImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'chapterId': instance.chapterId,
  'versionNumber': instance.versionNumber,
  'title': instance.title,
  'content': instance.content,
  'createdAt': instance.createdAt.toIso8601String(),
  'createdBy': instance.createdBy,
  'description': instance.description,
  'tags': instance.tags,
  'status': _$VersionStatusEnumMap[instance.status]!,
  'wordCount': instance.wordCount,
  'characterCount': instance.characterCount,
  'paragraphCount': instance.paragraphCount,
  'sizeInBytes': instance.sizeInBytes,
  'isMainVersion': instance.isMainVersion,
  'isAutoSave': instance.isAutoSave,
  'parentVersionId': instance.parentVersionId,
  'mergedFromVersionIds': instance.mergedFromVersionIds,
  'metadata': instance.metadata,
  'checksum': instance.checksum,
};

const _$VersionStatusEnumMap = {
  VersionStatus.draft: 'draft',
  VersionStatus.pendingReview: 'pendingReview',
  VersionStatus.inReview: 'inReview',
  VersionStatus.approved: 'approved',
  VersionStatus.rejected: 'rejected',
  VersionStatus.published: 'published',
  VersionStatus.archived: 'archived',
  VersionStatus.deleted: 'deleted',
};
