// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'version_diff.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

VersionDiff _$VersionDiffFromJson(Map<String, dynamic> json) {
  return _VersionDiff.fromJson(json);
}

/// @nodoc
mixin _$VersionDiff {
  /// 差异唯一标识符
  String get id => throw _privateConstructorUsedError;

  /// 源版本ID
  String get sourceVersionId => throw _privateConstructorUsedError;

  /// 目标版本ID
  String get targetVersionId => throw _privateConstructorUsedError;

  /// 差异创建时间
  DateTime get createdAt => throw _privateConstructorUsedError;

  /// 差异类型
  DiffType get diffType => throw _privateConstructorUsedError;

  /// 差异操作列表
  List<DiffOperation> get operations => throw _privateConstructorUsedError;

  /// 统计信息
  DiffStatistics get statistics => throw _privateConstructorUsedError;

  /// 差异元数据
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;

  /// Serializes this VersionDiff to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VersionDiff
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VersionDiffCopyWith<VersionDiff> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VersionDiffCopyWith<$Res> {
  factory $VersionDiffCopyWith(
    VersionDiff value,
    $Res Function(VersionDiff) then,
  ) = _$VersionDiffCopyWithImpl<$Res, VersionDiff>;
  @useResult
  $Res call({
    String id,
    String sourceVersionId,
    String targetVersionId,
    DateTime createdAt,
    DiffType diffType,
    List<DiffOperation> operations,
    DiffStatistics statistics,
    Map<String, dynamic> metadata,
  });

  $DiffStatisticsCopyWith<$Res> get statistics;
}

/// @nodoc
class _$VersionDiffCopyWithImpl<$Res, $Val extends VersionDiff>
    implements $VersionDiffCopyWith<$Res> {
  _$VersionDiffCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VersionDiff
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? sourceVersionId = null,
    Object? targetVersionId = null,
    Object? createdAt = null,
    Object? diffType = null,
    Object? operations = null,
    Object? statistics = null,
    Object? metadata = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            sourceVersionId: null == sourceVersionId
                ? _value.sourceVersionId
                : sourceVersionId // ignore: cast_nullable_to_non_nullable
                      as String,
            targetVersionId: null == targetVersionId
                ? _value.targetVersionId
                : targetVersionId // ignore: cast_nullable_to_non_nullable
                      as String,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            diffType: null == diffType
                ? _value.diffType
                : diffType // ignore: cast_nullable_to_non_nullable
                      as DiffType,
            operations: null == operations
                ? _value.operations
                : operations // ignore: cast_nullable_to_non_nullable
                      as List<DiffOperation>,
            statistics: null == statistics
                ? _value.statistics
                : statistics // ignore: cast_nullable_to_non_nullable
                      as DiffStatistics,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }

  /// Create a copy of VersionDiff
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DiffStatisticsCopyWith<$Res> get statistics {
    return $DiffStatisticsCopyWith<$Res>(_value.statistics, (value) {
      return _then(_value.copyWith(statistics: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$VersionDiffImplCopyWith<$Res>
    implements $VersionDiffCopyWith<$Res> {
  factory _$$VersionDiffImplCopyWith(
    _$VersionDiffImpl value,
    $Res Function(_$VersionDiffImpl) then,
  ) = __$$VersionDiffImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String sourceVersionId,
    String targetVersionId,
    DateTime createdAt,
    DiffType diffType,
    List<DiffOperation> operations,
    DiffStatistics statistics,
    Map<String, dynamic> metadata,
  });

  @override
  $DiffStatisticsCopyWith<$Res> get statistics;
}

/// @nodoc
class __$$VersionDiffImplCopyWithImpl<$Res>
    extends _$VersionDiffCopyWithImpl<$Res, _$VersionDiffImpl>
    implements _$$VersionDiffImplCopyWith<$Res> {
  __$$VersionDiffImplCopyWithImpl(
    _$VersionDiffImpl _value,
    $Res Function(_$VersionDiffImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VersionDiff
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? sourceVersionId = null,
    Object? targetVersionId = null,
    Object? createdAt = null,
    Object? diffType = null,
    Object? operations = null,
    Object? statistics = null,
    Object? metadata = null,
  }) {
    return _then(
      _$VersionDiffImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        sourceVersionId: null == sourceVersionId
            ? _value.sourceVersionId
            : sourceVersionId // ignore: cast_nullable_to_non_nullable
                  as String,
        targetVersionId: null == targetVersionId
            ? _value.targetVersionId
            : targetVersionId // ignore: cast_nullable_to_non_nullable
                  as String,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        diffType: null == diffType
            ? _value.diffType
            : diffType // ignore: cast_nullable_to_non_nullable
                  as DiffType,
        operations: null == operations
            ? _value._operations
            : operations // ignore: cast_nullable_to_non_nullable
                  as List<DiffOperation>,
        statistics: null == statistics
            ? _value.statistics
            : statistics // ignore: cast_nullable_to_non_nullable
                  as DiffStatistics,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$VersionDiffImpl implements _VersionDiff {
  const _$VersionDiffImpl({
    required this.id,
    required this.sourceVersionId,
    required this.targetVersionId,
    required this.createdAt,
    this.diffType = DiffType.textDiff,
    final List<DiffOperation> operations = const [],
    required this.statistics,
    final Map<String, dynamic> metadata = const {},
  }) : _operations = operations,
       _metadata = metadata;

  factory _$VersionDiffImpl.fromJson(Map<String, dynamic> json) =>
      _$$VersionDiffImplFromJson(json);

  /// 差异唯一标识符
  @override
  final String id;

  /// 源版本ID
  @override
  final String sourceVersionId;

  /// 目标版本ID
  @override
  final String targetVersionId;

  /// 差异创建时间
  @override
  final DateTime createdAt;

  /// 差异类型
  @override
  @JsonKey()
  final DiffType diffType;

  /// 差异操作列表
  final List<DiffOperation> _operations;

  /// 差异操作列表
  @override
  @JsonKey()
  List<DiffOperation> get operations {
    if (_operations is EqualUnmodifiableListView) return _operations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_operations);
  }

  /// 统计信息
  @override
  final DiffStatistics statistics;

  /// 差异元数据
  final Map<String, dynamic> _metadata;

  /// 差异元数据
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  String toString() {
    return 'VersionDiff(id: $id, sourceVersionId: $sourceVersionId, targetVersionId: $targetVersionId, createdAt: $createdAt, diffType: $diffType, operations: $operations, statistics: $statistics, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VersionDiffImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.sourceVersionId, sourceVersionId) ||
                other.sourceVersionId == sourceVersionId) &&
            (identical(other.targetVersionId, targetVersionId) ||
                other.targetVersionId == targetVersionId) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.diffType, diffType) ||
                other.diffType == diffType) &&
            const DeepCollectionEquality().equals(
              other._operations,
              _operations,
            ) &&
            (identical(other.statistics, statistics) ||
                other.statistics == statistics) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    sourceVersionId,
    targetVersionId,
    createdAt,
    diffType,
    const DeepCollectionEquality().hash(_operations),
    statistics,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of VersionDiff
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VersionDiffImplCopyWith<_$VersionDiffImpl> get copyWith =>
      __$$VersionDiffImplCopyWithImpl<_$VersionDiffImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VersionDiffImplToJson(this);
  }
}

abstract class _VersionDiff implements VersionDiff {
  const factory _VersionDiff({
    required final String id,
    required final String sourceVersionId,
    required final String targetVersionId,
    required final DateTime createdAt,
    final DiffType diffType,
    final List<DiffOperation> operations,
    required final DiffStatistics statistics,
    final Map<String, dynamic> metadata,
  }) = _$VersionDiffImpl;

  factory _VersionDiff.fromJson(Map<String, dynamic> json) =
      _$VersionDiffImpl.fromJson;

  /// 差异唯一标识符
  @override
  String get id;

  /// 源版本ID
  @override
  String get sourceVersionId;

  /// 目标版本ID
  @override
  String get targetVersionId;

  /// 差异创建时间
  @override
  DateTime get createdAt;

  /// 差异类型
  @override
  DiffType get diffType;

  /// 差异操作列表
  @override
  List<DiffOperation> get operations;

  /// 统计信息
  @override
  DiffStatistics get statistics;

  /// 差异元数据
  @override
  Map<String, dynamic> get metadata;

  /// Create a copy of VersionDiff
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VersionDiffImplCopyWith<_$VersionDiffImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DiffOperation _$DiffOperationFromJson(Map<String, dynamic> json) {
  return _DiffOperation.fromJson(json);
}

/// @nodoc
mixin _$DiffOperation {
  /// 操作类型
  OperationType get type => throw _privateConstructorUsedError;

  /// 操作位置（在源文本中的位置）
  int get position => throw _privateConstructorUsedError;

  /// 操作长度
  int get length => throw _privateConstructorUsedError;

  /// 操作内容
  String get content => throw _privateConstructorUsedError;

  /// 行号（如果适用）
  int? get lineNumber => throw _privateConstructorUsedError;

  /// 列号（如果适用）
  int? get columnNumber => throw _privateConstructorUsedError;

  /// 操作元数据
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;

  /// Serializes this DiffOperation to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DiffOperation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DiffOperationCopyWith<DiffOperation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DiffOperationCopyWith<$Res> {
  factory $DiffOperationCopyWith(
    DiffOperation value,
    $Res Function(DiffOperation) then,
  ) = _$DiffOperationCopyWithImpl<$Res, DiffOperation>;
  @useResult
  $Res call({
    OperationType type,
    int position,
    int length,
    String content,
    int? lineNumber,
    int? columnNumber,
    Map<String, dynamic> metadata,
  });
}

/// @nodoc
class _$DiffOperationCopyWithImpl<$Res, $Val extends DiffOperation>
    implements $DiffOperationCopyWith<$Res> {
  _$DiffOperationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DiffOperation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? position = null,
    Object? length = null,
    Object? content = null,
    Object? lineNumber = freezed,
    Object? columnNumber = freezed,
    Object? metadata = null,
  }) {
    return _then(
      _value.copyWith(
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as OperationType,
            position: null == position
                ? _value.position
                : position // ignore: cast_nullable_to_non_nullable
                      as int,
            length: null == length
                ? _value.length
                : length // ignore: cast_nullable_to_non_nullable
                      as int,
            content: null == content
                ? _value.content
                : content // ignore: cast_nullable_to_non_nullable
                      as String,
            lineNumber: freezed == lineNumber
                ? _value.lineNumber
                : lineNumber // ignore: cast_nullable_to_non_nullable
                      as int?,
            columnNumber: freezed == columnNumber
                ? _value.columnNumber
                : columnNumber // ignore: cast_nullable_to_non_nullable
                      as int?,
            metadata: null == metadata
                ? _value.metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DiffOperationImplCopyWith<$Res>
    implements $DiffOperationCopyWith<$Res> {
  factory _$$DiffOperationImplCopyWith(
    _$DiffOperationImpl value,
    $Res Function(_$DiffOperationImpl) then,
  ) = __$$DiffOperationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    OperationType type,
    int position,
    int length,
    String content,
    int? lineNumber,
    int? columnNumber,
    Map<String, dynamic> metadata,
  });
}

/// @nodoc
class __$$DiffOperationImplCopyWithImpl<$Res>
    extends _$DiffOperationCopyWithImpl<$Res, _$DiffOperationImpl>
    implements _$$DiffOperationImplCopyWith<$Res> {
  __$$DiffOperationImplCopyWithImpl(
    _$DiffOperationImpl _value,
    $Res Function(_$DiffOperationImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DiffOperation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? position = null,
    Object? length = null,
    Object? content = null,
    Object? lineNumber = freezed,
    Object? columnNumber = freezed,
    Object? metadata = null,
  }) {
    return _then(
      _$DiffOperationImpl(
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as OperationType,
        position: null == position
            ? _value.position
            : position // ignore: cast_nullable_to_non_nullable
                  as int,
        length: null == length
            ? _value.length
            : length // ignore: cast_nullable_to_non_nullable
                  as int,
        content: null == content
            ? _value.content
            : content // ignore: cast_nullable_to_non_nullable
                  as String,
        lineNumber: freezed == lineNumber
            ? _value.lineNumber
            : lineNumber // ignore: cast_nullable_to_non_nullable
                  as int?,
        columnNumber: freezed == columnNumber
            ? _value.columnNumber
            : columnNumber // ignore: cast_nullable_to_non_nullable
                  as int?,
        metadata: null == metadata
            ? _value._metadata
            : metadata // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$DiffOperationImpl implements _DiffOperation {
  const _$DiffOperationImpl({
    required this.type,
    required this.position,
    this.length = 0,
    this.content = '',
    this.lineNumber,
    this.columnNumber,
    final Map<String, dynamic> metadata = const {},
  }) : _metadata = metadata;

  factory _$DiffOperationImpl.fromJson(Map<String, dynamic> json) =>
      _$$DiffOperationImplFromJson(json);

  /// 操作类型
  @override
  final OperationType type;

  /// 操作位置（在源文本中的位置）
  @override
  final int position;

  /// 操作长度
  @override
  @JsonKey()
  final int length;

  /// 操作内容
  @override
  @JsonKey()
  final String content;

  /// 行号（如果适用）
  @override
  final int? lineNumber;

  /// 列号（如果适用）
  @override
  final int? columnNumber;

  /// 操作元数据
  final Map<String, dynamic> _metadata;

  /// 操作元数据
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  String toString() {
    return 'DiffOperation(type: $type, position: $position, length: $length, content: $content, lineNumber: $lineNumber, columnNumber: $columnNumber, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DiffOperationImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.position, position) ||
                other.position == position) &&
            (identical(other.length, length) || other.length == length) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.lineNumber, lineNumber) ||
                other.lineNumber == lineNumber) &&
            (identical(other.columnNumber, columnNumber) ||
                other.columnNumber == columnNumber) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    type,
    position,
    length,
    content,
    lineNumber,
    columnNumber,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of DiffOperation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DiffOperationImplCopyWith<_$DiffOperationImpl> get copyWith =>
      __$$DiffOperationImplCopyWithImpl<_$DiffOperationImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DiffOperationImplToJson(this);
  }
}

abstract class _DiffOperation implements DiffOperation {
  const factory _DiffOperation({
    required final OperationType type,
    required final int position,
    final int length,
    final String content,
    final int? lineNumber,
    final int? columnNumber,
    final Map<String, dynamic> metadata,
  }) = _$DiffOperationImpl;

  factory _DiffOperation.fromJson(Map<String, dynamic> json) =
      _$DiffOperationImpl.fromJson;

  /// 操作类型
  @override
  OperationType get type;

  /// 操作位置（在源文本中的位置）
  @override
  int get position;

  /// 操作长度
  @override
  int get length;

  /// 操作内容
  @override
  String get content;

  /// 行号（如果适用）
  @override
  int? get lineNumber;

  /// 列号（如果适用）
  @override
  int? get columnNumber;

  /// 操作元数据
  @override
  Map<String, dynamic> get metadata;

  /// Create a copy of DiffOperation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DiffOperationImplCopyWith<_$DiffOperationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DiffStatistics _$DiffStatisticsFromJson(Map<String, dynamic> json) {
  return _DiffStatistics.fromJson(json);
}

/// @nodoc
mixin _$DiffStatistics {
  /// 插入的字符数
  int get insertedCharacters => throw _privateConstructorUsedError;

  /// 删除的字符数
  int get deletedCharacters => throw _privateConstructorUsedError;

  /// 插入的单词数
  int get insertedWords => throw _privateConstructorUsedError;

  /// 删除的单词数
  int get deletedWords => throw _privateConstructorUsedError;

  /// 插入的行数
  int get insertedLines => throw _privateConstructorUsedError;

  /// 删除的行数
  int get deletedLines => throw _privateConstructorUsedError;

  /// 修改的行数
  int get modifiedLines => throw _privateConstructorUsedError;

  /// 相似度（0-1之间）
  double get similarity => throw _privateConstructorUsedError;

  /// 变更密度（变更数/总长度）
  double get changeDensity => throw _privateConstructorUsedError;

  /// 复杂度评分（1-10）
  int get complexityScore => throw _privateConstructorUsedError;

  /// Serializes this DiffStatistics to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DiffStatistics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DiffStatisticsCopyWith<DiffStatistics> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DiffStatisticsCopyWith<$Res> {
  factory $DiffStatisticsCopyWith(
    DiffStatistics value,
    $Res Function(DiffStatistics) then,
  ) = _$DiffStatisticsCopyWithImpl<$Res, DiffStatistics>;
  @useResult
  $Res call({
    int insertedCharacters,
    int deletedCharacters,
    int insertedWords,
    int deletedWords,
    int insertedLines,
    int deletedLines,
    int modifiedLines,
    double similarity,
    double changeDensity,
    int complexityScore,
  });
}

/// @nodoc
class _$DiffStatisticsCopyWithImpl<$Res, $Val extends DiffStatistics>
    implements $DiffStatisticsCopyWith<$Res> {
  _$DiffStatisticsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DiffStatistics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? insertedCharacters = null,
    Object? deletedCharacters = null,
    Object? insertedWords = null,
    Object? deletedWords = null,
    Object? insertedLines = null,
    Object? deletedLines = null,
    Object? modifiedLines = null,
    Object? similarity = null,
    Object? changeDensity = null,
    Object? complexityScore = null,
  }) {
    return _then(
      _value.copyWith(
            insertedCharacters: null == insertedCharacters
                ? _value.insertedCharacters
                : insertedCharacters // ignore: cast_nullable_to_non_nullable
                      as int,
            deletedCharacters: null == deletedCharacters
                ? _value.deletedCharacters
                : deletedCharacters // ignore: cast_nullable_to_non_nullable
                      as int,
            insertedWords: null == insertedWords
                ? _value.insertedWords
                : insertedWords // ignore: cast_nullable_to_non_nullable
                      as int,
            deletedWords: null == deletedWords
                ? _value.deletedWords
                : deletedWords // ignore: cast_nullable_to_non_nullable
                      as int,
            insertedLines: null == insertedLines
                ? _value.insertedLines
                : insertedLines // ignore: cast_nullable_to_non_nullable
                      as int,
            deletedLines: null == deletedLines
                ? _value.deletedLines
                : deletedLines // ignore: cast_nullable_to_non_nullable
                      as int,
            modifiedLines: null == modifiedLines
                ? _value.modifiedLines
                : modifiedLines // ignore: cast_nullable_to_non_nullable
                      as int,
            similarity: null == similarity
                ? _value.similarity
                : similarity // ignore: cast_nullable_to_non_nullable
                      as double,
            changeDensity: null == changeDensity
                ? _value.changeDensity
                : changeDensity // ignore: cast_nullable_to_non_nullable
                      as double,
            complexityScore: null == complexityScore
                ? _value.complexityScore
                : complexityScore // ignore: cast_nullable_to_non_nullable
                      as int,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DiffStatisticsImplCopyWith<$Res>
    implements $DiffStatisticsCopyWith<$Res> {
  factory _$$DiffStatisticsImplCopyWith(
    _$DiffStatisticsImpl value,
    $Res Function(_$DiffStatisticsImpl) then,
  ) = __$$DiffStatisticsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int insertedCharacters,
    int deletedCharacters,
    int insertedWords,
    int deletedWords,
    int insertedLines,
    int deletedLines,
    int modifiedLines,
    double similarity,
    double changeDensity,
    int complexityScore,
  });
}

/// @nodoc
class __$$DiffStatisticsImplCopyWithImpl<$Res>
    extends _$DiffStatisticsCopyWithImpl<$Res, _$DiffStatisticsImpl>
    implements _$$DiffStatisticsImplCopyWith<$Res> {
  __$$DiffStatisticsImplCopyWithImpl(
    _$DiffStatisticsImpl _value,
    $Res Function(_$DiffStatisticsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DiffStatistics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? insertedCharacters = null,
    Object? deletedCharacters = null,
    Object? insertedWords = null,
    Object? deletedWords = null,
    Object? insertedLines = null,
    Object? deletedLines = null,
    Object? modifiedLines = null,
    Object? similarity = null,
    Object? changeDensity = null,
    Object? complexityScore = null,
  }) {
    return _then(
      _$DiffStatisticsImpl(
        insertedCharacters: null == insertedCharacters
            ? _value.insertedCharacters
            : insertedCharacters // ignore: cast_nullable_to_non_nullable
                  as int,
        deletedCharacters: null == deletedCharacters
            ? _value.deletedCharacters
            : deletedCharacters // ignore: cast_nullable_to_non_nullable
                  as int,
        insertedWords: null == insertedWords
            ? _value.insertedWords
            : insertedWords // ignore: cast_nullable_to_non_nullable
                  as int,
        deletedWords: null == deletedWords
            ? _value.deletedWords
            : deletedWords // ignore: cast_nullable_to_non_nullable
                  as int,
        insertedLines: null == insertedLines
            ? _value.insertedLines
            : insertedLines // ignore: cast_nullable_to_non_nullable
                  as int,
        deletedLines: null == deletedLines
            ? _value.deletedLines
            : deletedLines // ignore: cast_nullable_to_non_nullable
                  as int,
        modifiedLines: null == modifiedLines
            ? _value.modifiedLines
            : modifiedLines // ignore: cast_nullable_to_non_nullable
                  as int,
        similarity: null == similarity
            ? _value.similarity
            : similarity // ignore: cast_nullable_to_non_nullable
                  as double,
        changeDensity: null == changeDensity
            ? _value.changeDensity
            : changeDensity // ignore: cast_nullable_to_non_nullable
                  as double,
        complexityScore: null == complexityScore
            ? _value.complexityScore
            : complexityScore // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$DiffStatisticsImpl implements _DiffStatistics {
  const _$DiffStatisticsImpl({
    this.insertedCharacters = 0,
    this.deletedCharacters = 0,
    this.insertedWords = 0,
    this.deletedWords = 0,
    this.insertedLines = 0,
    this.deletedLines = 0,
    this.modifiedLines = 0,
    this.similarity = 0.0,
    this.changeDensity = 0.0,
    this.complexityScore = 1,
  });

  factory _$DiffStatisticsImpl.fromJson(Map<String, dynamic> json) =>
      _$$DiffStatisticsImplFromJson(json);

  /// 插入的字符数
  @override
  @JsonKey()
  final int insertedCharacters;

  /// 删除的字符数
  @override
  @JsonKey()
  final int deletedCharacters;

  /// 插入的单词数
  @override
  @JsonKey()
  final int insertedWords;

  /// 删除的单词数
  @override
  @JsonKey()
  final int deletedWords;

  /// 插入的行数
  @override
  @JsonKey()
  final int insertedLines;

  /// 删除的行数
  @override
  @JsonKey()
  final int deletedLines;

  /// 修改的行数
  @override
  @JsonKey()
  final int modifiedLines;

  /// 相似度（0-1之间）
  @override
  @JsonKey()
  final double similarity;

  /// 变更密度（变更数/总长度）
  @override
  @JsonKey()
  final double changeDensity;

  /// 复杂度评分（1-10）
  @override
  @JsonKey()
  final int complexityScore;

  @override
  String toString() {
    return 'DiffStatistics(insertedCharacters: $insertedCharacters, deletedCharacters: $deletedCharacters, insertedWords: $insertedWords, deletedWords: $deletedWords, insertedLines: $insertedLines, deletedLines: $deletedLines, modifiedLines: $modifiedLines, similarity: $similarity, changeDensity: $changeDensity, complexityScore: $complexityScore)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DiffStatisticsImpl &&
            (identical(other.insertedCharacters, insertedCharacters) ||
                other.insertedCharacters == insertedCharacters) &&
            (identical(other.deletedCharacters, deletedCharacters) ||
                other.deletedCharacters == deletedCharacters) &&
            (identical(other.insertedWords, insertedWords) ||
                other.insertedWords == insertedWords) &&
            (identical(other.deletedWords, deletedWords) ||
                other.deletedWords == deletedWords) &&
            (identical(other.insertedLines, insertedLines) ||
                other.insertedLines == insertedLines) &&
            (identical(other.deletedLines, deletedLines) ||
                other.deletedLines == deletedLines) &&
            (identical(other.modifiedLines, modifiedLines) ||
                other.modifiedLines == modifiedLines) &&
            (identical(other.similarity, similarity) ||
                other.similarity == similarity) &&
            (identical(other.changeDensity, changeDensity) ||
                other.changeDensity == changeDensity) &&
            (identical(other.complexityScore, complexityScore) ||
                other.complexityScore == complexityScore));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    insertedCharacters,
    deletedCharacters,
    insertedWords,
    deletedWords,
    insertedLines,
    deletedLines,
    modifiedLines,
    similarity,
    changeDensity,
    complexityScore,
  );

  /// Create a copy of DiffStatistics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DiffStatisticsImplCopyWith<_$DiffStatisticsImpl> get copyWith =>
      __$$DiffStatisticsImplCopyWithImpl<_$DiffStatisticsImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$DiffStatisticsImplToJson(this);
  }
}

abstract class _DiffStatistics implements DiffStatistics {
  const factory _DiffStatistics({
    final int insertedCharacters,
    final int deletedCharacters,
    final int insertedWords,
    final int deletedWords,
    final int insertedLines,
    final int deletedLines,
    final int modifiedLines,
    final double similarity,
    final double changeDensity,
    final int complexityScore,
  }) = _$DiffStatisticsImpl;

  factory _DiffStatistics.fromJson(Map<String, dynamic> json) =
      _$DiffStatisticsImpl.fromJson;

  /// 插入的字符数
  @override
  int get insertedCharacters;

  /// 删除的字符数
  @override
  int get deletedCharacters;

  /// 插入的单词数
  @override
  int get insertedWords;

  /// 删除的单词数
  @override
  int get deletedWords;

  /// 插入的行数
  @override
  int get insertedLines;

  /// 删除的行数
  @override
  int get deletedLines;

  /// 修改的行数
  @override
  int get modifiedLines;

  /// 相似度（0-1之间）
  @override
  double get similarity;

  /// 变更密度（变更数/总长度）
  @override
  double get changeDensity;

  /// 复杂度评分（1-10）
  @override
  int get complexityScore;

  /// Create a copy of DiffStatistics
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DiffStatisticsImplCopyWith<_$DiffStatisticsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
