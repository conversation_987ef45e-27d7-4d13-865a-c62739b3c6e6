import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';

/// 应用日志器
class AppLogger {
  static final AppLogger _instance = AppLogger._internal();
  factory AppLogger() => _instance;
  AppLogger._internal();

  final List<LogEntry> _logBuffer = [];
  final StreamController<LogEntry> _logStreamController = 
      StreamController<LogEntry>.broadcast();
  
  Timer? _flushTimer;
  File? _logFile;
  bool _isInitialized = false;

  // 日志配置
  static const int _maxBufferSize = 1000;
  static const int _maxLogFileSize = 10 * 1024 * 1024; // 10MB
  static const int _maxLogFiles = 5;
  static const Duration _flushInterval = Duration(seconds: 30);

  LogLevel _minLogLevel = kDebugMode ? LogLevel.debug : LogLevel.info;

  /// 日志流
  Stream<LogEntry> get logStream => _logStreamController.stream;

  /// 日志缓冲区
  List<LogEntry> get logBuffer => List.unmodifiable(_logBuffer);

  /// 初始化日志器
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _initializeLogFile();
      _startPeriodicFlush();
      _isInitialized = true;
      
      info('Logger initialized successfully');
    } catch (e) {
      developer.log('Failed to initialize logger: $e', name: 'AppLogger');
    }
  }

  /// 初始化日志文件
  Future<void> _initializeLogFile() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final logDir = Directory('${appDir.path}/logs');
      
      if (!await logDir.exists()) {
        await logDir.create(recursive: true);
      }

      final logFileName = 'app_${DateTime.now().toIso8601String().split('T')[0]}.log';
      _logFile = File('${logDir.path}/$logFileName');

      // 检查并轮转日志文件
      await _rotateLogFilesIfNeeded();
      
    } catch (e) {
      developer.log('Failed to initialize log file: $e', name: 'AppLogger');
    }
  }

  /// 轮转日志文件
  Future<void> _rotateLogFilesIfNeeded() async {
    if (_logFile == null) return;

    try {
      // 检查当前日志文件大小
      if (await _logFile!.exists()) {
        final fileSize = await _logFile!.length();
        if (fileSize > _maxLogFileSize) {
          await _rotateLogFiles();
        }
      }
    } catch (e) {
      developer.log('Failed to rotate log files: $e', name: 'AppLogger');
    }
  }

  /// 执行日志文件轮转
  Future<void> _rotateLogFiles() async {
    if (_logFile == null) return;

    try {
      final logDir = _logFile!.parent;
      final logFiles = await logDir
          .list()
          .where((entity) => entity is File && entity.path.endsWith('.log'))
          .cast<File>()
          .toList();

      // 按修改时间排序
      logFiles.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));

      // 删除超出数量限制的旧日志文件
      if (logFiles.length >= _maxLogFiles) {
        for (int i = _maxLogFiles - 1; i < logFiles.length; i++) {
          await logFiles[i].delete();
        }
      }

      // 创建新的日志文件
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final newLogFileName = 'app_$timestamp.log';
      _logFile = File('${logDir.path}/$newLogFileName');
      
    } catch (e) {
      developer.log('Failed to execute log rotation: $e', name: 'AppLogger');
    }
  }

  /// 开始定期刷新
  void _startPeriodicFlush() {
    _flushTimer = Timer.periodic(_flushInterval, (_) {
      _flushLogs();
    });
  }

  /// 记录调试日志
  void debug(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? data}) {
    _log(LogLevel.debug, message, error: error, stackTrace: stackTrace, data: data);
  }

  /// 记录信息日志
  void info(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? data}) {
    _log(LogLevel.info, message, error: error, stackTrace: stackTrace, data: data);
  }

  /// 记录警告日志
  void warning(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? data}) {
    _log(LogLevel.warning, message, error: error, stackTrace: stackTrace, data: data);
  }

  /// 记录错误日志
  void error(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? data}) {
    _log(LogLevel.error, message, error: error, stackTrace: stackTrace, data: data);
  }

  /// 记录严重错误日志
  void critical(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? data}) {
    _log(LogLevel.critical, message, error: error, stackTrace: stackTrace, data: data);
  }

  /// 内部日志记录方法
  void _log(
    LogLevel level,
    String message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  }) {
    // 检查日志级别
    if (level.index < _minLogLevel.index) {
      return;
    }

    final logEntry = LogEntry(
      level: level,
      message: message,
      timestamp: DateTime.now(),
      error: error,
      stackTrace: stackTrace,
      data: data,
    );

    // 添加到缓冲区
    _logBuffer.add(logEntry);

    // 限制缓冲区大小
    if (_logBuffer.length > _maxBufferSize) {
      _logBuffer.removeAt(0);
    }

    // 发送到日志流
    _logStreamController.add(logEntry);

    // 输出到控制台（调试模式）
    if (kDebugMode) {
      _outputToConsole(logEntry);
    }

    // 立即刷新严重错误
    if (level == LogLevel.critical) {
      _flushLogs();
    }
  }

  /// 输出到控制台
  void _outputToConsole(LogEntry entry) {
    final timestamp = entry.timestamp.toIso8601String();
    final levelName = entry.level.name.toUpperCase();
    final message = '[$timestamp] [$levelName] ${entry.message}';
    
    switch (entry.level) {
      case LogLevel.debug:
        developer.log(message, name: 'DEBUG');
        break;
      case LogLevel.info:
        developer.log(message, name: 'INFO');
        break;
      case LogLevel.warning:
        developer.log(message, name: 'WARNING', level: 900);
        break;
      case LogLevel.error:
        developer.log(message, name: 'ERROR', level: 1000, error: entry.error, stackTrace: entry.stackTrace);
        break;
      case LogLevel.critical:
        developer.log(message, name: 'CRITICAL', level: 1200, error: entry.error, stackTrace: entry.stackTrace);
        break;
    }
  }

  /// 刷新日志到文件
  Future<void> _flushLogs() async {
    if (_logFile == null || _logBuffer.isEmpty) return;

    try {
      final logEntries = List<LogEntry>.from(_logBuffer);
      _logBuffer.clear();

      final logLines = logEntries.map((entry) => entry.toJsonString()).toList();
      final logContent = '${logLines.join('\n')}\n';

      await _logFile!.writeAsString(logContent, mode: FileMode.append);
      
      // 检查是否需要轮转日志文件
      await _rotateLogFilesIfNeeded();
      
    } catch (e) {
      developer.log('Failed to flush logs: $e', name: 'AppLogger');
    }
  }

  /// 设置最小日志级别
  void setMinLogLevel(LogLevel level) {
    _minLogLevel = level;
    info('Log level set to ${level.name}');
  }

  /// 获取日志统计
  LogStatistics getStatistics() {
    final now = DateTime.now();
    final last24Hours = now.subtract(const Duration(hours: 24));
    
    final recent24HoursLogs = _logBuffer
        .where((log) => log.timestamp.isAfter(last24Hours))
        .toList();

    final logsByLevel = <LogLevel, int>{};
    for (final log in _logBuffer) {
      logsByLevel[log.level] = (logsByLevel[log.level] ?? 0) + 1;
    }

    return LogStatistics(
      totalLogs: _logBuffer.length,
      logs24Hours: recent24HoursLogs.length,
      logsByLevel: logsByLevel,
      oldestLog: _logBuffer.isNotEmpty ? _logBuffer.first : null,
      newestLog: _logBuffer.isNotEmpty ? _logBuffer.last : null,
    );
  }

  /// 搜索日志
  List<LogEntry> searchLogs({
    String? query,
    LogLevel? level,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
  }) {
    var results = _logBuffer.where((log) {
      // 文本搜索
      if (query != null && query.isNotEmpty) {
        final searchText = query.toLowerCase();
        if (!log.message.toLowerCase().contains(searchText) &&
            (log.error?.toString().toLowerCase().contains(searchText) != true)) {
          return false;
        }
      }

      // 级别过滤
      if (level != null && log.level != level) {
        return false;
      }

      // 时间范围过滤
      if (startTime != null && log.timestamp.isBefore(startTime)) {
        return false;
      }
      if (endTime != null && log.timestamp.isAfter(endTime)) {
        return false;
      }

      return true;
    }).toList();

    // 按时间倒序排列
    results.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    // 限制结果数量
    if (limit != null && results.length > limit) {
      results = results.take(limit).toList();
    }

    return results;
  }

  /// 导出日志
  Future<String> exportLogs({
    DateTime? startTime,
    DateTime? endTime,
    LogLevel? minLevel,
  }) async {
    final buffer = StringBuffer();
    
    buffer.writeln('=== 日志导出 ===');
    buffer.writeln('导出时间: ${DateTime.now()}');
    buffer.writeln('');

    final filteredLogs = _logBuffer.where((log) {
      if (startTime != null && log.timestamp.isBefore(startTime)) {
        return false;
      }
      if (endTime != null && log.timestamp.isAfter(endTime)) {
        return false;
      }
      if (minLevel != null && log.level.index < minLevel.index) {
        return false;
      }
      return true;
    }).toList();

    for (final log in filteredLogs) {
      buffer.writeln(log.toFormattedString());
      buffer.writeln('---');
    }

    return buffer.toString();
  }

  /// 清理日志
  void clearLogs() {
    _logBuffer.clear();
    info('Log buffer cleared');
  }

  /// 清理旧日志文件
  Future<void> cleanupOldLogFiles({int daysToKeep = 30}) async {
    try {
      if (_logFile == null) return;

      final logDir = _logFile!.parent;
      final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));
      
      final logFiles = await logDir
          .list()
          .where((entity) => entity is File && entity.path.endsWith('.log'))
          .cast<File>()
          .toList();

      int deletedCount = 0;
      for (final file in logFiles) {
        final lastModified = await file.lastModified();
        if (lastModified.isBefore(cutoffDate)) {
          await file.delete();
          deletedCount++;
        }
      }

      if (deletedCount > 0) {
        info('Cleaned up $deletedCount old log files');
      }
      
    } catch (e) {
      error('Failed to cleanup old log files', error: e);
    }
  }

  /// 释放资源
  Future<void> dispose() async {
    _flushTimer?.cancel();
    await _flushLogs();
    _logStreamController.close();
    _logBuffer.clear();
  }
}

/// 日志条目
class LogEntry {
  final LogLevel level;
  final String message;
  final DateTime timestamp;
  final Object? error;
  final StackTrace? stackTrace;
  final Map<String, dynamic>? data;

  const LogEntry({
    required this.level,
    required this.message,
    required this.timestamp,
    this.error,
    this.stackTrace,
    this.data,
  });

  /// 转换为JSON字符串
  String toJsonString() {
    final json = {
      'level': level.name,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      if (error != null) 'error': error.toString(),
      if (stackTrace != null) 'stackTrace': stackTrace.toString(),
      if (data != null) 'data': data,
    };
    return jsonEncode(json);
  }

  /// 转换为格式化字符串
  String toFormattedString() {
    final buffer = StringBuffer();
    buffer.writeln('[${timestamp.toIso8601String()}] [${level.name.toUpperCase()}] $message');
    
    if (error != null) {
      buffer.writeln('Error: $error');
    }
    
    if (stackTrace != null) {
      buffer.writeln('Stack Trace:');
      buffer.writeln(stackTrace.toString());
    }
    
    if (data != null && data!.isNotEmpty) {
      buffer.writeln('Additional Data:');
      for (final entry in data!.entries) {
        buffer.writeln('  ${entry.key}: ${entry.value}');
      }
    }
    
    return buffer.toString();
  }

  /// 从JSON创建日志条目
  static LogEntry fromJson(Map<String, dynamic> json) {
    return LogEntry(
      level: LogLevel.values.firstWhere(
        (level) => level.name == json['level'],
        orElse: () => LogLevel.info,
      ),
      message: json['message'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
      error: json['error'],
      data: json['data'] as Map<String, dynamic>?,
    );
  }
}

/// 日志级别
enum LogLevel {
  debug,
  info,
  warning,
  error,
  critical,
}

/// 日志级别扩展
extension LogLevelExtension on LogLevel {
  String get displayName {
    switch (this) {
      case LogLevel.debug:
        return '调试';
      case LogLevel.info:
        return '信息';
      case LogLevel.warning:
        return '警告';
      case LogLevel.error:
        return '错误';
      case LogLevel.critical:
        return '严重';
    }
  }

  Color get color {
    switch (this) {
      case LogLevel.debug:
        return Colors.grey;
      case LogLevel.info:
        return Colors.blue;
      case LogLevel.warning:
        return Colors.orange;
      case LogLevel.error:
        return Colors.red;
      case LogLevel.critical:
        return Colors.purple;
    }
  }
}

/// 日志统计
class LogStatistics {
  final int totalLogs;
  final int logs24Hours;
  final Map<LogLevel, int> logsByLevel;
  final LogEntry? oldestLog;
  final LogEntry? newestLog;

  const LogStatistics({
    required this.totalLogs,
    required this.logs24Hours,
    required this.logsByLevel,
    this.oldestLog,
    this.newestLog,
  });
}

/// 日志过滤器
class LogFilter {
  final String? query;
  final LogLevel? level;
  final DateTime? startTime;
  final DateTime? endTime;

  const LogFilter({
    this.query,
    this.level,
    this.startTime,
    this.endTime,
  });
}

/// 日志格式化器
class LogFormatter {
  /// 格式化日志条目为表格行
  static List<String> formatAsTableRow(LogEntry entry) {
    return [
      entry.timestamp.toIso8601String(),
      entry.level.displayName,
      entry.message,
      entry.error?.toString() ?? '',
    ];
  }

  /// 格式化日志条目为CSV行
  static String formatAsCsvRow(LogEntry entry) {
    final fields = [
      entry.timestamp.toIso8601String(),
      entry.level.name,
      _escapeCsvField(entry.message),
      _escapeCsvField(entry.error?.toString() ?? ''),
    ];
    return fields.join(',');
  }

  /// 转义CSV字段
  static String _escapeCsvField(String field) {
    if (field.contains(',') || field.contains('"') || field.contains('\n')) {
      return '"${field.replaceAll('"', '""')}"';
    }
    return field;
  }
}

/// 日志工具类
class LoggerUtils {
  /// 创建带上下文的日志器
  static ContextLogger createContextLogger(String context) {
    return ContextLogger(context);
  }

  /// 测量执行时间并记录
  static Future<T> measureAndLog<T>(
    String operation,
    Future<T> Function() function, {
    LogLevel level = LogLevel.info,
  }) async {
    final stopwatch = Stopwatch()..start();
    try {
      final result = await function();
      stopwatch.stop();
      switch (level) {
        case LogLevel.debug:
          AppLogger().debug('$operation completed in ${stopwatch.elapsedMilliseconds}ms');
          break;
        case LogLevel.info:
          AppLogger().info('$operation completed in ${stopwatch.elapsedMilliseconds}ms');
          break;
        case LogLevel.warning:
          AppLogger().warning('$operation completed in ${stopwatch.elapsedMilliseconds}ms');
          break;
        case LogLevel.error:
          AppLogger().error('$operation completed in ${stopwatch.elapsedMilliseconds}ms');
          break;
        case LogLevel.critical:
          AppLogger().critical('$operation completed in ${stopwatch.elapsedMilliseconds}ms');
          break;
      }
      return result;
    } catch (error, stackTrace) {
      stopwatch.stop();
      AppLogger().error(
        '$operation failed after ${stopwatch.elapsedMilliseconds}ms',
        error: error,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }
}

/// 带上下文的日志器
class ContextLogger {
  final String context;
  final AppLogger _logger = AppLogger();

  ContextLogger(this.context);

  void debug(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? data}) {
    _logger.debug('[$context] $message', error: error, stackTrace: stackTrace, data: data);
  }

  void info(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? data}) {
    _logger.info('[$context] $message', error: error, stackTrace: stackTrace, data: data);
  }

  void warning(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? data}) {
    _logger.warning('[$context] $message', error: error, stackTrace: stackTrace, data: data);
  }

  void error(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? data}) {
    _logger.error('[$context] $message', error: error, stackTrace: stackTrace, data: data);
  }

  void critical(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? data}) {
    _logger.critical('[$context] $message', error: error, stackTrace: stackTrace, data: data);
  }
}