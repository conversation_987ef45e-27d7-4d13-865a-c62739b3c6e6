import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;
import 'package:logger/logger.dart';

import 'chapter_tree.dart';

/// 大纲面板
/// 显示章节结构和导航功能
class OutlinePanel extends ConsumerWidget {
  const OutlinePanel({super.key});

  static final Logger _logger = Logger();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        // 工具栏
        _buildToolbar(context),
        
        // 章节树
        Expanded(
          child: ChapterTree(
            onChapterSelected: (chapterId) {
              // TODO: 在编辑器中打开选中的章节
              _logger.d('Selected chapter: $chapterId');
            },
            onChapterEdit: (chapterId) {
              // TODO: 编辑章节标题
              _logger.d('Edit chapter: $chapterId');
            },
            onChapterDelete: (chapterId) {
              // TODO: 删除章节确认
              _logger.d('Delete chapter: $chapterId');
            },
          ),
        ),
      ],
    );
  }

  Widget _buildToolbar(BuildContext context) {
    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          Text(
            '章节大纲',
            style: fluent.FluentTheme.of(context).typography.bodyStrong,
          ),
          const Spacer(),
          fluent.IconButton(
            icon: const fluent.Icon(fluent.FluentIcons.more, size: 16),
            onPressed: () {
              // TODO: 显示更多选项
            },
          ),
        ],
      ),
    );
  }
}