E:/project/bamboofall/bamboofall_app/build/windows/x64/runner/Debug/data/icudtl.dat
E:/project/bamboofall/bamboofall_app/build/windows/x64/runner/Debug/flutter_windows.dll
E:/project/bamboofall/bamboofall_app/build/windows/x64/runner/Debug/flutter_secure_storage_windows_plugin.dll
E:/project/bamboofall/bamboofall_app/build/windows/x64/runner/Debug/screen_retriever_windows_plugin.dll
E:/project/bamboofall/bamboofall_app/build/windows/x64/runner/Debug/url_launcher_windows_plugin.dll
E:/project/bamboofall/bamboofall_app/build/windows/x64/runner/Debug/window_manager_plugin.dll
E:/project/bamboofall/bamboofall_app/build/windows/x64/runner/Debug/data/flutter_assets/AssetManifest.bin
E:/project/bamboofall/bamboofall_app/build/windows/x64/runner/Debug/data/flutter_assets/AssetManifest.json
E:/project/bamboofall/bamboofall_app/build/windows/x64/runner/Debug/data/flutter_assets/FontManifest.json
E:/project/bamboofall/bamboofall_app/build/windows/x64/runner/Debug/data/flutter_assets/fonts/MaterialIcons-Regular.otf
E:/project/bamboofall/bamboofall_app/build/windows/x64/runner/Debug/data/flutter_assets/kernel_blob.bin
E:/project/bamboofall/bamboofall_app/build/windows/x64/runner/Debug/data/flutter_assets/NativeAssetsManifest.json
E:/project/bamboofall/bamboofall_app/build/windows/x64/runner/Debug/data/flutter_assets/NOTICES.Z
E:/project/bamboofall/bamboofall_app/build/windows/x64/runner/Debug/data/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf
E:/project/bamboofall/bamboofall_app/build/windows/x64/runner/Debug/data/flutter_assets/packages/fluent_ui/assets/AcrylicNoise.png
E:/project/bamboofall/bamboofall_app/build/windows/x64/runner/Debug/data/flutter_assets/packages/fluent_ui/fonts/FluentIcons.ttf
E:/project/bamboofall/bamboofall_app/build/windows/x64/runner/Debug/data/flutter_assets/packages/fluent_ui/fonts/SegoeIcons.ttf
E:/project/bamboofall/bamboofall_app/build/windows/x64/runner/Debug/data/flutter_assets/shaders/ink_sparkle.frag