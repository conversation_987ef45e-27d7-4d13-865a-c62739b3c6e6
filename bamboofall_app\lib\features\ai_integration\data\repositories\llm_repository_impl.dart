import 'dart:async';
import 'dart:collection';
import 'dart:math';
import 'package:logger/logger.dart';

import '../../domain/entities/ai_model.dart';
import '../../domain/repositories/llm_repository.dart';
import '../datasources/openai_datasource.dart';
import '../datasources/claude_datasource.dart';
import '../datasources/gemini_datasource.dart';
import '../datasources/deepseek_datasource.dart';
import '../../../../core/storage/database.dart';
import '../../../../core/storage/secure_storage.dart';

/// LLM仓库实现
/// 整合所有AI服务提供商，提供统一的AI服务管理
class LLMRepositoryImpl implements LLMRepository {
  final OpenAIDataSource _openAIDataSource;
  final ClaudeDataSource _claudeDataSource;
  final GeminiDataSource _geminiDataSource;
  final DeepSeekDataSource _deepSeekDataSource;
  final LocalDatabase _database;
  final SecureStorageManager _secureStorage;
  final Logger _logger;
  
  // 缓存
  final Map<String, AIResponse> _responseCache = {};
  final Map<String, List<AIMessage>> _sessionCache = {};
  
  // 统计数据
  final Map<String, AIUsage> _usageStats = {};
  final Map<String, List<AIError>> _errorHistory = {};
  
  LLMRepositoryImpl({
    required OpenAIDataSource openAIDataSource,
    required ClaudeDataSource claudeDataSource,
    required GeminiDataSource geminiDataSource,
    required DeepSeekDataSource deepSeekDataSource,
    required LocalDatabase database,
    required SecureStorageManager secureStorage,
    Logger? logger,
  }) : _openAIDataSource = openAIDataSource,
       _claudeDataSource = claudeDataSource,
       _geminiDataSource = geminiDataSource,
       _deepSeekDataSource = deepSeekDataSource,
       _database = database,
       _secureStorage = secureStorage,
       _logger = logger ?? Logger();
  
  // ==================== 模型管理 ====================
  
  @override
  Future<List<AIModel>> getAvailableModels() async {
    try {
      final models = <AIModel>[];
      
      // 获取预定义模型
      models.addAll(PredefinedAIModels.models);
      
      // 尝试从各个数据源获取动态模型列表
      try {
        final openAIModels = await _openAIDataSource.getModels();
        models.addAll(openAIModels);
      } catch (e) {
        _logger.w('Failed to get OpenAI models: $e');
      }
      
      try {
        final geminiModels = await _geminiDataSource.getModels();
        models.addAll(geminiModels);
      } catch (e) {
        _logger.w('Failed to get Gemini models: $e');
      }
      
      // 去重并返回
      final uniqueModels = <String, AIModel>{};
      for (final model in models) {
        uniqueModels[model.id] = model;
      }
      
      return uniqueModels.values.toList();
    } catch (e) {
      _logger.e('Failed to get available models: $e');
      return PredefinedAIModels.models;
    }
  }
  
  @override
  Future<List<AIModel>> getModelsByProvider(AIProvider provider) async {
    final allModels = await getAvailableModels();
    return allModels.where((model) => model.provider == provider).toList();
  }
  
  @override
  Future<AIModel?> getModelById(String modelId) async {
    final allModels = await getAvailableModels();
    try {
      return allModels.firstWhere((model) => model.id == modelId);
    } catch (e) {
      return null;
    }
  }
  
  @override
  Future<bool> isModelAvailable(String modelId) async {
    final model = await getModelById(modelId);
    return model != null && model.status == AIModelStatus.available;
  }
  
  @override
  Future<AICapabilities> getModelCapabilities(String modelId) async {
    final model = await getModelById(modelId);
    if (model == null) {
      throw ModelUnavailableException('Model $modelId not found');
    }
    
    // 根据模型特性返回能力
    return AICapabilities(
      supportsStreaming: true,
      supportsSystemPrompt: model.provider != AIProvider.google,
      supportsFunctionCalling: model.supportedFeatures.contains('function_calling'),
      supportsImageInput: model.supportedFeatures.contains('vision'),
      supportsCodeExecution: model.supportedFeatures.contains('code_execution'),
      supportedLanguages: ['zh', 'en', 'ja', 'ko'],
      supportedFormats: ['text', 'markdown'],
    );
  }
  
  // ==================== 配置管理 ====================
  
  @override
  Future<void> saveModelConfig(AIModelConfig config) async {
    try {
      // 保存到安全存储
      final aiConfig = AIConfig(
        provider: config.modelId,
        apiKey: config.apiKey,
        baseUrl: config.baseUrl,
        parameters: {
          'temperature': config.temperature,
          'topP': config.topP,
          'maxTokens': config.maxTokens,
          'frequencyPenalty': config.frequencyPenalty,
          'presencePenalty': config.presencePenalty,
          'maxRetries': config.maxRetries,
          'timeoutMs': config.timeoutMs,
        },
        isActive: config.isEnabled,
      );
      
      await _secureStorage.addAIConfig(aiConfig);
      
      // 配置对应的数据源
      await _configureDataSource(config);
      
      _logger.d('Saved model config for ${config.modelId}');
    } catch (e) {
      _logger.e('Failed to save model config: $e');
      rethrow;
    }
  }
  
  @override
  Future<AIModelConfig?> getModelConfig(String modelId) async {
    try {
      final aiConfig = await _secureStorage.getAIConfig(modelId);
      if (aiConfig == null) return null;
      
      return AIModelConfig(
        modelId: modelId,
        apiKey: aiConfig.apiKey,
        baseUrl: aiConfig.baseUrl,
        temperature: aiConfig.parameters['temperature'] ?? 0.7,
        topP: aiConfig.parameters['topP'] ?? 1.0,
        maxTokens: aiConfig.parameters['maxTokens'] ?? 4096,
        frequencyPenalty: aiConfig.parameters['frequencyPenalty'] ?? 1.0,
        presencePenalty: aiConfig.parameters['presencePenalty'] ?? 1.0,
        maxRetries: aiConfig.parameters['maxRetries'] ?? 1,
        timeoutMs: aiConfig.parameters['timeoutMs'] ?? 30000,
        isEnabled: aiConfig.isActive,
      );
    } catch (e) {
      _logger.e('Failed to get model config: $e');
      return null;
    }
  }
  
  @override
  Future<List<AIModelConfig>> getAllModelConfigs() async {
    try {
      final aiConfigs = await _secureStorage.getAIConfigs();
      return aiConfigs.map((config) => AIModelConfig(
        modelId: config.provider,
        apiKey: config.apiKey,
        baseUrl: config.baseUrl,
        temperature: config.parameters['temperature'] ?? 0.7,
        topP: config.parameters['topP'] ?? 1.0,
        maxTokens: config.parameters['maxTokens'] ?? 4096,
        frequencyPenalty: config.parameters['frequencyPenalty'] ?? 1.0,
        presencePenalty: config.parameters['presencePenalty'] ?? 1.0,
        maxRetries: config.parameters['maxRetries'] ?? 1,
        timeoutMs: config.parameters['timeoutMs'] ?? 30000,
        isEnabled: config.isActive,
      )).toList();
    } catch (e) {
      _logger.e('Failed to get all model configs: $e');
      return [];
    }
  }
  
  @override
  Future<void> deleteModelConfig(String modelId) async {
    try {
      await _secureStorage.removeAIConfig(modelId);
      _logger.d('Deleted model config for $modelId');
    } catch (e) {
      _logger.e('Failed to delete model config: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> updateModelConfig(String modelId, AIModelConfig config) async {
    await saveModelConfig(config);
  }
  
  @override
  Future<bool> testModelConnection(String modelId) async {
    try {
      final testRequest = AIRequest(
        modelId: modelId,
        prompt: 'Hello',
        maxTokens: 10,
        temperature: 0.1,
      );
      
      final response = await chat(testRequest);
      return response.content.isNotEmpty;
    } catch (e) {
      _logger.w('Model connection test failed for $modelId: $e');
      return false;
    }
  }
  
  // ==================== AI交互 ====================
  
  @override
  Future<AIResponse> chat(AIRequest request) async {
    try {
      final dataSource = _getDataSourceForModel(request.modelId);
      final startTime = DateTime.now();
      
      AIResponse response;
      
      switch (dataSource) {
        case OpenAIDataSource():
          response = await dataSource.chatCompletion(request);
          break;
        case ClaudeDataSource():
          response = await dataSource.sendMessage(request);
          break;
        case GeminiDataSource():
          response = await dataSource.generateContent(request);
          break;
        case DeepSeekDataSource():
          response = await dataSource.chatCompletion(request);
          break;
        default:
          throw ModelUnavailableException('Unsupported model: ${request.modelId}');
      }
      
      // 计算响应时间
      final responseTime = DateTime.now().difference(startTime).inMilliseconds;
      response = response.copyWith(responseTimeMs: responseTime);
      
      // 记录使用统计
      await recordUsage(request.modelId, response.usage);
      
      return response;
    } catch (e) {
      await recordError(request.modelId, AIError(
        code: 'chat_error',
        message: e.toString(),
        timestamp: DateTime.now(),
      ));
      rethrow;
    }
  }
  
  @override
  Stream<AIResponse> chatStream(AIRequest request) async* {
    try {
      final dataSource = _getDataSourceForModel(request.modelId);
      
      switch (dataSource) {
        case OpenAIDataSource():
          yield* dataSource.chatCompletionStream(request);
          break;
        case ClaudeDataSource():
          yield* dataSource.sendMessageStream(request);
          break;
        case GeminiDataSource():
          yield* dataSource.generateContentStream(request);
          break;
        case DeepSeekDataSource():
          yield* dataSource.chatCompletionStream(request);
          break;
        default:
          throw ModelUnavailableException('Unsupported model: ${request.modelId}');
      }
    } catch (e) {
      await recordError(request.modelId, AIError(
        code: 'chat_stream_error',
        message: e.toString(),
        timestamp: DateTime.now(),
      ));
      rethrow;
    }
  }
  
  @override
  Future<AIResponse> completion(AIRequest request) async {
    // 对于不支持completion的模型，转换为chat
    if (_isOpenAICompatible(request.modelId)) {
      final dataSource = _getDataSourceForModel(request.modelId) as OpenAIDataSource;
      return await dataSource.completion(request);
    } else {
      // 转换为chat格式
      final chatRequest = request.copyWith(
        messages: [
          AIMessage(
            role: 'user',
            content: request.prompt,
            timestamp: DateTime.now(),
          ),
        ],
      );
      return await chat(chatRequest);
    }
  }
  
  @override
  Stream<AIResponse> completionStream(AIRequest request) async* {
    // 对于不支持completion的模型，转换为chat stream
    if (_isOpenAICompatible(request.modelId)) {
      final dataSource = _getDataSourceForModel(request.modelId) as OpenAIDataSource;
      yield* dataSource.chatCompletionStream(request);
    } else {
      // 转换为chat格式
      final chatRequest = request.copyWith(
        messages: [
          AIMessage(
            role: 'user',
            content: request.prompt,
            timestamp: DateTime.now(),
          ),
        ],
      );
      yield* chatStream(chatRequest);
    }
  }
  
  @override
  Future<List<AIResponse>> batchProcess(List<AIRequest> requests) async {
    final responses = <AIResponse>[];
    
    // 并发处理请求（限制并发数）
    const maxConcurrency = 3;
    final semaphore = Semaphore(maxConcurrency);
    
    final futures = requests.map((request) async {
      await semaphore.acquire();
      try {
        return await chat(request);
      } finally {
        semaphore.release();
      }
    });
    
    final results = await Future.wait(futures);
    responses.addAll(results);
    
    return responses;
  }
  
  // ==================== 会话管理 ====================
  
  @override
  Future<String> createSession({
    required String modelId,
    String? systemPrompt,
    Map<String, dynamic>? metadata,
  }) async {
    final sessionId = generateRequestId();
    final messages = <AIMessage>[];
    
    if (systemPrompt != null) {
      messages.add(AIMessage(
        role: 'system',
        content: systemPrompt,
        timestamp: DateTime.now(),
      ));
    }
    
    _sessionCache[sessionId] = messages;
    
    // 保存到数据库
    await _database.addDocument('sessions', {
      'id': sessionId,
      'modelId': modelId,
      'systemPrompt': systemPrompt,
      'metadata': metadata ?? {},
      'messages': messages.map((m) => m.toJson()).toList(),
    });
    
    return sessionId;
  }
  
  @override
  Future<List<AIMessage>> getSessionHistory(String sessionId) async {
    // 先从缓存获取
    if (_sessionCache.containsKey(sessionId)) {
      return _sessionCache[sessionId]!;
    }
    
    // 从数据库获取
    final session = await _database.findDocument('sessions', sessionId);
    if (session != null) {
      final messages = (session['messages'] as List)
          .map((m) => AIMessage.fromJson(m))
          .toList();
      _sessionCache[sessionId] = messages;
      return messages;
    }
    
    return [];
  }
  
  @override
  Future<void> addMessageToSession(String sessionId, AIMessage message) async {
    final messages = await getSessionHistory(sessionId);
    messages.add(message);
    _sessionCache[sessionId] = messages;
    
    // 更新数据库
    await _database.updateDocument('sessions', sessionId, {
      'messages': messages.map((m) => m.toJson()).toList(),
    });
  }
  
  @override
  Future<void> clearSessionHistory(String sessionId) async {
    _sessionCache[sessionId] = [];
    await _database.updateDocument('sessions', sessionId, {
      'messages': [],
    });
  }
  
  @override
  Future<void> deleteSession(String sessionId) async {
    _sessionCache.remove(sessionId);
    await _database.deleteDocument('sessions', sessionId);
  }
  
  @override
  Future<List<String>> getAllSessions() async {
    final sessions = await _database.readCollection('sessions');
    return sessions.map((s) => s['id'] as String).toList();
  }
  
  // ==================== 使用统计 ====================
  
  @override
  Future<void> recordUsage(String modelId, AIUsage usage) async {
    // 累加统计
    final currentUsage = _usageStats[modelId] ?? const AIUsage(
      promptTokens: 0,
      completionTokens: 0,
      totalTokens: 0,
    );
    
    _usageStats[modelId] = AIUsage(
      promptTokens: currentUsage.promptTokens + usage.promptTokens,
      completionTokens: currentUsage.completionTokens + usage.completionTokens,
      totalTokens: currentUsage.totalTokens + usage.totalTokens,
      cost: (currentUsage.cost ?? 0) + (usage.cost ?? 0),
      currency: usage.currency ?? currentUsage.currency,
    );
    
    // 保存到数据库
    await _database.addDocument('usage_stats', {
      'modelId': modelId,
      'usage': usage.toJson(),
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
  
  @override
  Future<AIUsage> getUsageStats(String modelId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    return _usageStats[modelId] ?? const AIUsage(
      promptTokens: 0,
      completionTokens: 0,
      totalTokens: 0,
    );
  }
  
  @override
  Future<Map<String, AIUsage>> getTotalUsageStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    return Map.from(_usageStats);
  }
  
  @override
  Future<void> resetUsageStats(String modelId) async {
    _usageStats.remove(modelId);
    // 从数据库删除相关记录
    final stats = await _database.queryDocuments('usage_stats',
        where: (doc) => doc['modelId'] == modelId);
    for (final stat in stats) {
      await _database.deleteDocument('usage_stats', stat['id']);
    }
  }
  
  // ==================== 错误处理 ====================
  
  @override
  Future<void> recordError(String modelId, AIError error) async {
    final errors = _errorHistory[modelId] ?? [];
    errors.add(error);
    _errorHistory[modelId] = errors;
    
    // 保存到数据库
    await _database.addDocument('error_history', {
      'modelId': modelId,
      'error': error.toJson(),
    });
  }
  
  @override
  Future<List<AIError>> getErrorHistory(String modelId, {
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    final errors = _errorHistory[modelId] ?? [];
    if (limit != null && errors.length > limit) {
      return errors.take(limit).toList();
    }
    return errors;
  }
  
  @override
  Future<void> clearErrorHistory(String modelId) async {
    _errorHistory.remove(modelId);
    // 从数据库删除相关记录
    final errors = await _database.queryDocuments('error_history',
        where: (doc) => doc['modelId'] == modelId);
    for (final error in errors) {
      await _database.deleteDocument('error_history', error['id']);
    }
  }
  
  // ==================== 缓存管理 ====================
  
  @override
  Future<void> cacheResponse(String key, AIResponse response) async {
    _responseCache[key] = response;
  }
  
  @override
  Future<AIResponse?> getCachedResponse(String key) async {
    return _responseCache[key];
  }
  
  @override
  Future<void> clearCache() async {
    _responseCache.clear();
  }
  
  @override
  Future<void> clearModelCache(String modelId) async {
    _responseCache.removeWhere((key, value) => value.modelId == modelId);
  }
  
  // ==================== 健康检查 ====================
  
  @override
  Future<Map<String, bool>> checkHealth() async {
    final health = <String, bool>{};
    
    final models = await getAvailableModels();
    for (final model in models.take(5)) { // 只检查前5个模型
      try {
        health[model.id] = await testModelConnection(model.id);
      } catch (e) {
        health[model.id] = false;
      }
    }
    
    return health;
  }
  
  @override
  Future<bool> checkModelHealth(String modelId) async {
    return await testModelConnection(modelId);
  }
  
  @override
  Future<Map<String, dynamic>> getServiceStatus() async {
    final health = await checkHealth();
    final totalModels = health.length;
    final healthyModels = health.values.where((h) => h).length;
    
    return {
      'totalModels': totalModels,
      'healthyModels': healthyModels,
      'healthyPercentage': totalModels > 0 ? (healthyModels / totalModels * 100) : 0,
      'modelHealth': health,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
  
  // ==================== 工具方法 ====================
  
  @override
  int estimateTokens(String text, {String? modelId}) {
    // 简单的token估算：大约4个字符等于1个token
    return (text.length / 4).ceil();
  }
  
  @override
  bool validateRequest(AIRequest request) {
    if (request.modelId.isEmpty) return false;
    if (request.prompt.isEmpty && (request.messages?.isEmpty ?? true)) return false;
    if (request.maxTokens <= 0) return false;
    if (request.temperature < 0 || request.temperature > 2) return false;
    return true;
  }
  
  @override
  String generateRequestId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(10000);
    return '${timestamp}_$random';
  }
  
  @override
  String formatError(AIError error) {
    return '[${error.code}] ${error.message}';
  }
  
  @override
  Future<double> calculateCost(String modelId, AIUsage usage) async {
    // 简单的成本计算（实际应该根据具体模型定价）
    const inputCostPer1K = 0.001; // $0.001 per 1K input tokens
    const outputCostPer1K = 0.002; // $0.002 per 1K output tokens
    
    final inputCost = (usage.promptTokens / 1000) * inputCostPer1K;
    final outputCost = (usage.completionTokens / 1000) * outputCostPer1K;
    
    return inputCost + outputCost;
  }
  
  // ==================== 私有方法 ====================
  
  /// 获取模型对应的数据源
  dynamic _getDataSourceForModel(String modelId) {
    final model = PredefinedAIModels.getModelById(modelId);
    if (model == null) {
      throw ModelUnavailableException('Model $modelId not found');
    }
    
    switch (model.provider) {
      case AIProvider.openai:
        return _openAIDataSource;
      case AIProvider.anthropic:
        return _claudeDataSource;
      case AIProvider.google:
        return _geminiDataSource;
      case AIProvider.deepseek:
        return _deepSeekDataSource;
      default:
        throw ModelUnavailableException('Unsupported provider: ${model.provider}');
    }
  }
  
  /// 配置数据源
  Future<void> _configureDataSource(AIModelConfig config) async {
    final model = await getModelById(config.modelId);
    if (model == null) return;
    
    switch (model.provider) {
      case AIProvider.openai:
        _openAIDataSource.setApiKey(config.apiKey, organizationId: config.organizationId);
        break;
      case AIProvider.anthropic:
        _claudeDataSource.setApiKey(config.apiKey);
        break;
      case AIProvider.google:
        _geminiDataSource.setApiKey(config.apiKey);
        break;
      case AIProvider.deepseek:
        _deepSeekDataSource.setApiKey(config.apiKey);
        break;
      default:
        break;
    }
  }
  
  /// 检查是否为OpenAI兼容模型
  bool _isOpenAICompatible(String modelId) {
    final model = PredefinedAIModels.getModelById(modelId);
    return model?.provider == AIProvider.openai || model?.provider == AIProvider.deepseek;
  }
}

/// 信号量实现，用于限制并发数
class Semaphore {
  final int maxCount;
  int _currentCount;
  final Queue<Completer<void>> _waitQueue = Queue<Completer<void>>();
  
  Semaphore(this.maxCount) : _currentCount = maxCount;
  
  Future<void> acquire() async {
    if (_currentCount > 0) {
      _currentCount--;
      return;
    }
    
    final completer = Completer<void>();
    _waitQueue.add(completer);
    return completer.future;
  }
  
  void release() {
    if (_waitQueue.isNotEmpty) {
      final completer = _waitQueue.removeFirst();
      completer.complete();
    } else {
      _currentCount++;
    }
  }
}