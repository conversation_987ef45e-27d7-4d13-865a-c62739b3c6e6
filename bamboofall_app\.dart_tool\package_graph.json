{"roots": ["bamboofall_app"], "packages": [{"name": "bamboofall_app", "version": "1.0.0+1", "dependencies": ["cupertino_icons", "device_info_plus", "dio", "equatable", "fluent_ui", "flutter", "flutter_markdown", "flutter_riverpod", "flutter_secure_storage", "freezed_annotation", "get_it", "json_annotation", "logger", "package_info_plus", "path_provider", "retrofit", "riverpod_annotation", "shared_preferences", "sqflite", "sqflite_common_ffi", "super_editor", "window_manager"], "devDependencies": ["build_runner", "flutter_lints", "flutter_test", "freezed", "integration_test", "json_serializable", "<PERSON><PERSON>", "retrofit_generator", "riverpod_generator"]}, {"name": "retrofit_generator", "version": "9.7.0", "dependencies": ["analyzer", "build", "built_collection", "code_builder", "dart_style", "dio", "protobuf", "retrofit", "source_gen"]}, {"name": "json_serializable", "version": "6.9.5", "dependencies": ["analyzer", "async", "build", "build_config", "collection", "dart_style", "json_annotation", "meta", "path", "pub_semver", "pubspec_parse", "source_gen", "source_helper"]}, {"name": "freezed", "version": "2.5.8", "dependencies": ["analyzer", "build", "build_config", "collection", "dart_style", "freezed_annotation", "json_annotation", "meta", "source_gen"]}, {"name": "riverpod_generator", "version": "2.6.4", "dependencies": ["analyzer", "build", "build_config", "collection", "crypto", "meta", "path", "riverpod_analyzer_utils", "riverpod_annotation", "source_gen"]}, {"name": "build_runner", "version": "2.5.4", "dependencies": ["analyzer", "args", "async", "build", "build_config", "build_daemon", "build_resolvers", "build_runner_core", "code_builder", "collection", "crypto", "dart_style", "frontend_server_client", "glob", "graphs", "http", "http_multi_server", "io", "js", "logging", "meta", "mime", "package_config", "path", "pool", "pub_semver", "pubspec_parse", "shelf", "shelf_web_socket", "stack_trace", "stream_transform", "timing", "watcher", "web", "web_socket_channel", "yaml"]}, {"name": "integration_test", "version": "0.0.0", "dependencies": ["flutter", "flutter_driver", "flutter_test", "path", "vm_service"]}, {"name": "<PERSON><PERSON>", "version": "5.4.6", "dependencies": ["analyzer", "build", "code_builder", "collection", "dart_style", "matcher", "meta", "path", "source_gen", "test_api"]}, {"name": "flutter_lints", "version": "5.0.0", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["clock", "collection", "fake_async", "flutter", "leak_tracker_flutter_testing", "matcher", "meta", "path", "stack_trace", "stream_channel", "test_api", "vector_math"]}, {"name": "package_info_plus", "version": "8.3.1", "dependencies": ["clock", "ffi", "flutter", "flutter_web_plugins", "http", "meta", "package_info_plus_platform_interface", "path", "web", "win32"]}, {"name": "device_info_plus", "version": "10.1.2", "dependencies": ["device_info_plus_platform_interface", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "web", "win32", "win32_registry"]}, {"name": "equatable", "version": "2.0.7", "dependencies": ["collection", "meta"]}, {"name": "logger", "version": "2.6.1", "dependencies": ["meta"]}, {"name": "get_it", "version": "8.2.0", "dependencies": ["async", "collection", "meta"]}, {"name": "freezed_annotation", "version": "2.4.4", "dependencies": ["collection", "json_annotation", "meta"]}, {"name": "flutter_markdown", "version": "0.7.7+1", "dependencies": ["flutter", "markdown", "meta", "path"]}, {"name": "super_editor", "version": "0.3.0-dev.34", "dependencies": ["attributed_text", "characters", "clock", "collection", "flutter", "flutter_test", "flutter_test_robots", "follow_the_leader", "http", "linkify", "logging", "overlord", "super_keyboard", "super_text_layout", "url_launcher", "uuid"]}, {"name": "window_manager", "version": "0.4.3", "dependencies": ["flutter", "path", "screen_retriever"]}, {"name": "fluent_ui", "version": "4.13.0", "dependencies": ["flutter", "flutter_localizations", "intl", "math_expressions", "recase", "scroll_pos"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "retrofit", "version": "4.7.2", "dependencies": ["dio", "meta"]}, {"name": "dio", "version": "5.9.0", "dependencies": ["async", "collection", "dio_web_adapter", "http_parser", "meta", "mime", "path"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "flutter_secure_storage", "version": "9.2.4", "dependencies": ["flutter", "flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_platform_interface", "flutter_secure_storage_web", "flutter_secure_storage_windows", "meta"]}, {"name": "riverpod_annotation", "version": "2.6.1", "dependencies": ["meta", "riverpod"]}, {"name": "flutter_riverpod", "version": "2.6.1", "dependencies": ["collection", "flutter", "meta", "riverpod", "state_notifier"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "source_gen", "version": "2.0.0", "dependencies": ["analyzer", "async", "build", "dart_style", "glob", "path", "pub_semver", "source_span", "yaml"]}, {"name": "protobuf", "version": "4.2.0", "dependencies": ["collection", "fixnum", "meta"]}, {"name": "dart_style", "version": "3.1.1", "dependencies": ["analyzer", "args", "collection", "package_config", "path", "pub_semver", "source_span", "yaml"]}, {"name": "code_builder", "version": "4.11.0", "dependencies": ["built_collection", "built_value", "collection", "matcher", "meta"]}, {"name": "built_collection", "version": "5.1.1", "dependencies": []}, {"name": "build", "version": "2.5.4", "dependencies": ["analyzer", "async", "build_runner_core", "built_collection", "built_value", "convert", "crypto", "glob", "graphs", "logging", "meta", "package_config", "path", "pool"]}, {"name": "analyzer", "version": "7.6.0", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "source_helper", "version": "1.3.7", "dependencies": ["analyzer", "source_gen"]}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "build_config", "version": "1.1.2", "dependencies": ["checked_yaml", "json_annotation", "path", "pubspec_parse", "yaml"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "riverpod_analyzer_utils", "version": "0.5.9", "dependencies": ["analyzer", "collection", "crypto", "custom_lint_core", "freezed_annotation", "meta", "path", "source_span"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "web_socket_channel", "version": "3.0.3", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "watcher", "version": "1.1.3", "dependencies": ["async", "path"]}, {"name": "timing", "version": "1.0.2", "dependencies": ["json_annotation"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "shelf_web_socket", "version": "3.0.0", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "pool", "version": "1.5.2", "dependencies": ["async", "stack_trace"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "http", "version": "1.5.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "build_runner_core", "version": "9.1.2", "dependencies": ["analyzer", "async", "build", "build_config", "build_resolvers", "build_runner", "built_collection", "built_value", "collection", "convert", "crypto", "glob", "graphs", "json_annotation", "logging", "meta", "package_config", "path", "pool", "timing", "watcher", "yaml"]}, {"name": "build_resolvers", "version": "2.5.4", "dependencies": ["analyzer", "async", "build", "build_runner_core", "collection", "convert", "crypto", "graphs", "logging", "package_config", "path", "pool", "pub_semver", "stream_transform"]}, {"name": "build_daemon", "version": "4.0.4", "dependencies": ["built_collection", "built_value", "crypto", "http_multi_server", "logging", "path", "pool", "shelf", "shelf_web_socket", "stream_transform", "watcher", "web_socket_channel"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "vm_service", "version": "15.0.2", "dependencies": []}, {"name": "flutter_driver", "version": "0.0.0", "dependencies": ["file", "flutter", "flutter_test", "fuchsia_remote_debug_protocol", "matcher", "meta", "path", "vm_service", "webdriver"]}, {"name": "test_api", "version": "0.7.6", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "lints", "version": "5.1.1", "dependencies": []}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.10", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.2.0", "dependencies": []}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "win32", "version": "5.14.0", "dependencies": ["ffi"]}, {"name": "package_info_plus_platform_interface", "version": "3.2.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["flutter"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "win32_registry", "version": "1.1.5", "dependencies": ["ffi", "win32"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "device_info_plus_platform_interface", "version": "7.0.3", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "markdown", "version": "7.3.0", "dependencies": ["args", "meta"]}, {"name": "flutter_test_robots", "version": "0.0.24", "dependencies": ["flutter", "flutter_test"]}, {"name": "overlord", "version": "0.0.3+5", "dependencies": ["collection", "flutter", "follow_the_leader", "logging"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "url_launcher", "version": "6.3.2", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "super_keyboard", "version": "0.2.2", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "flutter_test", "flutter_test_runners", "logging", "plugin_platform_interface"]}, {"name": "super_text_layout", "version": "0.1.19", "dependencies": ["attributed_text", "collection", "flutter", "flutter_test", "logging"]}, {"name": "linkify", "version": "5.0.0", "dependencies": []}, {"name": "follow_the_leader", "version": "0.0.4+8", "dependencies": ["flutter", "logging", "vector_math"]}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "attributed_text", "version": "0.4.5", "dependencies": ["characters", "collection", "logging", "meta", "test"]}, {"name": "screen_retriever", "version": "0.2.0", "dependencies": ["flutter", "screen_retriever_linux", "screen_retriever_macos", "screen_retriever_platform_interface", "screen_retriever_windows"]}, {"name": "math_expressions", "version": "2.7.0", "dependencies": ["petitparser", "vector_math"]}, {"name": "recase", "version": "4.1.0", "dependencies": []}, {"name": "scroll_pos", "version": "0.5.0", "dependencies": ["flutter"]}, {"name": "intl", "version": "0.20.2", "dependencies": ["clock", "meta", "path"]}, {"name": "flutter_localizations", "version": "0.0.0", "dependencies": ["flutter", "intl", "path"]}, {"name": "dio_web_adapter", "version": "2.1.1", "dependencies": ["dio", "http_parser", "meta", "web"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.13", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.2", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.18", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "flutter_secure_storage_windows", "version": "3.1.2", "dependencies": ["ffi", "flutter", "flutter_secure_storage_platform_interface", "path", "path_provider", "win32"]}, {"name": "flutter_secure_storage_web", "version": "1.2.1", "dependencies": ["flutter", "flutter_secure_storage_platform_interface", "flutter_web_plugins", "js"]}, {"name": "flutter_secure_storage_platform_interface", "version": "1.1.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_secure_storage_macos", "version": "3.1.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "flutter_secure_storage_linux", "version": "1.2.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "riverpod", "version": "2.6.1", "dependencies": ["collection", "meta", "stack_trace", "state_notifier"]}, {"name": "state_notifier", "version": "1.0.0", "dependencies": ["meta"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "built_value", "version": "8.12.0", "dependencies": ["built_collection", "collection", "fixnum", "meta"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "_fe_analyzer_shared", "version": "85.0.0", "dependencies": ["meta"]}, {"name": "checked_yaml", "version": "2.0.4", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "custom_lint_core", "version": "0.7.5", "dependencies": ["analyzer", "analyzer_plugin", "collection", "custom_lint_visitor", "glob", "matcher", "meta", "package_config", "path", "pubspec_parse", "source_span", "uuid", "yaml"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "web_socket", "version": "1.0.1", "dependencies": ["web"]}, {"name": "webdriver", "version": "3.1.0", "dependencies": ["matcher", "path", "stack_trace", "sync_http"]}, {"name": "fuchsia_remote_debug_protocol", "version": "0.0.0", "dependencies": ["meta", "process", "vm_service"]}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "leak_tracker_testing", "version": "3.0.2", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "11.0.2", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_web", "version": "2.4.1", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "url_launcher_macos", "version": "3.2.3", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_ios", "version": "6.3.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_android", "version": "6.3.22", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "flutter_test_runners", "version": "0.0.4", "dependencies": ["flutter", "flutter_test", "meta"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.30", "dependencies": ["flutter"]}, {"name": "test", "version": "1.26.2", "dependencies": ["analyzer", "async", "boolean_selector", "collection", "coverage", "http_multi_server", "io", "js", "matcher", "node_preamble", "package_config", "path", "pool", "shelf", "shelf_packages_handler", "shelf_static", "shelf_web_socket", "source_span", "stack_trace", "stream_channel", "test_api", "test_core", "typed_data", "web_socket_channel", "webkit_inspection_protocol", "yaml"]}, {"name": "screen_retriever_windows", "version": "0.2.0", "dependencies": ["flutter", "screen_retriever_platform_interface"]}, {"name": "screen_retriever_platform_interface", "version": "0.2.0", "dependencies": ["flutter", "json_annotation", "plugin_platform_interface"]}, {"name": "screen_retriever_macos", "version": "0.2.0", "dependencies": ["flutter", "screen_retriever_platform_interface"]}, {"name": "screen_retriever_linux", "version": "0.2.0", "dependencies": ["flutter", "screen_retriever_platform_interface"]}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "custom_lint_visitor", "version": "1.0.0+7.7.0", "dependencies": ["analyzer"]}, {"name": "analyzer_plugin", "version": "0.13.4", "dependencies": ["analyzer", "collection", "dart_style", "path", "pub_semver", "yaml"]}, {"name": "sync_http", "version": "0.3.1", "dependencies": []}, {"name": "process", "version": "5.0.5", "dependencies": ["file", "path", "platform"]}, {"name": "webkit_inspection_protocol", "version": "1.2.1", "dependencies": ["logging"]}, {"name": "test_core", "version": "0.6.11", "dependencies": ["analyzer", "args", "async", "boolean_selector", "collection", "coverage", "frontend_server_client", "glob", "io", "meta", "package_config", "path", "pool", "source_map_stack_trace", "source_maps", "source_span", "stack_trace", "stream_channel", "test_api", "vm_service", "yaml"]}, {"name": "shelf_static", "version": "1.1.3", "dependencies": ["convert", "http_parser", "mime", "path", "shelf"]}, {"name": "shelf_packages_handler", "version": "3.0.2", "dependencies": ["path", "shelf", "shelf_static"]}, {"name": "node_preamble", "version": "2.0.2", "dependencies": []}, {"name": "coverage", "version": "1.15.0", "dependencies": ["args", "cli_config", "glob", "logging", "meta", "package_config", "path", "source_maps", "stack_trace", "vm_service", "yaml"]}, {"name": "source_maps", "version": "0.10.13", "dependencies": ["source_span"]}, {"name": "source_map_stack_trace", "version": "2.1.2", "dependencies": ["path", "source_maps", "stack_trace"]}, {"name": "cli_config", "version": "0.2.0", "dependencies": ["args", "yaml"]}, {"name": "sqflite", "version": "2.4.2", "dependencies": ["flutter", "path", "sqflite_android", "sqflite_common", "sqflite_darwin", "sqflite_platform_interface"]}, {"name": "sqflite_platform_interface", "version": "2.4.0", "dependencies": ["flutter", "meta", "platform", "plugin_platform_interface", "sqflite_common"]}, {"name": "sqflite_darwin", "version": "2.4.2", "dependencies": ["flutter", "meta", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_common", "version": "2.5.6", "dependencies": ["meta", "path", "synchronized"]}, {"name": "sqflite_android", "version": "2.4.2+2", "dependencies": ["flutter", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_common_ffi", "version": "2.3.6", "dependencies": ["meta", "path", "sqflite_common", "sqlite3", "synchronized"]}, {"name": "synchronized", "version": "3.4.0", "dependencies": []}, {"name": "sqlite3", "version": "2.9.0", "dependencies": ["collection", "ffi", "meta", "path", "typed_data", "web"]}], "configVersion": 1}