import 'package:flutter/material.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

import '../../domain/entities/app_settings.dart';

/// 备份设置面板
class BackupSettingsPanel extends StatelessWidget {
  final BackupSettings settings;

  const BackupSettingsPanel({
    super.key,
    required this.settings,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '备份设置',
            style: fluent.FluentTheme.of(context).typography.title,
          ),
          const SizedBox(height: 16),
          
          // 备份设置选项
          fluent.Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '自动备份',
                    style: fluent.FluentTheme.of(context).typography.subtitle,
                  ),
                  const SizedBox(height: 8),
                  const Text('备份设置功能正在开发中...'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}