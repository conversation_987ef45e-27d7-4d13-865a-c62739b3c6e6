import 'package:flutter/material.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

import '../../domain/entities/app_settings.dart';

/// 隐私设置面板
class PrivacySettingsPanel extends StatelessWidget {
  final PrivacySettings settings;

  const PrivacySettingsPanel({
    super.key,
    required this.settings,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '隐私设置',
            style: fluent.FluentTheme.of(context).typography.title,
          ),
          const SizedBox(height: 16),
          
          // 隐私设置选项
          fluent.Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '数据收集',
                    style: fluent.FluentTheme.of(context).typography.subtitle,
                  ),
                  const SizedBox(height: 8),
                  const Text('隐私设置功能正在开发中...'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}